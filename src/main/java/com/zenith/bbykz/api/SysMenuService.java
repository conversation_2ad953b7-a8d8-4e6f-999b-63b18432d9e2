package com.zenith.bbykz.api;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.zenith.bbykz.model.dto.SysMenuDTO;
import com.zenith.bbykz.model.dto.SysMenuListDTO;
import com.zenith.bbykz.model.entity.SysMenu;
import com.zenith.bbykz.model.vo.SysMenuVO;

import java.util.List;

/**
 * <p>
 * 菜单表 服务Api
 * </p>
 *
 * <AUTHOR>
 * @date 2023-09-14 17:14:26
 */
public interface SysMenuService extends IService<SysMenu> {
    /***
     * 新增
     */
    SysMenu save(SysMenuDTO dto);

    /**
     * 详情
     */
    SysMenuVO findById(String id);

    /**
     * 修改
     */
    Boolean update(SysMenuDTO dto);

    /**
     * 删除
     */
    Boolean delete(String id);

    /**
     * 列表查询
     */
    Page<SysMenu> list(SysMenuListDTO dto);

    List<SysMenu> getListBySystemId(String systemId);
}
