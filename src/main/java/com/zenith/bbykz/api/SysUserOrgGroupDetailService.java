package com.zenith.bbykz.api;

import com.baomidou.mybatisplus.extension.service.IService;
import com.zenith.bbykz.model.entity.SysUserOrgGroupDetail;

import java.util.List;

/**
 * <p>
 * 用户机构组详情 服务Api
 * </p>
 *
 * <AUTHOR>
 * @date 2024-03-13 10:46:03
 */
public interface SysUserOrgGroupDetailService extends IService<SysUserOrgGroupDetail> {
    boolean saveByGroupId(String id, List<SysUserOrgGroupDetail> saveList);

    List<SysUserOrgGroupDetail> findByGroupId(String groupId);

    void deleteByGroupId(String groupId);
}
