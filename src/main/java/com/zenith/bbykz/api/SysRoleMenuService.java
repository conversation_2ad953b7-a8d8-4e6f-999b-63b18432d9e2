package com.zenith.bbykz.api;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.zenith.bbykz.model.dto.SysRoleDTO;
import com.zenith.bbykz.model.dto.SysRoleMenuDTO;
import com.zenith.bbykz.model.dto.SysRoleMenuListDTO;
import com.zenith.bbykz.model.entity.SysRoleMenu;
import com.zenith.bbykz.model.vo.SysRoleMenuVO;

import java.util.List;

/**
 * <p>
 * sys_role_menu 服务Api
 * </p>
 *
 * <AUTHOR>
 * @date 2023-09-14 17:14:26
 */
public interface SysRoleMenuService extends IService<SysRoleMenu> {
    /***
     * 新增
     */
    SysRoleMenu save(SysRoleMenuDTO dto);

    /**
     * 详情
     */
    SysRoleMenuVO findById(String id);

    /**
     * 修改
     */
    Boolean update(SysRoleMenuDTO dto);

    /**
     * 删除
     */
    Boolean delete(String id);

    /**
     * 列表查询
     */
    Page<SysRoleMenu> list(SysRoleMenuListDTO dto);

    boolean saveMenus(SysRoleDTO dto);

    List<String> findByRoleId(String roleId);

    List<SysRoleMenu> findByRoleList(List<String> roleList);
}
