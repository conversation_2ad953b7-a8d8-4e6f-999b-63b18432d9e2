package com.zenith.bbykz.api;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.efficient.common.result.Result;
import com.zenith.bbykz.model.dto.LoginDTO;
import com.zenith.bbykz.model.dto.SysUserDTO;
import com.zenith.bbykz.model.dto.SysUserListDTO;
import com.zenith.bbykz.model.entity.OrgInfo;
import com.zenith.bbykz.model.entity.SysUser;
import com.zenith.bbykz.model.vo.SysUserVO;

import java.util.List;

/**
 * <p>
 * sys_user 服务Api
 * </p>
 *
 * <AUTHOR>
 * @date 2023-09-14 17:14:26
 */
public interface SysUserService extends IService<SysUser> {
    /***
     * 新增
     */
    Result save(SysUserDTO dto);

    /**
     * 详情
     */
    SysUserVO findById(String id, String userPostId);

    /**
     * 修改
     */
    Result update(SysUserDTO dto);

    /**
     * 删除
     */
    Boolean delete(String id);

    /**
     * 列表查询
     */
    Page<SysUserVO> list(SysUserListDTO dto);

    Result login(LoginDTO dto);

    SysUser getByAccount(String account);

    SysUser getByZwddId(String zwddId);

    /**
     * 修改政务钉id
     */
    Result updateZwd(String zwddId, String accountId);

    Result updatePwd(String account, String password);

    SysUser getByAccountNeId(String account, String id);

    Result getPermission(String systemId, String userPostId);

    List<SysUser> getByAccountList(List<String> accountList);

    Page<SysUserVO> sysList(SysUserListDTO dto);

    String findZwddIdById(String userId);

    Result changeUnit(String userPostId);

    Result sysUpdate(SysUserDTO dto);

    List<SysUser> findUserByDeptIdAndSystemId(String unitId, String currSystemId);

    OrgInfo findOrgInfoByUserAndSystemId(String createUserId, String currSystemId);

    OrgInfo findOrgInfoByUserAndSystemIdAndPostId(String createUserId, String currSystemId, String userPostId);

    OrgInfo findBbUnitByUserId(String userId,String systemId);

    void deleteAll();

    List<SysUser> findBySzdwId(String unitId);

    List<SysUser> findBbUserByGeoCode(String geocode);
}
