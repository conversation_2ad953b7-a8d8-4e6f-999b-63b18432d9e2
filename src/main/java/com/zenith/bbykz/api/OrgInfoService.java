package com.zenith.bbykz.api;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.efficient.common.result.Result;
import com.zenith.bbykz.model.base.TreeNode;
import com.zenith.bbykz.model.dto.OrgInfoDTO;
import com.zenith.bbykz.model.dto.OrgInfoListDTO;
import com.zenith.bbykz.model.dto.OrgInfoTreeDTO;
import com.zenith.bbykz.model.entity.OrgInfo;
import com.zenith.bbykz.model.vo.OrgInfoVO;
import com.zenith.bbykz.model.vo.register.UnitRegisterInfo;

import java.util.Arrays;
import java.util.List;
import java.util.Set;

/**
 * <p>
 * org_info 服务Api
 * </p>
 *
 * <AUTHOR>
 * @date 2023-09-14 17:51:37
 */
public interface OrgInfoService extends IService<OrgInfo> {
    /***
     * 新增
     */
    Result save(OrgInfoDTO dto);

    /**
     * 详情
     */
    OrgInfoVO findById(String id);

    String findLevelCodeById(String id);

    /**
     * 获取登记信息查询code
     * @param id
     * @return
     */
    String findDjxxLevel(String id);

    /**
     * 修改
     */
    Result update(OrgInfoDTO dto);

    /**
     * 删除
     */
    Result delete(String id);

    /**
     * 列表查询
     */
    Page<OrgInfoVO> list(OrgInfoListDTO dto);

    TreeNode tree(OrgInfoTreeDTO dto);

    List<OrgInfo> getListByParentCodes(List<String> parentCodes);

    List<OrgInfo> getListByCodes(List<String> orgCodeList);

    String createLevelCode(String parentId);

    String getBelongById(String parentId);

    OrgInfo getByOrgCode(String organizationCode);

    OrgInfo getUnitByDeptId(String deptId);

    List<OrgInfo> findByIdList(List<String> orgIdList);

    List<OrgInfo> treeSearch(OrgInfoListDTO dto);

    OrgInfo findTop();

    OrgInfo getByLevelCode(String levelCode);

    List<OrgInfo> findByLevelCodeList(List<String> levelCodeList);

    Result updateNew(OrgInfoDTO dto);

    Result<UnitRegisterInfo> getRegisterInfo(String id);

    OrgInfo findByCreditCode(String creditCode);

    void deleteAll();

    List<OrgInfo> selectByParentCode(List<String> collect);

    OrgInfo findByJgsyCode(String jgsyCode);

    OrgInfo findOneByLevelCode(String levelCode);

    Set<String> treeLevel(String levelCode);

    List<String> findCreditCodeListById(String unitLevelCode);

    List<OrgInfo> findAllSy();

    Result findRegisterInfo(String id);
}
