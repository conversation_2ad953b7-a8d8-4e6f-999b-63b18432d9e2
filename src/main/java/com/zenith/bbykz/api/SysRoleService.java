package com.zenith.bbykz.api;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.efficient.common.result.Result;
import com.zenith.bbykz.model.dto.SysRoleDTO;
import com.zenith.bbykz.model.dto.SysRoleListDTO;
import com.zenith.bbykz.model.entity.SysRole;
import com.zenith.bbykz.model.vo.SysRoleVO;

/**
 * <p>
 * sys_role 服务Api
 * </p>
 *
 * <AUTHOR>
 * @date 2023-09-14 17:14:26
 */
public interface SysRoleService extends IService<SysRole> {
    /***
     * 新增
     */
    Result save(SysRoleDTO dto);

    /**
     * 详情
     */
    SysRoleVO findById(String id);

    /**
     * 修改
     */
    Result update(SysRoleDTO dto);

    /**
     * 删除
     */
    Boolean delete(String id);

    /**
     * 列表查询
     */
    Page<SysRole> list(SysRoleListDTO dto);

    Result deleteRole(String id);
}
