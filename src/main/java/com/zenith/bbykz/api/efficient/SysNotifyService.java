package com.zenith.bbykz.api.efficient;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.efficient.common.result.Result;
import com.zenith.bbykz.model.dto.efficient.SysNotifyDTO;
import com.zenith.bbykz.model.dto.efficient.SysNotifyListDTO;
import com.zenith.bbykz.model.entity.efficient.SysNotify;
import com.zenith.bbykz.model.vo.efficient.SysNotifyVO;

import java.util.List;

/**
 * <p>
 * 系统消息通知 服务Api
 * </p>
 *
 * <AUTHOR>
 * @date 2024-03-18 15:07:00
 */
public interface SysNotifyService extends IService<SysNotify> {
    SysNotify saveNotify(SysNotifyDTO notify);

    SysNotify getById(String notifyId);

    boolean changeState(String state, String notifyId);

    boolean changeState(String state, String recipientMsgId, String notifyId);

    boolean deleteById(String notifyId);

    boolean deleteByBatchNum(String batchNum);

    Result sendTodo(SysNotifyDTO dto);

    Result finishTodo(String userId, String notifyId);

    Page<SysNotifyVO> list(SysNotifyListDTO dto);

    SysNotify getByBizId(String bizId);

    Result<SysNotifyVO> find(String id);

    List<SysNotify> findListByBizId(String bizId);
}
