package com.zenith.bbykz.api;

import com.baomidou.mybatisplus.extension.service.IService;
import com.efficient.common.result.Result;
import com.zenith.bbykz.model.dto.UserSystemDTO;
import com.zenith.bbykz.model.entity.SysUserSystem;
import com.zenith.bbykz.model.vo.SysUserSystemVO;

import java.util.List;

/**
 * <p>
 * 用户系统表 服务Api
 * </p>
 *
 * <AUTHOR>
 * @date 2024-02-29 10:36:56
 */
public interface SysUserSystemService extends IService<SysUserSystem> {
    Result save(UserSystemDTO dto);

    List<SysUserSystemVO> findByUserId(String userId);

    List<SysUserSystem> findByUserPostId(String userPostId);

    boolean deleteByUserAndSystemIdList(String userId, List<String> systemIdList);

    Result findSystemList();

    SysUserSystem findByUserIdAndSystemId(String userId, String systemId, String userPostId);

    void updateUserOrgGroup(String currSystemId, String userId, String userOrgGroupId);

    SysUserSystem findByUserPostIdAndSystemId(String userPostId, String currSystemId);
}
