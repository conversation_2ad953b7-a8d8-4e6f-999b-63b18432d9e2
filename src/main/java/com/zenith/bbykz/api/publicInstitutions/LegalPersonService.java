package com.zenith.bbykz.api.publicInstitutions;

import cn.hutool.core.date.DateTime;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.efficient.common.result.Result;
import com.zenith.bbykz.model.dto.LegalPersonRegistrationListDTO;
import com.zenith.bbykz.model.entity.LegalPersonRegistration;
import com.zenith.bbykz.model.entity.register.dynamic.UnitBd;
import com.zenith.bbykz.model.vo.LegalPersonRegistrationVO;
import com.zenith.bbykz.model.vo.register.UnitRegisterInfo;

import javax.validation.constraints.NotNull;
import java.io.IOException;
import java.util.List;

/**
 * 事业单位法人登记服务
 *
 * <AUTHOR>
 * @date 2023/9/12
 */
public interface LegalPersonService extends IService<LegalPersonRegistration> {
    Result importFile(String fileId) throws IOException;

    Result queryImportRecords( Integer pageNum,  Integer pageSize, String keyWord, String type, String key, String captcha);

    Page<LegalPersonRegistration> list(LegalPersonRegistrationListDTO dto);

    LegalPersonRegistration findLastVersion();

    void deleteByVersion(Integer lastVersion);

    Result rollback();

    Page<LegalPersonRegistrationVO> bdList(LegalPersonRegistrationListDTO dto);

    UnitRegisterInfo findByCreditCode(String creditCode);

    LegalPersonRegistration findEntityByCreditCode(String creditCode);

    List<LegalPersonRegistration> findCertificateExpiration(DateTime startDate, DateTime endDate);

    UnitBd createWarnInfo(LegalPersonRegistration et);

    /**
     * 和中央事业单位数据进行比对
     * @return
     */
    Result verifyZyInfo();
}
