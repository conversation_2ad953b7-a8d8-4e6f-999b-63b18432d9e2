package com.zenith.bbykz.api.publicInstitutions;

import cn.hutool.core.date.DateTime;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.efficient.common.result.Result;
import com.zenith.bbykz.model.dto.UnifiedSocialCreditCodeDTO;
import com.zenith.bbykz.model.entity.UnifiedSocialCreditCode;
import com.zenith.bbykz.model.vo.UnifiedSocialCreditCodeVO;
import com.zenith.bbykz.model.vo.register.UnitRegisterInfo;

import java.io.IOException;
import java.util.List;

/**
 * 机关事业单位统一社会信用代码服务
 *
 * <AUTHOR>
 * @date 2023/9/12
 */
public interface UnifiedSocialCreditCodeService extends IService<UnifiedSocialCreditCode> {
    Result importFile(String fileId) throws IOException;

    Result queryImportRecords(Integer pageNum, Integer pageSize, String keyWord, String key, String captcha);

    Page<UnifiedSocialCreditCode> list(UnifiedSocialCreditCodeDTO dto);

    UnifiedSocialCreditCode findLastVersion();

    void deleteByVersion(Integer lastVersion);

    Result rollback();

    Page<UnifiedSocialCreditCodeVO> bdList(UnifiedSocialCreditCodeDTO dto);

    UnitRegisterInfo findByCreditCode(String creditCode);

    UnifiedSocialCreditCode findEntityByCreditCode(String creditCode);

    List<UnifiedSocialCreditCode> findCertificateExpiration(DateTime startDate, DateTime endDate);

    void createWarnInfo(UnifiedSocialCreditCode et);

    Result verifyZyInfo();
}
