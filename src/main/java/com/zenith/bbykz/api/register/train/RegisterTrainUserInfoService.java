package com.zenith.bbykz.api.register.train;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.efficient.common.result.Result;
import com.zenith.bbykz.model.dto.register.train.RegisterTrainUserInfoDTO;
import com.zenith.bbykz.model.dto.register.train.RegisterTrainUserInfoListDTO;
import com.zenith.bbykz.model.entity.register.train.RegisterTrainUserInfo;
import com.zenith.bbykz.model.vo.register.train.RegisterTrainUserInfoVO;

import java.util.List;

/**
 * <p>
 * 登记信息-培训管理-学习进度 服务Api
 * </p>
 *
 * <AUTHOR>
 * @date 2024-03-06 14:39:04
 */
public interface RegisterTrainUserInfoService extends IService<RegisterTrainUserInfo> {
    /***
     * 新增
     */
    Result<RegisterTrainUserInfo> save(RegisterTrainUserInfoDTO dto);

    /**
     * 详情
     */
    Result<RegisterTrainUserInfoVO> findById(String id);

    /**
     * 修改
     */
    Result<Boolean> update(RegisterTrainUserInfoDTO dto);

    /**
     * 删除
     */
    Result<Boolean> delete(String id);

    /**
     * 列表查询
     */
    Page<RegisterTrainUserInfoVO> list(RegisterTrainUserInfoListDTO dto);

    RegisterTrainUserInfo findByUserIdAndCourseId(String userId, String courseId, String courseDetailId, String trainFileId, String fileId);

    List<RegisterTrainUserInfo> findCourseUserInfo(String userId, String courseId);

    void syncCourseUser(String userId, String courseId);
}
