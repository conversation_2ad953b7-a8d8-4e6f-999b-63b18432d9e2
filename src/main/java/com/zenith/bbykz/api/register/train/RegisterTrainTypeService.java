package com.zenith.bbykz.api.register.train;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.efficient.common.entity.TreeNode;
import com.efficient.common.result.Result;
import com.zenith.bbykz.model.dto.register.train.RegisterTrainTypeDTO;
import com.zenith.bbykz.model.dto.register.train.RegisterTrainTypeListDTO;
import com.zenith.bbykz.model.entity.register.train.RegisterTrainType;
import com.zenith.bbykz.model.vo.register.train.RegisterTrainTypeVO;

import java.util.List;

/**
 * <p>
 * 登记信息-培训管理-课程分类 服务Api
 * </p>
 *
 * <AUTHOR>
 * @date 2024-03-05 15:05:10
 */
public interface RegisterTrainTypeService extends IService<RegisterTrainType> {
    /***
     * 新增
     */
    Result<RegisterTrainType> save(RegisterTrainTypeDTO dto);

    /**
     * 详情
     */
    Result<RegisterTrainTypeVO> findById(String id);

    /**
     * 修改
     */
    Result<Boolean> update(RegisterTrainTypeDTO dto);

    /**
     * 删除
     */
    Result<Boolean> delete(String id);

    /**
     * 列表查询
     */
    Page<RegisterTrainType> list(RegisterTrainTypeListDTO dto);

    List<TreeNode> select(RegisterTrainTypeDTO dto);

    Result<Boolean> enabled(String id, Integer enabled);
}
