package com.zenith.bbykz.api.register.train;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.efficient.common.result.Result;
import com.zenith.bbykz.model.dto.register.train.RegisterTrainCourseDTO;
import com.zenith.bbykz.model.dto.register.train.RegisterTrainCourseListDTO;
import com.zenith.bbykz.model.entity.register.train.RegisterTrainCourse;
import com.zenith.bbykz.model.vo.register.train.RegisterTrainCourseVO;

/**
 * <p>
 * 登记信息-培训管理-课程管理 服务Api
 * </p>
 *
 * <AUTHOR>
 * @date 2024-03-05 15:44:53
 */
public interface RegisterTrainCourseService extends IService<RegisterTrainCourse> {
    /***
     * 新增
     */
    Result<RegisterTrainCourse> save(RegisterTrainCourseDTO dto);

    /**
     * 详情
     */
    Result<RegisterTrainCourseVO> findById(String id);

    /**
     * 修改
     */
    Result<Boolean> update(RegisterTrainCourseDTO dto);

    /**
     * 删除
     */
    Result<Boolean> delete(String id);

    /**
     * 列表查询
     */
    Page<RegisterTrainCourse> list(RegisterTrainCourseListDTO dto);
}
