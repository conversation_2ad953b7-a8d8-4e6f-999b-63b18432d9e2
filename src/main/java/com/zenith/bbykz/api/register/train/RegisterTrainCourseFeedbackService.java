package com.zenith.bbykz.api.register.train;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.efficient.common.result.Result;
import com.zenith.bbykz.model.dto.register.train.RegisterTrainCourseFeedbackDTO;
import com.zenith.bbykz.model.dto.register.train.RegisterTrainCourseFeedbackListDTO;
import com.zenith.bbykz.model.entity.register.train.RegisterTrainCourseFeedback;
import com.zenith.bbykz.model.vo.register.train.RegisterTrainCourseFeedbackVO;

/**
 * <p>
 * 登记信息-培训管理-反馈意见 服务Api
 * </p>
 *
 * <AUTHOR>
 * @date 2024-03-05 15:44:53
 */
public interface RegisterTrainCourseFeedbackService extends IService<RegisterTrainCourseFeedback> {
    /***
     * 新增
     */
    Result<RegisterTrainCourseFeedback> save(RegisterTrainCourseFeedbackDTO dto);

    /**
     * 详情
     */
    Result<RegisterTrainCourseFeedbackVO> findById(String id);

    /**
     * 修改
     */
    Result<Boolean> update(RegisterTrainCourseFeedbackDTO dto);

    /**
     * 删除
     */
    Result<Boolean> delete(String id);

    /**
     * 列表查询
     */
    Page<RegisterTrainCourseFeedbackVO> list(RegisterTrainCourseFeedbackListDTO dto);
}
