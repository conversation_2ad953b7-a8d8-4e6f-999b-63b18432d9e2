package com.zenith.bbykz.api.register.train;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.efficient.common.result.Result;
import com.zenith.bbykz.model.dto.register.train.RegisterTrainCourseDetailDTO;
import com.zenith.bbykz.model.dto.register.train.RegisterTrainCourseDetailListDTO;
import com.zenith.bbykz.model.entity.register.train.RegisterTrainCourseDetail;
import com.zenith.bbykz.model.vo.register.train.RegisterTrainCourseDetailVO;

import java.util.List;

/**
 * <p>
 * 登记信息-培训管理-课程管理关联课件 服务Api
 * </p>
 *
 * <AUTHOR>
 * @date 2024-03-05 15:44:53
 */
public interface RegisterTrainCourseDetailService extends IService<RegisterTrainCourseDetail> {
    /***
     * 新增
     */
    Result<RegisterTrainCourseDetail> save(RegisterTrainCourseDetailDTO dto);

    /**
     * 详情
     */
    Result<RegisterTrainCourseDetailVO> findById(String id);

    /**
     * 修改
     */
    Result<Boolean> update(RegisterTrainCourseDetailDTO dto);

    /**
     * 删除
     */
    Result<Boolean> delete(String id);

    /**
     * 列表查询
     */
    Page<RegisterTrainCourseDetail> list(RegisterTrainCourseDetailListDTO dto);

    List<RegisterTrainCourseDetail> findByCourseId(String courseId);
}
