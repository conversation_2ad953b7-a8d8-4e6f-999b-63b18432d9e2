package com.zenith.bbykz.api.register.dynamic;

import com.baomidou.mybatisplus.extension.service.IService;
import com.zenith.bbykz.common.enums.NotifyState;
import com.zenith.bbykz.model.entity.register.dynamic.UnitBdFlow;

import java.util.List;

/**
 * <p>
 * 登记信息-变动流程记录 服务Api
 * </p>
 *
 * <AUTHOR>
 * @date 2024-04-18 10:01:34
 */
public interface UnitBdFlowService extends IService<UnitBdFlow> {

    boolean saveByBizId(String bizId, NotifyState notifyState);

    List<UnitBdFlow> findByBizId(String bizId);

    UnitBdFlow findByBizIdAndState(String bizId, String state);

    void deleteAll();
}
