package com.zenith.bbykz.api.register.reconnaissance;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.efficient.common.result.Result;
import com.zenith.bbykz.model.dto.register.reconnaissance.RegisterReconnaissanceDTO;
import com.zenith.bbykz.model.dto.register.reconnaissance.RegisterReconnaissanceListDTO;
import com.zenith.bbykz.model.entity.register.reconnaissance.RegisterReconnaissance;
import com.zenith.bbykz.model.vo.register.reconnaissance.RegisterReconnaissanceVO;

/**
 * <p>
 * 登记信息-内部事务-实地勘察 服务Api
 * </p>
 *
 * <AUTHOR>
 * @date 2024-03-28 11:28:37
 */
public interface RegisterReconnaissanceService extends IService<RegisterReconnaissance> {
    /***
     * 新增
     */
    Result<RegisterReconnaissance> save(RegisterReconnaissanceDTO dto);

    /**
     * 详情
     */
    Result<RegisterReconnaissanceVO> findById(String id);

    /**
     * 修改
     */
    Result<Boolean> update(RegisterReconnaissanceDTO dto);

    /**
     * 删除
     */
    Result<Boolean> delete(String id);

    /**
     * 列表查询
     */
    Page<RegisterReconnaissanceVO> list(RegisterReconnaissanceListDTO dto);
}
