package com.zenith.bbykz.api.register.interior;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.efficient.common.result.Result;
import com.zenith.bbykz.model.dto.register.interior.RegisterLegalPersonLogDTO;
import com.zenith.bbykz.model.dto.register.interior.RegisterLegalPersonLogListDTO;
import com.zenith.bbykz.model.entity.register.interior.RegisterLegalPersonLib;
import com.zenith.bbykz.model.entity.register.interior.RegisterLegalPersonLog;
import com.zenith.bbykz.model.vo.register.interior.RegisterLegalPersonLogVO;

import java.util.List;

/**
 * <p>
 * 登记信息-内部事务-法人库日志 服务Api
 * </p>
 *
 * <AUTHOR>
 * @date 2024-03-12 17:24:37
 */
public interface RegisterLegalPersonLogService extends IService<RegisterLegalPersonLog> {
    /***
     * 新增
     */
    Result<RegisterLegalPersonLog> save(RegisterLegalPersonLogDTO dto);

    /**
     * 详情
     */
    Result<RegisterLegalPersonLogVO> findById(String id);

    /**
     * 修改
     */
    Result<Boolean> update(RegisterLegalPersonLogDTO dto);

    /**
     * 删除
     */
    Result<Boolean> delete(String id);

    /**
     * 列表查询
     */
    Page<RegisterLegalPersonLogVO> list(RegisterLegalPersonLogListDTO dto);

    void saveByVerify(String name, String idCard, List<RegisterLegalPersonLib> list);
}
