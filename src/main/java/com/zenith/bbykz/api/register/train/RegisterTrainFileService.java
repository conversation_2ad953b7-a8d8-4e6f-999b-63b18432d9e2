package com.zenith.bbykz.api.register.train;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.efficient.common.result.Result;
import com.zenith.bbykz.model.dto.register.train.RegisterTrainFileDTO;
import com.zenith.bbykz.model.dto.register.train.RegisterTrainFileListDTO;
import com.zenith.bbykz.model.entity.register.train.RegisterTrainFile;
import com.zenith.bbykz.model.vo.register.train.RegisterTrainFileVO;

/**
 * <p>
 * 登记信息-培训管理-课件管理 服务Api
 * </p>
 *
 * <AUTHOR>
 * @date 2024-03-05 15:44:53
 */
public interface RegisterTrainFileService extends IService<RegisterTrainFile> {
    /***
     * 新增
     */
    Result<RegisterTrainFile> save(RegisterTrainFileDTO dto);

    /**
     * 详情
     */
    Result<RegisterTrainFileVO> findById(String id);

    /**
     * 修改
     */
    Result<Boolean> update(RegisterTrainFileDTO dto);

    /**
     * 删除
     */
    Result<Boolean> delete(String id);

    /**
     * 列表查询
     */
    Page<RegisterTrainFile> list(RegisterTrainFileListDTO dto);
}
