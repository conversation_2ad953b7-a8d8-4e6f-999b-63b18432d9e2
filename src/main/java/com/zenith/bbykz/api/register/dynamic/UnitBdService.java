package com.zenith.bbykz.api.register.dynamic;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.efficient.common.result.Result;
import com.zenith.bbykz.model.dto.register.dynamic.UnitBdDTO;
import com.zenith.bbykz.model.dto.register.dynamic.UnitBdFlowDTO;
import com.zenith.bbykz.model.dto.register.dynamic.UnitBdListDTO;
import com.zenith.bbykz.model.entity.register.dynamic.UnitBd;
import com.zenith.bbykz.model.vo.register.dynamic.UnitBdFlowVO;
import com.zenith.bbykz.model.vo.register.dynamic.UnitBdVO;
import com.zenith.bbykz.model.vo.register.dynamic.UnitBdWarnVO;

import java.util.List;

/**
 * <p>
 * 登记信息-预警感知-信息变动 服务Api
 * </p>
 *
 * <AUTHOR>
 * @date 2024-04-09 11:50:41
 */
public interface UnitBdService extends IService<UnitBd> {
    /***
     * 新增
     */
    Result<UnitBd> save(UnitBdDTO dto);

    /**
     * 详情
     */
    Result<UnitBdVO> findById(String id);

    /**
     * 修改
     */
    Result<Boolean> update(UnitBdDTO dto);

    /**
     * 删除
     */
    Result<Boolean> delete(String id);

    /**
     * 列表查询
     */
    Page<UnitBdVO> list(UnitBdListDTO dto);

    Page<UnitBdWarnVO> warnList(UnitBdListDTO dto);

    Result sendMsg(UnitBdDTO dto);

    Result<List<UnitBdFlowVO>> findFlow(String id);

    Result changeState(UnitBdFlowDTO dto);

    void deleteAll();

    List<UnitBd> findByState(String state);
}
