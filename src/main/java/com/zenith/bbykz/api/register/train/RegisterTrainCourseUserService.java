package com.zenith.bbykz.api.register.train;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.efficient.common.result.Result;
import com.zenith.bbykz.model.dto.register.train.RegisterTrainCourseUserDTO;
import com.zenith.bbykz.model.dto.register.train.RegisterTrainCourseUserListDTO;
import com.zenith.bbykz.model.entity.register.train.RegisterTrainCourseUser;
import com.zenith.bbykz.model.vo.register.train.RegisterTrainCourseUserVO;

import java.math.BigDecimal;

/**
 * <p>
 * 登记信息-培训管理-用户课程 服务Api
 * </p>
 *
 * <AUTHOR>
 * @date 2024-03-12 10:11:37
 */
public interface RegisterTrainCourseUserService extends IService<RegisterTrainCourseUser> {
    /***
     * 新增
     */
    Result<RegisterTrainCourseUser> save(RegisterTrainCourseUserDTO dto);

    /**
     * 详情
     */
    Result<RegisterTrainCourseUserVO> findById(String id);

    /**
     * 修改
     */
    Result<Boolean> update(RegisterTrainCourseUserDTO dto);

    /**
     * 删除
     */
    Result<Boolean> delete(String id);

    /**
     * 列表查询
     */
    Page<RegisterTrainCourseUserVO> list(RegisterTrainCourseUserListDTO dto);

    void updateByUserIdAndCourseId(String userId, String courseId, BigDecimal average);

    RegisterTrainCourseUser findByUserIdAndCourseId(String userId, String courseId);

    void addTrainUserInfo(RegisterTrainCourseUser entity);
}
