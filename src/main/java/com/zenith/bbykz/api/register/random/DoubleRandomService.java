package com.zenith.bbykz.api.register.random;

import com.efficient.common.result.Result;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.zenith.bbykz.model.dto.register.random.DoubleRandomDTO;
import com.zenith.bbykz.model.dto.register.random.DoubleRandomListDTO;
import com.zenith.bbykz.model.entity.register.random.DoubleRandom;
import com.zenith.bbykz.model.vo.register.random.DoubleRandomVO;
import com.baomidou.mybatisplus.extension.service.IService;

/**
* <p>
* 登记信息-内部事务-双随机检查 服务Api
* </p>
*
* <AUTHOR>
* @date 2024-05-06 15:48:28
*/
public interface DoubleRandomService extends IService<DoubleRandom> {
    /***
    * 新增
    */
    Result<DoubleRandom> save(DoubleRandomDTO dto);

    /**
    * 详情
    */
    Result<DoubleRandomVO> findById(String id);

    /**
    * 修改
    */
    Result<Boolean> update(DoubleRandomDTO dto);

    /**
    * 删除
    */
    Result<Boolean> delete(String id);

    /**
    * 列表查询
    */
    Page<DoubleRandomVO> list(DoubleRandomListDTO dto);
}
