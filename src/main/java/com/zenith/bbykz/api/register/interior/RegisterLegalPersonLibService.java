package com.zenith.bbykz.api.register.interior;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.efficient.common.result.Result;
import com.zenith.bbykz.model.dto.register.interior.RegisterLegalPersonLibDTO;
import com.zenith.bbykz.model.dto.register.interior.RegisterLegalPersonLibListDTO;
import com.zenith.bbykz.model.entity.register.interior.RegisterLegalPersonLib;
import com.zenith.bbykz.model.vo.register.interior.RegisterLegalPersonLibVO;

import java.util.List;

/**
 * <p>
 * 登记信息-内部事务-法人库 服务Api
 * </p>
 *
 * <AUTHOR>
 * @date 2024-03-12 14:55:18
 */
public interface RegisterLegalPersonLibService extends IService<RegisterLegalPersonLib> {
    /***
     * 新增
     */
    Result<RegisterLegalPersonLib> save(RegisterLegalPersonLibDTO dto);

    /**
     * 详情
     */
    Result<RegisterLegalPersonLibVO> findById(String id);

    /**
     * 修改
     */
    Result<Boolean> update(RegisterLegalPersonLibDTO dto);

    /**
     * 删除
     */
    Result<Boolean> delete(String id);

    /**
     * 列表查询
     */
    Page<RegisterLegalPersonLib> list(RegisterLegalPersonLibListDTO dto);

    String importExcel(String fileId);

    RegisterLegalPersonLib findLastVersion();

    void deleteByVersion(Integer lastVersion);

    Result rollback();

    List<RegisterLegalPersonLib> verify(String name, String idCard);
}
