package com.zenith.bbykz.api.sd;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.efficient.common.result.Result;
import com.zenith.bbykz.model.dto.sd.SdOneThingDTO;
import com.zenith.bbykz.model.dto.sd.SdOneThingListDTO;
import com.zenith.bbykz.model.entity.sd.SdOneThing;
import com.zenith.bbykz.model.vo.sd.SdBusinessItemOneVO;
import com.zenith.bbykz.model.vo.sd.SdOneThingVO;

/**
 * <p>
 * 三定-一件事 服务Api
 * </p>
 *
 * <AUTHOR>
 * @date 2024-04-11 16:17:20
 */
public interface SdOneThingService extends IService<SdOneThing> {
    /***
     * 新增
     */
    Result<SdOneThing> save(SdOneThingDTO dto);

    /**
     * 详情
     */
    Result<SdOneThingVO> findById(String id);

    /**
     * 修改
     */
    Result<Boolean> update(SdOneThingDTO dto);

    /**
     * 删除
     */
    Result<Boolean> delete(String id);

    /**
     * 列表查询
     */
    Page<SdOneThingVO> list(SdOneThingListDTO dto);

    Page<SdBusinessItemOneVO> oneItemList(SdOneThingListDTO dto);
}
