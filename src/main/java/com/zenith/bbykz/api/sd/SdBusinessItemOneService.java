package com.zenith.bbykz.api.sd;

import com.baomidou.mybatisplus.extension.service.IService;
import com.efficient.common.result.Result;
import com.zenith.bbykz.model.dto.sd.SdBusinessItemOneDTO;
import com.zenith.bbykz.model.dto.sd.SdOneThingDTO;
import com.zenith.bbykz.model.entity.sd.SdBusinessItemOne;

import java.util.List;

/**
 * <p>
 * 三定-业务事项一件事关联表 服务Api
 * </p>
 *
 * <AUTHOR>
 * @date 2024-04-11 16:17:20
 */
public interface SdBusinessItemOneService extends IService<SdBusinessItemOne> {

    List<SdBusinessItemOne> findByOneId(String oneId);

    void saveByItemId(String itemId, List<SdOneThingDTO> oneThingList);

    Result<Boolean> updateItemOne(SdBusinessItemOneDTO dto);

    SdBusinessItemOne findByItemIdAndOneId(String itemId, String oneId);

    void deleteByItemId(String itemId);

    List<SdBusinessItemOne> findByItemId(String itemId);
}
