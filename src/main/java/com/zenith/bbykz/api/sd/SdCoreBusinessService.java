package com.zenith.bbykz.api.sd;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.efficient.common.result.Result;
import com.zenith.bbykz.model.dto.sd.SdCoreBusinessDTO;
import com.zenith.bbykz.model.dto.sd.SdCoreBusinessListDTO;
import com.zenith.bbykz.model.dto.sd.SdSaveCoreBusinessDTO;
import com.zenith.bbykz.model.entity.sd.SdCoreBusiness;
import com.zenith.bbykz.model.vo.sd.SdCoreBusinessVO;

import java.util.List;

/**
 * <p>
 * 三定-核心业务 服务Api
 * </p>
 *
 * <AUTHOR>
 * @date 2024-04-11 16:17:20
 */
public interface SdCoreBusinessService extends IService<SdCoreBusiness> {
    /***
     * 新增
     */
    Result<SdCoreBusiness> save(SdSaveCoreBusinessDTO dto);

    /**
     * 详情
     */
    Result<SdCoreBusinessVO> findById(String id);

    /**
     * 修改
     */
    Result<Boolean> update(SdCoreBusinessDTO dto);

    /**
     * 删除
     */
    Result<Boolean> delete(String id);

    /**
     * 列表查询
     */
    Page<SdCoreBusiness> list(SdCoreBusinessListDTO dto);

    void countBusinessItem(String coreBusinessId);

    Page<SdCoreBusinessVO> coreList(SdCoreBusinessListDTO dto);
}
