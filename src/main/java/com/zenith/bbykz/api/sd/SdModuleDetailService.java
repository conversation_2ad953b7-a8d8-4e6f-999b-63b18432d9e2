package com.zenith.bbykz.api.sd;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.efficient.common.result.Result;
import com.zenith.bbykz.model.dto.sd.SdModuleDetailDTO;
import com.zenith.bbykz.model.dto.sd.SdModuleDetailListDTO;
import com.zenith.bbykz.model.dto.sd.SdSaveModuleDetailDTO;
import com.zenith.bbykz.model.entity.sd.SdModuleDetail;
import com.zenith.bbykz.model.vo.sd.SdModuleDetailVO;
import com.zenith.bbykz.model.vo.sd.SdSearchVO;

import java.util.List;

/**
 * <p>
 * 三定-三定明细 服务Api
 * </p>
 *
 * <AUTHOR>
 * @date 2024-04-11 16:17:20
 */
public interface SdModuleDetailService extends IService<SdModuleDetail> {
    /***
     * 新增
     */
    Result<SdModuleDetail> save(SdSaveModuleDetailDTO dto);

    /**
     * 详情
     */
    Result<SdModuleDetailVO> findById(String id);

    /**
     * 修改
     */
    Result<Boolean> update(SdModuleDetailDTO dto);

    /**
     * 删除
     */
    Result<Boolean> delete(String id);

    /**
     * 列表查询
     */
    Page<SdModuleDetailVO> list(SdModuleDetailListDTO dto);

    Page<SdSearchVO> searchList(SdModuleDetailListDTO dto);
}
