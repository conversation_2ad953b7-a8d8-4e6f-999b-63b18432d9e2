package com.zenith.bbykz.api.sd;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.efficient.common.result.Result;
import com.zenith.bbykz.model.dto.sd.SdDutyDetailDTO;
import com.zenith.bbykz.model.dto.sd.SdDutyDetailListDTO;
import com.zenith.bbykz.model.dto.sd.SdSaveDutyDetailDTO;
import com.zenith.bbykz.model.dto.sd.SdSaveModuleDetailDTO;
import com.zenith.bbykz.model.entity.sd.SdDutyDetail;
import com.zenith.bbykz.model.vo.sd.SdDutyDetailVO;

import java.util.List;

/**
 * <p>
 * 三定-细化职责 服务Api
 * </p>
 *
 * <AUTHOR>
 * @date 2024-04-11 16:17:19
 */
public interface SdDutyDetailService extends IService<SdDutyDetail> {
    /***
     * 新增
     */
    Result<SdDutyDetail> save(SdSaveDutyDetailDTO dto);

    /**
     * 详情
     */
    Result<SdDutyDetailVO> findById(String id);

    /**
     * 修改
     */
    Result<Boolean> update(SdDutyDetailDTO dto);

    /**
     * 删除
     */
    Result<Boolean> delete(String id);

    /**
     * 列表查询
     */
    Page<SdDutyDetail> list(SdDutyDetailListDTO dto);

    Page<SdDutyDetailVO> searchList(SdDutyDetailListDTO dto);
}
