package com.zenith.bbykz.api.sd;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.efficient.common.result.Result;
import com.zenith.bbykz.model.dto.sd.SdOrgRelationListDTO;
import com.zenith.bbykz.model.entity.OrgInfo;
import com.zenith.bbykz.model.entity.sd.SdOrgRelation;
import com.zenith.bbykz.model.vo.sd.SdAllVO;
import com.zenith.bbykz.model.vo.sd.SdOrgRelationVO;
import com.zenith.bbykz.model.vo.sd.SdSearchVO;

/**
 * <p>
 * 三定-三定机构关联关系 服务Api
 * </p>
 *
 * <AUTHOR>
 * @date 2024-04-11 16:17:20
 */
public interface SdOrgRelationService extends IService<SdOrgRelation> {
    /**
     * --  sd_module   三定类型  1
     * -- sd_module_detail  三定明细  2
     * -- sd_duty_detail  细化职责   3
     * -- sd_core_business  核心业务  4
     * -- sd_business_item  业务事项  5
     * -- sd_business_item_one  业务事项一件事关联表 6
     *
     * @param orgInfo  单位信息
     * @param typeCode 类型，
     */
    void countAllAndSet(OrgInfo orgInfo, Integer typeCode);

    SdOrgRelation findByUnitId(String unitId);

    Page<SdOrgRelationVO> list(SdOrgRelationListDTO dto);

    void countAllAndSetByModuleDetailId(String moduleId, Integer typeCode);

    Result<SdAllVO> findAll(String unitId);

    SdOrgRelation getByUnitId(String unitId);

    Page<SdSearchVO> dutyList(SdOrgRelationListDTO dto);
}
