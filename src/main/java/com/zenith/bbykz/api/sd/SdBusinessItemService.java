package com.zenith.bbykz.api.sd;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.efficient.common.result.Result;
import com.zenith.bbykz.model.dto.sd.SdBusinessItemDTO;
import com.zenith.bbykz.model.dto.sd.SdBusinessItemListDTO;
import com.zenith.bbykz.model.dto.sd.SdSaveBusinessItemDTO;
import com.zenith.bbykz.model.entity.sd.SdBusinessItem;
import com.zenith.bbykz.model.vo.sd.SdBusinessItemVO;

import java.util.List;

/**
 * <p>
 * 三定-业务事项 服务Api
 * </p>
 *
 * <AUTHOR>
 * @date 2024-04-11 16:17:20
 */
public interface SdBusinessItemService extends IService<SdBusinessItem> {
    /***
     * 新增
     */
    Result<SdBusinessItem> save(SdSaveBusinessItemDTO dto);

    /**
     * 详情
     */
    Result<SdBusinessItemVO> findById(String id);

    /**
     * 修改
     */
    Result<Boolean> update(SdBusinessItemDTO dto);

    /**
     * 删除
     */
    Result<Boolean> delete(String id);

    /**
     * 列表查询
     */
    Page<SdBusinessItem> list(SdBusinessItemListDTO dto);
}
