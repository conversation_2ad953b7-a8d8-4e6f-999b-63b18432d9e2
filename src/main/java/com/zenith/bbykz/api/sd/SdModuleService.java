package com.zenith.bbykz.api.sd;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.efficient.common.result.Result;
import com.zenith.bbykz.model.dto.sd.SdModuleCountDTO;
import com.zenith.bbykz.model.dto.sd.SdModuleDTO;
import com.zenith.bbykz.model.dto.sd.SdModuleListDTO;
import com.zenith.bbykz.model.entity.sd.SdModule;
import com.zenith.bbykz.model.vo.sd.SdModuleCountVO;
import com.zenith.bbykz.model.vo.sd.SdModuleVO;

import java.util.Map;

/**
 * <p>
 * 三定-三定类型 服务Api
 * </p>
 *
 * <AUTHOR>
 * @date 2024-04-11 16:17:20
 */
public interface SdModuleService extends IService<SdModule> {
    /***
     * 新增
     */
    Result<SdModule> save(SdModuleDTO dto);

    /**
     * 详情
     */
    Result<SdModuleVO> findById(String id);

    /**
     * 修改
     */
    Result<Boolean> update(SdModuleDTO dto);

    /**
     * 删除
     */
    Result<Boolean> delete(String id);

    /**
     * 列表查询
     */
    Page<SdModule> list(SdModuleListDTO dto);

    Map<String, String> findMap();

    SdModuleCountVO analysis(SdModuleCountDTO dto);

}
