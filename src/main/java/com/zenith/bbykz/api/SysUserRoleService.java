package com.zenith.bbykz.api;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.zenith.bbykz.model.dto.SysUserRoleDTO;
import com.zenith.bbykz.model.dto.SysUserRoleListDTO;
import com.zenith.bbykz.model.entity.SysUserRole;
import com.zenith.bbykz.model.vo.SysUserRoleVO;

import java.util.List;

/**
 * <p>
 * sys_user_role 服务Api
 * </p>
 *
 * <AUTHOR>
 * @date 2023-09-14 17:14:26
 */
public interface SysUserRoleService extends IService<SysUserRole> {
    /***
     * 新增
     */
    SysUserRole save(SysUserRoleDTO dto);

    /**
     * 详情
     */
    SysUserRoleVO findById(String id);

    /**
     * 修改
     */
    Boolean update(SysUserRoleDTO dto);

    /**
     * 删除
     */
    Boolean delete(String id);

    /**
     * 列表查询
     */
    Page<SysUserRole> list(SysUserRoleListDTO dto);

    List<SysUserRole> findByUserId(String userId, String systemId);

    List<SysUserRole> getByIsDefault(Integer isDefault);

    List<SysUserRole> getList(String id);

    List<SysUserRole> findByUserSystemId(String userId,String systemId);
}
