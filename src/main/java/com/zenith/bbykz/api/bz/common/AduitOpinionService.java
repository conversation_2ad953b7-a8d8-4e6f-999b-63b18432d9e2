package com.zenith.bbykz.api.bz.common;

import com.efficient.common.result.Result;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.zenith.bbykz.model.dto.bz.common.AduitOpinionDTO;
import com.zenith.bbykz.model.dto.bz.common.AduitOpinionListDTO;
import com.zenith.bbykz.model.entity.bz.common.AduitOpinion;
import com.zenith.bbykz.model.vo.bz.common.AduitOpinionVO;
import com.baomidou.mybatisplus.extension.service.IService;

/**
* <p>
* 核编管理-常用审批意见 服务Api
* </p>
*
* <AUTHOR>
* @date 2024-05-17 11:10:37
*/
public interface AduitOpinionService extends IService<AduitOpinion> {
    /***
    * 新增
    */
    Result<AduitOpinion> save(AduitOpinionDTO dto);

    /**
    * 详情
    */
    Result<AduitOpinionVO> findById(String id);

    /**
    * 修改
    */
    Result<Boolean> update(AduitOpinionDTO dto);

    /**
    * 删除
    */
    Result<Boolean> delete(String id);

    /**
    * 列表查询
    */
    Page<AduitOpinionVO> list(AduitOpinionListDTO dto);
}
