package com.zenith.bbykz.api.bz.rybd;

import com.efficient.common.result.Result;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.zenith.bbykz.model.dto.bz.rybd.BzUnitBdDTO;
import com.zenith.bbykz.model.dto.bz.rybd.BzUnitBdExportDTO;
import com.zenith.bbykz.model.dto.bz.rybd.BzUnitBdListDTO;
import com.zenith.bbykz.model.entity.bz.rybd.BzUnitBd;
import com.zenith.bbykz.model.vo.bz.rybd.BzUnitBdVO;
import com.baomidou.mybatisplus.extension.service.IService;

/**
* <p>
* 核编管理-动态信息维护 服务Api
* </p>
*
* <AUTHOR>
* @date 2024-04-22 11:21:43
*/
public interface BzUnitBdService extends IService<BzUnitBd> {
    /***
    * 新增
    */
    Result<BzUnitBd> save(BzUnitBdDTO dto);

    /**
    * 详情
    */
    Result<BzUnitBdVO> findById(String id, String pageType);

    /**
    * 修改
    */
    Result<Boolean> update(BzUnitBdDTO dto);

    /**
    * 删除
    */
    Result<Boolean> delete(String id);

    /**
    * 列表查询
    */
    Page<BzUnitBdVO> list(BzUnitBdListDTO dto);

    Page<BzUnitBdVO> bdList(BzUnitBdListDTO dto);

    Result<String> exportRybd(BzUnitBdExportDTO dto) throws Exception;
}
