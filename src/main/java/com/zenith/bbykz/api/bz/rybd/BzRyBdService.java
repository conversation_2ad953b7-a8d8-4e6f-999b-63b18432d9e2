package com.zenith.bbykz.api.bz.rybd;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.efficient.common.result.Result;
import com.zenith.bbykz.model.dto.bz.rybd.BzRyBdDTO;
import com.zenith.bbykz.model.dto.bz.rybd.BzRyBdListDTO;
import com.zenith.bbykz.model.entity.bz.rybd.BzRyBd;
import com.zenith.bbykz.model.vo.bz.rybd.BzRyBdVO;

import java.util.List;

/**
 * <p>
 * 核编管理-动态信息维护-人员详情 服务Api
 * </p>
 *
 * <AUTHOR>
 * @date 2024-04-22 11:21:43
 */
public interface BzRyBdService extends IService<BzRyBd> {
    /***
     * 新增
     */
    Result<BzRyBd> save(BzRyBdDTO dto);

    /**
     * 详情
     */
    Result<BzRyBdVO> findById(String id);

    /**
     * 修改
     */
    Result<Boolean> update(BzRyBdDTO dto);

    /**
     * 删除
     */
    Result<Boolean> delete(String id);

    /**
     * 列表查询
     */
    Page<BzRyBdVO> list(BzRyBdListDTO dto);

    void saveByBizId(String bizId, List<BzRyBdDTO> rybdList);

    List<BzRyBd> findListByBizId(String bizId);

    void removeByBizId(String bizId);

    void deleteByIdList(List<String> deleteIdList);

    List<BzRyBd> findListByBizIdList(List<String> idList);
}
