package com.zenith.bbykz.api.bz.bzsq;

import com.baomidou.mybatisplus.extension.service.IService;
import com.efficient.common.result.Result;
import com.zenith.bbykz.model.dto.bz.bzsq.BzBzsqDetailDTO;
import com.zenith.bbykz.model.dto.bz.bzsq.BzBzsqDetailListDTO;
import com.zenith.bbykz.model.entity.bz.bzsq.BzBzsqDetail;
import com.zenith.bbykz.model.vo.bz.bzsq.BzBzsqDetailVO;

import java.util.List;

/**
 * <p>
 * 核编管理-用编申请-明细 服务Api
 * </p>
 *
 * <AUTHOR>
 * @date 2024-03-29 16:05:19
 */
public interface BzBzsqDetailService extends IService<BzBzsqDetail> {

    /**
     * 删除
     */
    Result<Boolean> deleteByBizId(String bizId);

    /**
     * 列表查询
     */
    List<BzBzsqDetail> list(BzBzsqDetailListDTO dto);

    void saveListByBizId(String bizId, List<BzBzsqDetailDTO> detailList);

    List<BzBzsqDetailVO> getDetailListByBizId(String bizId, Integer isBack);
}
