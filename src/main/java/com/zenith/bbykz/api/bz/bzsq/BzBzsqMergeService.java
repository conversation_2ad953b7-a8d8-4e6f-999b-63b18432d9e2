package com.zenith.bbykz.api.bz.bzsq;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.efficient.common.result.Result;
import com.zenith.bbykz.model.dto.bz.bzsq.BzBzsqMergeDTO;
import com.zenith.bbykz.model.dto.bz.bzsq.BzBzsqMergeListDTO;
import com.zenith.bbykz.model.entity.bz.bzsq.BzBzsqMerge;
import com.zenith.bbykz.model.vo.bz.bzsq.BzBzsqMergeVO;

/**
 * <p>
 * 核编管理-用编审核-合并审核 服务Api
 * </p>
 *
 * <AUTHOR>
 * @date 2024-04-03 15:26:58
 */
public interface BzBzsqMergeService extends IService<BzBzsqMerge> {
    /***
     * 新增
     */
    Result<BzBzsqMerge> save(BzBzsqMergeDTO dto);

    /**
     * 详情
     */
    Result<BzBzsqMergeVO> findById(String id);

    /**
     * 删除
     */
    Result<Boolean> delete(String id);

    /**
     * 列表查询
     */
    Page<BzBzsqMergeVO> list(BzBzsqMergeListDTO dto);

    Result approve(BzBzsqMergeDTO dto);

    String downAudit(String id) throws Exception;
}
