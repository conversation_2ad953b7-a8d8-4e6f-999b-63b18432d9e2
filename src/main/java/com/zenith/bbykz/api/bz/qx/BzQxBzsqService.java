package com.zenith.bbykz.api.bz.qx;

import com.efficient.common.result.Result;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.zenith.bbykz.model.dto.bz.qx.BzQxBzsqAuditDTO;
import com.zenith.bbykz.model.dto.bz.qx.BzQxBzsqDTO;
import com.zenith.bbykz.model.dto.bz.qx.BzQxBzsqListDTO;
import com.zenith.bbykz.model.entity.bz.qx.BzQxBzsq;
import com.zenith.bbykz.model.vo.bz.qx.BzQxBzsqVO;
import com.baomidou.mybatisplus.extension.service.IService;

/**
* <p>
* 核编管理-用编管理-区县用编申请计划 服务Api
* </p>
*
* <AUTHOR>
* @date 2024-05-06 17:05:40
*/
public interface BzQxBzsqService extends IService<BzQxBzsq> {
    /***
    * 新增
    */
    Result<BzQxBzsq> save(BzQxBzsqDTO dto);

    /**
    * 详情
    */
    Result<BzQxBzsqVO> findById(String id);

    /**
    * 修改
    */
    Result<Boolean> update(BzQxBzsqDTO dto);

    /**
    * 删除
    */
    Result<Boolean> delete(String id);

    /**
    * 列表查询
    */
    Page<BzQxBzsqVO> list(BzQxBzsqListDTO dto);

    Result<Boolean> revoke(String id);

    Result<Boolean> audit(BzQxBzsqAuditDTO dto);

    String downReply(String id);
}
