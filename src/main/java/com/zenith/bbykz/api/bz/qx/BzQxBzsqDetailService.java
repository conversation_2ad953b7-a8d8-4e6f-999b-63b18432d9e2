package com.zenith.bbykz.api.bz.qx;

import com.efficient.common.result.Result;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.zenith.bbykz.model.dto.bz.qx.BzQxBzsqDetailDTO;
import com.zenith.bbykz.model.dto.bz.qx.BzQxBzsqDetailListDTO;
import com.zenith.bbykz.model.entity.bz.qx.BzQxBzsqDetail;
import com.zenith.bbykz.model.vo.bz.qx.BzQxBzsqDetailVO;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;

/**
* <p>
* 核编管理-用编管理-区县用编申请计划明细 服务Api
* </p>
*
* <AUTHOR>
* @date 2024-05-06 17:05:40
*/
public interface BzQxBzsqDetailService extends IService<BzQxBzsqDetail> {
    /***
    * 新增
    */
    Result<BzQxBzsqDetail> save(BzQxBzsqDetailDTO dto);

    /**
    * 详情
    */
    Result<BzQxBzsqDetailVO> findById(String id);

    /**
    * 修改
    */
    Result<Boolean> update(BzQxBzsqDetailDTO dto);

    /**
    * 删除
    */
    Result<Boolean> delete(String id);

    /**
    * 列表查询
    */
    Page<BzQxBzsqDetailVO> list(BzQxBzsqDetailListDTO dto);

    void saveByBizId(String id, List<BzQxBzsqDetailDTO> detailList);

    List<BzQxBzsqDetail> listByBiz(String bizId);

    void deleteByBizId(String bizId);
}
