package com.zenith.bbykz.api.bz.bzsq;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.efficient.common.result.Result;
import com.efficient.file.model.vo.FileVO;
import com.zenith.bbykz.model.dto.bz.bzsq.BzBzsqDTO;
import com.zenith.bbykz.model.dto.bz.bzsq.BzBzsqListDTO;
import com.zenith.bbykz.model.entity.bz.bzsq.BzBzsq;
import com.zenith.bbykz.model.vo.bz.bzsq.BzBzsqVO;

import java.util.List;

/**
 * <p>
 * 核编管理-用编申请 服务Api
 * </p>
 *
 * <AUTHOR>
 * @date 2024-03-29 16:05:19
 */
public interface BzBzsqService extends IService<BzBzsq> {
    /***
     * 新增
     */
    Result<BzBzsq> save(BzBzsqDTO dto);

    /**
     * 详情
     */
    Result<BzBzsqVO> findById(String id, Integer isBack);

    /**
     * 修改
     */
    Result<Boolean> update(BzBzsqDTO dto);

    /**
     * 删除
     */
    Result<Boolean> delete(String id);

    /**
     * 列表查询
     */
    Page<BzBzsqVO> list(BzBzsqListDTO dto);

    Result changeStatus(String id, String status);

    Result approve(BzBzsqDTO dto);

    List<BzBzsqVO> listByMergeId(String mergeId);

    String downReply(String id);

    String downAudit(String id);
}
