package com.zenith.bbykz.api.bz.bzsq;

import com.baomidou.mybatisplus.extension.service.IService;
import com.zenith.bbykz.model.entity.bz.bzsq.BzBzsqFlow;
import com.zenith.bbykz.model.vo.bz.bzsq.BzBzsqFlowVO;

import java.util.List;

/**
 * <p>
 * 核编管理-用编审核 服务Api
 * </p>
 *
 * <AUTHOR>
 * @date 2024-04-02 11:51:22
 */
public interface BzBzsqFlowService extends IService<BzBzsqFlow> {

    List<BzBzsqFlowVO> getFlow(String id);

    void saveByBizId(String id, String status);
}
