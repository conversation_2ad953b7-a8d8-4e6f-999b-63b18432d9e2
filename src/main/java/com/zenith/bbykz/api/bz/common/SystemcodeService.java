package com.zenith.bbykz.api.bz.common;

import com.efficient.common.result.Result;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.zenith.bbykz.model.dto.bz.common.SystemcodeDTO;
import com.zenith.bbykz.model.dto.bz.common.SystemcodeListDTO;
import com.zenith.bbykz.model.entity.bz.common.Systemcode;
import com.zenith.bbykz.model.vo.bz.common.SystemcodeVO;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;

/**
* <p>
* SYSTEMCODE 服务Api
* </p>
*
* <AUTHOR>
* @date 2024-08-27 15:52:58
*/
public interface SystemcodeService extends IService<Systemcode> {


    /**
    * 列表查询
    */

    List<Systemcode> getAll();

}
