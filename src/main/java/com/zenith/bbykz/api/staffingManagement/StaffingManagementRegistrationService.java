package com.zenith.bbykz.api.staffingManagement;

import com.baomidou.mybatisplus.extension.service.IService;
import com.zenith.bbykz.model.dto.StaffingManagementRegistrationDTO;
import com.zenith.bbykz.model.entity.StaffingManagementRegistration;
import com.zenith.bbykz.model.vo.StaffingManagementRegistrationVO;

import java.util.List;

/**
 * <p>
 * 用编管理登记表 服务Api
 * </p>
 *
 * <AUTHOR>
 * @date 2023-09-28 13:53:11
 */
public interface StaffingManagementRegistrationService extends IService<StaffingManagementRegistration> {
    /***
     * 新增
     */
    Boolean addUpdate(List<StaffingManagementRegistrationDTO> registrations, String staffingManagementId);

    List<StaffingManagementRegistrationVO> getListByStaffingManagementId(String staffingManagementId);

}
