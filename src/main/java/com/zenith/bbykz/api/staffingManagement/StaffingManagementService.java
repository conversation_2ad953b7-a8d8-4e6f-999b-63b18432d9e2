package com.zenith.bbykz.api.staffingManagement;

import com.baomidou.mybatisplus.extension.service.IService;
import com.efficient.common.result.Result;
import com.zenith.bbykz.model.dto.RegistrationDTO;
import com.zenith.bbykz.model.dto.RegistrationListDTO;
import com.zenith.bbykz.model.dto.StaffingManagementDTO;
import com.zenith.bbykz.model.dto.StaffingManagementListDTO;
import com.zenith.bbykz.model.entity.StaffingManagement;

import javax.servlet.http.HttpServletResponse;

/**
 * <p>
 * 用编管理 服务Api
 * </p>
 *
 * <AUTHOR>
 * @date 2023-09-13
 */
public interface StaffingManagementService extends IService<StaffingManagement> {
    /***
     * 新增
     */
    Result addUpdate(StaffingManagementDTO dto);

    /**
     * 详情
     */
    Result findById(String id);

    /**
     * 删除
     */
    Boolean delete(String id);

    /**
     * 列表查询
     */
    Result list(StaffingManagementListDTO dto);

    /**
     * 审核
     */
    Result approve(String id, String status, String option);

    /**
     * 编办用户查询列表
     */
    Result bbList(StaffingManagementListDTO dto);

    /**
     * 导出审核列表
     */
    void exportBbList(StaffingManagementListDTO dto, HttpServletResponse response) throws Exception;

    /**
     * 导出申请列表
     */
    void exportList(StaffingManagementListDTO dto, HttpServletResponse response) throws Exception;

    /**
     * 用编登记
     */
    Result registration(RegistrationDTO dto);

    /**
     * 查询登记列表
     */
    Result countList(RegistrationListDTO dto);

    /**
     * 导出统计列表
     */
    void exportCountList(RegistrationListDTO dto, HttpServletResponse response) throws Exception;
}
