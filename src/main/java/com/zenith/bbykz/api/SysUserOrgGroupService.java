package com.zenith.bbykz.api;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.efficient.common.result.Result;
import com.zenith.bbykz.model.dto.SysUserOrgGroupDTO;
import com.zenith.bbykz.model.dto.SysUserOrgGroupDetailDTO;
import com.zenith.bbykz.model.dto.SysUserOrgGroupListDTO;
import com.zenith.bbykz.model.entity.SysUserOrgGroup;
import com.zenith.bbykz.model.vo.SysUserOrgGroupVO;

import java.util.List;

/**
 * <p>
 * 用户机构组 服务Api
 * </p>
 *
 * <AUTHOR>
 * @date 2024-03-13 10:48:03
 */
public interface SysUserOrgGroupService extends IService<SysUserOrgGroup> {
    /***
     * 新增
     */
    Result<SysUserOrgGroup> save(SysUserOrgGroupDTO dto);

    /**
     * 详情
     */
    Result<SysUserOrgGroupVO> findById(String id);

    /**
     * 修改
     */
    Result<Boolean> update(SysUserOrgGroupDTO dto);

    /**
     * 删除
     */
    Result<Boolean> delete(String id);

    /**
     * 列表查询
     */
    Page<SysUserOrgGroup> list(SysUserOrgGroupListDTO dto);

    void saveDeatilList(String id, List<SysUserOrgGroupDetailDTO> detailList);
}
