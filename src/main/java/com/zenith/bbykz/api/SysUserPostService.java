package com.zenith.bbykz.api;

import com.baomidou.mybatisplus.extension.service.IService;
import com.zenith.bbykz.model.entity.SysUserPost;

import java.util.List;

/**
 * <p>
 * 用户职位信息 服务Api
 * </p>
 *
 * <AUTHOR>
 * @date 2024-03-26 15:04:24
 */
public interface SysUserPostService extends IService<SysUserPost> {
    List<SysUserPost> findByUserId(String userId);

    SysUserPost findByUserIdAndDeptId(String userId, String deptId);
}
