package com.zenith.bbykz.model.easy.excel.model;

import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;

import java.util.Date;

/**
 * 机关事业单位统一社会信用代码
 *
 * <AUTHOR>
 * @date 2023/9/12
 */
@Data
public class UnifiedSocialCreditCodeModel {
    @ExcelProperty(value = "统一社会信用代码")
    private String code;

    @ExcelProperty(value = "编号")
    private String num;

    @ExcelProperty(value = "机构名称")
    private String institutionName;

    @ExcelProperty(value = "机构状态")
    private String institutionalStatus;

    @ExcelProperty(value = "机构地址")
    private String institutionAddress;

    @ExcelProperty(value = "负责人")
    private String head;

    @ExcelProperty(value = "机构规格")
    private String institutionalSpecifications;

    @ExcelProperty(value = "机构分类")
    private String institutionalType;

    @ExcelProperty(value = "机构性质")
    private String institutionalNature;

    @ExcelProperty(value = "机构类别")
    private String institutionCategory;

    @ExcelProperty(value = "批准机构")
    private String approvingAuthority;

    @ExcelProperty(value = "批准文号")
    private String approvalNumber;

    @ExcelProperty(value = "证书份数")
    private Integer certificateNumber;

    @ExcelProperty(value = "发证人")
    private String licensor;

    @ExcelProperty(value = "颁发日期")
    private String issueTime;

    @ExcelProperty(value = "证书截止日期")
    private String certificateExpirationTime;

    @ExcelProperty(value = "领证人")
    private String certificateRecipient;

    @ExcelProperty(value = "领证日期")
    private String certificateAcquisitionTime;

    @ExcelProperty(value = "联系人")
    private String contacts;

    @ExcelProperty(value = "联系电话")
    private String telephone;

    @ExcelProperty(value = "邮政编码")
    private String postalCode;

}
