package com.zenith.bbykz.model.easy.excel.model;

import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;

/**
 * 事业单位法人登记
 *
 * <AUTHOR>
 * @date 2023/9/11
 */
@Data
public class LegalPersonModel {

    @ExcelProperty(value = "统一社会信用代码")
    private String unifiedSocialCreditCode;

    @ExcelProperty(value = "证书号")
    private String certificateNo;

    @ExcelProperty(value = "名称")
    private String name;

    @ExcelProperty(value = "设立类型")
    private String establishmentType;

    @ExcelProperty(value = "法定代表人")
    private String legalRepresentative;

    @ExcelProperty(value = "住所")
    private String residence;

    @ExcelProperty(value = "举办单位")
    private String organizer;

    @ExcelProperty(value = "经费来源")
    private String fundingSource;

    @ExcelProperty(value = "开办资金")
    private String initialFunds;

    @ExcelProperty(value = "宗旨和业务范围")
    private String purpose;

    @ExcelProperty(value = "状态")
    private String status;

    @ExcelProperty(value = "事业单位第二名称")
    private String secondName;

    @ExcelProperty(value = "事业单位第三名称")
    private String thirdName;

    @ExcelProperty(value = "事业单位其他名称")
    private String otherNames;

    @ExcelProperty(value = "举办单位类别")
    private String organizerCategory;

    @ExcelProperty(value = "固定资产")
    private String fixedAssets;

    @ExcelProperty(value = "流动资产")
    private String currentAssets;

    @ExcelProperty(value = "其他资金")
    private String otherFunds;

    @ExcelProperty(value = "开户银行")
    private String depositBank;

    @ExcelProperty(value = "银行帐号")
    private String bankAccount;

    @ExcelProperty(value = "审批机关")
    private String approvalAuthority;

    @ExcelProperty(value = "批准文号")
    private String approvalNumber;

    @ExcelProperty(value = "执业许可证书")
    private String practicingLicenseCertificate;

    @ExcelProperty(value = "机构规格")
    private String institutionalSpecifications;

    @ExcelProperty(value = "人员编制")
    private String staffEstablishing;

    @ExcelProperty(value = "从业人数")
    private Integer employeeNumber;

    @ExcelProperty(value = "设立时间")
    private String establishTime;

    @ExcelProperty(value = "发证人")
    private String licensor;

    @ExcelProperty(value = "领证人")
    private String certificateRecipient;

    @ExcelProperty(value = "领证日期")
    private String certificateAcquisitionTime;

    @ExcelProperty(value = "副本份数")
    private Integer copiesNumber;

    @ExcelProperty(value = "证书有效起始日期")
    private String certificateValidityStartTime;

    @ExcelProperty(value = "证书有效结束日期")
    private String certificateValidityEndTime;

    @ExcelProperty(value = "联系人")
    private String contacts;

    @ExcelProperty(value = "联系电话")
    private String telephone;

    @ExcelProperty(value = "邮政编码")
    private String postalCode;

    @ExcelProperty(value = "行业类别")
    private String industryCategory;

    @ExcelProperty(value = "登记机关区划码")
    private String registrationAuthorityCode;

    @ExcelProperty(value = "年度报告情况")
    private String annualReport;

    @ExcelProperty(value = "年度报告日期")
    private String annualReportTime;

    @ExcelProperty(value = "法定代表人电话")
    private String legalRepresentativePhone;

    @ExcelProperty(value = "登记管理机关")
    private String registrationManagementAuthority;

}
