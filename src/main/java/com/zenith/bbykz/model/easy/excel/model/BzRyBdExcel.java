package com.zenith.bbykz.model.easy.excel.model;

import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 * @since 2024/5/7 10:30
 */
@Data
public class BzRyBdExcel {

    /**
     * 名称
     */
    // @ExcelProperty(value = "人员姓名")
    private String name;
    /**
     * 身份证
     */
    // @ExcelProperty(value = "身份证号码")
    private String idCard;
    /**
     * 人员变动类型，BZ_RY_BD_TYPE
     */
    // @ExcelProperty(value = "变动类型")
    private String bdTypeName;

    /**
     * 现单位名称
     */
    // @ExcelProperty(value = "原所属单位")
    private String oldUnitName;
    /**
     * /**
     * 现处室层名称
     */
    // @ExcelProperty(value = "原所属处室")
    private String oldDeptName;
    // @ExcelProperty(value = "原职位")
    private String oldPostName;

    // @ExcelProperty(value = "编制类型")
    private String oldBzTypeName;
    /**
     * 现用编类型，BZ_JFXX
     */
    // @ExcelProperty(value = "用编类型")
    private String oldYbTypeName;

    // @ExcelProperty(value = "现所属单位")
    private String newUnitName;

    // @ExcelProperty(value = "现所属处室")
    private String newDeptName;
    // @ExcelProperty(value = "现职位")
    private String newPostName;
    // @ExcelProperty(value = "编制类型")
    private String newBzTypeName;
    // @ExcelProperty(value = "用编类型")
    private String newYbTypeName;
    // @ExcelProperty(value = "变动时间")
    private Date bdTime;

    // @ExcelProperty(value = "备注")
    private String remark;

}
