package com.zenith.bbykz.model.easy.excel.model;

import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;

import java.util.Date;

/**
 * 事业单位法人登记
 *
 * <AUTHOR>
 * @date 2023/9/11
 */
@Data
public class RegisterLegalPersonLibExcel {

    @ExcelProperty(value = "姓名")
    private String name;
    /**
     * 身份证
     */
    @ExcelProperty(value = "身份证")
    private String idCard;
    /**
     * 单位名称
     */
    @ExcelProperty(value = "单位名称")
    private String unitName;
    /**
     * 注册时间
     */
    @ExcelProperty(value = "注册时间")
    private Date registerTime;
    /**
     * 有效期开始时间
     */
    @ExcelProperty(value = "生效时间")
    private Date startTime;
    /**
     * 有效期结束时间
     */
    @ExcelProperty(value = "截至时间")
    private Date endTime;
    /**
     * 创建时间
     */
    @ExcelProperty(value = "备注")
    private String remark;

}
