package com.zenith.bbykz.model.converter.bz.bzsq;

import com.zenith.bbykz.model.dto.bz.bzsq.BzBzsqDTO;
import com.zenith.bbykz.model.entity.bz.bzsq.BzBzsq;
import com.zenith.bbykz.model.vo.bz.bzsq.BzBzsqVO;
import org.mapstruct.Mapper;
import org.mapstruct.Mappings;
import org.mapstruct.factory.Mappers;

/**
 * <p>
 * 核编管理-用编申请 模型转换器
 * </p>
 *
 * <AUTHOR>
 * @date 2024-03-29 16:05:19
 */
@Mapper(componentModel = "spring")
public interface BzBzsqConverter {

    BzBzsqConverter INSTANCE = Mappers.getMapper(BzBzsqConverter.class);

    @Mappings({})
    BzBzsq dto2Entity(BzBzsqDTO dto);

    @Mappings({})
    BzBzsqVO entity2Vo(BzBzsq entity);

}
