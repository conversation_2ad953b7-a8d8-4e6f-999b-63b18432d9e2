package com.zenith.bbykz.model.converter.bz.bzsq;

import com.zenith.bbykz.model.dto.bz.bzsq.BzBzsqFlowDTO;
import com.zenith.bbykz.model.entity.bz.bzsq.BzBzsqFlow;
import com.zenith.bbykz.model.vo.bz.bzsq.BzBzsqFlowVO;
import org.mapstruct.Mapper;
import org.mapstruct.Mappings;
import org.mapstruct.factory.Mappers;

/**
 * <p>
 * 核编管理-用编审核 模型转换器
 * </p>
 *
 * <AUTHOR>
 * @date 2024-04-02 11:51:22
 */
@Mapper(componentModel = "spring")
public interface BzBzsqFlowConverter {

    BzBzsqFlowConverter INSTANCE = Mappers.getMapper(BzBzsqFlowConverter.class);

    @Mappings({})
    BzBzsqFlow dto2Entity(BzBzsqFlowDTO dto);

    @Mappings({})
    BzBzsqFlowVO entity2Vo(BzBzsqFlow entity);

}
