package com.zenith.bbykz.model.converter.bz.common;

import com.zenith.bbykz.model.dto.bz.common.SystemcodeDTO;
import com.zenith.bbykz.model.entity.bz.common.Systemcode;
import com.zenith.bbykz.model.vo.bz.common.SystemcodeVO;
import org.mapstruct.Mapper;
import org.mapstruct.Mappings;
import org.mapstruct.factory.Mappers;

/**
* <p>
* SYSTEMCODE 模型转换器
* </p>
*
* <AUTHOR>
* @date 2024-08-27 15:52:58
*/
@Mapper(componentModel = "spring")
public interface SystemcodeConverter {

    SystemcodeConverter INSTANCE = Mappers.getMapper(SystemcodeConverter.class);

    @Mappings({})
    Systemcode dto2Entity(SystemcodeDTO dto);

    @Mappings({})
    SystemcodeVO entity2Vo(Systemcode entity);

}
