package com.zenith.bbykz.model.converter.register.interior;

import com.zenith.bbykz.model.easy.excel.model.UnifiedSocialCreditCodeModel;
import com.zenith.bbykz.model.entity.UnifiedSocialCreditCode;
import org.mapstruct.Mapper;
import org.mapstruct.Mappings;
import org.mapstruct.factory.Mappers;

/**
 * <AUTHOR>
 * @since 2024/5/13 10:27
 */
@Mapper(componentModel = "spring")
public interface UnifiedSocialCreditCodeConverter {
    UnifiedSocialCreditCodeConverter INSTANCE = Mappers.getMapper(UnifiedSocialCreditCodeConverter.class);

    @Mappings({})
    UnifiedSocialCreditCode excel2Entity(UnifiedSocialCreditCodeModel dto);
}
