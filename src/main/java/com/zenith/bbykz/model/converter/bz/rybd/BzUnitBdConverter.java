package com.zenith.bbykz.model.converter.bz.rybd;

import com.zenith.bbykz.model.dto.bz.rybd.BzUnitBdDTO;
import com.zenith.bbykz.model.entity.bz.rybd.BzUnitBd;
import com.zenith.bbykz.model.vo.bz.rybd.BzUnitBdVO;
import org.mapstruct.Mapper;
import org.mapstruct.Mappings;
import org.mapstruct.factory.Mappers;

/**
* <p>
* 核编管理-动态信息维护 模型转换器
* </p>
*
* <AUTHOR>
* @date 2024-04-22 11:21:43
*/
@Mapper(componentModel = "spring")
public interface BzUnitBdConverter {

    BzUnitBdConverter INSTANCE = Mappers.getMapper(BzUnitBdConverter.class);

    @Mappings({})
    BzUnitBd dto2Entity(BzUnitBdDTO dto);

    @Mappings({})
    BzUnitBdVO entity2Vo(BzUnitBd entity);

}
