package com.zenith.bbykz.model.converter.register.train;

import com.zenith.bbykz.model.dto.register.train.RegisterTrainFileDTO;
import com.zenith.bbykz.model.entity.register.train.RegisterTrainFile;
import com.zenith.bbykz.model.vo.register.train.RegisterTrainFileVO;
import org.mapstruct.Mapper;
import org.mapstruct.Mappings;
import org.mapstruct.factory.Mappers;

/**
 * <p>
 * 登记信息-培训管理-课件管理 模型转换器
 * </p>
 *
 * <AUTHOR>
 * @date 2024-03-05 15:44:53
 */
@Mapper(componentModel = "spring")
public interface RegisterTrainFileConverter {

    RegisterTrainFileConverter INSTANCE = Mappers.getMapper(RegisterTrainFileConverter.class);

    @Mappings({})
    RegisterTrainFile dto2Entity(RegisterTrainFileDTO dto);

    @Mappings({})
    RegisterTrainFileVO entity2Vo(RegisterTrainFile entity);

}
