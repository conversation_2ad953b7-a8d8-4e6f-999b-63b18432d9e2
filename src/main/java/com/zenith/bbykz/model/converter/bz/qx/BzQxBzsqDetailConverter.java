package com.zenith.bbykz.model.converter.bz.qx;

import com.zenith.bbykz.model.dto.bz.qx.BzQxBzsqDetailDTO;
import com.zenith.bbykz.model.entity.bz.qx.BzQxBzsqDetail;
import com.zenith.bbykz.model.vo.bz.qx.BzQxBzsqDetailVO;
import org.mapstruct.Mapper;
import org.mapstruct.Mappings;
import org.mapstruct.factory.Mappers;

/**
* <p>
* 核编管理-用编管理-区县用编申请计划明细 模型转换器
* </p>
*
* <AUTHOR>
* @date 2024-05-06 17:05:40
*/
@Mapper(componentModel = "spring")
public interface BzQxBzsqDetailConverter {

    BzQxBzsqDetailConverter INSTANCE = Mappers.getMapper(BzQxBzsqDetailConverter.class);

    @Mappings({})
    BzQxBzsqDetail dto2Entity(BzQxBzsqDetailDTO dto);

    @Mappings({})
    BzQxBzsqDetailVO entity2Vo(BzQxBzsqDetail entity);

}
