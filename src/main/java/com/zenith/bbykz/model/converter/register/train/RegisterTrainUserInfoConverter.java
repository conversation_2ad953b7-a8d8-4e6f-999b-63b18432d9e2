package com.zenith.bbykz.model.converter.register.train;

import com.zenith.bbykz.model.dto.register.train.RegisterTrainUserInfoDTO;
import com.zenith.bbykz.model.entity.register.train.RegisterTrainUserInfo;
import com.zenith.bbykz.model.vo.register.train.RegisterTrainUserInfoVO;
import org.mapstruct.Mapper;
import org.mapstruct.Mappings;
import org.mapstruct.factory.Mappers;

/**
 * <p>
 * 登记信息-培训管理-学习进度 模型转换器
 * </p>
 *
 * <AUTHOR>
 * @date 2024-03-06 14:39:04
 */
@Mapper(componentModel = "spring")
public interface RegisterTrainUserInfoConverter {

    RegisterTrainUserInfoConverter INSTANCE = Mappers.getMapper(RegisterTrainUserInfoConverter.class);

    @Mappings({})
    RegisterTrainUserInfo dto2Entity(RegisterTrainUserInfoDTO dto);

    @Mappings({})
    RegisterTrainUserInfoVO entity2Vo(RegisterTrainUserInfo entity);

}
