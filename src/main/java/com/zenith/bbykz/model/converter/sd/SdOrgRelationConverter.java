package com.zenith.bbykz.model.converter.sd;

import com.zenith.bbykz.model.dto.sd.SdOrgRelationDTO;
import com.zenith.bbykz.model.entity.sd.SdOrgRelation;
import com.zenith.bbykz.model.vo.sd.SdOrgRelationVO;
import org.mapstruct.Mapper;
import org.mapstruct.Mappings;
import org.mapstruct.factory.Mappers;

/**
 * <p>
 * 三定-三定机构关联关系 模型转换器
 * </p>
 *
 * <AUTHOR>
 * @date 2024-04-11 16:17:20
 */
@Mapper(componentModel = "spring")
public interface SdOrgRelationConverter {

    SdOrgRelationConverter INSTANCE = Mappers.getMapper(SdOrgRelationConverter.class);

    @Mappings({})
    SdOrgRelation dto2Entity(SdOrgRelationDTO dto);

    @Mappings({})
    SdOrgRelationVO entity2Vo(SdOrgRelation entity);

}
