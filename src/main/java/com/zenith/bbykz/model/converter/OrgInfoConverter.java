package com.zenith.bbykz.model.converter;

import com.zenith.bbykz.model.dto.OrgInfoDTO;
import com.zenith.bbykz.model.entity.OrgInfo;
import com.zenith.bbykz.model.vo.OrgInfoVO;
import org.mapstruct.Mapper;
import org.mapstruct.Mappings;
import org.mapstruct.factory.Mappers;

/**
 * <p>
 * org_info 模型转换器
 * </p>
 *
 * <AUTHOR>
 * @date 2023-09-14 17:51:37
 */
@Mapper(componentModel = "spring")
public interface OrgInfoConverter {

    OrgInfoConverter INSTANCE = Mappers.getMapper(OrgInfoConverter.class);

    @Mappings({})
    OrgInfo dto2Entity(OrgInfoDTO dto);

    @Mappings({})
    OrgInfoVO entity2Vo(OrgInfo entity);

}
