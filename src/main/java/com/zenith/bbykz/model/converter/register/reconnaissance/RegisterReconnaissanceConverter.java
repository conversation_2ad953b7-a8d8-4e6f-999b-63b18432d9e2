package com.zenith.bbykz.model.converter.register.reconnaissance;

import com.zenith.bbykz.model.dto.register.reconnaissance.RegisterReconnaissanceDTO;
import com.zenith.bbykz.model.entity.register.reconnaissance.RegisterReconnaissance;
import com.zenith.bbykz.model.vo.register.reconnaissance.RegisterReconnaissanceVO;
import org.mapstruct.Mapper;
import org.mapstruct.Mappings;
import org.mapstruct.factory.Mappers;

/**
 * <p>
 * 登记信息-内部事务-实地勘察 模型转换器
 * </p>
 *
 * <AUTHOR>
 * @date 2024-03-28 11:28:37
 */
@Mapper(componentModel = "spring")
public interface RegisterReconnaissanceConverter {

    RegisterReconnaissanceConverter INSTANCE = Mappers.getMapper(RegisterReconnaissanceConverter.class);

    @Mappings({})
    RegisterReconnaissance dto2Entity(RegisterReconnaissanceDTO dto);

    @Mappings({})
    RegisterReconnaissanceVO entity2Vo(RegisterReconnaissance entity);

}
