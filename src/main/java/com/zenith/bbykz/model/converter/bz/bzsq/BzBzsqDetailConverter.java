package com.zenith.bbykz.model.converter.bz.bzsq;

import com.zenith.bbykz.model.dto.bz.bzsq.BzBzsqDetailDTO;
import com.zenith.bbykz.model.entity.bz.bzsq.BzBzsqDetail;
import com.zenith.bbykz.model.vo.bz.bzsq.BzBzsqDetailVO;
import org.mapstruct.Mapper;
import org.mapstruct.Mappings;
import org.mapstruct.factory.Mappers;

/**
 * <p>
 * 核编管理-用编申请-明细 模型转换器
 * </p>
 *
 * <AUTHOR>
 * @date 2024-03-29 16:05:19
 */
@Mapper(componentModel = "spring")
public interface BzBzsqDetailConverter {

    BzBzsqDetailConverter INSTANCE = Mappers.getMapper(BzBzsqDetailConverter.class);

    @Mappings({})
    BzBzsqDetail dto2Entity(BzBzsqDetailDTO dto);

    @Mappings({})
    BzBzsqDetailVO entity2Vo(BzBzsqDetail entity);

}
