package com.zenith.bbykz.model.converter.sd;

import com.zenith.bbykz.model.dto.sd.SdDutyDetailDTO;
import com.zenith.bbykz.model.entity.sd.SdDutyDetail;
import com.zenith.bbykz.model.vo.sd.SdDutyDetailVO;
import org.mapstruct.Mapper;
import org.mapstruct.Mappings;
import org.mapstruct.factory.Mappers;

/**
 * <p>
 * 三定-细化职责 模型转换器
 * </p>
 *
 * <AUTHOR>
 * @date 2024-04-11 16:17:19
 */
@Mapper(componentModel = "spring")
public interface SdDutyDetailConverter {

    SdDutyDetailConverter INSTANCE = Mappers.getMapper(SdDutyDetailConverter.class);

    @Mappings({})
    SdDutyDetail dto2Entity(SdDutyDetailDTO dto);

    @Mappings({})
    SdDutyDetailVO entity2Vo(SdDutyDetail entity);

}
