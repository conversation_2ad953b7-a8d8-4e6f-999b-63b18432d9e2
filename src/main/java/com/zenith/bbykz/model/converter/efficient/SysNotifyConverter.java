package com.zenith.bbykz.model.converter.efficient;

import com.zenith.bbykz.model.dto.efficient.SysNotifyDTO;
import com.zenith.bbykz.model.entity.efficient.SysNotify;
import com.zenith.bbykz.model.vo.efficient.SysNotifyVO;
import org.mapstruct.Mapper;
import org.mapstruct.Mappings;
import org.mapstruct.factory.Mappers;

/**
 * <p>
 * 系统消息通知 模型转换器
 * </p>
 *
 * <AUTHOR>
 * @date 2024-03-18 15:07:00
 */
@Mapper(componentModel = "spring")
public interface SysNotifyConverter {

    SysNotifyConverter INSTANCE = Mappers.getMapper(SysNotifyConverter.class);

    @Mappings({})
    SysNotify dto2Entity(SysNotifyDTO dto);

    @Mappings({})
    SysNotifyVO entity2Vo(SysNotify entity);

}
