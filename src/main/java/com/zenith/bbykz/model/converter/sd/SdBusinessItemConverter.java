package com.zenith.bbykz.model.converter.sd;

import com.zenith.bbykz.model.dto.sd.SdBusinessItemDTO;
import com.zenith.bbykz.model.entity.sd.SdBusinessItem;
import com.zenith.bbykz.model.vo.sd.SdBusinessItemVO;
import org.mapstruct.Mapper;
import org.mapstruct.Mappings;
import org.mapstruct.factory.Mappers;

/**
 * <p>
 * 三定-业务事项 模型转换器
 * </p>
 *
 * <AUTHOR>
 * @date 2024-04-11 16:17:20
 */
@Mapper(componentModel = "spring")
public interface SdBusinessItemConverter {

    SdBusinessItemConverter INSTANCE = Mappers.getMapper(SdBusinessItemConverter.class);

    @Mappings({})
    SdBusinessItem dto2Entity(SdBusinessItemDTO dto);

    @Mappings({})
    SdBusinessItemVO entity2Vo(SdBusinessItem entity);

}
