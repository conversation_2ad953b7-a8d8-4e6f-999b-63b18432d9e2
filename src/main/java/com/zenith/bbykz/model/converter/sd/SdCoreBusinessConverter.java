package com.zenith.bbykz.model.converter.sd;

import com.zenith.bbykz.model.dto.sd.SdCoreBusinessDTO;
import com.zenith.bbykz.model.entity.sd.SdCoreBusiness;
import com.zenith.bbykz.model.vo.sd.SdCoreBusinessVO;
import org.mapstruct.Mapper;
import org.mapstruct.Mappings;
import org.mapstruct.factory.Mappers;

/**
 * <p>
 * 三定-核心业务 模型转换器
 * </p>
 *
 * <AUTHOR>
 * @date 2024-04-11 16:17:20
 */
@Mapper(componentModel = "spring")
public interface SdCoreBusinessConverter {

    SdCoreBusinessConverter INSTANCE = Mappers.getMapper(SdCoreBusinessConverter.class);

    @Mappings({})
    SdCoreBusiness dto2Entity(SdCoreBusinessDTO dto);

    @Mappings({})
    SdCoreBusinessVO entity2Vo(SdCoreBusiness entity);

}
