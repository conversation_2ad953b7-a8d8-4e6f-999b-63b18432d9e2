package com.zenith.bbykz.model.converter.bz.qx;

import com.zenith.bbykz.model.dto.bz.qx.BzQxBzsqDTO;
import com.zenith.bbykz.model.entity.bz.qx.BzQxBzsq;
import com.zenith.bbykz.model.vo.bz.qx.BzQxBzsqVO;
import org.mapstruct.Mapper;
import org.mapstruct.Mappings;
import org.mapstruct.factory.Mappers;

/**
* <p>
* 核编管理-用编管理-区县用编申请计划 模型转换器
* </p>
*
* <AUTHOR>
* @date 2024-05-06 17:05:40
*/
@Mapper(componentModel = "spring")
public interface BzQxBzsqConverter {

    BzQxBzsqConverter INSTANCE = Mappers.getMapper(BzQxBzsqConverter.class);

    @Mappings({})
    BzQxBzsq dto2Entity(BzQxBzsqDTO dto);

    @Mappings({})
    BzQxBzsqVO entity2Vo(BzQxBzsq entity);

}
