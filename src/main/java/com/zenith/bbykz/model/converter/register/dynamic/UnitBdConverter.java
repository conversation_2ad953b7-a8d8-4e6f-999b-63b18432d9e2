package com.zenith.bbykz.model.converter.register.dynamic;

import com.zenith.bbykz.model.dto.register.dynamic.UnitBdDTO;
import com.zenith.bbykz.model.entity.register.dynamic.UnitBd;
import com.zenith.bbykz.model.vo.register.dynamic.UnitBdVO;
import com.zenith.bbykz.model.vo.register.dynamic.UnitBdWarnVO;
import org.mapstruct.Mapper;
import org.mapstruct.Mappings;
import org.mapstruct.factory.Mappers;

/**
 * <p>
 * 登记信息-预警感知-信息变动 模型转换器
 * </p>
 *
 * <AUTHOR>
 * @date 2024-04-09 11:50:41
 */
@Mapper(componentModel = "spring")
public interface UnitBdConverter {

    UnitBdConverter INSTANCE = Mappers.getMapper(UnitBdConverter.class);

    @Mappings({})
    UnitBd dto2Entity(UnitBdDTO dto);

    @Mappings({})
    UnitBdVO entity2Vo(UnitBd entity);

    @Mappings({})
    UnitBdWarnVO entity2Warn(UnitBd entity);

}
