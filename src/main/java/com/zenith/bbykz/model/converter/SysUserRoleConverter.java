package com.zenith.bbykz.model.converter;

import com.zenith.bbykz.model.dto.SysUserRoleDTO;
import com.zenith.bbykz.model.entity.SysUserRole;
import com.zenith.bbykz.model.vo.SysUserRoleVO;
import org.mapstruct.Mapper;
import org.mapstruct.Mappings;
import org.mapstruct.factory.Mappers;

/**
 * <p>
 * sys_user_role 模型转换器
 * </p>
 *
 * <AUTHOR>
 * @date 2023-09-14 17:14:26
 */
@Mapper(componentModel = "spring")
public interface SysUserRoleConverter {

    SysUserRoleConverter INSTANCE = Mappers.getMapper(SysUserRoleConverter.class);

    @Mappings({})
    SysUserRole dto2Entity(SysUserRoleDTO dto);

    @Mappings({})
    SysUserRoleVO entity2Vo(SysUserRole entity);

}
