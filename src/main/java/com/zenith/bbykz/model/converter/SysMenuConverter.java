package com.zenith.bbykz.model.converter;

import com.zenith.bbykz.model.dto.SysMenuDTO;
import com.zenith.bbykz.model.entity.SysMenu;
import com.zenith.bbykz.model.vo.SysMenuVO;
import org.mapstruct.Mapper;
import org.mapstruct.Mappings;
import org.mapstruct.factory.Mappers;

/**
 * <p>
 * 菜单表 模型转换器
 * </p>
 *
 * <AUTHOR>
 * @date 2023-09-14 17:14:26
 */
@Mapper(componentModel = "spring")
public interface SysMenuConverter {

    SysMenuConverter INSTANCE = Mappers.getMapper(SysMenuConverter.class);

    @Mappings({})
    SysMenu dto2Entity(SysMenuDTO dto);

    @Mappings({})
    SysMenuVO entity2Vo(SysMenu entity);

}
