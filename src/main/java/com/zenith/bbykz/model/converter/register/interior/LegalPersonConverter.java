package com.zenith.bbykz.model.converter.register.interior;

import com.zenith.bbykz.model.easy.excel.model.LegalPersonModel;
import com.zenith.bbykz.model.entity.LegalPersonRegistration;
import org.mapstruct.Mapper;
import org.mapstruct.Mappings;
import org.mapstruct.factory.Mappers;

/**
 * <AUTHOR>
 * @since 2024/5/13 10:30
 */
@Mapper(componentModel = "spring")
public interface LegalPersonConverter {
    LegalPersonConverter INSTANCE = Mappers.getMapper(LegalPersonConverter.class);

    @Mappings({})
    LegalPersonRegistration excel2Entity(LegalPersonModel dto);
}
