package com.zenith.bbykz.model.converter.bz.rybd;

import com.zenith.bbykz.model.dto.bz.rybd.BzRyBdDTO;
import com.zenith.bbykz.model.easy.excel.model.BzRyBdExcel;
import com.zenith.bbykz.model.entity.bz.rybd.BzRyBd;
import com.zenith.bbykz.model.vo.bz.rybd.BzRyBdVO;
import org.mapstruct.Mapper;
import org.mapstruct.Mappings;
import org.mapstruct.factory.Mappers;

/**
 * <p>
 * 核编管理-动态信息维护-人员详情 模型转换器
 * </p>
 *
 * <AUTHOR>
 * @date 2024-04-22 11:21:43
 */
@Mapper(componentModel = "spring")
public interface BzRyBdConverter {

    BzRyBdConverter INSTANCE = Mappers.getMapper(BzRyBdConverter.class);

    @Mappings({})
    BzRyBd dto2Entity(BzRyBdDTO dto);

    @Mappings({})
    BzRyBdVO entity2Vo(BzRyBd entity);

    @Mappings({})
    BzRyBdExcel entity2Excel(BzRyBd entity);
}
