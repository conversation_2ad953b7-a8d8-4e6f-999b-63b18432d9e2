package com.zenith.bbykz.model.converter;

import com.zenith.bbykz.model.dto.GeocodeDTO;
import com.zenith.bbykz.model.entity.Geocode;
import com.zenith.bbykz.model.vo.GeocodeVO;
import org.mapstruct.Mapper;
import org.mapstruct.Mappings;
import org.mapstruct.factory.Mappers;

/**
 * <p>
 * 区划代码 模型转换器
 * </p>
 *
 * <AUTHOR>
 * @date 2024-03-13 09:52:37
 */
@Mapper(componentModel = "spring")
public interface GeocodeConverter {

    GeocodeConverter INSTANCE = Mappers.getMapper(GeocodeConverter.class);

    @Mappings({})
    Geocode dto2Entity(GeocodeDTO dto);

    @Mappings({})
    GeocodeVO entity2Vo(Geocode entity);

}
