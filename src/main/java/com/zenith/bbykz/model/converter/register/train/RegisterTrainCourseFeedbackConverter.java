package com.zenith.bbykz.model.converter.register.train;

import com.zenith.bbykz.model.dto.register.train.RegisterTrainCourseFeedbackDTO;
import com.zenith.bbykz.model.entity.register.train.RegisterTrainCourseFeedback;
import com.zenith.bbykz.model.vo.register.train.RegisterTrainCourseFeedbackVO;
import org.mapstruct.Mapper;
import org.mapstruct.Mappings;
import org.mapstruct.factory.Mappers;

/**
 * <p>
 * 登记信息-培训管理-反馈意见 模型转换器
 * </p>
 *
 * <AUTHOR>
 * @date 2024-03-05 15:44:53
 */
@Mapper(componentModel = "spring")
public interface RegisterTrainCourseFeedbackConverter {

    RegisterTrainCourseFeedbackConverter INSTANCE = Mappers.getMapper(RegisterTrainCourseFeedbackConverter.class);

    @Mappings({})
    RegisterTrainCourseFeedback dto2Entity(RegisterTrainCourseFeedbackDTO dto);

    @Mappings({})
    RegisterTrainCourseFeedbackVO entity2Vo(RegisterTrainCourseFeedback entity);

}
