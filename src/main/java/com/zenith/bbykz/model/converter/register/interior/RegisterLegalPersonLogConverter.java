package com.zenith.bbykz.model.converter.register.interior;

import com.zenith.bbykz.model.dto.register.interior.RegisterLegalPersonLogDTO;
import com.zenith.bbykz.model.entity.register.interior.RegisterLegalPersonLog;
import com.zenith.bbykz.model.vo.register.interior.RegisterLegalPersonLogVO;
import org.mapstruct.Mapper;
import org.mapstruct.Mappings;
import org.mapstruct.factory.Mappers;

/**
 * <p>
 * 登记信息-内部事务-法人库日志 模型转换器
 * </p>
 *
 * <AUTHOR>
 * @date 2024-03-12 17:24:37
 */
@Mapper(componentModel = "spring")
public interface RegisterLegalPersonLogConverter {

    RegisterLegalPersonLogConverter INSTANCE = Mappers.getMapper(RegisterLegalPersonLogConverter.class);

    @Mappings({})
    RegisterLegalPersonLog dto2Entity(RegisterLegalPersonLogDTO dto);

    @Mappings({})
    RegisterLegalPersonLogVO entity2Vo(RegisterLegalPersonLog entity);

}
