package com.zenith.bbykz.model.converter.register.random;

import com.zenith.bbykz.model.dto.register.random.DoubleRandomDTO;
import com.zenith.bbykz.model.entity.register.random.DoubleRandom;
import com.zenith.bbykz.model.vo.register.random.DoubleRandomVO;
import org.mapstruct.Mapper;
import org.mapstruct.Mappings;
import org.mapstruct.factory.Mappers;

/**
* <p>
* 登记信息-内部事务-双随机检查 模型转换器
* </p>
*
* <AUTHOR>
* @date 2024-05-06 15:48:28
*/
@Mapper(componentModel = "spring")
public interface DoubleRandomConverter {

    DoubleRandomConverter INSTANCE = Mappers.getMapper(DoubleRandomConverter.class);

    @Mappings({})
    DoubleRandom dto2Entity(DoubleRandomDTO dto);

    @Mappings({})
    DoubleRandomVO entity2Vo(DoubleRandom entity);

}
