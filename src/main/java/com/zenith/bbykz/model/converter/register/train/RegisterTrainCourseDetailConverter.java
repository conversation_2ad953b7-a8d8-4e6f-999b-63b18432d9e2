package com.zenith.bbykz.model.converter.register.train;

import com.zenith.bbykz.model.dto.register.train.RegisterTrainCourseDetailDTO;
import com.zenith.bbykz.model.entity.register.train.RegisterTrainCourseDetail;
import com.zenith.bbykz.model.vo.register.train.RegisterTrainCourseDetailVO;
import org.mapstruct.Mapper;
import org.mapstruct.Mappings;
import org.mapstruct.factory.Mappers;

/**
 * <p>
 * 登记信息-培训管理-课程管理关联课件 模型转换器
 * </p>
 *
 * <AUTHOR>
 * @date 2024-03-05 15:44:53
 */
@Mapper(componentModel = "spring")
public interface RegisterTrainCourseDetailConverter {

    RegisterTrainCourseDetailConverter INSTANCE = Mappers.getMapper(RegisterTrainCourseDetailConverter.class);

    @Mappings({})
    RegisterTrainCourseDetail dto2Entity(RegisterTrainCourseDetailDTO dto);

    @Mappings({})
    RegisterTrainCourseDetailVO entity2Vo(RegisterTrainCourseDetail entity);

}
