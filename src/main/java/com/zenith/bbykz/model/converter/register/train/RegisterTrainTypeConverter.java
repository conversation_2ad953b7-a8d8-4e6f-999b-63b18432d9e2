package com.zenith.bbykz.model.converter.register.train;

import com.zenith.bbykz.model.dto.register.train.RegisterTrainTypeDTO;
import com.zenith.bbykz.model.entity.register.train.RegisterTrainType;
import com.zenith.bbykz.model.vo.register.train.RegisterTrainTypeVO;
import org.mapstruct.Mapper;
import org.mapstruct.Mappings;
import org.mapstruct.factory.Mappers;

/**
 * <p>
 * 登记信息-培训管理-课程分类 模型转换器
 * </p>
 *
 * <AUTHOR>
 * @date 2024-03-05 15:05:10
 */
@Mapper(componentModel = "spring")
public interface RegisterTrainTypeConverter {

    RegisterTrainTypeConverter INSTANCE = Mappers.getMapper(RegisterTrainTypeConverter.class);

    @Mappings({})
    RegisterTrainType dto2Entity(RegisterTrainTypeDTO dto);

    @Mappings({})
    RegisterTrainTypeVO entity2Vo(RegisterTrainType entity);

}
