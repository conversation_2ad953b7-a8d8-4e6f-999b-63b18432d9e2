package com.zenith.bbykz.model.converter.bz.common;

import com.zenith.bbykz.model.dto.bz.common.AduitOpinionDTO;
import com.zenith.bbykz.model.entity.bz.common.AduitOpinion;
import com.zenith.bbykz.model.vo.bz.common.AduitOpinionVO;
import org.mapstruct.Mapper;
import org.mapstruct.Mappings;
import org.mapstruct.factory.Mappers;

/**
* <p>
* 核编管理-常用审批意见 模型转换器
* </p>
*
* <AUTHOR>
* @date 2024-05-17 11:10:37
*/
@Mapper(componentModel = "spring")
public interface AduitOpinionConverter {

    AduitOpinionConverter INSTANCE = Mappers.getMapper(AduitOpinionConverter.class);

    @Mappings({})
    AduitOpinion dto2Entity(AduitOpinionDTO dto);

    @Mappings({})
    AduitOpinionVO entity2Vo(AduitOpinion entity);

}
