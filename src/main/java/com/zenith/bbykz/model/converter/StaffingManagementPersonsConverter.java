package com.zenith.bbykz.model.converter;

import com.zenith.bbykz.model.dto.StaffingManagementPersonsDTO;
import com.zenith.bbykz.model.entity.StaffingManagementPersons;
import com.zenith.bbykz.model.vo.StaffingManagementPersonsVO;
import org.mapstruct.Mapper;
import org.mapstruct.Mappings;
import org.mapstruct.factory.Mappers;

/**
 * <p>
 * 用编管理人员信息 模型转换器
 * </p>
 *
 * <AUTHOR>
 * @date 2023-09-13
 */
@Mapper(componentModel = "spring")
public interface StaffingManagementPersonsConverter {

    StaffingManagementPersonsConverter INSTANCE = Mappers.getMapper(StaffingManagementPersonsConverter.class);

    @Mappings({})
    StaffingManagementPersons dto2Entity(StaffingManagementPersonsDTO dto);

    @Mappings({})
    StaffingManagementPersonsVO entity2Vo(StaffingManagementPersons entity);

}
