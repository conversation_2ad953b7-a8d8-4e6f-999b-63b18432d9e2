package com.zenith.bbykz.model.converter;

import com.zenith.bbykz.model.dto.StaffingManagementRegistrationDTO;
import com.zenith.bbykz.model.entity.StaffingManagementRegistration;
import com.zenith.bbykz.model.vo.StaffingManagementRegistrationVO;
import org.mapstruct.Mapper;
import org.mapstruct.Mappings;
import org.mapstruct.factory.Mappers;

/**
 * <p>
 * 用编管理登记表 模型转换器
 * </p>
 *
 * <AUTHOR>
 * @date 2023-09-28 14:28:24
 */
@Mapper(componentModel = "spring")
public interface StaffingManagementRegistrationConverter {

    StaffingManagementRegistrationConverter INSTANCE = Mappers.getMapper(StaffingManagementRegistrationConverter.class);

    @Mappings({})
    StaffingManagementRegistration dto2Entity(StaffingManagementRegistrationDTO dto);

    @Mappings({})
    StaffingManagementRegistrationVO entity2Vo(StaffingManagementRegistration entity);

}
