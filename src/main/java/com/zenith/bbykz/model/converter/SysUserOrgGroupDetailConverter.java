package com.zenith.bbykz.model.converter;

import com.zenith.bbykz.model.dto.SysUserOrgGroupDetailDTO;
import com.zenith.bbykz.model.entity.SysUserOrgGroupDetail;
import com.zenith.bbykz.model.vo.SysUserOrgGroupDetailVO;
import org.mapstruct.Mapper;
import org.mapstruct.Mappings;
import org.mapstruct.factory.Mappers;

/**
 * <p>
 * 用户机构组详情 模型转换器
 * </p>
 *
 * <AUTHOR>
 * @date 2024-03-13 10:46:03
 */
@Mapper(componentModel = "spring")
public interface SysUserOrgGroupDetailConverter {

    SysUserOrgGroupDetailConverter INSTANCE = Mappers.getMapper(SysUserOrgGroupDetailConverter.class);

    @Mappings({})
    SysUserOrgGroupDetail dto2Entity(SysUserOrgGroupDetailDTO dto);

    @Mappings({})
    SysUserOrgGroupDetailVO entity2Vo(SysUserOrgGroupDetail entity);

}
