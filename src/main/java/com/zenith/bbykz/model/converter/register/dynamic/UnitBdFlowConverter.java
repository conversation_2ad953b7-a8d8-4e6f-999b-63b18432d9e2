package com.zenith.bbykz.model.converter.register.dynamic;

import com.zenith.bbykz.model.dto.register.dynamic.UnitBdFlowDTO;
import com.zenith.bbykz.model.entity.register.dynamic.UnitBdFlow;
import com.zenith.bbykz.model.vo.register.dynamic.UnitBdFlowVO;
import org.mapstruct.Mapper;
import org.mapstruct.Mappings;
import org.mapstruct.factory.Mappers;

/**
* <p>
* 登记信息-变动流程记录 模型转换器
* </p>
*
* <AUTHOR>
* @date 2024-04-18 10:01:34
*/
@Mapper(componentModel = "spring")
public interface UnitBdFlowConverter {

    UnitBdFlowConverter INSTANCE = Mappers.getMapper(UnitBdFlowConverter.class);

    @Mappings({})
    UnitBdFlow dto2Entity(UnitBdFlowDTO dto);

    @Mappings({})
    UnitBdFlowVO entity2Vo(UnitBdFlow entity);

}
