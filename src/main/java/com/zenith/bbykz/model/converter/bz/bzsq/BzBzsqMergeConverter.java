package com.zenith.bbykz.model.converter.bz.bzsq;

import com.zenith.bbykz.model.dto.bz.bzsq.BzBzsqMergeDTO;
import com.zenith.bbykz.model.entity.bz.bzsq.BzBzsqMerge;
import com.zenith.bbykz.model.vo.bz.bzsq.BzBzsqMergeVO;
import org.mapstruct.Mapper;
import org.mapstruct.Mappings;
import org.mapstruct.factory.Mappers;

/**
 * <p>
 * 核编管理-用编审核-合并审核 模型转换器
 * </p>
 *
 * <AUTHOR>
 * @date 2024-04-03 15:26:58
 */
@Mapper(componentModel = "spring")
public interface BzBzsqMergeConverter {

    BzBzsqMergeConverter INSTANCE = Mappers.getMapper(BzBzsqMergeConverter.class);

    @Mappings({})
    BzBzsqMerge dto2Entity(BzBzsqMergeDTO dto);

    @Mappings({})
    BzBzsqMergeVO entity2Vo(BzBzsqMerge entity);

}
