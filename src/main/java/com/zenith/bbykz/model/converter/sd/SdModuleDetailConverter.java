package com.zenith.bbykz.model.converter.sd;

import com.zenith.bbykz.model.dto.sd.SdModuleDetailDTO;
import com.zenith.bbykz.model.entity.sd.SdModuleDetail;
import com.zenith.bbykz.model.vo.sd.SdModuleDetailVO;
import org.mapstruct.Mapper;
import org.mapstruct.Mappings;
import org.mapstruct.factory.Mappers;

/**
 * <p>
 * 三定-三定明细 模型转换器
 * </p>
 *
 * <AUTHOR>
 * @date 2024-04-11 16:17:20
 */
@Mapper(componentModel = "spring")
public interface SdModuleDetailConverter {

    SdModuleDetailConverter INSTANCE = Mappers.getMapper(SdModuleDetailConverter.class);

    @Mappings({})
    SdModuleDetail dto2Entity(SdModuleDetailDTO dto);

    @Mappings({})
    SdModuleDetailVO entity2Vo(SdModuleDetail entity);

}
