package com.zenith.bbykz.model.converter;

import com.zenith.bbykz.model.dto.SysUserOrgGroupDTO;
import com.zenith.bbykz.model.entity.SysUserOrgGroup;
import com.zenith.bbykz.model.vo.SysUserOrgGroupVO;
import org.mapstruct.Mapper;
import org.mapstruct.Mappings;
import org.mapstruct.factory.Mappers;

/**
 * <p>
 * 用户机构组 模型转换器
 * </p>
 *
 * <AUTHOR>
 * @date 2024-03-13 10:48:03
 */
@Mapper(componentModel = "spring")
public interface SysUserOrgGroupConverter {

    SysUserOrgGroupConverter INSTANCE = Mappers.getMapper(SysUserOrgGroupConverter.class);

    @Mappings({})
    SysUserOrgGroup dto2Entity(SysUserOrgGroupDTO dto);

    @Mappings({})
    SysUserOrgGroupVO entity2Vo(SysUserOrgGroup entity);

}
