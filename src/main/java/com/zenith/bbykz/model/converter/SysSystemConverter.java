package com.zenith.bbykz.model.converter;

import com.zenith.bbykz.model.dto.SysSystemDTO;
import com.zenith.bbykz.model.entity.SysSystem;
import com.zenith.bbykz.model.vo.SysSystemVO;
import org.mapstruct.Mapper;
import org.mapstruct.Mappings;
import org.mapstruct.factory.Mappers;

/**
 * <p>
 * sys_system 模型转换器
 * </p>
 *
 * <AUTHOR>
 * @date 2024-02-29 11:43:56
 */
@Mapper(componentModel = "spring")
public interface SysSystemConverter {

    SysSystemConverter INSTANCE = Mappers.getMapper(SysSystemConverter.class);

    @Mappings({})
    SysSystem dto2Entity(SysSystemDTO dto);

    @Mappings({})
    SysSystemVO entity2Vo(SysSystem entity);

}
