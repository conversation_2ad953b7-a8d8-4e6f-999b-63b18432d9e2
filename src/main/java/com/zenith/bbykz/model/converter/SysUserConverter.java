package com.zenith.bbykz.model.converter;

import com.zenith.bbykz.model.dto.SysUserDTO;
import com.zenith.bbykz.model.entity.SysUser;
import com.zenith.bbykz.model.vo.SysUserVO;
import org.mapstruct.Mapper;
import org.mapstruct.Mappings;
import org.mapstruct.factory.Mappers;

/**
 * <p>
 * sys_user 模型转换器
 * </p>
 *
 * <AUTHOR>
 * @date 2023-09-14 17:14:26
 */
@Mapper(componentModel = "spring")
public interface SysUserConverter {

    SysUserConverter INSTANCE = Mappers.getMapper(SysUserConverter.class);

    @Mappings({})
    SysUser dto2Entity(SysUserDTO dto);

    @Mappings({})
    SysUserVO entity2Vo(SysUser entity);

}
