package com.zenith.bbykz.model.converter.sd;

import com.zenith.bbykz.model.dto.sd.SdBusinessItemOneDTO;
import com.zenith.bbykz.model.entity.sd.SdBusinessItemOne;
import com.zenith.bbykz.model.vo.sd.SdBusinessItemOneVO;
import org.mapstruct.Mapper;
import org.mapstruct.Mappings;
import org.mapstruct.factory.Mappers;

/**
 * <p>
 * 三定-业务事项一件事关联表 模型转换器
 * </p>
 *
 * <AUTHOR>
 * @date 2024-04-11 16:17:20
 */
@Mapper(componentModel = "spring")
public interface SdBusinessItemOneConverter {

    SdBusinessItemOneConverter INSTANCE = Mappers.getMapper(SdBusinessItemOneConverter.class);

    @Mappings({})
    SdBusinessItemOne dto2Entity(SdBusinessItemOneDTO dto);

    @Mappings({})
    SdBusinessItemOneVO entity2Vo(SdBusinessItemOne entity);

}
