package com.zenith.bbykz.model.converter.register.train;

import com.zenith.bbykz.model.dto.register.train.RegisterTrainCourseDTO;
import com.zenith.bbykz.model.entity.register.train.RegisterTrainCourse;
import com.zenith.bbykz.model.vo.register.train.RegisterTrainCourseVO;
import org.mapstruct.Mapper;
import org.mapstruct.Mappings;
import org.mapstruct.factory.Mappers;

/**
 * <p>
 * 登记信息-培训管理-课程管理 模型转换器
 * </p>
 *
 * <AUTHOR>
 * @date 2024-03-05 15:44:53
 */
@Mapper(componentModel = "spring")
public interface RegisterTrainCourseConverter {

    RegisterTrainCourseConverter INSTANCE = Mappers.getMapper(RegisterTrainCourseConverter.class);

    @Mappings({})
    RegisterTrainCourse dto2Entity(RegisterTrainCourseDTO dto);

    @Mappings({})
    RegisterTrainCourseVO entity2Vo(RegisterTrainCourse entity);

}
