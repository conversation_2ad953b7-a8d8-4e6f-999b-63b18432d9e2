package com.zenith.bbykz.model.converter;

import com.zenith.bbykz.model.dto.SysRoleDTO;
import com.zenith.bbykz.model.entity.SysRole;
import com.zenith.bbykz.model.vo.SysRoleVO;
import org.mapstruct.Mapper;
import org.mapstruct.Mappings;
import org.mapstruct.factory.Mappers;

/**
 * <p>
 * sys_role 模型转换器
 * </p>
 *
 * <AUTHOR>
 * @date 2023-09-14 17:14:26
 */
@Mapper(componentModel = "spring")
public interface SysRoleConverter {

    SysRoleConverter INSTANCE = Mappers.getMapper(SysRoleConverter.class);

    @Mappings({})
    SysRole dto2Entity(SysRoleDTO dto);

    @Mappings({})
    SysRoleVO entity2Vo(SysRole entity);

}
