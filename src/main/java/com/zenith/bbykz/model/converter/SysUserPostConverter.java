package com.zenith.bbykz.model.converter;

import com.zenith.bbykz.model.dto.SysUserPostDTO;
import com.zenith.bbykz.model.entity.SysUserPost;
import com.zenith.bbykz.model.vo.SysUserPostVO;
import org.mapstruct.Mapper;
import org.mapstruct.Mappings;
import org.mapstruct.factory.Mappers;

/**
 * <p>
 * 用户职位信息 模型转换器
 * </p>
 *
 * <AUTHOR>
 * @date 2024-03-26 15:04:24
 */
@Mapper(componentModel = "spring")
public interface SysUserPostConverter {

    SysUserPostConverter INSTANCE = Mappers.getMapper(SysUserPostConverter.class);

    @Mappings({})
    SysUserPost dto2Entity(SysUserPostDTO dto);

    @Mappings({})
    SysUserPostVO entity2Vo(SysUserPost entity);

}
