package com.zenith.bbykz.model.converter.sd;

import com.zenith.bbykz.model.dto.sd.SdModuleDTO;
import com.zenith.bbykz.model.entity.sd.SdModule;
import com.zenith.bbykz.model.vo.sd.SdModuleVO;
import org.mapstruct.Mapper;
import org.mapstruct.Mappings;
import org.mapstruct.factory.Mappers;

/**
 * <p>
 * 三定-三定类型 模型转换器
 * </p>
 *
 * <AUTHOR>
 * @date 2024-04-11 16:17:20
 */
@Mapper(componentModel = "spring")
public interface SdModuleConverter {

    SdModuleConverter INSTANCE = Mappers.getMapper(SdModuleConverter.class);

    @Mappings({})
    SdModule dto2Entity(SdModuleDTO dto);

    @Mappings({})
    SdModuleVO entity2Vo(SdModule entity);

}
