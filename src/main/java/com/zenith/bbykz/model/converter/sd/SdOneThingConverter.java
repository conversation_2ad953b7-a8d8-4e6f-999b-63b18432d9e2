package com.zenith.bbykz.model.converter.sd;

import com.zenith.bbykz.model.dto.sd.SdOneThingDTO;
import com.zenith.bbykz.model.entity.sd.SdOneThing;
import com.zenith.bbykz.model.vo.sd.SdOneThingVO;
import org.mapstruct.Mapper;
import org.mapstruct.Mappings;
import org.mapstruct.factory.Mappers;

/**
 * <p>
 * 三定-一件事 模型转换器
 * </p>
 *
 * <AUTHOR>
 * @date 2024-04-11 16:17:20
 */
@Mapper(componentModel = "spring")
public interface SdOneThingConverter {

    SdOneThingConverter INSTANCE = Mappers.getMapper(SdOneThingConverter.class);

    @Mappings({})
    SdOneThing dto2Entity(SdOneThingDTO dto);

    @Mappings({})
    SdOneThingVO entity2Vo(SdOneThing entity);

}
