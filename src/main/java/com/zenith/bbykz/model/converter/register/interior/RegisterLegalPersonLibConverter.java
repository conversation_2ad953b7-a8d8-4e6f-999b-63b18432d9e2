package com.zenith.bbykz.model.converter.register.interior;

import com.zenith.bbykz.model.dto.register.interior.RegisterLegalPersonLibDTO;
import com.zenith.bbykz.model.entity.register.interior.RegisterLegalPersonLib;
import com.zenith.bbykz.model.vo.register.interior.RegisterLegalPersonLibVO;
import org.mapstruct.Mapper;
import org.mapstruct.Mappings;
import org.mapstruct.factory.Mappers;

/**
 * <p>
 * 登记信息-内部事务-法人库 模型转换器
 * </p>
 *
 * <AUTHOR>
 * @date 2024-03-12 14:55:18
 */
@Mapper(componentModel = "spring")
public interface RegisterLegalPersonLibConverter {

    RegisterLegalPersonLibConverter INSTANCE = Mappers.getMapper(RegisterLegalPersonLibConverter.class);

    @Mappings({})
    RegisterLegalPersonLib dto2Entity(RegisterLegalPersonLibDTO dto);

    @Mappings({})
    RegisterLegalPersonLibVO entity2Vo(RegisterLegalPersonLib entity);

}
