package com.zenith.bbykz.model.converter;

import com.zenith.bbykz.model.dto.StaffingManagementDTO;
import com.zenith.bbykz.model.entity.StaffingManagement;
import com.zenith.bbykz.model.vo.StaffingManagementVO;
import org.mapstruct.Mapper;
import org.mapstruct.Mappings;
import org.mapstruct.factory.Mappers;

/**
 * <p>
 * 用编管理 模型转换器
 * </p>
 *
 * <AUTHOR>
 * @date 2023-09-13
 */
@Mapper(componentModel = "spring")
public interface StaffingManagementConverter {

    StaffingManagementConverter INSTANCE = Mappers.getMapper(StaffingManagementConverter.class);

    @Mappings({})
    StaffingManagement dto2Entity(StaffingManagementDTO dto);

    @Mappings({})
    StaffingManagementVO entity2Vo(StaffingManagement entity);

}
