package com.zenith.bbykz.model.converter;

import com.zenith.bbykz.model.dto.SysRoleMenuDTO;
import com.zenith.bbykz.model.entity.SysRoleMenu;
import com.zenith.bbykz.model.vo.SysRoleMenuVO;
import org.mapstruct.Mapper;
import org.mapstruct.Mappings;
import org.mapstruct.factory.Mappers;

/**
 * <p>
 * sys_role_menu 模型转换器
 * </p>
 *
 * <AUTHOR>
 * @date 2023-09-14 17:14:26
 */
@Mapper(componentModel = "spring")
public interface SysRoleMenuConverter {

    SysRoleMenuConverter INSTANCE = Mappers.getMapper(SysRoleMenuConverter.class);

    @Mappings({})
    SysRoleMenu dto2Entity(SysRoleMenuDTO dto);

    @Mappings({})
    SysRoleMenuVO entity2Vo(SysRoleMenu entity);

}
