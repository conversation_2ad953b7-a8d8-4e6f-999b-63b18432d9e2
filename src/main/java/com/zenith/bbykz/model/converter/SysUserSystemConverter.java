package com.zenith.bbykz.model.converter;

import com.zenith.bbykz.model.dto.SysUserSystemDTO;
import com.zenith.bbykz.model.entity.SysUserSystem;
import com.zenith.bbykz.model.vo.SysUserSystemVO;
import org.mapstruct.Mapper;
import org.mapstruct.Mappings;
import org.mapstruct.factory.Mappers;

/**
 * <p>
 * 用户系统表 模型转换器
 * </p>
 *
 * <AUTHOR>
 * @date 2024-02-29 10:36:56
 */
@Mapper(componentModel = "spring")
public interface SysUserSystemConverter {

    SysUserSystemConverter INSTANCE = Mappers.getMapper(SysUserSystemConverter.class);

    @Mappings({})
    SysUserSystem dto2Entity(SysUserSystemDTO dto);

    @Mappings({})
    SysUserSystemVO entity2Vo(SysUserSystem entity);

}
