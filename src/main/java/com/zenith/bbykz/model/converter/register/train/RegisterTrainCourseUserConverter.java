package com.zenith.bbykz.model.converter.register.train;

import com.zenith.bbykz.model.dto.register.train.RegisterTrainCourseUserDTO;
import com.zenith.bbykz.model.entity.register.train.RegisterTrainCourseUser;
import com.zenith.bbykz.model.vo.register.train.RegisterTrainCourseUserVO;
import org.mapstruct.Mapper;
import org.mapstruct.Mappings;
import org.mapstruct.factory.Mappers;

/**
 * <p>
 * 登记信息-培训管理-用户课程 模型转换器
 * </p>
 *
 * <AUTHOR>
 * @date 2024-03-12 10:11:37
 */
@Mapper(componentModel = "spring")
public interface RegisterTrainCourseUserConverter {

    RegisterTrainCourseUserConverter INSTANCE = Mappers.getMapper(RegisterTrainCourseUserConverter.class);

    @Mappings({})
    RegisterTrainCourseUser dto2Entity(RegisterTrainCourseUserDTO dto);

    @Mappings({})
    RegisterTrainCourseUserVO entity2Vo(RegisterTrainCourseUser entity);

}
