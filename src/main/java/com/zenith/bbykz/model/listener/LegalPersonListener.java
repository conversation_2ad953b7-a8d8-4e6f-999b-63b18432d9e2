package com.zenith.bbykz.model.listener;

import cn.hutool.core.date.TimeInterval;
import cn.hutool.core.util.StrUtil;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.event.AnalysisEventListener;
import com.alibaba.excel.exception.ExcelAnalysisException;
import com.alibaba.excel.util.StringUtils;
import com.efficient.cache.util.ProgressUtil;
import com.efficient.common.auth.RequestHolder;
import com.efficient.common.constant.DbConstant;
import com.efficient.common.util.CollUtil;
import com.zenith.bbykz.api.publicInstitutions.LegalPersonService;
import com.zenith.bbykz.common.constant.BbCommonConstant;
import com.zenith.bbykz.model.converter.register.interior.LegalPersonConverter;
import com.zenith.bbykz.model.easy.excel.model.LegalPersonModel;
import com.zenith.bbykz.model.entity.LegalPersonRegistration;
import lombok.extern.slf4j.Slf4j;

import java.lang.reflect.Field;
import java.util.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

import static com.zenith.bbykz.common.constant.BbCommonConstant.CQ_CODE;
import static com.zenith.bbykz.common.constant.BbCommonConstant.ZY_CODE;

@Slf4j
public class LegalPersonListener extends AnalysisEventListener<LegalPersonModel> {

    private final List<LegalPersonRegistration> cachedDataList = new ArrayList<>();
    private final LegalPersonService service;
    private final ProgressUtil progressUtil;
    private final String cacheKey;
    private final LegalPersonConverter legalPersonConverter;
    private final AtomicInteger count = new AtomicInteger(0);

    public LegalPersonListener(LegalPersonService service, LegalPersonConverter legalPersonConverter,
                               ProgressUtil progressUtil, String cacheKey) {
        this.service = service;
        this.progressUtil = progressUtil;
        this.cacheKey = cacheKey;
        this.legalPersonConverter = legalPersonConverter;
    }

    @Override
    public void invoke(LegalPersonModel model, AnalysisContext analysisContext) {
        // 获取总行数（含表头）
        Integer rowNumber = analysisContext.readSheetHolder().getApproximateTotalRowNumber();
        int andIncrement = count.getAndIncrement();
        double ratio = ((double) (andIncrement * 100) / rowNumber) * (0.65) + 10;
        progressUtil.running("正在读取表格文件", ratio, cacheKey);
        LegalPersonRegistration entity = legalPersonConverter.excel2Entity(model);
        // LegalPersonRegistration entity = new LegalPersonRegistration();
        // BeanUtils.copyProperties(model, entity);
        cachedDataList.add(entity);

    }

    @Override
    public void doAfterAllAnalysed(AnalysisContext analysisContext) {
        progressUtil.running("准备保存文件数据到数据库中", 75, cacheKey);
        if (!cachedDataList.isEmpty()) {
            Date date = new Date();
            // 查找最后一次版本号
            LegalPersonRegistration lastVersionEntity = service.findLastVersion();
            Integer lastVersion = Objects.isNull(lastVersionEntity) ? 0 : Objects.isNull(lastVersionEntity.getVersion()) ? 0 : lastVersionEntity.getVersion();
            List<LegalPersonRegistration> list = cachedDataList.stream().map(item -> setAttribut(item, date, lastVersion)).collect(Collectors.toList());
            list.stream().forEach(et -> {
                String num = et.getRegistrationAuthorityCode();
                if (StrUtil.isNotBlank(num)) {
                    String geocode = num.substring(0, 6);
                    if (StrUtil.equals(geocode, ZY_CODE)) {
                        geocode = CQ_CODE;
                    } else if (StrUtil.equals(geocode, "500222")) {
                        geocode = "500124";
                    } else if (StrUtil.equals(geocode, "500111")) {
                        geocode = "500125";
                    } else if (StrUtil.equals(geocode, "500221")) {
                        geocode = "500115";
                    }
                    et.setRegistrationAuthorityCode(geocode);
                }
            });
            // progressUtil.running("正在保存文件数据到数据库中", 85, cacheKey);
            TimeInterval timeInterval = new TimeInterval();
            timeInterval.start();
            List<List<LegalPersonRegistration>> splitList = CollUtil.splitList(list, DbConstant.BATCH_LITTLE_SIZE);
            int size = splitList.size();
            int count = 0;
            for (List<LegalPersonRegistration> registrations : splitList) {
                count++;
                service.saveBatch(registrations, DbConstant.BATCH_LITTLE_SIZE);
                // service.saveBatch(registrations, 100);
                double ration = ((double) (count * 100) / size) * (0.2) + 75;
                progressUtil.running("正在保存文件数据到数据库中", ration, cacheKey);
            }
            long l = timeInterval.intervalSecond();
            log.info("导入耗时：{},导入数据量：{}", l,list.size());
            progressUtil.running("正在修改版本号", 95, cacheKey);
            // 移除之前的版本号
            service.deleteByVersion(lastVersion);
        }
    }

    private LegalPersonRegistration setAttribut(LegalPersonRegistration item, Date date, Integer lastVersion) {
        item.setIsDelete(BbCommonConstant.FALSE_INT);
        item.setCreater(RequestHolder.getCurrUser().getAccount());
        item.setCreateTime(date);
        item.setVersion(lastVersion + 1);
        return item;
    }

    /**
     * @Description invokeHeadMap读取excel表头，校验表头是否正确
     * <AUTHOR>
     * @Date 2023/9/26 15:24
     */
    @Override
    public void invokeHeadMap(Map<Integer, String> headMap, AnalysisContext context) {
        Map<Integer, String> head = new HashMap<>();
        progressUtil.running("正在校验表格数据", 35, cacheKey);
        try {
            //通过class获取到使用@ExcelProperty注解配置的字段
            head = getIndexNameMap(LegalPersonModel.class);
        } catch (NoSuchFieldException e) {
            log.error("导入事业单位登记表异常", e);
        }
        //解析到的excel表头和实体配置的进行比对
        Set<Integer> keySet = head.keySet();
        for (Integer key : keySet) {
            if (StringUtils.isEmpty(headMap.get(key))) {
                throw new ExcelAnalysisException("解析excel出错，请传入正确格式的excel");
            }
            if (!headMap.get(key).equals(head.get(key))) {
                throw new ExcelAnalysisException("解析excel出错，请传入正确格式的excel");
            }
        }
    }

    /**
     * @Description 通过class获取类字段信息
     * <AUTHOR>
     * @Date 2023/9/26 15:24
     */
    public Map<Integer, String> getIndexNameMap(Class clazz) throws NoSuchFieldException {
        Map<Integer, String> result = new HashMap<>();
        Field field;
        //获取类中所有的属性
        Field[] fields = clazz.getDeclaredFields();
        for (int i = 0; i < fields.length; i++) {
            field = clazz.getDeclaredField(fields[i].getName());
            field.setAccessible(true);
            //获取根据注解的方式获取ExcelProperty修饰的字段
            ExcelProperty excelProperty = field.getAnnotation(ExcelProperty.class);
            if (excelProperty != null) {
                String[] values = excelProperty.value();
                StringBuilder value = new StringBuilder();
                for (String v : values) {
                    value.append(v);
                }
                result.put(i, value.toString());
            }
        }
        return result;
    }
}
