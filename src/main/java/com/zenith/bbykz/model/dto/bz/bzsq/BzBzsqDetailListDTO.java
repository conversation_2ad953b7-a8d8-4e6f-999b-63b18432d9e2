package com.zenith.bbykz.model.dto.bz.bzsq;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import io.swagger.models.auth.In;
import lombok.Data;

import java.io.Serializable;

import javax.validation.constraints.NotNull;

/**
 * <p>
 * 核编管理-用编申请-明细 列表查询DTO
 * </p>
 *
 * <AUTHOR>
 * @date 2024-03-29 16:05:19
 */
@Data
@ApiModel("核编管理-用编申请-明细 列表查询-BzBzsqDetailListDTO")
public class BzBzsqDetailListDTO implements Serializable {
    private static final long serialVersionUID = 8073189474511534895L;
    @ApiModelProperty(value = "关键字搜索")
    private String keyword;
    @ApiModelProperty(value = "用编申请主键")
    private String bizId;

    @ApiModelProperty(value = "是否退回，1-是，0-否")
    private Integer isBack;
}
