package com.zenith.bbykz.model.dto.bz.bzsq;

import com.baomidou.mybatisplus.annotation.TableField;
import com.efficient.common.validate.Common3Group;
import com.efficient.file.model.dto.FileIdList;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * <p>
 * 核编管理-用编申请-明细 DTO
 * </p>
 *
 * <AUTHOR>
 * @date 2024-03-29 16:05:19
 */
@Data
@ApiModel("核编管理-用编申请-明细 请求实体-BzBzsqDetailDTO")
public class BzBzsqDetailDTO extends FileIdList implements Serializable {
    private static final long serialVersionUID = 4473080232671362405L;

    /**
     * 主键id
     */
    @ApiModelProperty(value = "主键id")
    private String id;
    /**
     * 编制申请主键id
     */
    @ApiModelProperty(value = "编制申请主键id")
    private String bzsqId;
    /**
     * 编制类型
     */
    @ApiModelProperty(value = "编制类型")
    private String bzType;
    /**
     * 用编类型
     */
    @ApiModelProperty(value = "用编类型")
    private String ybType;
    /**
     * 待上编数
     */
    @ApiModelProperty(value = "待上编数")
    private Integer dsbNum;
    /**
     * 在编数
     */
    @ApiModelProperty(value = "在编数")
    private Integer zbNum;
    /**
     * 余编数
     */
    @ApiModelProperty(value = "余编数")
    private Integer ybNum;
    /**
     * 申请数
     */
    @ApiModelProperty(value = "申请数")
    private Integer applyNum;
    /**
     * 备注
     */
    @ApiModelProperty(value = "备注")
    private String remark;
    /**
     * 退回原因
     */
    @ApiModelProperty(value = "退回原因")
    @NotBlank(groups = Common3Group.class, message = "backReason 不能为空")
    private String backReason;

    @ApiModelProperty(value = "是否被退回，1-是，0-否")
    @NotNull(groups = Common3Group.class, message = "isBack 不能为空")
    private Integer isBack;

    /**
     * 编制使用单位id
     */
    @ApiModelProperty(value = "编制使用单位id")
    private String useBzUnitId;
    /**
     * 编制使用单位名称
     */
    @ApiModelProperty(value = "编制使用单位名称")
    private String useBzUnitName;
    /**
     * 编制使用单位层级码
     */
    @ApiModelProperty(value = "编制使用单位层级码")
    private String useBzUnitLevelCode;
}

