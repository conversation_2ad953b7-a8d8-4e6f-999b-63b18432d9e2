package com.zenith.bbykz.model.dto.register.dynamic;

import com.efficient.common.validate.Common1Group;
import com.efficient.common.validate.Common2Group;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.Date;

/**
 * <p>
 * 登记信息-预警感知-信息变动 列表查询DTO
 * </p>
 *
 * <AUTHOR>
 * @date 2024-04-09 11:50:41
 */
@Data
@ApiModel("登记信息-预警感知-信息变动 列表查询-UnitBdListDTO")
public class UnitBdListDTO implements Serializable {
    private static final long serialVersionUID = 2954454518549494293L;
    @NotNull(message = "pageNum 不能为空")
    private Integer pageNum;
    @NotNull(message = "pageSize 不能为空")
    private Integer pageSize;
    @ApiModelProperty(value = "层级码")
    @NotBlank(groups = Common1Group.class, message = "orgLevelCode 不能为空")
    private String orgLevelCode;
    @ApiModelProperty(value = "关键字搜索")
    private String keyword;
    @ApiModelProperty(value = "类型")
    private String type;
    @ApiModelProperty(value = "变动时间")
    private Date startDate;
    @ApiModelProperty(value = "变动时间")
    private Date endDate;
    @ApiModelProperty(value = "页面类型，1-预警信息，2-历史信息")
    @NotNull(groups = {Common2Group.class}, message = "pageType 不能为空，页面类型，1-预警信息，2-历史信息")
    private Integer pageType;
    @ApiModelProperty(value = "统一社会信用代码")
    private String creditCode;
}
