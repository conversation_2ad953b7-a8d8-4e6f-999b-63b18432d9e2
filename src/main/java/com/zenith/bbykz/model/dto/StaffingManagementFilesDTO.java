package com.zenith.bbykz.model.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;

/**
 * <p>
 * 用编管理附件 DTO
 * </p>
 *
 * <AUTHOR>
 * @date 2023-09-14
 */
@Data
public class StaffingManagementFilesDTO {

    @ApiModelProperty(value = "文件名称")
    @NotBlank(message = "文件名称不能为空")
    private String fileName;

    @ApiModelProperty(value = "文件id")
    @NotBlank(message = "文件id不能为空")
    private String fileId;

}
