package com.zenith.bbykz.model.dto.register.train;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * <p>
 * 登记信息-培训管理-用户课程 DTO
 * </p>
 *
 * <AUTHOR>
 * @date 2024-03-12 10:11:37
 */
@Data
@ApiModel("登记信息-培训管理-用户课程 请求实体-RegisterTrainCourseUserDTO")
public class RegisterTrainCourseUserDTO implements Serializable {
    private static final long serialVersionUID = 9007203557703895900L;

    /**
     * 主键id
     */
    @ApiModelProperty(value = "主键id")
    private String id;
    /**
     * 课程主键
     */
    @ApiModelProperty(value = "课程主键")
    private String courseId;
    /**
     * 用户id
     */
    @ApiModelProperty(value = "用户id")
    private String userId;
    /**
     * 课件时长
     */
    @ApiModelProperty(value = "课件时长")
    private Integer mins;
    /**
     * 学习时长
     */
    @ApiModelProperty(value = "学习时长")
    private Integer viewMins;
    /**
     * 是否完成
     */
    @ApiModelProperty(value = "是否完成")
    private Integer isFinish;
    /**
     * 是否关注
     */
    @ApiModelProperty(value = "是否关注")
    private Integer isAttention;
    /**
     * 进度
     */
    @ApiModelProperty(value = "进度")
    private java.math.BigDecimal progress;
    /**
     * 创建时间
     */
    @ApiModelProperty(value = "创建时间")
    private Date createTime;
    /**
     * 创建人
     */
    @ApiModelProperty(value = "创建人")
    private String createUser;
    /**
     * 修改时间
     */
    @ApiModelProperty(value = "修改时间")
    private Date updateTime;
    /**
     * 修改人
     */
    @ApiModelProperty(value = "修改人")
    private String updateUser;
    /**
     * 是否删除
     */
    @ApiModelProperty(value = "是否删除")
    private Integer isDelete;

}

