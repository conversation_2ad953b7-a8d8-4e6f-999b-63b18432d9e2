package com.zenith.bbykz.model.dto;

import io.swagger.annotations.ApiModel;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * <p>
 * sys_user 列表查询DTO
 * </p>
 *
 * <AUTHOR>
 * @date 2023-09-14 17:14:26
 */
@Data
@ApiModel("sys_user 列表查询-SysUserListDTO")
public class SysUserListDTO {
    private static final long serialVersionUID = 2081551886327070609L;
    @NotNull(message = "pageNum 不能为空")
    private Integer pageNum;
    @NotNull(message = "pageSize 不能为空")
    private Integer pageSize;

    @NotNull(message = "orgCode 不能为空")
    private String orgCode;

    private String account;

    private String phone;
    private String subordinate;
    private Integer roleLevel;
    private String systemId;
    private String userId;
    private List<String> neUserIds;

}
