package com.zenith.bbykz.model.dto.sd;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

import javax.validation.constraints.NotNull;

/**
 * <p>
 * 三定-业务事项一件事关联表 列表查询DTO
 * </p>
 *
 * <AUTHOR>
 * @date 2024-04-11 16:17:20
 */
@Data
@ApiModel("三定-业务事项一件事关联表 列表查询-SdBusinessItemOneListDTO")
public class SdBusinessItemOneListDTO implements Serializable {
    private static final long serialVersionUID = 1951851764974832648L;
    @NotNull(message = "pageNum 不能为空")
    private Integer pageNum;
    @NotNull(message = "pageSize 不能为空")
    private Integer pageSize;
    @ApiModelProperty(value = "关键字搜索")
    private String keyword;
}
