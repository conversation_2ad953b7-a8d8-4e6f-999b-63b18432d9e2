package com.zenith.bbykz.model.dto.register.train;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * <p>
 * 登记信息-培训管理-反馈意见 DTO
 * </p>
 *
 * <AUTHOR>
 * @date 2024-03-05 16:17:55
 */
@Data
@ApiModel("登记信息-培训管理-反馈意见 请求实体-RegisterTrainCourseFeedbackDTO")
public class RegisterTrainCourseFeedbackDTO implements Serializable {
    private static final long serialVersionUID = 5176941942851484738L;

    /**
     * 主键id
     */
    @ApiModelProperty(value = "主键id")
    private String id;
    /**
     * 课程主键
     */
    @ApiModelProperty(value = "课程主键")
    private String courseId;
    private String courseName;
    /**
     * 描述
     */
    @ApiModelProperty(value = "描述")
    private String describe;
    /**
     * 创建时间
     */
    @ApiModelProperty(value = "创建时间")
    private Date createTime;
    /**
     * 创建人
     */
    @ApiModelProperty(value = "创建人")
    private String createUser;
    /**
     * 修改时间
     */
    @ApiModelProperty(value = "修改时间")
    private Date updateTime;
    /**
     * 修改人
     */
    @ApiModelProperty(value = "修改人")
    private String updateUser;
    /**
     * 是否删除
     */
    @ApiModelProperty(value = "是否删除")
    private Integer isDelete;

}

