package com.zenith.bbykz.model.dto;

import com.efficient.common.validate.Common1Group;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * 事业单位法人登记 列表查询
 *
 * <AUTHOR>
 * @date 2023/9/20
 */
@Data
public class LegalPersonRegistrationListDTO {

    @NotNull(message = "pageNum 不能为空")
    @ApiModelProperty(value = "页码")
    private Integer pageNum;

    @NotNull(message = "pageSize 不能为空")
    @ApiModelProperty(value = "页大小")
    private Integer pageSize;
    @ApiModelProperty(value = "层级码")
    @NotBlank(groups = Common1Group.class,message = "unitLevelCode 不能为空")
    private String unitLevelCode;
    @ApiModelProperty(value = "统一社会信用代码")
    private String unifiedSocialCreditCode;
    @ApiModelProperty(value = "关键字")
    private String keyword;

}
