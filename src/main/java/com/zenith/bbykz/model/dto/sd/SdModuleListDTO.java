package com.zenith.bbykz.model.dto.sd;

import com.efficient.common.validate.QueryGroup;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * <p>
 * 三定-三定类型 列表查询DTO
 * </p>
 *
 * <AUTHOR>
 * @date 2024-04-11 16:17:20
 */
@Data
@ApiModel("三定-三定类型 列表查询-SdModuleListDTO")
public class SdModuleListDTO implements Serializable {
    private static final long serialVersionUID = 810535174428706943L;
    @NotNull(message = "pageNum 不能为空")
    private Integer pageNum;
    @NotNull(message = "pageSize 不能为空")
    private Integer pageSize;
    // @ApiModelProperty(value = "单位ID")
    // @NotBlank(groups = QueryGroup.class, message = "unitId 不能为空")
    // private String unitId;
    @ApiModelProperty(value = "是否启用，1-是，0-否")
    private Integer isEnable;
}
