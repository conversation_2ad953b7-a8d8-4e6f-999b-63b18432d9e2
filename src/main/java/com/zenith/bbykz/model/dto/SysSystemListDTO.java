package com.zenith.bbykz.model.dto;

import io.swagger.annotations.ApiModel;
import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * <p>
 * sys_system 列表查询DTO
 * </p>
 *
 * <AUTHOR>
 * @date 2024-02-29 11:43:55
 */
@Data
@ApiModel("sys_system 列表查询-SysSystemListDTO")
public class SysSystemListDTO {
    private static final long serialVersionUID = 8537010881129658083L;
    @NotNull(message = "pageNum 不能为空")
    private Integer pageNum;
    @NotNull(message = "pageSize 不能为空")
    private Integer pageSize;
}
