package com.zenith.bbykz.model.dto.bz.rybd;

import com.efficient.common.validate.Common1Group;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2024/5/7 10:24
 */
@Data
@ApiModel("BzUnitBdExportDTO")
public class BzUnitBdExportDTO {
    @ApiModelProperty("导出ID集合")
    @NotEmpty(groups = Common1Group.class,message = "idList 不能为空")
    private List<String> idList;
    @ApiModelProperty("导出页面，1-上编管理，2-人员调整功能，3-下编管理")
    @NotEmpty(groups = Common1Group.class,message = "pageType 不能为空")
    private String pageType;
}
