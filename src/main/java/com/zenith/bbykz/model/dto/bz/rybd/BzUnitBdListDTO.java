package com.zenith.bbykz.model.dto.bz.rybd;

import com.efficient.common.validate.Common1Group;
import com.efficient.common.validate.Common2Group;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import java.io.Serializable;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
* <p>
* 核编管理-动态信息维护 列表查询DTO
* </p>
*
* <AUTHOR>
* @date 2024-04-22 11:21:43
*/
@Data
@ApiModel("核编管理-动态信息维护 列表查询-BzUnitBdListDTO")
public class BzUnitBdListDTO implements Serializable {
    private static final long serialVersionUID = 3827607826123438850L;
    @NotNull(message = "pageNum 不能为空")
    private Integer pageNum;
    @NotNull(message = "pageSize 不能为空")
    private Integer pageSize;
    @ApiModelProperty(value = "关键字搜索")
    private String keyword;
    @ApiModelProperty(value = "页面类型，1-上编，2-人员调整，3-下编")
    @NotBlank(groups = Common2Group.class,message = "pageType 不能为空")
    private String pageType;
    @NotBlank(groups = Common1Group.class,message = "unitLevelCode 不能为空")
    private String unitLevelCode;
}
