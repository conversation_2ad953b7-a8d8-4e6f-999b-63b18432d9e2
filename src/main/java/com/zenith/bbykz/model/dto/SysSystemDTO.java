package com.zenith.bbykz.model.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

/**
 * <p>
 * sys_system DTO
 * </p>
 *
 * <AUTHOR>
 * @date 2024-02-29 11:43:55
 */
@Data
@ApiModel("sys_system 请求实体-SysSystemDTO")
public class SysSystemDTO {
    private static final long serialVersionUID = 5990086598115478963L;

    /**
     *
     */
    @ApiModelProperty(value = "")
    private String id;
    /**
     *
     */
    @ApiModelProperty(value = "")
    private String name;
    /**
     * 排序
     */
    @ApiModelProperty(value = "排序")
    private Integer sort;
    /**
     * 备注
     */
    @ApiModelProperty(value = "备注")
    private String remark;

}

