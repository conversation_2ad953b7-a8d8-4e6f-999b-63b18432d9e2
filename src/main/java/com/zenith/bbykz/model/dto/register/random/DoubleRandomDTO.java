package com.zenith.bbykz.model.dto.register.random;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
* <p>
* 登记信息-内部事务-双随机检查 DTO
* </p>
*
* <AUTHOR>
* @date 2024-05-06 15:48:28
*/
@Data
@ApiModel("登记信息-内部事务-双随机检查 请求实体-DoubleRandomDTO")
public class DoubleRandomDTO implements Serializable {
    private static final long serialVersionUID = 7478103725697905764L;

    /**
    *主键id
    */
    @ApiModelProperty(value = "主键id")
    private String id;
    /**
    *录入单位ID
    */
    @ApiModelProperty(value = "录入单位ID,机构树选中节点")
    private String recordUnitId;
    /**
    *录入单位层级码
    */
    @ApiModelProperty(value = "录入单位层级码")
    private String recordUnitLevelCode;
    /**
    *录入单位名称
    */
    @ApiModelProperty(value = "录入单位名称")
    private String recordUnitName;
    /**
    *名称
    */
    @ApiModelProperty(value = "名称")
    private String unitName;
    /**
    *统一社会信用代码
    */
    @ApiModelProperty(value = "统一社会信用代码")
    private String creditCode;
    /**
    *证书编号
    */
    @ApiModelProperty(value = "证书编号")
    private String certificateNum;
    /**
    *法定代表人
    */
    @ApiModelProperty(value = "法定代表人")
    private String legalRepresentative;
    /**
    *经费来源
    */
    @ApiModelProperty(value = "经费来源")
    private String sourceOfFunds;
    /**
    *开办资金
    */
    @ApiModelProperty(value = "开办资金")
    private String startUpCapital;
    /**
    *举办单位
    */
    @ApiModelProperty(value = "举办单位")
    private String holdOrganizer;
    /**
    *宗旨和业务范围
    */
    @ApiModelProperty(value = "宗旨和业务范围")
    private String businessScope;
    /**
    *住所
    */
    @ApiModelProperty(value = "住所")
    private String domicile;
    /**
    *开始时间
    */
    @ApiModelProperty(value = "开始时间")
    private Date startTime;
    /**
    *结束时间
    */
    @ApiModelProperty(value = "结束时间")
    private Date endTime;
    /**
    *
    */
    @ApiModelProperty(value = "")
    private String orgId;
    /**
    *
    */
    @ApiModelProperty(value = "")
    private String orgLevelCode;
    /**
    *抽取时间
    */
    @ApiModelProperty(value = "抽取时间")
    private Date checkDate;
    /**
    *抽取方式
    */
    @ApiModelProperty(value = "抽取方式")
    private String checkType;
    /**
    *检查结果
    */
    @ApiModelProperty(value = "检查结果")
    private String checkResult;
    /**
    *检查单位
    */
    @ApiModelProperty(value = "检查单位")
    private String checkUnit;
    /**
    *联系人
    */
    @ApiModelProperty(value = "联系人")
    private String checkUser;
    /**
    *联系电话
    */
    @ApiModelProperty(value = "联系电话")
    private String checkUserPhone;
    /**
    *检查记录
    */
    @ApiModelProperty(value = "检查记录")
    private String checkRecord;
    /**
    *创建时间
    */
    @ApiModelProperty(value = "创建时间")
    private Date createTime;
    /**
    *创建人
    */
    @ApiModelProperty(value = "创建人")
    private String createUser;
    /**
    *修改时间
    */
    @ApiModelProperty(value = "修改时间")
    private Date updateTime;
    /**
    *修改人
    */
    @ApiModelProperty(value = "修改人")
    private String updateUser;
    /**
    *是否删除
    */
    @ApiModelProperty(value = "是否删除")
    private Integer isDelete;

}

