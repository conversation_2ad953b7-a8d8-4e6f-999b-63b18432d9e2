package com.zenith.bbykz.model.dto.sd;

import com.efficient.common.validate.Common1Group;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;
import java.util.Date;

/**
 * <p>
 * 三定-业务事项一件事关联表 DTO
 * </p>
 *
 * <AUTHOR>
 * @date 2024-04-11 16:28:25
 */
@Data
@ApiModel("三定-业务事项一件事关联表 请求实体-SdBusinessItemOneDTO")
public class SdBusinessItemOneDTO implements Serializable {
    private static final long serialVersionUID = 29175338178986411L;

    /**
     * 主键id
     */
    @ApiModelProperty(value = "主键id")
    private String id;
    /**
     * 业务事项主键
     */
    @ApiModelProperty(value = "业务事项主键")
    @NotBlank(groups = Common1Group.class, message = "itemId 不能为空")
    private String itemId;
    private String itemName;
    private String itemRemark;

    /**
     * 一件事主键
     */
    @ApiModelProperty(value = "一件事主键")
    @NotBlank(groups = Common1Group.class, message = "oneId 不能为空")
    private String oneId;
    /**
     *
     */
    @ApiModelProperty(value = "")
    private Date createTime;
    /**
     *
     */
    @ApiModelProperty(value = "")
    private String createUser;
    /**
     *
     */
    @ApiModelProperty(value = "")
    private Date updateTime;
    /**
     *
     */
    @ApiModelProperty(value = "")
    private String updateUser;
    /**
     *
     */
    @ApiModelProperty(value = "")
    private Integer isDelete;

}

