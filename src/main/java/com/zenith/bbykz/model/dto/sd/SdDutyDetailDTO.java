package com.zenith.bbykz.model.dto.sd;

import com.efficient.common.validate.AddGroup;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;
import java.util.Date;

/**
 * <p>
 * 三定-细化职责 DTO
 * </p>
 *
 * <AUTHOR>
 * @date 2024-04-11 16:28:25
 */
@Data
@ApiModel("三定-细化职责 请求实体-SdDutyDetailDTO")
public class SdDutyDetailDTO implements Serializable {
    private static final long serialVersionUID = 7048044737408680496L;

    /**
     * 主键id
     */
    @ApiModelProperty(value = "主键id")
    private String id;
    /**
     * 三定类型主键
     */
    @ApiModelProperty(value = "三定类型主键")
    @NotBlank(groups = AddGroup.class, message = "moduleId 不能为空！")
    private String moduleId;
    /**
     * 三定明细主键
     */
    @ApiModelProperty(value = "三定明细主键")
    @NotBlank(groups = AddGroup.class, message = "moduleDetailId 不能为空！")
    private String moduleDetailId;
    /**
     * 承担处室id
     */
    @ApiModelProperty(value = "承担处室id")
    @NotBlank(groups = AddGroup.class, message = "takeUnitId 不能为空！")
    private String takeUnitId;
    /**
     * 承担处室层级码
     */
    @ApiModelProperty(value = "承担处室层级码")
    private String takeUnitLevelCode;
    /**
     * 承担处室名称
     */
    @ApiModelProperty(value = "承担处室名称")
    private String takeUnitName;
    /**
     * 名称
     */
    @ApiModelProperty(value = "名称")
    private String title;
    /**
     * 描述
     */
    @ApiModelProperty(value = "描述")
    private String describe;
    /**
     * 备注
     */
    @ApiModelProperty(value = "备注")
    private String remark;
    /**
     * 排序号
     */
    @ApiModelProperty(value = "排序号")
    private Integer sort;
    /**
     * 文号
     */
    @ApiModelProperty(value = "文号")
    private String recordNum;
}

