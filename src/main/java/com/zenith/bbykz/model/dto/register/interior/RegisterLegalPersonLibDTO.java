package com.zenith.bbykz.model.dto.register.interior;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * <p>
 * 登记信息-内部事务-法人库 DTO
 * </p>
 *
 * <AUTHOR>
 * @date 2024-03-12 14:55:18
 */
@Data
@ApiModel("登记信息-内部事务-法人库 请求实体-RegisterLegalPersonLibDTO")
public class RegisterLegalPersonLibDTO implements Serializable {
    private static final long serialVersionUID = 4230020648928429650L;

    /**
     * 主键id
     */
    @ApiModelProperty(value = "主键id")
    private String id;
    /**
     * 名称
     */
    @ApiModelProperty(value = "名称")
    private String name;
    /**
     * 身份证
     */
    @ApiModelProperty(value = "身份证")
    private String idCard;
    /**
     * 单位名称
     */
    @ApiModelProperty(value = "单位名称")
    private String unitName;
    /**
     * 注册时间
     */
    @ApiModelProperty(value = "注册时间")
    private Date registerTime;
    /**
     * 有效期开始时间
     */
    @ApiModelProperty(value = "有效期开始时间")
    private Date startTime;
    /**
     * 有效期结束时间
     */
    @ApiModelProperty(value = "有效期结束时间")
    private Date endTime;
    @ApiModelProperty(value = "备注")
    private String remark;
    /**
     * 版本号
     */
    @ApiModelProperty(value = "版本号")
    private Integer version;
    /**
     * 创建时间
     */
    @ApiModelProperty(value = "创建时间")
    private Date createTime;
    /**
     * 创建人
     */
    @ApiModelProperty(value = "创建人")
    private String createUser;
    /**
     * 修改时间
     */
    @ApiModelProperty(value = "修改时间")
    private Date updateTime;
    /**
     * 修改人
     */
    @ApiModelProperty(value = "修改人")
    private String updateUser;
    /**
     * 是否删除
     */
    @ApiModelProperty(value = "是否删除")
    private Integer isDelete;

}

