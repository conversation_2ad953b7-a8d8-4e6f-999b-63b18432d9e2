package com.zenith.bbykz.model.dto.sd;

import com.efficient.common.validate.Common1Group;
import com.efficient.common.validate.QueryGroup;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * <p>
 * 三定-核心业务 列表查询DTO
 * </p>
 *
 * <AUTHOR>
 * @date 2024-04-11 16:17:20
 */
@Data
@ApiModel("三定-核心业务 列表查询-SdCoreBusinessListDTO")
public class SdCoreBusinessListDTO implements Serializable {
    private static final long serialVersionUID = 6169274276928245490L;
    @NotNull(message = "pageNum 不能为空")
    private Integer pageNum;
    @NotNull(message = "pageSize 不能为空")
    private Integer pageSize;
    @ApiModelProperty(value = "三定类型主键")
    @NotBlank(groups = QueryGroup.class, message = "dutyDetailId 不能为空")
    private String dutyDetailId;

    @ApiModelProperty(value = "层级码")
    @NotBlank(groups = Common1Group.class, message = "unitLevelCode 不能为空")
    private String unitLevelCode;
    @ApiModelProperty(value = "核心业务名称")
    private String coreBusinessName;
    @ApiModelProperty(value = "细化职责名称")
    private String dutyDetailName;
    // @ApiModelProperty(value = "业务事项名称")
    // private String businessItemName;
}
