package com.zenith.bbykz.model.dto.register.dynamic;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * <p>
 * 登记信息-变动流程记录 DTO
 * </p>
 *
 * <AUTHOR>
 * @date 2024-04-18 10:01:33
 */
@Data
@ApiModel("登记信息-变动流程记录 请求实体-UnitBdFlowDTO")
public class UnitBdFlowDTO implements Serializable {
    private static final long serialVersionUID = 8614141606514675364L;
    /**
     * 业务主键
     */
    @ApiModelProperty(value = "业务主键")
    private String id;
    @ApiModelProperty(value = "消息主键")
    private String notifyId;
    @ApiModelProperty(value = "反馈状态，4-已处理，5-已完成")
    private String state;

    @ApiModelProperty(value = "反馈内容")
    private String backContent;
    @ApiModelProperty(value = "反馈时间")
    private Date backTime;
    @ApiModelProperty(value = "备注")
    private String remark;
}

