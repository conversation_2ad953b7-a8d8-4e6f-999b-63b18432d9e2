package com.zenith.bbykz.model.dto.efficient;

import com.efficient.common.validate.QueryGroup;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.Date;

/**
 * <p>
 * 系统消息通知 列表查询DTO
 * </p>
 *
 * <AUTHOR>
 * @date 2024-03-18 15:07:00
 */
@Data
@ApiModel("系统消息通知 列表查询-SysNotifyListDTO")
public class SysNotifyListDTO implements Serializable {
    private static final long serialVersionUID = 3947914170318434334L;
    @NotNull(message = "pageNum 不能为空")
    private Integer pageNum;
    @NotNull(message = "pageSize 不能为空")
    private Integer pageSize;
    @ApiModelProperty(value = "单位名称")
    private String unitName;
    @ApiModelProperty(value = "页面类型，1-发送的消息，2-接收的消息")
    @NotNull(groups = QueryGroup.class, message = "pageType 不能为空")
    private Integer pageType;
    @ApiModelProperty(value = "已读未读，1-已读，2-未读")
    private Integer isRead;
    @ApiModelProperty(value = "开始时间")
    private Date startDate;
    @ApiModelProperty(value = "结束时间")
    private Date endDate;
}
