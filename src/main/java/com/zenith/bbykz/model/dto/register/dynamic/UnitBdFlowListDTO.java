package com.zenith.bbykz.model.dto.register.dynamic;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import java.io.Serializable;

import javax.validation.constraints.NotNull;

/**
* <p>
* 登记信息-变动流程记录 列表查询DTO
* </p>
*
* <AUTHOR>
* @date 2024-04-18 10:01:34
*/
@Data
@ApiModel("登记信息-变动流程记录 列表查询-UnitBdFlowListDTO")
public class UnitBdFlowListDTO implements Serializable {
    private static final long serialVersionUID = 2436134032785650632L;
    @NotNull(message = "pageNum 不能为空")
    private Integer pageNum;
    @NotNull(message = "pageSize 不能为空")
    private Integer pageSize;
    @ApiModelProperty(value = "关键字搜索")
    private String keyword;
}
