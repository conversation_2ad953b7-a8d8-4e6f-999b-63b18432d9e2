package com.zenith.bbykz.model.dto.register.train;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * <p>
 * 登记信息-培训管理-反馈意见 列表查询DTO
 * </p>
 *
 * <AUTHOR>
 * @date 2024-03-05 16:17:55
 */
@Data
@ApiModel("登记信息-培训管理-反馈意见 列表查询-RegisterTrainCourseFeedbackListDTO")
public class RegisterTrainCourseFeedbackListDTO implements Serializable {
    private static final long serialVersionUID = 6926396584616805137L;
    @NotNull(message = "pageNum 不能为空")
    private Integer pageNum;
    @NotNull(message = "pageSize 不能为空")
    private Integer pageSize;
    @ApiModelProperty(value = "关键字搜索")
    private String keyword;
    @ApiModelProperty(value = "是否只显示自己的反馈意见，1-是，0-否")
    private Integer isOwn;
    @ApiModelProperty(value = "课程名称")
    private String courseName;
}
