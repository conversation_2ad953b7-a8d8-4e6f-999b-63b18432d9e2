package com.zenith.bbykz.model.dto.register.interior;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

import javax.validation.constraints.NotNull;

/**
 * <p>
 * 登记信息-内部事务-法人库日志 列表查询DTO
 * </p>
 *
 * <AUTHOR>
 * @date 2024-03-12 17:24:37
 */
@Data
@ApiModel("登记信息-内部事务-法人库日志 列表查询-RegisterLegalPersonLogListDTO")
public class RegisterLegalPersonLogListDTO implements Serializable {
    private static final long serialVersionUID = 872044159697699636L;
    @NotNull(message = "pageNum 不能为空")
    private Integer pageNum;
    @NotNull(message = "pageSize 不能为空")
    private Integer pageSize;
    @ApiModelProperty(value = "关键字搜索")
    private String keyword;
    @ApiModelProperty(value = "名称")
    private String name;
    @ApiModelProperty(value = "身份证")
    private String idCard;
}
