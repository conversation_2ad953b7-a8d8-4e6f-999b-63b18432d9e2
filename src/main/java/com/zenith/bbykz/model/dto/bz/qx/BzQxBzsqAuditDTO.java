package com.zenith.bbykz.model.dto.bz.qx;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * <p>
 * 核编管理-用编管理-区县用编申请计划 DTO
 * </p>
 *
 * <AUTHOR>
 * @date 2024-05-06 17:05:40
 */
@Data
@ApiModel("核编管理-用编管理-区县用编申请计划 请求实体-BzQxBzsqAuditDTO")
public class BzQxBzsqAuditDTO implements Serializable {
    private static final long serialVersionUID = 3415910479114945670L;

    /**
     * 主键id
     */
    @ApiModelProperty(value = "主键id")
    private String id;
    @ApiModelProperty(value = "审核状态,1-未提交,2-未核准,3-撤回,5-核准通过,6-核准未通过")
    private String status;
    @ApiModelProperty(value = "审核理由")
    private String checkReason;
}

