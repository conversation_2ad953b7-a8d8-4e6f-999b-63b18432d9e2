package com.zenith.bbykz.model.dto.register.reconnaissance;

import com.baomidou.mybatisplus.annotation.TableField;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * <p>
 * 登记信息-内部事务-实地勘察 DTO
 * </p>
 *
 * <AUTHOR>
 * @date 2024-03-28 11:28:37
 */
@Data
@ApiModel("登记信息-内部事务-实地勘察 请求实体-RegisterReconnaissanceDTO")
public class RegisterReconnaissanceDTO implements Serializable {
    private static final long serialVersionUID = 387073379827628132L;

    /**
     * 主键id
     */
    @ApiModelProperty(value = "主键id")
    private String id;
    /**
     * 名称
     */
    @ApiModelProperty(value = "名称")
    private String unitName;
    /**
     * 统一社会信用代码
     */
    @ApiModelProperty(value = "统一社会信用代码")
    private String creditCode;
    /**
     * 法定代表人
     */
    @ApiModelProperty(value = "法定代表人")
    private String legalRepresentative;
    /**
     * 经费来源
     */
    @ApiModelProperty(value = "经费来源")
    private String sourceOfFunds;
    /**
     * 开办资金
     */
    @ApiModelProperty(value = "开办资金")
    private String startUpCapital;
    /**
     * 举办单位
     */
    @ApiModelProperty(value = "举办单位")
    private String holdOrganizer;
    /**
     * 宗旨和业务范围
     */
    @ApiModelProperty(value = "宗旨和业务范围")
    private String businessScope;
    /**
     * 开始时间
     */
    @ApiModelProperty(value = "开始时间")
    private Date startTime;
    /**
     * 结束时间
     */
    @ApiModelProperty(value = "结束时间")
    private Date endTime;
    /**
     * 勘察时间
     */
    @ApiModelProperty(value = "勘察时间")
    private Date checkDate;
    /**
     * 勘察原因
     */
    @ApiModelProperty(value = "勘察原因")
    private String checkReason;
    /**
     * 勘察结果 JGSY_SDKC_STATUS
     */
    @ApiModelProperty(value = "勘察结果 JGSY_SDKC_STATUS")
    private String checkResult;
    /**
     * 勘察单位
     */
    @ApiModelProperty(value = "勘察单位")
    private String checkUnit;
    /**
     * 勘察人
     */
    @ApiModelProperty(value = "勘察人")
    private String checkUser;
    /**
     * 勘察人电话
     */
    @ApiModelProperty(value = "勘察人电话")
    private String checkUserPhone;
    /**
     * 勘察记录
     */
    @ApiModelProperty(value = "勘察记录")
    private String checkRecord;
    /**
     * 创建时间
     */
    @ApiModelProperty(value = "创建时间")
    private Date createTime;
    /**
     * 创建人
     */
    @ApiModelProperty(value = "创建人")
    private String createUser;
    /**
     * 修改时间
     */
    @ApiModelProperty(value = "修改时间")
    private Date updateTime;
    /**
     * 修改人
     */
    @ApiModelProperty(value = "修改人")
    private String updateUser;
    /**
     * 是否删除
     */
    @ApiModelProperty(value = "是否删除")
    private Integer isDelete;
    @ApiModelProperty(value = "证书编号")
    private String certificateNum;
    @ApiModelProperty(value = "住所")
    private String domicile;
    @ApiModelProperty(value = "机构id")
    private String orgId;
    @ApiModelProperty(value = "机构层级码")
    private String orgLevelCode;
}

