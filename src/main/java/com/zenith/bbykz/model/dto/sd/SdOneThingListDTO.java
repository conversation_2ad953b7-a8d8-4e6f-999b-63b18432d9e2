package com.zenith.bbykz.model.dto.sd;

import com.efficient.common.validate.QueryGroup;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * <p>
 * 三定-一件事 列表查询DTO
 * </p>
 *
 * <AUTHOR>
 * @date 2024-04-11 16:17:20
 */
@Data
@ApiModel("三定-一件事 列表查询-SdOneThingListDTO")
public class SdOneThingListDTO implements Serializable {
    private static final long serialVersionUID = 6061455299864211911L;
    @NotNull(message = "pageNum 不能为空")
    private Integer pageNum;
    @NotNull(message = "pageSize 不能为空")
    private Integer pageSize;
    // @ApiModelProperty(value = "关键字搜索")
    // private String keyword;
    @ApiModelProperty(value = "一件事类型")
    private String oneType;
    @ApiModelProperty(value = "一件事名称搜索")
    private String oneName;
    @ApiModelProperty(value = "业务事项名称搜索")
    private String itemName;
    @ApiModelProperty(value = "业务事项名称搜索")
    @NotBlank(groups = QueryGroup.class, message = "unitLevelCode 不能为空")
    private String unitLevelCode;
}
