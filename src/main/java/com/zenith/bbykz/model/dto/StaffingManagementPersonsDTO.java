package com.zenith.bbykz.model.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * <p>
 * 用编管理人员信息 DTO
 * </p>
 *
 * <AUTHOR>
 * @date 2023-09-13
 */
@Data
@ApiModel("用编管理附件人员信息 请求实体-StaffingManagementPersonsDTO")
public class StaffingManagementPersonsDTO {
    private static final long serialVersionUID = 1040589868084933247L;

    /**
     * 主键id
     */
    @ApiModelProperty(value = "主键id")
    private String id;
    /**
     * 用编管理id
     */
    @ApiModelProperty(value = "用编管理id")
    private String staffingManagementId;
    /**
     * 姓名
     */
    @ApiModelProperty(value = "姓名")
    @NotBlank(message = "姓名不能为空")
    private String name;
    /**
     * 性别
     */
    @ApiModelProperty(value = "性别")
    @NotBlank(message = "性别不能为空")
    private String sex;
    /**
     * 年龄
     */
    @ApiModelProperty(value = "年龄")
    @NotNull(message = "年龄不能为空")
    private Integer age;
    /**
     * 学历学位
     */
    @ApiModelProperty(value = "学历学位")
    @NotBlank(message = "学历学位不能为空")
    private String education;
    /**
     * 现工作单位及职务职级
     */
    @ApiModelProperty(value = "现工作单位及职务职级")
    @NotBlank(message = "现工作单位及职务职级不能为空")
    private String workUnitDuties;
    /**
     * 拟安排工作岗位
     */
    @ApiModelProperty(value = "拟安排工作岗位")
    @NotBlank(message = "拟安排工作岗位不能为空")
    private String proposedJob;
    /**
     * 备注
     */
    @ApiModelProperty(value = "备注")
    @NotBlank(message = "标题不能为空")
    private String comments;

}

