package com.zenith.bbykz.model.dto.bz.qx;

import com.baomidou.mybatisplus.annotation.TableField;
import com.efficient.file.model.vo.FileVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * <p>
 * 核编管理-用编管理-区县用编申请计划 DTO
 * </p>
 *
 * <AUTHOR>
 * @date 2024-05-06 17:05:40
 */
@Data
@ApiModel("核编管理-用编管理-区县用编申请计划 请求实体-BzQxBzsqDTO")
public class BzQxBzsqDTO implements Serializable {
    private static final long serialVersionUID = 3415910479114945670L;

    /**
     * 主键id
     */
    @ApiModelProperty(value = "主键id")
    private String id;
    /**
     * 所属区县
     */
    // @ApiModelProperty(value = "所属区县")
    // private String geocode;
    /**
     * 录入单位ID
     */
    // @ApiModelProperty(value = "录入单位ID，机构树选中节点")
    // private String recordUnitId;
    // /**
    //  * 录入单位层级码
    //  */
    // @ApiModelProperty(value = "录入单位层级码")
    // private String recordUnitLevelCode;
    // /**
    //  * 录入单位名称
    //  */
    // @ApiModelProperty(value = "录入单位名称")
    // private String recordUnitName;
    /**
     * 标题
     */
    @ApiModelProperty(value = "标题")
    private String title;
    // /**
    //  * 申请单位ID
    //  */
    // @ApiModelProperty(value = "申请单位ID")
    // private String applyUnitId;
    /**
     * 申请单位名称
     */
    @ApiModelProperty(value = "申请单位名称")
    private String applyUnitName;
    @ApiModelProperty(value = "审核状态,1-未提交,2-未核准,3-撤回,5-核准通过,6-核准未通过")
    private String status;
    @ApiModelProperty(value = "核编单号")
    private String approvalNum;
    // /**
    //  * 申请单位层级码
    //  */
    // @ApiModelProperty(value = "申请单位层级码")
    // private String applyUnitLevelCode;
    /**
     * 申请时间
     */
    @ApiModelProperty(value = "申请时间")
    private Date applyTime;
    /**
     * 联系人姓名
     */
    @ApiModelProperty(value = "联系人姓名")
    private String contactName;
    /**
     * 联系人电话
     */
    @ApiModelProperty(value = "联系人电话")
    private String contactPhone;

    @ApiModelProperty(value = "申请明细")
    private List<BzQxBzsqDetailDTO> detailList;

    @ApiModelProperty(value = "文件列表")
    private List<FileVO> fileList;
}

