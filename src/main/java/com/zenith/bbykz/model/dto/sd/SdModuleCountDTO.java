package com.zenith.bbykz.model.dto.sd;

import com.efficient.common.validate.QueryGroup;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;

/**
 * <p>
 * 三定-三定类型 列表查询DTO
 * </p>
 *
 * <AUTHOR>
 * @date 2024-04-11 16:17:20
 */
@Data
@ApiModel("三定-职能职责分析-SdModuleCountDTO")
public class SdModuleCountDTO implements Serializable {
    private static final long serialVersionUID = 4835540858380260841L;
    @ApiModelProperty(value = "关键字查询")
    private String keyword;
    @ApiModelProperty(value = "单位层级码")
    @NotBlank(groups = QueryGroup.class, message = "unitLevelCode 不能为空")
    private String unitLevelCode;
}
