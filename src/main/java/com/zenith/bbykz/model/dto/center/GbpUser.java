package com.zenith.bbykz.model.dto.center;

import com.baomidou.mybatisplus.annotation.*;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
* <p>
* 用户表 实体类
* </p>
*
* <AUTHOR> generator
* @date 2022-05-06 17:33:04
*/
@Data
@ApiModel("用户表")
public class GbpUser implements Serializable {

    private static final long serialVersionUID = 4058944786392828335L;

    /**
    *主键
    */
    @ApiModelProperty(value = "主键")
    private Long id;
    /**
    *用户名
    */
    @ApiModelProperty(value = "用户名")
    private String name;
    /**
    *密码
    */
    @ApiModelProperty(value = "密码")
    private String password;
    /**
    *单位外键
    */
    @ApiModelProperty(value = "单位外键")
    private Long unitid;
    /**
    *是否有效
    */
    @ApiModelProperty(value = "是否有效")
    private Integer enabled;
    /**
    *真实姓名
    */
    @ApiModelProperty(value = "真实姓名")
    private String realname;
    /**
    *备注
    */
    @ApiModelProperty(value = "备注")
    private String remark;
    /**
    *电话
    */
    @ApiModelProperty(value = "电话")
    private String tel;
    /**
    *传真
    */
    @ApiModelProperty(value = "传真")
    private String fax;
    /**
    *移动电话
    */
    @ApiModelProperty(value = "移动电话")
    private String mobtel;
    /**
    *其它电话
    */
    @ApiModelProperty(value = "其它电话")
    private String othertel;
    /**
    *电子邮箱
    */
    @ApiModelProperty(value = "电子邮箱")
    private String email;
    /**
    *证件号码
    */
    @ApiModelProperty(value = "证件号码")
    private String certno;
    /**
    *职务
    */
    @ApiModelProperty(value = "职务")
    private String duty;
    /**
    *状态
    */
    @ApiModelProperty(value = "状态")
    private String status;
    /**
    *排序编号
    */
    @ApiModelProperty(value = "排序编号")
    private Long sortno;
    /**
    *区划代码
    */
    @ApiModelProperty(value = "区划代码")
    private String depCode;
    /**
    *是否编办用户
    */
    @ApiModelProperty(value = "是否编办用户")
    private String isBbuser;
    /**
    *所属事业
    */
    @ApiModelProperty(value = "所属事业")
    private String ssdwid;
    /**
    *账户设置时间
    */
    @ApiModelProperty(value = "账户设置时间")
    private Date pwdSetdate;
    /**
    *账号解锁时间
    */
    @ApiModelProperty(value = "账号解锁时间")
    private Date unlocktime;
    /**
    *管理员是否重置过密码，默认0，表示没有重置过；1表示重置过
    */
    @ApiModelProperty(value = "管理员是否重置过密码，默认0，表示没有重置过；1表示重置过")
    private String ifModpwd;
    /**
    *用户密级，1-公开，2-内部，7-密码，8-机密，9-绝密
    */
    @ApiModelProperty(value = "用户密级，1-公开，2-内部，7-密码，8-机密，9-绝密")
    private Integer mj;

    /**
     *审核状态，1--审核未通过，2--审核通过，3--审核未通过
     */
    @ApiModelProperty(value = "审核状态，1--审核未通过，2--审核通过，3--审核未通过")
    private String checkStatus;
}
