package com.zenith.bbykz.model.dto.sd;

import com.efficient.common.validate.AddGroup;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;
import java.util.Date;

/**
 * <p>
 * 三定-三定类型 DTO
 * </p>
 *
 * <AUTHOR>
 * @date 2024-04-11 16:17:20
 */
@Data
@ApiModel("三定-三定类型 请求实体-SdModuleDTO")
public class SdModuleDTO implements Serializable {
    private static final long serialVersionUID = 5087063067928839172L;

    /**
     * 主键id
     */
    @ApiModelProperty(value = "主键id")
    private String id;
    /**
     * 单位ID
     */
    @ApiModelProperty(value = "单位ID")
    // @NotBlank(groups = AddGroup.class, message = "unitId 不能为空！")
    private String unitId;
    /**
     * 单位层级码
     */
    @ApiModelProperty(value = "单位层级码")
    private String unitLevelCode;
    /**
     * 单位名称
     */
    @ApiModelProperty(value = "单位名称")
    private String unitName;
    /**
     * 区划code
     */
    @ApiModelProperty(value = "区划code")
    private String geocode;
    /**
     * 名称
     */
    @ApiModelProperty(value = "名称")
    private String title;
    /**
     * 描述
     */
    @ApiModelProperty(value = "描述")
    private String describe;
    /**
     * 备注
     */
    @ApiModelProperty(value = "备注")
    private String remark;
    /**
     * 排序号
     */
    @ApiModelProperty(value = "排序号")
    private Integer sort;
    @ApiModelProperty(value = "是否启用，1-是，0-否")
    private Integer isEnable;
}

