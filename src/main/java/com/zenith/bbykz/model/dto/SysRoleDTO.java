package com.zenith.bbykz.model.dto;

import com.baomidou.mybatisplus.annotation.TableField;
import com.zenith.bbykz.model.entity.SysRoleMenu;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * <p>
 * sys_role DTO
 * </p>
 *
 * <AUTHOR>
 * @date 2023-09-14 17:14:26
 */
@Data
@ApiModel("sys_role 请求实体-SysRoleDTO")
public class SysRoleDTO {
    private static final long serialVersionUID = 4341702717539006924L;

    /**
     *
     */
    @ApiModelProperty(value = "")
    private String id;
    /**
     *
     */
    @ApiModelProperty(value = "")
    private String name;
    /**
     *
     */
    @ApiModelProperty(value = "")
    private Integer isDefault;
    /**
     *
     */
    @ApiModelProperty(value = "")
    private String systemCode;

    @ApiModelProperty(value = "父级角色id")
    private String parentId;
    private List<SysMenuDTO> menus;
    @ApiModelProperty(value = "创建人")
    private String createUser;
    @ApiModelProperty(value = "创建时间")
    private Date createTime;
}

