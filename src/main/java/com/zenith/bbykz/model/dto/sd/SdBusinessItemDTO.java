package com.zenith.bbykz.model.dto.sd;

import com.efficient.common.validate.AddGroup;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;
import java.util.List;

/**
 * <p>
 * 三定-业务事项 DTO
 * </p>
 *
 * <AUTHOR>
 * @date 2024-04-11 16:28:25
 */
@Data
@ApiModel("三定-业务事项 请求实体-SdBusinessItemDTO")
public class SdBusinessItemDTO implements Serializable {
    private static final long serialVersionUID = 37967051253855556L;

    /**
     * 主键id
     */
    @ApiModelProperty(value = "主键id")
    private String id;
    /**
     * 三定类型主键
     */
    @ApiModelProperty(value = "三定类型主键")
    @NotBlank(groups = AddGroup.class, message = "moduleId 不能为空！")
    private String moduleId;
    /**
     * 三定明细主键
     */
    @ApiModelProperty(value = "三定明细主键")
    @NotBlank(groups = AddGroup.class, message = "moduleDetailId 不能为空！")
    private String moduleDetailId;
    /**
     * 细化职责主键
     */
    @ApiModelProperty(value = "细化职责主键")
    @NotBlank(groups = AddGroup.class, message = "dutyDetailId 不能为空！")
    private String dutyDetailId;
    /**
     * 核心业务主键
     */
    @ApiModelProperty(value = "核心业务主键")
    @NotBlank(groups = AddGroup.class, message = "coreBusinessId 不能为空！")
    private String coreBusinessId;
    /**
     * 名称
     */
    @ApiModelProperty(value = "名称")
    private String title;
    /**
     * 描述
     */
    @ApiModelProperty(value = "描述")
    private String describe;
    /**
     * 备注
     */
    @ApiModelProperty(value = "备注")
    private String remark;
    /**
     * 排序号
     */
    @ApiModelProperty(value = "排序号")
    private Integer sort;
    @ApiModelProperty(value = "一件事ID")
    private List<SdOneThingDTO> oneThingList;

}

