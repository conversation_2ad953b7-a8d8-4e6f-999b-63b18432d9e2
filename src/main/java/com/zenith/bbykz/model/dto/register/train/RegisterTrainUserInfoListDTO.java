package com.zenith.bbykz.model.dto.register.train;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * <p>
 * 登记信息-培训管理-学习进度 列表查询DTO
 * </p>
 *
 * <AUTHOR>
 * @date 2024-03-06 14:39:04
 */
@Data
@ApiModel("登记信息-培训管理-学习进度 列表查询-RegisterTrainUserInfoListDTO")
public class RegisterTrainUserInfoListDTO implements Serializable {
    private static final long serialVersionUID = 7078505882356105730L;
    @NotNull(message = "pageNum 不能为空")
    private Integer pageNum;
    @NotNull(message = "pageSize 不能为空")
    private Integer pageSize;
    @ApiModelProperty(value = "关键字搜索")
    private String keyword;
    @ApiModelProperty(value = "用户id")
    @NotBlank(message = "userId 不能为空")
    private String userId;
    @ApiModelProperty(value = "是否完成，1-是，0-否")
    private Integer isFinish;
    @ApiModelProperty(value = "是否关注，1-是，0-否")
    private Integer isAttention;

}
