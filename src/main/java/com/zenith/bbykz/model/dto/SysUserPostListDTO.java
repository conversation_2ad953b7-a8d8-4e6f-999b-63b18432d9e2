package com.zenith.bbykz.model.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

import javax.validation.constraints.NotNull;

/**
 * <p>
 * 用户职位信息 列表查询DTO
 * </p>
 *
 * <AUTHOR>
 * @date 2024-03-26 15:04:24
 */
@Data
@ApiModel("用户职位信息 列表查询-SysUserPostListDTO")
public class SysUserPostListDTO implements Serializable {
    private static final long serialVersionUID = 1234461072953986058L;
    @NotNull(message = "pageNum 不能为空")
    private Integer pageNum;
    @NotNull(message = "pageSize 不能为空")
    private Integer pageSize;
    @ApiModelProperty(value = "关键字搜索")
    private String keyword;
}
