package com.zenith.bbykz.model.dto.sd;

import com.efficient.common.validate.QueryGroup;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * <p>
 * 三定-三定机构关联关系 列表查询DTO
 * </p>
 *
 * <AUTHOR>
 * @date 2024-04-11 16:17:20
 */
@Data
@ApiModel("三定-三定机构关联关系 列表查询-SdOrgRelationListDTO")
public class SdOrgRelationListDTO implements Serializable {
    private static final long serialVersionUID = 3159871329013775686L;
    @NotNull(message = "pageNum 不能为空")
    private Integer pageNum;
    @NotNull(message = "pageSize 不能为空")
    private Integer pageSize;
    @ApiModelProperty(value = "层级码")
    @NotBlank(groups = QueryGroup.class, message = "unitLevelCode")
    private String unitLevelCode;
    @ApiModelProperty(value = "单位名称")
    private String name;
}
