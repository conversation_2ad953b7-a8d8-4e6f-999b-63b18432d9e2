package com.zenith.bbykz.model.dto;

import com.baomidou.mybatisplus.annotation.TableField;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

/**
 * <p>
 * sys_user_role DTO
 * </p>
 *
 * <AUTHOR>
 * @date 2023-09-14 17:14:26
 */
@Data
@ApiModel("sys_user_role 请求实体-SysUserRoleDTO")
public class SysUserRoleDTO {
    private static final long serialVersionUID = 7952827423865786197L;

    /**
     *
     */
    @ApiModelProperty(value = "")
    private String id;
    /**
     *
     */
    @ApiModelProperty(value = "")
    private String userId;
    /**
     *
     */
    @ApiModelProperty(value = "")
    private String roleId;
    /**
     *
     */
    @ApiModelProperty(value = "")
    private String systemCode;
    @ApiModelProperty(value = "")
    private String userSystemId;

}

