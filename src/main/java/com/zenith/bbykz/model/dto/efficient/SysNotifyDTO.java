package com.zenith.bbykz.model.dto.efficient;

import com.baomidou.mybatisplus.annotation.TableField;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * <p>
 * 系统消息通知 DTO
 * </p>
 *
 * <AUTHOR>
 * @date 2024-03-18 15:07:00
 */
@Data
@ApiModel("系统消息通知 请求实体-SysNotifyDTO")
public class SysNotifyDTO implements Serializable {
    private static final long serialVersionUID = 6330814309165937120L;

    /**
     * 系统消息通知 ID
     */
    @ApiModelProperty(value = "系统消息通知 ID")
    private String id;
    /**
     * 业务主键
     */
    @ApiModelProperty(value = "业务主键")
    private String bizId;
    /**
     * 批次号
     */
    @ApiModelProperty(value = "批次号")
    private String batchNum;
    /**
     * 系统标识
     */
    @ApiModelProperty(value = "系统标识")
    private String systemId;
    /**
     * 菜单ID
     */
    @ApiModelProperty(value = "菜单ID")
    private String menuId;
    /**
     * 通知类型，1-普通消息，2-短信，3-邮件，4-待办，5-工作通知，6-ding消息，7-公告，9-其他
     */
    @ApiModelProperty(value = "通知类型，1-普通消息，2-短信，3-邮件，4-待办，5-工作通知，6-ding消息，7-公告，9-其他")
    private String notifyType;
    /**
     * 接收系统，1-本系统，2-渝快政，9-其他系统
     */
    @ApiModelProperty(value = "接收系统，1-本系统，2-渝快政，9-其他系统")
    private String recipientSystemType;
    /**
     * 创建用户id
     */
    @ApiModelProperty(value = "创建用户id")
    private String createUserId;
    /**
     * 创建用户政务钉钉id
     */
    @ApiModelProperty(value = "创建用户政务钉钉id")
    private String createZwddId;
    /**
     * 接收人ID
     */
    @ApiModelProperty(value = "接收人ID")
    private String recipientUserId;
    /**
     * 接收人政务钉钉id
     */
    @ApiModelProperty(value = "接收人政务钉钉id")
    private String recipientZwddId;
    /**
     * 标题
     */
    @ApiModelProperty(value = "标题")
    private String title;
    /**
     * 内容
     */
    @ApiModelProperty(value = "内容")
    private String content;
    /**
     * 备注
     */
    @ApiModelProperty(value = "备注")
    private String remark;
    /**
     * pc路由
     */
    @ApiModelProperty(value = "pc路由")
    private String pcUrl;
    /**
     * app端路由
     */
    @ApiModelProperty(value = "app端路由")
    private String appUrl;
    /**
     * 接受人类型，1-用户，2-角色，3-机构
     */
    @ApiModelProperty(value = "接受人类型")
    private String recipientType;
    /**
     * 接收系统消息ID
     */
    @ApiModelProperty(value = "接收系统消息ID")
    private String recipientMsgId;
    /**
     * 消息状态，1-待发送，2-已发送，3-已查看，4-已处理
     */
    @ApiModelProperty(value = "消息状态，1-待发送，2-已发送，3-已查看，4-已处理")
    private String state;
    /**
     * 创建时间
     */
    @ApiModelProperty(value = "创建时间")
    private Date createTime;
    /**
     *
     */
    @ApiModelProperty(value = "")
    private String createUser;
    /**
     * 修改时间
     */
    @ApiModelProperty(value = "修改时间")
    private Date updateTime;
    /**
     *
     */
    @ApiModelProperty(value = "")
    private String updateUser;
    /**
     * 是否删除，1-是，0-否
     */
    @ApiModelProperty(value = "是否删除，1-是，0-否")
    private Integer isDelete;
    /**
     * 业务类型
     */
    @ApiModelProperty(value = "业务类型")
    private String bizType;
    /**
     * 创建单位id
     */
    @ApiModelProperty(value = "创建单位id")
    private String createUnitId;
    /**
     * 创建单位名称
     */
    @ApiModelProperty(value = "创建单位名称")
    private String createUnitName;
    /**
     * 接收单位ID
     */
    @ApiModelProperty(value = "接收单位ID")
    private String recipientUnitId;
    /**
     * 接收单位名称
     */
    @ApiModelProperty(value = "接收单位名称")
    private String recipientUnitName;
    /**
     * 查看时间
     */
    @ApiModelProperty(value = "查看时间")
    private Date readTime;
    /**
     * 接收单位名称
     */
    @ApiModelProperty(value = "处理时间")
    private Date handleTime;
}

