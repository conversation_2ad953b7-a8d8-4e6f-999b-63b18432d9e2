package com.zenith.bbykz.model.dto;

import io.swagger.annotations.ApiModel;
import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * <p>
 * sys_role_menu 列表查询DTO
 * </p>
 *
 * <AUTHOR>
 * @date 2023-09-14 17:14:26
 */
@Data
@ApiModel("sys_role_menu 列表查询-SysRoleMenuListDTO")
public class SysRoleMenuListDTO {
    private static final long serialVersionUID = 6133369586886459620L;
    @NotNull(message = "pageNum 不能为空")
    private Integer pageNum;
    @NotNull(message = "pageSize 不能为空")
    private Integer pageSize;
}
