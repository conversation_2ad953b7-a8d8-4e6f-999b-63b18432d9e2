package com.zenith.bbykz.model.dto.register.train;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * <p>
 * 登记信息-培训管理-用户课程 列表查询DTO
 * </p>
 *
 * <AUTHOR>
 * @date 2024-03-12 10:11:37
 */
@Data
@ApiModel("登记信息-培训管理-用户课程 列表查询-RegisterTrainCourseUserListDTO")
public class RegisterTrainCourseUserListDTO implements Serializable {
    private static final long serialVersionUID = 1383189655906108702L;
    @NotNull(message = "pageNum 不能为空")
    private Integer pageNum;
    @NotNull(message = "pageSize 不能为空")
    private Integer pageSize;
    @ApiModelProperty(value = "关键字搜索")
    private String keyword;
    @ApiModelProperty(value = "用户id")
    @NotBlank(message = "userId 不能为空")
    private String userId;
    @ApiModelProperty(value = "是否完成，1-是，0-否")
    private Integer isFinish;
    @ApiModelProperty(value = "是否关注，1-是，0-否")
    private Integer isAttention;
    private String roleId;
}
