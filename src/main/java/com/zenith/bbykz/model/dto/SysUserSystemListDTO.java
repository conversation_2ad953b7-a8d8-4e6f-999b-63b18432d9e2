package com.zenith.bbykz.model.dto;

import io.swagger.annotations.ApiModel;
import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * <p>
 * 用户系统表 列表查询DTO
 * </p>
 *
 * <AUTHOR>
 * @date 2024-02-29 10:36:56
 */
@Data
@ApiModel("用户系统表 列表查询-SysUserSystemListDTO")
public class SysUserSystemListDTO {
    private static final long serialVersionUID = 7421539970611788758L;
    @NotNull(message = "pageNum 不能为空")
    private Integer pageNum;
    @NotNull(message = "pageSize 不能为空")
    private Integer pageSize;
}
