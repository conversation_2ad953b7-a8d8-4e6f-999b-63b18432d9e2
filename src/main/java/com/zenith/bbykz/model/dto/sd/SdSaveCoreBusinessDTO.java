package com.zenith.bbykz.model.dto.sd;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * <p>
 * 三定-核心业务 DTO
 * </p>
 *
 * <AUTHOR>
 * @date 2024-04-11 16:28:25
 */
@Data
@ApiModel("三定-核心业务 请求实体-SdSaveCoreBusinessDTO")
public class SdSaveCoreBusinessDTO implements Serializable {
    private static final long serialVersionUID = 8867901345904966402L;

    /**
     * 主键id
     */
    @ApiModelProperty()
    private List<SdCoreBusinessDTO> dtoList;

}

