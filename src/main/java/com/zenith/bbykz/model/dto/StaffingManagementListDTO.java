package com.zenith.bbykz.model.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.Date;

/**
 * <p>
 * 用编管理 列表查询DTO
 * </p>
 *
 * <AUTHOR>
 * @date 2023-09-13
 */
@Data
@ApiModel("用编管理 列表查询-StaffingManagementListDTO")
public class StaffingManagementListDTO {
    private static final long serialVersionUID = 4966565478637981495L;
    @NotNull(message = "pageNum 不能为空")
    @ApiModelProperty(value = "页码")
    private Integer pageNum;
    @NotNull(message = "pageSize 不能为空")
    @ApiModelProperty(value = "页大小")
    private Integer pageSize;

    @NotBlank(message = "机构不能为空")
    @ApiModelProperty(value = "机构")
    private String orgCode;

    @ApiModelProperty(value = "状态")
    private String status;

    @NotNull(message = "是否包含下级不能为空")
    @ApiModelProperty(value = "是否包含下级")
    private Integer subordinate;

    @ApiModelProperty(value = "入编时间开始")
    private Date start;

    @ApiModelProperty(value = "入编时间结束")
    private Date end;

    @ApiModelProperty(value = "标题")
    private String title;

    @ApiModelProperty(value = "申请单位")
    private String applicantUnit;

    @ApiModelProperty(value = "编制类型")
    private String preparationType;

    @ApiModelProperty(value = "是否查询全部")
    private Integer queryAll;
}
