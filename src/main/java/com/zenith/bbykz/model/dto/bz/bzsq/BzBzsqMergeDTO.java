package com.zenith.bbykz.model.dto.bz.bzsq;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * <p>
 * 核编管理-用编审核-合并审核 DTO
 * </p>
 *
 * <AUTHOR>
 * @date 2024-04-03 15:26:58
 */
@Data
@ApiModel("核编管理-用编审核-合并审核 请求实体-BzBzsqMergeDTO")
public class BzBzsqMergeDTO implements Serializable {
    private static final long serialVersionUID = 4525580297037955549L;

    /**
     * 主键id
     */
    @ApiModelProperty(value = "主键id")
    private String id;
    /**
     *
     */
    @ApiModelProperty(value = "")
    private String title;
    @ApiModelProperty(value = "区划")
    private String geocode;
    /**
     * 是否同意
     */
    @ApiModelProperty(value = "是否同意")
    private Integer isAgree;
    private String status;
    @ApiModelProperty(value = "创建人姓名")
    private String createUserName;
    @ApiModelProperty(value = "创建时间")
    private Date createTime;
    @ApiModelProperty(value = "编制申请集合")
    private List<BzBzsqDTO> bzBzsqList;
    @ApiModelProperty(value = "意见")
    private String opinion;
    @ApiModelProperty(value = "备注")
    private String remark;
}

