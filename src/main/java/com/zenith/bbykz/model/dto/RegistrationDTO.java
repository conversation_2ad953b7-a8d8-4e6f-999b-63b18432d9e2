package com.zenith.bbykz.model.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.Valid;
import java.util.List;

/**
 * @description: 用编登记DTO
 * @author: yanggm
 * @date: 2023/10/7 10:06
 */
@Data
@ApiModel("用编管理登记 请求实体-RegistrationDTO")
public class RegistrationDTO {

    /**
     * 用编管理
     */
    @ApiModelProperty(value = "用编id")
    private String staffingManagementId;

    /**
     * 登记列表
     */
    @Valid
    @ApiModelProperty(value = "登记列表")
    private List<StaffingManagementRegistrationDTO> registrationList;
}
