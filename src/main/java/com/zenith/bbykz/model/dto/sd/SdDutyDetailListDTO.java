package com.zenith.bbykz.model.dto.sd;

import com.efficient.common.validate.Common1Group;
import com.efficient.common.validate.QueryGroup;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * <p>
 * 三定-细化职责 列表查询DTO
 * </p>
 *
 * <AUTHOR>
 * @date 2024-04-11 16:17:19
 */
@Data
@ApiModel("三定-细化职责 列表查询-SdDutyDetailListDTO")
public class SdDutyDetailListDTO implements Serializable {
    private static final long serialVersionUID = 6869409010465747075L;
    @NotNull(message = "pageNum 不能为空")
    private Integer pageNum;
    @NotNull(message = "pageSize 不能为空")
    private Integer pageSize;
    @ApiModelProperty(value = "三定类型主键")
    @NotBlank(groups = QueryGroup.class, message = "moduleDetailId 不能为空")
    private String moduleDetailId;
    @NotBlank(groups = {Common1Group.class}, message = "unitLevelCode  不能为空")
    private String unitLevelCode;
    @ApiModelProperty(value = "三定分类主键")
    private String moduleId;
    @ApiModelProperty(value = "关键字搜索")
    private String keyword;
}
