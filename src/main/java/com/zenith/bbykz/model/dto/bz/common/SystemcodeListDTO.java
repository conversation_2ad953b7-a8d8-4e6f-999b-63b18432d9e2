package com.zenith.bbykz.model.dto.bz.common;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import java.io.Serializable;

import javax.validation.constraints.NotNull;

/**
* <p>
* SYSTEMCODE 列表查询DTO
* </p>
*
* <AUTHOR>
* @date 2024-08-27 15:52:58
*/
@Data
@ApiModel("SYSTEMCODE 列表查询-SystemcodeListDTO")
public class SystemcodeListDTO implements Serializable {
    private static final long serialVersionUID = 3869699440599846850L;
    @NotNull(message = "pageNum 不能为空")
    private Integer pageNum;
    @NotNull(message = "pageSize 不能为空")
    private Integer pageSize;
    @ApiModelProperty(value = "关键字搜索")
    private String keyword;
}
