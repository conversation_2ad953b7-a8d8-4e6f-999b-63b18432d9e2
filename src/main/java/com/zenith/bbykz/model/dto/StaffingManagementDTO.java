package com.zenith.bbykz.model.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.Min;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.Date;
import java.util.List;

/**
 * <p>
 * 用编管理 DTO
 * </p>
 *
 * <AUTHOR>
 * @date 2023-09-13
 */
@Data
@ApiModel("用编管理 请求实体-StaffingManagementDTO")
public class StaffingManagementDTO {
    private static final long serialVersionUID = 1762710009472500060L;

    /**
     * 主键id
     */
    @ApiModelProperty(value = "主键id")
    private String id;
    /**
     * 标题
     */
    @NotBlank(message = "标题不能为空")
    @ApiModelProperty(value = "标题")
    private String title;
    /**
     * 申请单位
     */
    @NotBlank(message = "申请单位不能为空")
    @ApiModelProperty(value = "申请单位")
    private String applicantUnit;
    /**
     * 编制类型
     */
    @NotBlank(message = "标题不能为空")
    @ApiModelProperty(value = "编制类型")
    private String preparationType;
    /**
     * 用编数量
     */
    @NotNull(message = "用编数量不能为空")
    @ApiModelProperty(value = "用编数量")
    @Min(value = 0, message = "用编数量不能是负数")
    private Integer employeesNumber;
    /**
     * 入编时间
     */
    @NotNull(message = "入编时间不能为空")
    @ApiModelProperty(value = "入编时间")
    private Date enrollmentTime;
    /**
     * 联系人
     */
    @NotBlank(message = "联系人不能为空")
    @ApiModelProperty(value = "联系人")
    private String contacts;
    /**
     * 联系电话
     */
    @NotBlank(message = "联系电话不能为空")
    @ApiModelProperty(value = "联系电话")
    private String telephone;
    /**
     * 申请时间
     */
    @ApiModelProperty(value = "申请时间")
    private Date applicatTime;
    /**
     * 状态
     */
    @ApiModelProperty(value = "状态")
    private String status;
    /**
     * 申请人
     */
    @ApiModelProperty(value = "申请人")
    private String applicatPerson;

    /**
     * 关联人员
     */
//    @Valid
    @ApiModelProperty(value = "关联人员")
    private List<StaffingManagementPersonsDTO> persons;

    /**
     * 附件信息
     */
//    @Valid
    @ApiModelProperty(value = "附件信息")
    private List<StaffingManagementFilesDTO> filesList;

    /**
     * 机构
     */
    @NotBlank(message = "机构不能为空")
    @ApiModelProperty(value = "机构")
    private String orgCode;
}

