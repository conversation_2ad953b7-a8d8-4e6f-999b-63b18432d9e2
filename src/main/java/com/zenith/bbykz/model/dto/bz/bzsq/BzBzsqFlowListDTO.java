package com.zenith.bbykz.model.dto.bz.bzsq;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

import javax.validation.constraints.NotNull;

/**
 * <p>
 * 核编管理-用编审核 列表查询DTO
 * </p>
 *
 * <AUTHOR>
 * @date 2024-04-02 11:51:22
 */
@Data
@ApiModel("核编管理-用编审核 列表查询-BzBzsqFlowListDTO")
public class BzBzsqFlowListDTO implements Serializable {
    private static final long serialVersionUID = 6174410293805533818L;
    @NotNull(message = "pageNum 不能为空")
    private Integer pageNum;
    @NotNull(message = "pageSize 不能为空")
    private Integer pageSize;
    @ApiModelProperty(value = "关键字搜索")
    private String keyword;
}
