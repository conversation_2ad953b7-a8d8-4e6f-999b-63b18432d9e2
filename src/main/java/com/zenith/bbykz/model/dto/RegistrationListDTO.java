package com.zenith.bbykz.model.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * @description: 查询用编统计DTO
 * @author: yanggm
 * @date: 2023/10/7 11:38
 */
@Data
@ApiModel("用编统计 请求实体-RegistrationListDTO")
public class RegistrationListDTO {

    @ApiModelProperty(value = "机构code")
    private String orgCode;

    @NotNull(message = "pageNum 不能为空")
    @ApiModelProperty(value = "页码")
    private Integer pageNum;

    @NotNull(message = "pageSize 不能为空")
    @ApiModelProperty(value = "页大小")
    private Integer pageSize;

    @NotNull(message = "是否包含下级不能为空")
    @ApiModelProperty(value = "是否包含下级")
    private Integer subordinate;

}
