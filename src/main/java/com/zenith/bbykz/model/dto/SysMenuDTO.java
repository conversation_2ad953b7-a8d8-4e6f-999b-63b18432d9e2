package com.zenith.bbykz.model.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

/**
 * <p>
 * 菜单表 DTO
 * </p>
 *
 * <AUTHOR>
 * @date 2023-09-14 17:14:26
 */
@Data
@ApiModel("菜单表 请求实体-SysMenuDTO")
public class SysMenuDTO {
    private static final long serialVersionUID = 3013571924353960193L;

    /**
     *
     */
    @ApiModelProperty(value = "")
    private String id;
    /**
     *
     */
    @ApiModelProperty(value = "")
    private String code;
    /**
     *
     */
    @ApiModelProperty(value = "")
    private String parentCode;
    /**
     *
     */
    @ApiModelProperty(value = "")
    private String name;
    /**
     *
     */
    @ApiModelProperty(value = "")
    private String description;
    /**
     *
     */
    @ApiModelProperty(value = "")
    private Integer enable;
    /**
     *
     */
    @ApiModelProperty(value = "")
    private Boolean isLeaf;
    /**
     *
     */
    @ApiModelProperty(value = "")
    private Date createTime;
    /**
     *
     */
    @ApiModelProperty(value = "")
    private String systemCode;

}

