package com.zenith.bbykz.model.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * <p>
 * 用户机构组详情 DTO
 * </p>
 *
 * <AUTHOR>
 * @date 2024-03-13 10:46:03
 */
@Data
@ApiModel("用户机构组详情 请求实体-SysUserOrgGroupDetailDTO")
public class SysUserOrgGroupDetailDTO implements Serializable {
    private static final long serialVersionUID = 3652183690880175882L;

    /**
     * 单位ID
     */
    @ApiModelProperty(value = "单位ID")
    private String orgId;
    /**
     * 层级码
     */
    @ApiModelProperty(value = "层级码")
    private String orgLevelCode;
    @ApiModelProperty(value = "1-全选，0-半选")
    private Integer allSelect;

}

