package com.zenith.bbykz.model.dto.register.train;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * <p>
 * 登记信息-培训管理-课程分类 列表查询DTO
 * </p>
 *
 * <AUTHOR>
 * @date 2024-03-05 15:05:10
 */
@Data
@ApiModel("登记信息-培训管理-课程分类 列表查询-RegisterTrainTypeListDTO")
public class RegisterTrainTypeListDTO {
    private static final long serialVersionUID = 4271967428890018574L;
    @NotNull(message = "pageNum 不能为空")
    private Integer pageNum;
    @NotNull(message = "pageSize 不能为空")
    private Integer pageSize;
    @ApiModelProperty(value = "关键字搜索")
    private String keyword;
}
