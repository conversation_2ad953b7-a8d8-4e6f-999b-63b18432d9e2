package com.zenith.bbykz.model.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * <p>
 * 用户职位信息 DTO
 * </p>
 *
 * <AUTHOR>
 * @date 2024-03-26 15:04:24
 */
@Data
@ApiModel("用户职位信息 请求实体-SysUserPostDTO")
public class SysUserPostDTO implements Serializable {
    private static final long serialVersionUID = 2888522807980150677L;

    /**
     * 主键
     */
    @ApiModelProperty(value = "主键")
    private String id;
    /**
     * 用户id
     */
    @ApiModelProperty(value = "用户id")
    private String userId;
    /**
     * 部门ID
     */
    @ApiModelProperty(value = "部门ID")
    private String deptId;
    /**
     * 部门层级码
     */
    @ApiModelProperty(value = "部门层级码")
    private String deptLevelCode;
    /**
     * 单位ID
     */
    @ApiModelProperty(value = "单位ID")
    private String unitId;
    /**
     * 单位层级码
     */
    @ApiModelProperty(value = "单位层级码")
    private String unitLevelCode;
    /**
     * 是否主职务
     */
    @ApiModelProperty(value = "是否主职务")
    private Integer mainJob;
    /**
     *
     */
    @ApiModelProperty(value = "")
    private Date joinDate;
    /**
     * 管理单位ID
     */
    @ApiModelProperty(value = "管理单位ID")
    private String manageId;
    /**
     * 管理单位层级码
     */
    @ApiModelProperty(value = "管理单位层级码")
    private String manageCode;
    /**
     * 用户机构组
     */
    @ApiModelProperty(value = "用户机构组")
    private String userGroupOrgId;
    /**
     * 排序
     */
    @ApiModelProperty(value = "排序")
    private Long sort;
    /**
     * 职务
     */
    @ApiModelProperty(value = "职务")
    private String postName;
    /**
     *
     */
    @ApiModelProperty(value = "")
    private Date createTime;
    /**
     *
     */
    @ApiModelProperty(value = "")
    private Date updateTime;
    /**
     *
     */
    @ApiModelProperty(value = "")
    private Integer isDelete;
    /**
     * 拉取时间
     */
    @ApiModelProperty(value = "拉取时间")
    private Date pullTime;

}

