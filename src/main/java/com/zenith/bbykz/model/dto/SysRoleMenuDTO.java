package com.zenith.bbykz.model.dto;

import com.baomidou.mybatisplus.annotation.TableField;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

/**
 * <p>
 * sys_role_menu DTO
 * </p>
 *
 * <AUTHOR>
 * @date 2023-09-14 17:14:26
 */
@Data
@ApiModel("sys_role_menu 请求实体-SysRoleMenuDTO")
public class SysRoleMenuDTO {
    private static final long serialVersionUID = 7147575948709678902L;

    /**
     *
     */
    @ApiModelProperty(value = "")
    private String id;
    /**
     *
     */
    @ApiModelProperty(value = "")
    private String roleId;
    /**
     *
     */
    @ApiModelProperty(value = "")
    private String menuCode;
    @ApiModelProperty(value = "")
    private String menuId;
}

