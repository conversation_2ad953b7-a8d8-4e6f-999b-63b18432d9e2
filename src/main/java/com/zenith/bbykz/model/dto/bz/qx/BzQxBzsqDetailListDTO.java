package com.zenith.bbykz.model.dto.bz.qx;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import java.io.Serializable;

import javax.validation.constraints.NotNull;

/**
* <p>
* 核编管理-用编管理-区县用编申请计划明细 列表查询DTO
* </p>
*
* <AUTHOR>
* @date 2024-05-06 17:05:40
*/
@Data
@ApiModel("核编管理-用编管理-区县用编申请计划明细 列表查询-BzQxBzsqDetailListDTO")
public class BzQxBzsqDetailListDTO implements Serializable {
    private static final long serialVersionUID = 7264260602495847439L;
    @NotNull(message = "pageNum 不能为空")
    private Integer pageNum;
    @NotNull(message = "pageSize 不能为空")
    private Integer pageSize;
    @ApiModelProperty(value = "关键字搜索")
    private String keyword;
}
