package com.zenith.bbykz.model.dto.sd;

import com.baomidou.mybatisplus.annotation.TableField;
import com.efficient.common.validate.AddGroup;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;
import java.util.Date;

/**
 * <p>
 * 三定-三定明细 DTO
 * </p>
 *
 * <AUTHOR>
 * @date 2024-04-11 16:28:25
 */
@Data
@ApiModel("三定-三定明细 请求实体-SdModuleDetailDTO")
public class SdModuleDetailDTO implements Serializable {
    private static final long serialVersionUID = 1971159584162036901L;

    /**
     * 主键id
     */
    @ApiModelProperty(value = "主键id")
    private String id;
    /**
     * 三定类型主键
     */
    @ApiModelProperty(value = "三定类型主键,字典表 SD_MODULE_TYPE  code")
    @NotBlank(groups = AddGroup.class, message = "moduleId 不能为空！")
    private String moduleId;
    /**
     * 名称
     */
    @ApiModelProperty(value = "名称")
    private String title;
    /**
     * 描述
     */
    @ApiModelProperty(value = "描述")
    private String describe;
    /**
     * 备注
     */
    @ApiModelProperty(value = "备注")
    private String remark;
    /**
     * 排序号
     */
    @ApiModelProperty(value = "排序号")
    private Integer sort;
    /**
     * 单位ID
     */
    @ApiModelProperty(value = "单位ID")
    @NotBlank(groups = AddGroup.class, message = "unitId 不能为空！")
    private String unitId;
    /**
     * 单位层级码
     */
    @ApiModelProperty(value = "单位层级码")
    private String unitLevelCode;
    /**
     * 单位名称
     */
    @ApiModelProperty(value = "单位名称")
    private String unitName;
    /**
     * 区划code
     */
    @ApiModelProperty(value = "区划code")
    private String geocode;
    /**
     * 文号
     */
    @ApiModelProperty(value = "文号")
    private String recordNum;
}

