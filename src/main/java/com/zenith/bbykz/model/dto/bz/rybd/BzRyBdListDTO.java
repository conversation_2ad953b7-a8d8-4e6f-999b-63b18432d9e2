package com.zenith.bbykz.model.dto.bz.rybd;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import java.io.Serializable;

import javax.validation.constraints.NotNull;

/**
* <p>
* 核编管理-动态信息维护-人员详情 列表查询DTO
* </p>
*
* <AUTHOR>
* @date 2024-04-22 11:21:43
*/
@Data
@ApiModel("核编管理-动态信息维护-人员详情 列表查询-BzRyBdListDTO")
public class BzRyBdListDTO implements Serializable {
    private static final long serialVersionUID = 5285230394167476577L;
    @NotNull(message = "pageNum 不能为空")
    private Integer pageNum;
    @NotNull(message = "pageSize 不能为空")
    private Integer pageSize;
    @ApiModelProperty(value = "关键字搜索")
    private String keyword;
}
