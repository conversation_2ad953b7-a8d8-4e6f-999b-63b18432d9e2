package com.zenith.bbykz.model.dto.sd;

import com.efficient.common.validate.QueryGroup;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * <p>
 * 三定-核心业务 列表查询DTO
 * </p>
 *
 * <AUTHOR>
 * @date 2024-04-11 16:17:20
 */
@Data
@ApiModel("三定-核心业务 列表查询-SdBusinessItemListDTO")
public class SdBusinessItemListDTO implements Serializable {
    private static final long serialVersionUID = 7348814761127331200L;
    @NotNull(message = "pageNum 不能为空")
    private Integer pageNum;
    @NotNull(message = "pageSize 不能为空")
    private Integer pageSize;
    @ApiModelProperty(value = "三定类型主键")
    @NotBlank(groups = QueryGroup.class, message = "coreBusinessId 不能为空")
    private String coreBusinessId;
}
