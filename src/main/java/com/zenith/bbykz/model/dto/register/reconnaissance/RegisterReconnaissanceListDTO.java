package com.zenith.bbykz.model.dto.register.reconnaissance;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

import javax.validation.constraints.NotNull;

/**
 * <p>
 * 登记信息-内部事务-实地勘察 列表查询DTO
 * </p>
 *
 * <AUTHOR>
 * @date 2024-03-28 11:28:37
 */
@Data
@ApiModel("登记信息-内部事务-实地勘察 列表查询-RegisterReconnaissanceListDTO")
public class RegisterReconnaissanceListDTO implements Serializable {
    private static final long serialVersionUID = 5425754706558629622L;
    @NotNull(message = "pageNum 不能为空")
    private Integer pageNum;
    @NotNull(message = "pageSize 不能为空")
    private Integer pageSize;
    @NotNull(message = "orgLevelCode 不能为空")
    private String orgLevelCode;
    @ApiModelProperty(value = "统一社会信用代码")
    private String creditCode;
    @ApiModelProperty(value = "单位名称")
    private String unitName;
    @ApiModelProperty(value = "证书编号")
    private String certificateNum;
}
