package com.zenith.bbykz.model.dto.bz.qx;

import com.efficient.common.validate.Common1Group;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.Date;

/**
 * <p>
 * 核编管理-用编管理-区县用编申请计划 列表查询DTO
 * </p>
 *
 * <AUTHOR>
 * @date 2024-05-06 17:05:40
 */
@Data
@ApiModel("核编管理-用编管理-区县用编申请计划 列表查询-BzQxBzsqListDTO")
public class BzQxBzsqListDTO implements Serializable {
    private static final long serialVersionUID = 4396390392468583936L;
    @NotNull(message = "pageNum 不能为空")
    private Integer pageNum;
    @NotNull(message = "pageSize 不能为空")
    private Integer pageSize;

    @ApiModelProperty(value = "1-申请页面，2-审核页面")
    @NotBlank(groups = Common1Group.class, message = "pageType 不能为空，1-申请页面，2-审核页面")
    private String pageType;
    @ApiModelProperty(value = "标题")
    private String title;
    @ApiModelProperty(value = "申请单位名称")
    private String applyUnitName;
    @ApiModelProperty(value = "状态")
    private String status;
    @ApiModelProperty(value = "开始时间")
    private Date startDate;
    @ApiModelProperty(value = "结束时间")
    private Date endDate;
}
