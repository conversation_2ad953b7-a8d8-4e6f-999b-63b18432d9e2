package com.zenith.bbykz.model.dto;

import com.baomidou.mybatisplus.annotation.TableField;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

/**
 * <p>
 * 用户系统表 DTO
 * </p>
 *
 * <AUTHOR>
 * @date 2024-02-29 10:36:56
 */
@Data
@ApiModel("用户系统表 请求实体-SysUserSystemDTO")
public class SysUserSystemDTO {
    private static final long serialVersionUID = 3955300400901176554L;

    /**
     * 主键id
     */
    @ApiModelProperty(value = "主键id")
    private String id;
    /**
     * 用户id
     */
    @ApiModelProperty(value = "用户id")
    private String userId;
    /**
     * 系统id
     */
    @ApiModelProperty(value = "系统id")
    private String systemId;
    /**
     * 是否锁定
     */
    @ApiModelProperty(value = "是否锁定")
    private Integer isLock;
    /**
     * 解锁时间
     */
    @ApiModelProperty(value = "解锁时间")
    private Date unlockTime;
    /**
     * 创建时间
     */
    @ApiModelProperty(value = "创建时间")
    private Date createTime;
    /**
     * 创建人
     */
    @ApiModelProperty(value = "创建人")
    private String createUser;
    /**
     * 修改时间
     */
    @ApiModelProperty(value = "修改时间")
    private Date updateTime;
    /**
     * 修改人
     */
    @ApiModelProperty(value = "修改人")
    private String updateUser;
    /**
     * 是否删除
     */
    @ApiModelProperty(value = "是否删除")
    private Integer isDelete;
    @ApiModelProperty(value = "是否默认角色")
    private Integer isDefault;

    @ApiModelProperty(value = "所属机构ID")
    private String orgId;
    @ApiModelProperty(value = "所属机构层级码")
    private String orgLevelCode;
    @ApiModelProperty(value = "是否编办用户")
    private Integer isBbUser;
    @ApiModelProperty(value = "管理层级")
    private String manageCode;
    @ApiModelProperty(value = "区划层级")
    private String geocode;
    @ApiModelProperty(value = "用户机构组")
    private String userOrgGroupId;
    @ApiModelProperty(value = "是否主职务")
    private Integer mainJob;
    @ApiModelProperty(value = "部门ID")
    private String deptId;
    @ApiModelProperty(value = "部门层级码")
    private String deptLevelCode;
    @ApiModelProperty(value = "单位ID")
    private String unitId;
    @ApiModelProperty(value = "单位层级码")
    private String unitLevelCode;
    @ApiModelProperty(value = "职务")
    private String postName;
    @ApiModelProperty(value = "职务id")
    private String userPostId;
}

