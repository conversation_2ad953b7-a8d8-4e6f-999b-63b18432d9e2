package com.zenith.bbykz.model.dto.register.train;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

/**
 * <p>
 * 登记信息-培训管理-课程分类 DTO
 * </p>
 *
 * <AUTHOR>
 * @date 2024-03-05 15:05:10
 */
@Data
@ApiModel("登记信息-培训管理-课程分类 请求实体-RegisterTrainTypeDTO")
public class RegisterTrainTypeDTO {
    private static final long serialVersionUID = 712468955427877220L;

    /**
     * 主键id
     */
    @ApiModelProperty(value = "主键id")
    private String id;
    /**
     * 分类名称
     */
    @ApiModelProperty(value = "分类名称")
    private String name;
    /**
     * 父级ID
     */
    @ApiModelProperty(value = "父级ID")
    private String parentId;
    /**
     * 是否启用
     */
    @ApiModelProperty(value = "是否启用")
    private Integer enabled;
    /**
     * 序号
     */
    @ApiModelProperty(value = "序号")
    private Integer sort;
    /**
     * 创建时间
     */
    @ApiModelProperty(value = "创建时间")
    private Date createTime;
    /**
     * 创建人
     */
    @ApiModelProperty(value = "创建人")
    private String createUser;
    /**
     * 修改时间
     */
    @ApiModelProperty(value = "修改时间")
    private Date updateTime;
    /**
     * 修改人
     */
    @ApiModelProperty(value = "修改人")
    private String updateUser;
    /**
     * 是否删除
     */
    @ApiModelProperty(value = "是否删除")
    private Integer isDelete;

}

