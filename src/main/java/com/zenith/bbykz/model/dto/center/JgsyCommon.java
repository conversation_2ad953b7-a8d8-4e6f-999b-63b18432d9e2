package com.zenith.bbykz.model.dto.center;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
* <p>
* 核定编制校验表 实体类
* </p>
*
* <AUTHOR> generator
* @date 2022-05-16 10:38:27
*/
@Data
public class JgsyCommon implements Serializable {

    private static final long serialVersionUID = 3654936453420003570L;

    /**
    *机构代码
    */
    @ApiModelProperty(value = "机构代码")
    private String jgsyCode;
    /**
    *上级代码
    */
    @ApiModelProperty(value = "上级代码")
    private String jgsyParentCode;
    /**
    *区划代码
    */
    @ApiModelProperty(value = "区划代码")
    private String depCode;
    /**
    *机构名称
    */
    @ApiModelProperty(value = "机构名称")
    private String jgsyName;
    /**
    *单位类型
    */
    @ApiModelProperty(value = "单位类型")
    private String jgsyType;
    /**
    *上级单位类型
    */
    @ApiModelProperty(value = "上级单位类型")
    private String jgsyParentType;
    /**
    *系统类别代码
    */
    @ApiModelProperty(value = "系统类别代码")
    private String jgsySystemCode;
    /**
    *机构状态
    */
    @ApiModelProperty(value = "机构状态")
    private String jgsyStates;
    /**
    *子节点个数
    */
    @ApiModelProperty(value = "子节点个数")
    private Integer jgsyChildrensum;
    /**
    *排序字段
    */
    @ApiModelProperty(value = "排序字段")
    private String jgsyCodeOrder;
    /**
    *最后修改时间
    */
    @ApiModelProperty(value = "最后修改时间")
    private Date updateTime;
    /**
    *撤销时间
    */
    @ApiModelProperty(value = "撤销时间")
    private Date chexiaoTime;
    /**
    *撤销文号
    */
    @ApiModelProperty(value = "撤销文号")
    private String chexiaoWh;
    /**
    *撤销系统时间
    */
    @ApiModelProperty(value = "撤销系统时间")
    private Date chexiaoSystemTime;
    /**
    *撤销原因
    */
    @ApiModelProperty(value = "撤销原因")
    private Integer edition;
    /**
    *最后修改者
    */
    @ApiModelProperty(value = "最后修改者")
    private String updateUnit;
    /**
    *数据交换码预留字段1
    */
    @ApiModelProperty(value = "数据交换码预留字段1")
    private String jgsyStandby1;
    /**
    *数据交换码预留字段2
    */
    @ApiModelProperty(value = "数据交换码预留字段2")
    private String jgsyStandby2;
    /**
    *是否垂直
    */
    @ApiModelProperty(value = "是否垂直")
    private String jgsyIfvertical;
    /**
    *机构编制职数上报方式（填报、实有人员）
    */
    @ApiModelProperty(value = "机构编制职数上报方式（填报、实有人员）")
    private String jgsyRysbfs;
    /**
    *可删除
    */
    @ApiModelProperty(value = "可删除")
    private String jgsySzbm;
    /**
    *可删除
    */
    @ApiModelProperty(value = "可删除")
    private Integer jgsyCheckstate;
    /**
    *是否政法机构
    */
    @ApiModelProperty(value = "是否政法机构")
    private String jgsyIfzfjg;
    /**
    *撤销原因
    */
    @ApiModelProperty(value = "撤销原因")
    private String chexiaoYy;
    /**
    *是否审批
    */
    @ApiModelProperty(value = "是否审批")
    private String ifShenpi;
    /**
    *可删除
    */
    @ApiModelProperty(value = "可删除")
    private String jgbzId;
    /**
    *超机构预警　　0：空机构 　　1：正常 2：超机构
    */
    @ApiModelProperty(value = "超机构预警　　0：空机构 　　1：正常 2：超机构")
    private String jgsyChaojg;
    /**
    *超编制预警　　0：空编 　　1：正常 2：超编
    */
    @ApiModelProperty(value = "超编制预警　　0：空编 　　1：正常 2：超编")
    private String jgsyChaobz;
    /**
    *超职数预警　　0：空职数 　　1：正常 2：超职数
    */
    @ApiModelProperty(value = "超职数预警　　0：空职数 　　1：正常 2：超职数")
    private String jgsyChaozs;
    /**
    *
    */
    @ApiModelProperty(value = "")
    private String jgsyStandby3;
    /**
    *
    */
    @ApiModelProperty(value = "")
    private String jgsyStandby4;
    /**
    *
    */
    @ApiModelProperty(value = "")
    private String jgsyStandby5;
    /**
    *
    */
    @ApiModelProperty(value = "")
    private String jgsyStandby6;
    /**
    *
    */
    @ApiModelProperty(value = "")
    private String jgsyStandby7;
    /**
    *
    */
    @ApiModelProperty(value = "")
    private String creditCode;
    /**
    *该机构含有的下级机构数量
    */
    @ApiModelProperty(value = "该机构含有的下级机构数量")
    private Integer jgsyShiyouChildrensum;
    /**
    *三定文号
    */
    @ApiModelProperty(value = "三定文号")
    private String sandingWh;
    /**
    *是否是核查导入数据:1是，0或null实名制录入
    */
    @ApiModelProperty(value = "是否是核查导入数据:1是，0或null实名制录入")
    private String sfhcdrsj;
    /**
    *是否是开发区: 0:否;1:是;
    */
    @ApiModelProperty(value = "是否是开发区: 0:否;1:是;")
    private String sfskfq;
    /**
    *是否特殊机构: 0:否;1:是(非实名制机构)
    */
    @ApiModelProperty(value = "是否特殊机构: 0:否;1:是(非实名制机构)")
    private String jgsyIfts;
    /**
    *统一社会信用代码
    */
    @ApiModelProperty(value = "统一社会信用代码")
    private String unifyCode;
    /**
    *附件ID
    */
    @ApiModelProperty(value = "附件ID")
    private String ysbhPks;
    /**
    *
    */
    @ApiModelProperty(value = "编制使用层级")
    private String jgsyBzsycj;
    /**
    *是否执法队伍
    */
    @ApiModelProperty(value = "是否执法队伍")
    private String jgsyIfzfdw;
    /**
    *上次更新时间
    */
    @ApiModelProperty(value = "上次更新时间")
    private Date lastUpdateTime;
    /**
    *机构设置时间(创建时间)
    */
    @ApiModelProperty(value = "机构设置时间(创建时间)")
    private Date jgszsj;
}
