package com.zenith.bbykz.model.dto.register.train;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

/**
 * <p>
 * 登记信息-培训管理-课程管理关联课件 DTO
 * </p>
 *
 * <AUTHOR>
 * @date 2024-03-05 15:44:53
 */
@Data
@ApiModel("登记信息-培训管理-课程管理关联课件 请求实体-RegisterTrainCourseDetailDTO")
public class RegisterTrainCourseDetailDTO {
    private static final long serialVersionUID = 27711489033260877L;

    /**
     * 主键id
     */
    @ApiModelProperty(value = "主键id")
    private String id;
    /**
     * 课程主键
     */
    @ApiModelProperty(value = "课程主键")
    private String courseId;
    /**
     *
     */
    @ApiModelProperty(value = "")
    private String trainFileId;
    /**
     * 课件名称
     */
    @ApiModelProperty(value = "课件名称")
    private String detailName;
    /**
     * 时长，秒
     */
    @ApiModelProperty(value = "时长，秒")
    private Integer mins;
    /**
     * 创建时间
     */
    @ApiModelProperty(value = "创建时间")
    private Date createTime;
    /**
     * 创建人
     */
    @ApiModelProperty(value = "创建人")
    private String createUser;
    /**
     * 修改时间
     */
    @ApiModelProperty(value = "修改时间")
    private Date updateTime;
    /**
     * 修改人
     */
    @ApiModelProperty(value = "修改人")
    private String updateUser;
    /**
     * 是否删除
     */
    @ApiModelProperty(value = "是否删除")
    private Integer isDelete;

}

