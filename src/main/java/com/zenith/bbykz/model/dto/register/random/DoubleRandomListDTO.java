package com.zenith.bbykz.model.dto.register.random;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import java.io.Serializable;

import javax.validation.constraints.NotNull;

/**
* <p>
* 登记信息-内部事务-双随机检查 列表查询DTO
* </p>
*
* <AUTHOR>
* @date 2024-05-06 15:48:28
*/
@Data
@ApiModel("登记信息-内部事务-双随机检查 列表查询-DoubleRandomListDTO")
public class DoubleRandomListDTO implements Serializable {
    private static final long serialVersionUID = 1907617357774303416L;
    @NotNull(message = "pageNum 不能为空")
    private Integer pageNum;
    @NotNull(message = "pageSize 不能为空")
    private Integer pageSize;
    @NotNull(message = "orgLevelCode 不能为空")
    private String orgLevelCode;
    @ApiModelProperty(value = "统一社会信用代码")
    private String creditCode;
    @ApiModelProperty(value = "单位名称")
    private String unitName;
}
