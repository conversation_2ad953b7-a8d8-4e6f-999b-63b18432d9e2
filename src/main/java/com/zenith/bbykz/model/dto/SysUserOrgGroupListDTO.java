package com.zenith.bbykz.model.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

import javax.validation.constraints.NotNull;

/**
 * <p>
 * 用户机构组 列表查询DTO
 * </p>
 *
 * <AUTHOR>
 * @date 2024-03-13 10:48:03
 */
@Data
@ApiModel("用户机构组 列表查询-SysUserOrgGroupListDTO")
public class SysUserOrgGroupListDTO implements Serializable {
    private static final long serialVersionUID = 4267074224542625855L;
    @NotNull(message = "pageNum 不能为空")
    private Integer pageNum;
    @NotNull(message = "pageSize 不能为空")
    private Integer pageSize;
    @ApiModelProperty(value = "关键字搜索")
    private String keyword;
}
