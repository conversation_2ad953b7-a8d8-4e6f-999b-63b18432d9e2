package com.zenith.bbykz.model.dto.bz.bzsq;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

import javax.validation.constraints.NotNull;

/**
 * <p>
 * 核编管理-用编审核-合并审核 列表查询DTO
 * </p>
 *
 * <AUTHOR>
 * @date 2024-04-03 15:26:58
 */
@Data
@ApiModel("核编管理-用编审核-合并审核 列表查询-BzBzsqMergeListDTO")
public class BzBzsqMergeListDTO implements Serializable {
    private static final long serialVersionUID = 2136104250027979029L;
    @NotNull(message = "pageNum 不能为空")
    private Integer pageNum;
    @NotNull(message = "pageSize 不能为空")
    private Integer pageSize;
    @ApiModelProperty(value = "关键字搜索")
    private String keyword;
    @ApiModelProperty(value = "标题")
    private String title;
    @ApiModelProperty(value = "状态")
    private String status;
    @ApiModelProperty(value = "开始时间")
    private Date startTime;
    @ApiModelProperty(value = "结束时间")
    private Date endTime;
    private String unitLevelCode;

}
