package com.zenith.bbykz.model.dto;

import io.swagger.annotations.ApiModel;
import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * <p>
 * 菜单表 列表查询DTO
 * </p>
 *
 * <AUTHOR>
 * @date 2023-09-14 17:14:26
 */
@Data
@ApiModel("菜单表 列表查询-SysMenuListDTO")
public class SysMenuListDTO {
    private static final long serialVersionUID = 6831390750584424100L;
    @NotNull(message = "pageNum 不能为空")
    private Integer pageNum;
    @NotNull(message = "pageSize 不能为空")
    private Integer pageSize;
    private String systemId;
}
