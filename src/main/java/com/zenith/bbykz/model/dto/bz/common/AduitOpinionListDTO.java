package com.zenith.bbykz.model.dto.bz.common;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import java.io.Serializable;

import javax.validation.constraints.NotNull;

/**
* <p>
* 核编管理-常用审批意见 列表查询DTO
* </p>
*
* <AUTHOR>
* @date 2024-05-17 11:10:37
*/
@Data
@ApiModel("核编管理-常用审批意见 列表查询-AduitOpinionListDTO")
public class AduitOpinionListDTO implements Serializable {
    private static final long serialVersionUID = 4714621064656275444L;
    @NotNull(message = "pageNum 不能为空")
    private Integer pageNum;
    @NotNull(message = "pageSize 不能为空")
    private Integer pageSize;
    // @ApiModelProperty(value = "关键字搜索")
    // private String keyword;
}
