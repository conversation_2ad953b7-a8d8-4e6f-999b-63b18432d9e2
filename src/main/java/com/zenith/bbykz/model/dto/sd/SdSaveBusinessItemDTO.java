package com.zenith.bbykz.model.dto.sd;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * <p>
 * 三定-业务事项 DTO
 * </p>
 *
 * <AUTHOR>
 * @date 2024-04-11 16:28:25
 */
@Data
@ApiModel("三定-业务事项 请求实体-SdSaveBusinessItemDTO")
public class SdSaveBusinessItemDTO implements Serializable {
    private static final long serialVersionUID = 37967051253855556L;

    /**
     * 主键id
     */
    @ApiModelProperty(value = "主键id")
    private List<SdBusinessItemDTO> dtoList;

}

