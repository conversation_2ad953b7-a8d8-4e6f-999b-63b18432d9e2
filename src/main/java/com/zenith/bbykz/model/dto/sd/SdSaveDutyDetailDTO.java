package com.zenith.bbykz.model.dto.sd;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * <p>
 * 三定-细化职责 DTO
 * </p>
 *
 * <AUTHOR>
 * @date 2024-04-11 16:28:25
 */
@Data
@ApiModel("三定-细化职责 请求实体-SdSaveDutyDetailDTO")
public class SdSaveDutyDetailDTO implements Serializable {
    private static final long serialVersionUID = 7048044737408680496L;
    @ApiModelProperty
    private List<SdDutyDetailDTO> dtoList;

}

