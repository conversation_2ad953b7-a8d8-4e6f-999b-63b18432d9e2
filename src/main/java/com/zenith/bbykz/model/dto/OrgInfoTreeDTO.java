package com.zenith.bbykz.model.dto;

import io.swagger.annotations.ApiModel;
import lombok.Data;

import javax.validation.constraints.NotBlank;

/**
 * <p>
 * org_info 列表查询DTO
 * </p>
 *
 * <AUTHOR>
 * @date 2023-09-14 17:51:37
 */
@Data
@ApiModel("org_info 列表查询-OrgInfoTreeDTO")
public class OrgInfoTreeDTO {
    private static final long serialVersionUID = 2593990117316801796L;
    @NotBlank(message = "orgCode 不能为空")
    private String orgCode;
    private String systemId;
    private String userPostId;
    /**
     * 是否编办机构树
     */
    private boolean isBb;
    /**
     * 是否内设机构树
     */
    private boolean isNs;
    /**
     * 是否包含参公
     */
    private boolean hasCg;
    /**
     * 是否排除内设机构
     */
    private boolean excludeNs;
}
