package com.zenith.bbykz.model.dto.bz.bzsq;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.Date;

/**
 * <p>
 * 核编管理-用编申请 列表查询DTO
 * </p>
 *
 * <AUTHOR>
 * @date 2024-03-29 16:05:19
 */
@Data
@ApiModel("核编管理-用编申请 列表查询-BzBzsqListDTO")
public class BzBzsqListDTO implements Serializable {
    private static final long serialVersionUID = 7244648516780837819L;
    @NotNull(message = "pageNum 不能为空")
    private Integer pageNum;
    @NotNull(message = "pageSize 不能为空")
    private Integer pageSize;
    @ApiModelProperty(value = "关键字搜索")
    private String keyword;
    @ApiModelProperty(value = "标题")
    private String title;
    @ApiModelProperty(value = "申请单位")
    private String applyUnitName;
    @ApiModelProperty(value = "状态")
    private String status;
    @ApiModelProperty(value = "开始时间")
    private Date startTime;
    @ApiModelProperty(value = "结束时间")
    private Date endTime;
    @ApiModelProperty(value = "结束时间")
    @NotNull(message = "pageType 不能为空,1-申请页面，2-审核页面")
    private Integer pageType;
    private String unitLevelCode;

}
