package com.zenith.bbykz.model.dto;

import com.efficient.common.validate.Common1Group;
import io.swagger.annotations.ApiModel;
import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * <p>
 * org_info 列表查询DTO
 * </p>
 *
 * <AUTHOR>
 * @date 2023-09-14 17:51:37
 */
@Data
@ApiModel("org_info 列表查询-OrgInfoListDTO")
public class OrgInfoListDTO {
    private static final long serialVersionUID = 2593990117316801796L;
    @NotNull(groups = Common1Group.class, message = "pageNum 不能为空")
    private Integer pageNum;
    @NotNull(groups = Common1Group.class, message = "pageSize 不能为空")
    private Integer pageSize;
    @NotNull(groups = Common1Group.class, message = "orgCode 不能为空")
    private String orgCode;

    private Integer subordinate;
    private String keyword;
    private Integer limitCount;
    private boolean excludeNs;
}
