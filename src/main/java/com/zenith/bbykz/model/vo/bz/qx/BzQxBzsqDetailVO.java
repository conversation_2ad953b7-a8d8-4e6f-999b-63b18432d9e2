package com.zenith.bbykz.model.vo.bz.qx;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.util.Date;

/**
* <p>
* 核编管理-用编管理-区县用编申请计划明细 VO
* </p>
*
* <AUTHOR>
* @date 2024-05-06 17:05:40
*/
@Data
@ApiModel("核编管理-用编管理-区县用编申请计划明细 返回实体-BzQxBzsqDetailVO")
public class BzQxBzsqDetailVO implements Serializable {

    private static final long serialVersionUID = 5434959471732505202L;

    /**
    *主键id
    */
    @ApiModelProperty(value = "主键id")
    private String id;
    /**
    *录入单位ID
    */
    @ApiModelProperty(value = "录入单位ID")
    private String qxBzsqId;
    /**
    *编制类型，字典表-BZ_BZLX
    */
    @ApiModelProperty(value = "编制类型，字典表-BZ_BZLX")
    private String bzType;
    private String bzTypeName;
    /**
    *用编类型，字典表-BZ_YBLX
    */
    @ApiModelProperty(value = "用编类型，字典表-BZ_YBLX")
    private String ybType;
    private String ybTypeName;
    /**
    *申请用途
    */
    @ApiModelProperty(value = "申请用途")
    private String applyUsed;
    /**
    *有效期限
    */
    @ApiModelProperty(value = "有效期限")
    private Date validTime;
    /**
    *申请数量
    */
    @ApiModelProperty(value = "申请数量")
    private Integer applyNum;
    /**
    *备注
    */
    @ApiModelProperty(value = "备注")
    private String remark;
    /**
    *创建时间
    */
    @ApiModelProperty(value = "创建时间")
    private Date createTime;
    /**
    *创建人
    */
    @ApiModelProperty(value = "创建人")
    private String createUser;
    /**
    *修改时间
    */
    @ApiModelProperty(value = "修改时间")
    private Date updateTime;
    /**
    *修改人
    */
    @ApiModelProperty(value = "修改人")
    private String updateUser;
    /**
    *是否删除
    */
    @ApiModelProperty(value = "是否删除")
    private Integer isDelete;
}
