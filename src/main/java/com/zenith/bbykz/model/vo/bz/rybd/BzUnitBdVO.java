package com.zenith.bbykz.model.vo.bz.rybd;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * <p>
 * 核编管理-动态信息维护 VO
 * </p>
 *
 * <AUTHOR>
 * @date 2024-04-22 11:21:43
 */
@Data
@ApiModel("核编管理-动态信息维护 返回实体-BzUnitBdVO")
public class BzUnitBdVO implements Serializable {

    private static final long serialVersionUID = 5303145157841563879L;

    /**
     * 主键id
     */
    @ApiModelProperty(value = "主键id")
    private String id;
    /**
     * 标题
     */
    @ApiModelProperty(value = "标题")
    private String title;
    /**
     * 单位主键
     */
    @ApiModelProperty(value = "单位主键")
    private String unitId;
    /**
     * 单位层级码
     */
    @ApiModelProperty(value = "单位层级码")
    private String unitLevelCode;
    /**
     * 单位名称
     */
    @ApiModelProperty(value = "单位名称")
    private String unitName;
    /**
     * 实名制单位code
     */
    @ApiModelProperty(value = "实名制单位code")
    private String smzUnitCode;
    /**
     * 统一社会信用代码
     */
    @ApiModelProperty(value = "统一社会信用代码")
    private String creditCode;
    /**
     * 变动类型，BZ_UNIT_BD_TYPE，默认值
     */
    @ApiModelProperty(value = "变动类型，BZ_UNIT_BD_TYPE，默认值")
    private String bdType;
    private String bdTypeName;
    /**
     * 备注
     */
    @ApiModelProperty(value = "备注")
    private String remark;
    /**
     * 感知时间
     */
    @ApiModelProperty(value = "感知时间")
    private Date changeTime;

    private List<BzRyBdVO> rybdList;
}
