package com.zenith.bbykz.model.vo.bz.bzsq;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * <p>
 * 核编管理-用编审核-合并审核 VO
 * </p>
 *
 * <AUTHOR>
 * @date 2024-04-03 15:26:58
 */
@Data
@ApiModel("核编管理-用编审核-合并审核 返回实体-BzBzsqMergeVO")
public class BzBzsqMergeVO implements Serializable {

    private static final long serialVersionUID = 3500111563041228014L;

    /**
     * 主键id
     */
    @ApiModelProperty(value = "主键id")
    private String id;
    /**
     *
     */
    @ApiModelProperty(value = "")
    private String title;
    /**
     * 标题
     */
    @ApiModelProperty(value = "状态")
    private String status;
    private String statusName;
    @ApiModelProperty(value = "区划")
    private String geocode;
    /**
     * 是否同意
     */
    @ApiModelProperty(value = "是否同意")
    private Integer isAgree;
    /**
     * 是否合并审核
     */
    @ApiModelProperty(value = "是否合并审核")
    private String bzsqIdList;
    /**
     * 创建人姓名
     */
    @ApiModelProperty(value = "创建人姓名")
    private String createUserName;
    /**
     * 创建时间
     */
    @ApiModelProperty(value = "创建时间")
    private Date createTime;
    /**
     * 创建人
     */
    @ApiModelProperty(value = "创建人")
    private String createUser;
    /**
     * 修改时间
     */
    @ApiModelProperty(value = "修改时间")
    private Date updateTime;
    /**
     * 修改人
     */
    @ApiModelProperty(value = "修改人")
    private String updateUser;
    /**
     * 是否删除
     */
    @ApiModelProperty(value = "是否删除")
    private Integer isDelete;
    @ApiModelProperty(value = "编制申请详情")
    private List<BzBzsqVO> bzBzsqList;
    @ApiModelProperty(value = "意见")
    private String opinion;
    @ApiModelProperty(value = "备注")
    private String remark;
}
