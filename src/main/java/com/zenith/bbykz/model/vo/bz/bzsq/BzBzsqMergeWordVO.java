package com.zenith.bbykz.model.vo.bz.bzsq;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @since 2024/5/11 11:42
 */
@Data
public class BzBzsqMergeWordVO {
    @ApiModelProperty(value = "序号")
    private String sort;
    @ApiModelProperty(value = "编制申请单位")
    private String applyUnitName;
    @ApiModelProperty(value = "编制使用单位")
    private String useBzUnitName;
    @ApiModelProperty(value = "编制类型")
    private String bzTypeName;
    @ApiModelProperty(value = "用编类型")
    private String ybTypeName;

    @ApiModelProperty(value = "待上编数")
    private String dsbNum;

    @ApiModelProperty(value = "在编数")
    private String zbNum;

    @ApiModelProperty(value = "余编数")
    private String ybNum;

    @ApiModelProperty(value = "申请数")
    private String applyNum;

    @ApiModelProperty(value = "备注")
    private String remark;
}
