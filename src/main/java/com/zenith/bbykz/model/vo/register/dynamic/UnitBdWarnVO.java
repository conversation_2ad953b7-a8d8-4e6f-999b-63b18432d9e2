package com.zenith.bbykz.model.vo.register.dynamic;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * <p>
 * 登记信息-预警感知-信息变动 VO
 * </p>
 *
 * <AUTHOR>
 * @date 2024-04-09 11:50:41
 */
@Data
@ApiModel("登记信息-预警感知-预警信息")
public class UnitBdWarnVO implements Serializable {

    private static final long serialVersionUID = 4930086526823319141L;

    /**
     * 主键id
     */
    @ApiModelProperty(value = "主键id")
    private String id;
    /**
     * 变动类型
     */
    @ApiModelProperty(value = "变动类型")
    private String type;
    private String typeName;
    /**
     * 区划
     */
    @ApiModelProperty(value = "区划")
    private String geocode;
    /**
     * 单位ID
     */
    @ApiModelProperty(value = "单位ID")
    private String unitId;
    /**
     * 单位层级码
     */
    @ApiModelProperty(value = "单位层级码")
    private String unitLevelCode;
    /**
     * 单位名称
     */
    @ApiModelProperty(value = "单位名称")
    private String unitName;
    /**
     * 原信息
     */
    @ApiModelProperty(value = "警告信息")
    private String warnInfo;
    /**
     * 新信息
     */
    @ApiModelProperty(value = "新信息")
    private String oldInfo;
    @ApiModelProperty(value = "新信息")
    private String newInfo;
    /**
     * 是否处理
     */
    @ApiModelProperty(value = "是否处理")
    private Integer isHandle;
    /**
     * 处理时间
     */
    @ApiModelProperty(value = "处理时间")
    private Date handleTime;
    /**
     * 备注
     */
    @ApiModelProperty(value = "备注")
    private String remark;
    /**
     * 变动时间
     */
    @ApiModelProperty(value = "变动时间")
    private Date bdTime;
    /**
     * 来源类型，1-自建，2-同步
     */
    @ApiModelProperty(value = "来源类型，1-自建，2-同步")
    private Integer sourceType;
    /**
     * 来源类型，1-自建，2-同步
     */
    @ApiModelProperty(value = "来源类型，描述")
    private String sourceName;
    /**
     * 是否发送,0-未发送，1-发送，发送后所属单位才能看到预警信息
     */
    @ApiModelProperty(value = "是否发送,0-未发送，1-发送，发送后所属单位才能看到预警信息")
    private Integer isSend;
    private String state;
    private String stateName;
}
