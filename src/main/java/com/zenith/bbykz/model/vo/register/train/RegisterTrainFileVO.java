package com.zenith.bbykz.model.vo.register.train;

import com.efficient.file.model.entity.SysFileInfo;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * <p>
 * 登记信息-培训管理-课件管理 VO
 * </p>
 *
 * <AUTHOR>
 * @date 2024-03-05 15:44:53
 */
@Data
@ApiModel("登记信息-培训管理-课件管理 返回实体-RegisterTrainFileVO")
public class RegisterTrainFileVO {

    private static final long serialVersionUID = 1270569550978105596L;

    /**
     * 主键id
     */
    @ApiModelProperty(value = "主键id")
    private String id;
    @ApiModelProperty(value = "主键id")
    private String courseDetailId;
    /**
     * 名称
     */
    @ApiModelProperty(value = "名称")
    private String name;
    private String trainFileId;
    /**
     * 分类ID
     */
    @ApiModelProperty(value = "分类ID")
    private String typeId;
    /**
     * 分类名称
     */
    @ApiModelProperty(value = "分类名称")
    private String typeName;
    /**
     * 时长
     */
    @ApiModelProperty(value = "时长")
    private String mins;
    /**
     * 描述
     */
    @ApiModelProperty(value = "描述")
    private String describe;
    /**
     * 创建时间
     */
    @ApiModelProperty(value = "创建时间")
    private Date createTime;
    /**
     * 创建人
     */
    @ApiModelProperty(value = "创建人")
    private String createUser;
    /**
     * 修改时间
     */
    @ApiModelProperty(value = "修改时间")
    private Date updateTime;
    /**
     * 修改人
     */
    @ApiModelProperty(value = "修改人")
    private String updateUser;
    /**
     * 是否删除
     */
    @ApiModelProperty(value = "是否删除")
    private Integer isDelete;
    private String fileId;
    private String fileName;
    private List<SysFileInfo> fileInfoList;
}
