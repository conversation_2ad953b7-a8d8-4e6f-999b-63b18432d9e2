package com.zenith.bbykz.model.vo.sd;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * <p>
 * 三定-业务事项一件事关联表 VO
 * </p>
 *
 * <AUTHOR>
 * @date 2024-04-11 16:28:25
 */
@Data
@ApiModel("三定-业务事项一件事关联表 返回实体-SdBusinessItemOneVO")
public class SdBusinessItemOneAllVO implements Serializable {

    private static final long serialVersionUID = 1381014080929350527L;

    /**
     * 主键id
     */
    @ApiModelProperty(value = "主键id")
    private String id;
    /**
     * 业务事项主键
     */
    @ApiModelProperty(value = "业务事项主键")
    private String itemId;
    /**
     * 一件事主键
     */
    @ApiModelProperty(value = "一件事主键")
    private String oneId;
    @ApiModelProperty(value = "一件事名称")
    private String oneName;

}
