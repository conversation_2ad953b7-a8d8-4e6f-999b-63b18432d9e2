package com.zenith.bbykz.model.vo;

import cn.afterturn.easypoi.excel.annotation.Excel;
import lombok.Data;

/**
 * @description: 用编管理导出实体
 * @author: yanggm
 * @date: 2023/9/25 11:27
 */
@Data
public class StaffingManagementVoExcel {

    /**
     * 序号
     */
    @Excel(name = "序号", orderNum = "1", width = 10, isImportField = "true_st")
    private Integer index;

    /**
     * 标题
     */
    @Excel(name = "标题", orderNum = "2", width = 30, isImportField = "true_st")
    private String title;

    /**
     * 申请单位
     */
    @Excel(name = "申请单位", orderNum = "3", width = 10, isImportField = "true_st")
    private String applicantUnit;

    /**
     * 编制类型
     */
    @Excel(name = "编制类型", orderNum = "4", width = 20, isImportField = "true_st")
    private String preparationType;

    /**
     * 用编数量
     */
    @Excel(name = "用编数量", orderNum = "5", width = 10, isImportField = "true_st")
    private Integer employeesNumber;

    /**
     * 入编时间
     */
    @Excel(name = "入编时间", orderNum = "6", width = 20, isImportField = "true_st")
    private String enrollmentTime;

    /**
     * 联系人
     */
    @Excel(name = "联系人", orderNum = "7", width = 10, isImportField = "true_st")
    private String contacts;

    /**
     * 联系电话
     */
    @Excel(name = "联系电话", orderNum = "8", width = 20, isImportField = "true_st")
    private String telephone;

    /**
     * 申请时间
     */
    @Excel(name = "申请时间", orderNum = "9", width = 20, isImportField = "true_st")
    private String applicatTime;

    /**
     * 状态
     */
    @Excel(name = "状态", orderNum = "10", width = 10, replace = {"未审核_0", "同意_1", "不同意_2", "未提交_3"}, isImportField = "true_st")
    private String status;

    /**
     * 申请人
     */
    @Excel(name = "申请人", orderNum = "11", width = 20, isImportField = "true_st")
    private String applicatPerson;

    /**
     * 审批人
     */
    @Excel(name = "审批人", orderNum = "12", width = 20, isImportField = "true_st")
    private String approvedPerson;

    /**
     * 核编单号
     */
    @Excel(name = "核编单号", orderNum = "13", width = 20, isImportField = "true_st")
    private String approvalNumber;
}
