package com.zenith.bbykz.model.vo.register.interior;

import com.zenith.bbykz.model.entity.register.interior.RegisterLegalPersonLib;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * <p>
 * 登记信息-内部事务-法人库日志 VO
 * </p>
 *
 * <AUTHOR>
 * @date 2024-03-12 17:24:37
 */
@Data
@ApiModel("登记信息-内部事务-法人库日志 返回实体-RegisterLegalPersonLogVO")
public class RegisterLegalPersonLogVO implements Serializable {

    private static final long serialVersionUID = 3457655122063345935L;

    /**
     * 主键id
     */
    @ApiModelProperty(value = "主键id")
    private String id;
    /**
     * 名称
     */
    @ApiModelProperty(value = "名称")
    private String name;
    /**
     * 身份证
     */
    @ApiModelProperty(value = "身份证")
    private String idCard;
    /**
     * 单位名称
     */
    @ApiModelProperty(value = "单位名称")
    private String unitName;
    /**
     * 是否通过
     */
    @ApiModelProperty(value = "是否通过")
    private Integer isSuccess;
    /**
     * 描述
     */
    @ApiModelProperty(value = "描述")
    private String description;
    /**
     * 创建时间
     */
    @ApiModelProperty(value = "创建时间")
    private Date createTime;
    /**
     * 创建人
     */
    @ApiModelProperty(value = "创建人")
    private String createUser;
    /**
     * 修改时间
     */
    @ApiModelProperty(value = "修改时间")
    private Date updateTime;
    /**
     * 修改人
     */
    @ApiModelProperty(value = "修改人")
    private String updateUser;
    /**
     * 是否删除
     */
    @ApiModelProperty(value = "是否删除")
    private Integer isDelete;

    private List<RegisterLegalPersonLib> libList;
}
