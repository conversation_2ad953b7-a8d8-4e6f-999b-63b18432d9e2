package com.zenith.bbykz.model.vo.sd;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * <p>
 * 三定-业务事项一件事关联表 VO
 * </p>
 *
 * <AUTHOR>
 * @date 2024-04-11 16:28:25
 */
@Data
@ApiModel("三定-业务事项一件事关联表 返回实体-SdBusinessItemOneVO")
public class SdBusinessItemOneVO implements Serializable {

    private static final long serialVersionUID = 1381014080929350527L;

    /**
     * 业务事项主键
     */
    @ApiModelProperty(value = "业务事项主键")
    private String itemId;
    @ApiModelProperty(value = "业务事项名称")
    private String itemName;
    @ApiModelProperty(value = "业务事项说明")
    private String itemRemark;
    /**
     * 一件事主键
     */
    @ApiModelProperty(value = "一件事主键")
    private String oneId;
    @ApiModelProperty(value = "一件事名称")
    private String oneName;
    @ApiModelProperty(value = "一件事类型")
    private String oneType;
    @ApiModelProperty(value = "一件事类型名称")
    private String oneTypeName;
    @ApiModelProperty(value = "一件事描述")
    private String oneDescribe;
    @ApiModelProperty(value = "业务事项一件事关联主键")
    private String itemOneId;
}
