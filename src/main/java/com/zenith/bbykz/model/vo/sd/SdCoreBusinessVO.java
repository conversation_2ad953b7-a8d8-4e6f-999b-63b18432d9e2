package com.zenith.bbykz.model.vo.sd;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * <p>
 * 三定-核心业务 VO
 * </p>
 *
 * <AUTHOR>
 * @date 2024-04-11 16:28:25
 */
@Data
@ApiModel("三定-核心业务 返回实体-SdCoreBusinessVO")
public class SdCoreBusinessVO implements Serializable {

    private static final long serialVersionUID = 2501247206611533124L;

    /**
     * 主键id
     */
    @ApiModelProperty(value = "主键id")
    private String id;
    /**
     * 三定类型主键
     */
    @ApiModelProperty(value = "三定类型主键")
    private String moduleId;
    /**
     * 三定明细主键
     */
    @ApiModelProperty(value = "三定明细主键")
    private String moduleDetailId;
    /**
     * 细化职责主键
     */
    @ApiModelProperty(value = "细化职责主键")
    private String dutyDetailId;
    /**
     * 名称
     */
    @ApiModelProperty(value = "名称")
    private String title;
    /**
     * 描述
     */
    @ApiModelProperty(value = "描述")
    private String describe;
    /**
     * 备注
     */
    @ApiModelProperty(value = "备注")
    private String remark;
    /**
     * 排序号
     */
    @ApiModelProperty(value = "排序号")
    private Integer sort;
    /**
     *
     */
    @ApiModelProperty(value = "")
    private Date createTime;
    /**
     *
     */
    @ApiModelProperty(value = "")
    private String createUser;
    /**
     *
     */
    @ApiModelProperty(value = "")
    private Date updateTime;
    /**
     *
     */
    @ApiModelProperty(value = "")
    private String updateUser;
    /**
     *
     */
    @ApiModelProperty(value = "")
    private Integer isDelete;
    @ApiModelProperty(value = "业务事项数量")
    private Integer businessItemCount;
    @ApiModelProperty(value = "细化职责名称")
    private String dutyDetailName;
    @ApiModelProperty(value = "单位id")
    private String unitId;
    @ApiModelProperty(value = "单位名称")
    private String unitName;
    @ApiModelProperty(value = "单位层级")
    private String belong;
    private Integer businessItem;
}
