package com.zenith.bbykz.model.vo.bz.rybd;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.util.Date;

/**
* <p>
* 核编管理-动态信息维护-人员详情 VO
* </p>
*
* <AUTHOR>
* @date 2024-04-22 11:21:43
*/
@Data
@ApiModel("核编管理-动态信息维护-人员详情 返回实体-BzRyBdVO")
public class BzRyBdVO implements Serializable {

    private static final long serialVersionUID = 6470914984020131293L;

    /**
    *主键id
    */
    @ApiModelProperty(value = "主键id")
    private String id;
    /**
    *变动信息主键
    */
    @ApiModelProperty(value = "变动信息主键")
    private String unitBdId;
    /**
    *名称
    */
    @ApiModelProperty(value = "名称")
    private String name;
    /**
    *身份证
    */
    @ApiModelProperty(value = "身份证")
    private String idCard;
    /**
    *人员变动类型，BZ_RY_BD_TYPE
    */
    @ApiModelProperty(value = "人员变动类型，BZ_RY_BD_TYPE")
    private String bdType;
    private String bdTypeName;
    /**
    *现单位ID
    */
    @ApiModelProperty(value = "现单位ID")
    private String oldUnitId;
    /**
    *现单位层级码
    */
    @ApiModelProperty(value = "现单位层级码")
    private String oldUnitLevelCode;
    /**
    *现单位名称
    */
    @ApiModelProperty(value = "现单位名称")
    private String oldUnitName;
    /**
    *现处室ID
    */
    @ApiModelProperty(value = "现处室ID")
    private String oldDeptId;
    /**
    *现处室层级码
    */
    @ApiModelProperty(value = "现处室层级码")
    private String oldDeptLevelCode;
    /**
    *现处室层名称
    */
    @ApiModelProperty(value = "现处室层名称")
    private String oldDeptName;
    /**
    *现编制类型，BZ_BZLX
    */
    @ApiModelProperty(value = "现编制类型，BZ_BZLX")
    private String oldBzType;
    private String oldBzTypeName;
    /**
    *现用编类型，BZ_JFXX
    */
    @ApiModelProperty(value = "现用编类型，BZ_JFXX")
    private String oldYbType;
    private String oldYbTypeName;
    /**
    *现职务
    */
    @ApiModelProperty(value = "现职务")
    private String oldPostName;
    /**
    *现实名制单位code
    */
    @ApiModelProperty(value = "现实名制单位code")
    private String oldSmzUnitCode;
    /**
    *现单位统一社会信用代码
    */
    @ApiModelProperty(value = "现单位统一社会信用代码")
    private String oldCreditCode;
    /**
    *
    */
    @ApiModelProperty(value = "")
    private String newUnitId;
    /**
    *
    */
    @ApiModelProperty(value = "")
    private String newUnitLevelCode;
    /**
    *
    */
    @ApiModelProperty(value = "")
    private String newUnitName;
    /**
    *
    */
    @ApiModelProperty(value = "")
    private String newDeptId;
    /**
    *
    */
    @ApiModelProperty(value = "")
    private String newDeptLevelCode;
    /**
    *
    */
    @ApiModelProperty(value = "")
    private String newDeptName;
    /**
    *
    */
    @ApiModelProperty(value = "")
    private String newBzType;
    private String newBzTypeName;
    /**
    *
    */
    @ApiModelProperty(value = "")
    private String newYbType;
    private String newYbTypeName;
    /**
    *
    */
    @ApiModelProperty(value = "")
    private String newPostName;
    /**
    *
    */
    @ApiModelProperty(value = "")
    private String newSmzUnitCode;
    /**
    *
    */
    @ApiModelProperty(value = "")
    private String newCreditCode;
    /**
    *变动时间
    */
    @ApiModelProperty(value = "变动时间")
    private Date bdTime;
    /**
    *备注
    */
    @ApiModelProperty(value = "备注")
    private String remark;
    /**
    *
    */
    @ApiModelProperty(value = "")
    private Date createTime;
    /**
    *
    */
    @ApiModelProperty(value = "")
    private String createUser;
    /**
    *
    */
    @ApiModelProperty(value = "")
    private Date updateTime;
    /**
    *
    */
    @ApiModelProperty(value = "")
    private String updateUser;
    /**
    *
    */
    @ApiModelProperty(value = "")
    private Integer isDelete;
}
