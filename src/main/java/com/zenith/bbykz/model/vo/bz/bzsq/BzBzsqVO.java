package com.zenith.bbykz.model.vo.bz.bzsq;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * <p>
 * 核编管理-用编申请 VO
 * </p>
 *
 * <AUTHOR>
 * @date 2024-03-29 16:05:19
 */
@Data
@ApiModel("核编管理-用编申请 返回实体-BzBzsqVO")
public class BzBzsqVO implements Serializable {

    private static final long serialVersionUID = 1700343563536741815L;

    /**
     * 主键id
     */
    @ApiModelProperty(value = "主键id")
    private String id;
    /**
     * 所属区划
     */
    @ApiModelProperty(value = "所属区划")
    private String geocode;
    /**
     * 是否编办单位申请
     */
    @ApiModelProperty(value = "是否编办单位申请")
    private Integer isBb;
    /**
     *
     */
    @ApiModelProperty(value = "")
    private String orgId;
    /**
     * 提交单位层级码
     */
    @ApiModelProperty(value = "提交单位层级码")
    private String orgLevelCode;
    /**
     * 标题
     */
    @ApiModelProperty(value = "标题")
    private String title;
    /**
     * 申请单位名称
     */
    @ApiModelProperty(value = "申请单位名称")
    private String applyUnitName;
    /**
     * 申请时间
     */
    @ApiModelProperty(value = "申请时间")
    private Date applyTime;
    /**
     * 联系人
     */
    @ApiModelProperty(value = "联系人")
    private String concatName;
    /**
     * 联系电话
     */
    @ApiModelProperty(value = "联系电话")
    private String concatPhone;
    /**
     * 审批状态，字典表 BZ_HBZT
     */
    @ApiModelProperty(value = "审批状态，字典表 BZ_HBZT")
    private String status;
    private String statusName;

    @ApiModelProperty(value = "意见")
    private String opinion;
    /**
     * 有效期限
     */
    @ApiModelProperty(value = "有效期限")
    private Date deadline;
    /**
     * 批复单号
     */
    @ApiModelProperty(value = "批复单号")
    private String approvalNum;
    /**
     * 创建时间
     */
    @ApiModelProperty(value = "创建时间")
    private Date createTime;
    /**
     * 创建人
     */
    @ApiModelProperty(value = "创建人")
    private String createUser;
    /**
     * 修改时间
     */
    @ApiModelProperty(value = "修改时间")
    private Date updateTime;
    /**
     * 修改人
     */
    @ApiModelProperty(value = "修改人")
    private String updateUser;
    /**
     * 是否删除
     */
    @ApiModelProperty(value = "是否删除")
    private Integer isDelete;

    @ApiModelProperty(value = "编制明细")
    private List<BzBzsqDetailVO> detailList;
    @ApiModelProperty(value = "是否同意，1-是，0-否")
    private Integer isAgree;
    @ApiModelProperty(value = "合并ID")
    private String mergeId;
    @ApiModelProperty(value = "编制使用单位id")
    private String useBzUnitId;
    @ApiModelProperty(value = "编制使用单位名称")
    private String useBzUnitName;
    private String useBzUnitLevelCode;
    @ApiModelProperty(value = "备注")
    private String remark;
}
