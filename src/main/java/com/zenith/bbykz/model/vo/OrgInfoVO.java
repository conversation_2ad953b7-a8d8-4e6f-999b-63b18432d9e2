package com.zenith.bbykz.model.vo;

import com.baomidou.mybatisplus.annotation.TableField;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

/**
 * <p>
 * 机构信息 VO
 * </p>
 *
 * <AUTHOR>
 * @date 2023-09-19 14:29:03
 */
@Data
@ApiModel("机构信息 返回实体-OrgInfoVO")
public class OrgInfoVO {

    private static final long serialVersionUID = 1039792120906625110L;

    /**
     *
     */
    @ApiModelProperty(value = "")
    private String id;
    /**
     *
     */
    @ApiModelProperty(value = "")
    private String levelCode;
    /**
     * 机构名称
     */
    @ApiModelProperty(value = "机构名称")
    private String name;
    /**
     * 机构简称
     */
    @ApiModelProperty(value = "机构简称")
    private String shortName;
    /**
     *
     */
    @ApiModelProperty(value = "")
    private String parentCode;

    @ApiModelProperty(value = "")
    private String parentName;
    /**
     *
     */
    @ApiModelProperty(value = "")
    private String typeCode;

    /**
     * 排序
     */
    @ApiModelProperty(value = "排序")
    private Long sort;
    /**
     * 实名制机构表主键
     */
    @ApiModelProperty(value = "实名制机构表主键")
    private String jgsyCode;
    /**
     * 其他全称
     */
    @ApiModelProperty(value = "其他全称")
    private String jgsyOtherName;
    /**
     * 规范简称
     */
    @ApiModelProperty(value = "规范简称")
    private String gfjc;
    /**
     * 习惯简称
     */
    @ApiModelProperty(value = "习惯简称")
    private String xgjc;
    /**
     * 机构规格
     */
    @ApiModelProperty(value = "机构规格")
    private String jggg;
    /**
     * 机构性质
     */
    @ApiModelProperty(value = "机构性质")
    private String jgxz;
    /**
     * 系统类别
     */
    @ApiModelProperty(value = "系统类别")
    private String jgsySystemCode;
    /**
     * 机构类别
     */
    @ApiModelProperty(value = "机构类别")
    private String jglb;
    /**
     * 机构编码
     */
    @ApiModelProperty(value = "机构编码")
    private String jgbm;
    /**
     * 批准设立文号
     */
    @ApiModelProperty(value = "批准设立文号")
    private String docNo;
    /**
     * 批准设立时间
     */
    @ApiModelProperty(value = "批准设立时间")
    private Date pzslsj;
    /**
     * 是否园区管理机构
     */
    @ApiModelProperty(value = "是否园区管理机构")
    private String sfskfq;
    /**
     * 编制使用层级
     */
    @ApiModelProperty(value = "编制使用层级")
    private String jgsyBzsycj;
    /**
     * 统一社会信用代码
     */
    @ApiModelProperty(value = "统一社会信用代码")
    private String unifyCode;
    /**
     * 是否执法队伍
     */
    @ApiModelProperty(value = "是否执法队伍")
    private String jgsyIfzfdw;
    /**
     * 是否特殊机构: 0:否;1:是(非实名制机构)
     */
    @ApiModelProperty(value = "是否特殊机构: 0:否;1:是(非实名制机构)")
    private String jgsyIfts;
    /**
     * 是否编办机构: 0:否;1:是
     */
    @ApiModelProperty(value = "是否编办机构: 0:否;1:是")
    @TableField("is_bb_org")
    private Integer isBbOrg;
    /**
     * 是否编办机构: 0:否;1:是
     */
    @ApiModelProperty(value = "是否市级编办审核: 0:否;1:是")
    @TableField("is_sjbb_audit")
    private Integer isSjbbAudit;
    /**
     * 机构区划能新增: 0:否;1:是
     */
    @ApiModelProperty(value = "机构区划能新增: 0:否;1:是")
    @TableField("org_qh")
    private Integer orgQh;
    @ApiModelProperty(value = "父级id")
    private String parentId;
    @ApiModelProperty(value = "渝快政机构编码")
    private String orgCode;
    @ApiModelProperty(value = "渝快政机构编码")
    private String parentOrgCode;
    @ApiModelProperty(value = "区划代码")
    private String geocode;
    @ApiModelProperty(value = "所属层级")
    private String belong;
    @ApiModelProperty(value = "渝快政单位类型")
    private String unitType;
    private String unitTypeName;
    @ApiModelProperty(value = "统一社会信用代码")
    private String creditCode;
    @ApiModelProperty(value = "单位来源，1-事业单位，2-机关登记信息")
    private String unitSource;
}
