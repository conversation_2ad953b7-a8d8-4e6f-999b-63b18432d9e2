package com.zenith.bbykz.model.vo.sd;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * <p>
 * 三定-细化职责 VO
 * </p>
 *
 * <AUTHOR>
 * @date 2024-04-11 16:28:25
 */
@Data
@ApiModel("三定-细化职责 返回实体-SdDutyDetailAllVO")
public class SdDutyDetailAllVO implements Serializable {

    private static final long serialVersionUID = 581986937337234429L;

    /**
     * 主键id
     */
    @ApiModelProperty(value = "主键id")
    private String id;
    /**
     * 三定类型主键
     */
    @ApiModelProperty(value = "三定类型主键")
    private String moduleId;
    /**
     * 三定明细主键
     */
    @ApiModelProperty(value = "三定明细主键")
    private String moduleDetailId;
    /**
     * 名称
     */
    @ApiModelProperty(value = "名称")
    private String title;
    @ApiModelProperty(value = "承担处室")
    private String takeUnitName;
    @ApiModelProperty(value = "排序号")
    private Integer sort;
    @ApiModelProperty(value = "文号")
    private String recordNum;
    private List<SdCoreBusinessAllVO> children;
}
