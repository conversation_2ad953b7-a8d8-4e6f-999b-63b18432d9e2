package com.zenith.bbykz.model.vo.sd;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * <p>
 * 三定-三定机构关联关系 VO
 * </p>
 *
 * <AUTHOR>
 * @date 2024-04-11 16:28:25
 */
@Data
@ApiModel("三定-三定机构关联关系 返回实体-SdOrgRelationVO")
public class SdOrgRelationVO implements Serializable {

    private static final long serialVersionUID = 6976787107298194173L;

    /**
     * 主键id
     */
    // @ApiModelProperty(value = "主键id")
    // private String id;
    @ApiModelProperty(value = "单位名称")
    private String unitName;
    @ApiModelProperty(value = "所属层级")
    private String unitBelong;
    /**
     * 单位ID
     */
    @ApiModelProperty(value = "单位ID")
    private String unitId;
    /**
     * 单位层级码
     */
    @ApiModelProperty(value = "单位层级码")
    private String unitLevelCode;
    /**
     * 三定类型数量
     */
    @ApiModelProperty(value = "三定类型数量")
    private Integer moduleCount;
    /**
     * 三定明细数量
     */
    @ApiModelProperty(value = "三定明细数量")
    private Integer moduleDetailCount;
    /**
     * 细化职责数量
     */
    @ApiModelProperty(value = "细化职责数量")
    private Integer dutyDetailCount;
    /**
     * 核心业务数量
     */
    @ApiModelProperty(value = "核心业务数量")
    private Integer coreBusinessCount;
    /**
     * 业务事项数量
     */
    @ApiModelProperty(value = "业务事项数量")
    private Integer businessItemCount;
    /**
     * 一件事数量
     */
    @ApiModelProperty(value = "一件事数量")
    private Integer oneThingCount;

}
