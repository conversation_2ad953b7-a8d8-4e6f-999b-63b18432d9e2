package com.zenith.bbykz.model.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <p>
 * 用编管理人员信息 VO
 * </p>
 *
 * <AUTHOR>
 * @date 2023-09-13
 */
@Data
@ApiModel("用编管理附件人员信息 返回实体-StaffingManagementPersonsVO")
public class StaffingManagementPersonsVO {

    private static final long serialVersionUID = 8252279852303600827L;

    /**
     * 主键id
     */
    @ApiModelProperty(value = "主键id")
    private String id;
    /**
     * 用编管理id
     */
    @ApiModelProperty(value = "用编管理id")
    private String staffingManagementId;
    /**
     * 姓名
     */
    @ApiModelProperty(value = "姓名")
    private String name;
    /**
     * 性别
     */
    @ApiModelProperty(value = "性别")
    private String sex;
    /**
     * 年龄
     */
    @ApiModelProperty(value = "年龄")
    private Integer age;
    /**
     * 学历学位
     */
    @ApiModelProperty(value = "学历学位")
    private String education;
    /**
     * 现工作单位及职务职级
     */
    @ApiModelProperty(value = "现工作单位及职务职级")
    private String workUnitDuties;
    /**
     * 拟安排工作岗位
     */
    @ApiModelProperty(value = "拟安排工作岗位")
    private String proposedJob;
    /**
     * 备注
     */
    @ApiModelProperty(value = "备注")
    private String comments;
}
