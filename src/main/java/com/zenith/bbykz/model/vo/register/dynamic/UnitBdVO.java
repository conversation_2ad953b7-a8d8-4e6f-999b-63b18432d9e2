package com.zenith.bbykz.model.vo.register.dynamic;

import com.baomidou.mybatisplus.annotation.TableField;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * <p>
 * 登记信息-预警感知-信息变动 VO
 * </p>
 *
 * <AUTHOR>
 * @date 2024-04-09 11:50:41
 */
@Data
@ApiModel("登记信息-预警感知-信息变动 返回实体-UnitBdVO")
public class UnitBdVO implements Serializable {

    private static final long serialVersionUID = 4930086526823319141L;

    /**
     * 主键id
     */
    @ApiModelProperty(value = "主键id")
    private String id;
    /**
     * 变动类型
     */
    @ApiModelProperty(value = "变动类型")
    private String type;
    private String typeName;
    /**
     * 区划
     */
    @ApiModelProperty(value = "区划")
    private String geocode;
    /**
     * 单位ID
     */
    @ApiModelProperty(value = "单位ID")
    private String unitId;
    /**
     * 单位层级码
     */
    @ApiModelProperty(value = "单位层级码")
    private String unitLevelCode;
    /**
     * 单位名称
     */
    @ApiModelProperty(value = "单位名称")
    private String unitName;
    /**
     * 原信息
     */
    @ApiModelProperty(value = "原信息")
    private String oldInfo;
    /**
     * 新信息
     */
    @ApiModelProperty(value = "新信息")
    private String newInfo;
    /**
     * 是否处理
     */
    @ApiModelProperty(value = "是否处理")
    private Integer isHandle;
    /**
     * 处理时间
     */
    @ApiModelProperty(value = "处理时间")
    private Date handleTime;
    /**
     * 备注
     */
    @ApiModelProperty(value = "备注")
    private String remark;
    /**
     * 来源类型，1-自建，2-同步
     */
    @ApiModelProperty(value = "来源类型，1-自建，2-同步")
    private Integer sourceType;
    /**
     * 来源类型，1-自建，2-同步
     */
    @ApiModelProperty(value = "来源类型，描述")
    private String sourceName;
    /**
     * 变动时间
     */
    @ApiModelProperty(value = "变动时间")
    private Date bdTime;
    /**
     * 是否发送,0-未发送，1-发送，发送后所属单位才能看到预警信息
     */
    @ApiModelProperty(value = "是否发送,0-未发送，1-发送，发送后所属单位才能看到预警信息")
    private Integer isSend;
    @ApiModelProperty(value = "反馈内容")
    private String backContent;
    @ApiModelProperty(value = "反馈时间")
    private Date backTime;
    @ApiModelProperty(value = "变动状态消息状态，1-已感知，2-已发送，3-已查看，4-已处理,5-已完成")
    private String state;
    private String stateName;
    @ApiModelProperty(value = "统一社会信用代码")
    private String creditCode;
    @ApiModelProperty(value = "单位来源，1-事业单位，2-机关登记信息")
    private String unitSource;
}
