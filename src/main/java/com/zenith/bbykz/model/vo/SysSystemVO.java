package com.zenith.bbykz.model.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <p>
 * sys_system VO
 * </p>
 *
 * <AUTHOR>
 * @date 2024-02-29 11:43:55
 */
@Data
@ApiModel("sys_system 返回实体-SysSystemVO")
public class SysSystemVO {

    private static final long serialVersionUID = 2974859360037287489L;

    /**
     *
     */
    @ApiModelProperty(value = "")
    private String id;
    /**
     *
     */
    @ApiModelProperty(value = "")
    private String name;
    /**
     * 排序
     */
    @ApiModelProperty(value = "排序")
    private Integer sort;
    /**
     * 备注
     */
    @ApiModelProperty(value = "备注")
    private String remark;
}
