package com.zenith.bbykz.model.vo.register.train;

import com.zenith.bbykz.model.base.SelectModel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * <p>
 * 登记信息-培训管理-课程管理 VO
 * </p>
 *
 * <AUTHOR>
 * @date 2024-03-05 15:44:53
 */
@Data
@ApiModel("登记信息-培训管理-课程管理 返回实体-RegisterTrainCourseVO")
public class RegisterTrainCourseVO implements Serializable {

    private static final long serialVersionUID = 1687506472800108636L;
    // @ApiModelProperty(value = "课件库集合")
    // List<RegisterTrainCourseDetailVO> detailList;
    /**
     * 主键id
     */
    @ApiModelProperty(value = "主键id")
    private String id;
    /**
     * 名称
     */
    @ApiModelProperty(value = "名称")
    private String name;
    /**
     * 阅读范围控制
     */
    @ApiModelProperty(value = "阅读范围控制,角色id，多个用逗号分离")
    private String scopeId;
    private List<SelectModel> scopeList;
    /**
     * 分类ID
     */
    @ApiModelProperty(value = "分类ID")
    private String typeId;
    /**
     * 分类名称
     */
    @ApiModelProperty(value = "分类名称")
    private String typeName;
    /**
     * 描述
     */
    @ApiModelProperty(value = "描述")
    private String describe;
    /**
     * 截至时间
     */
    @ApiModelProperty(value = "截至时间")
    private Date deadline;
    /**
     * 创建时间
     */
    @ApiModelProperty(value = "创建时间")
    private Date createTime;
    /**
     * 创建人
     */
    @ApiModelProperty(value = "创建人")
    private String createUser;
    /**
     * 修改时间
     */
    @ApiModelProperty(value = "修改时间")
    private Date updateTime;
    /**
     * 修改人
     */
    @ApiModelProperty(value = "修改人")
    private String updateUser;
    /**
     * 是否删除
     */
    @ApiModelProperty(value = "是否删除")
    private Integer isDelete;
    @ApiModelProperty(value = "课件库集合")
    // List<RegisterTrainCourseDetailDTO> detailList;
    List<RegisterTrainFileVO> detailList;
}
