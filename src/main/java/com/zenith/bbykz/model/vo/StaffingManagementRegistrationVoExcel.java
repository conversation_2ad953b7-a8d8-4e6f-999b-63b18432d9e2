package com.zenith.bbykz.model.vo;

import cn.afterturn.easypoi.excel.annotation.Excel;
import lombok.Data;

/**
 * @description: 用编统计导出
 * @author: yanggm
 * @date: 2023/10/7 13:38
 */
@Data
public class StaffingManagementRegistrationVoExcel {

    /**
     * 序号
     */
    @Excel(name = "序号", orderNum = "1", width = 10, isImportField = "true_st")
    private Integer index;

    /**
     * 申请单位
     */
    @Excel(name = "申请单位", orderNum = "2", width = 10, isImportField = "true_st")
    private String applicantUnit;

    /**
     * 申请数量
     */
    @Excel(name = "申请数量", orderNum = "3", width = 10, isImportField = "true_st")
    private Integer employeesNumber;

    /**
     * 使用数量
     */
    @Excel(name = "使用数量", orderNum = "4", width = 10, isImportField = "true_st")
    private Integer usedNumber;

    /**
     * 剩余数量
     */
    @Excel(name = "剩余数量", orderNum = "5", width = 10, isImportField = "true_st")
    private Integer remainingNumber;

}
