package com.zenith.bbykz.model.vo.bz.bzsq;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * <p>
 * 核编管理-用编审核 VO
 * </p>
 *
 * <AUTHOR>
 * @date 2024-04-02 11:51:22
 */
@Data
@ApiModel("核编管理-用编审核 返回实体-BzBzsqFlowVO")
public class BzBzsqFlowVO implements Serializable {

    private static final long serialVersionUID = 5994955081574730077L;

    /**
     * 主键id
     */
    @ApiModelProperty(value = "主键id")
    private String id;
    /**
     * 编制申请主键id
     */
    @ApiModelProperty(value = "编制申请主键id")
    private String bzsqId;
    /**
     * 当前状态
     */
    @ApiModelProperty(value = "当前状态")
    private String bzsqStatus;
    /**
     * 状态描述
     */
    @ApiModelProperty(value = "状态描述")
    private String statusDesc;
    /**
     * 是否合并审核
     */
    @ApiModelProperty(value = "是否合并审核")
    private Integer isMerge;
    /**
     * 意见
     */
    @ApiModelProperty(value = "意见")
    private String opinion;
    /**
     * 是否同意
     */
    @ApiModelProperty(value = "是否同意")
    private Integer isAgree;
    /**
     * 是否结束
     */
    @ApiModelProperty(value = "是否结束")
    private Integer isFinish;
    /**
     * 创建时间
     */
    @ApiModelProperty(value = "创建时间")
    private Date createTime;
    /**
     * 创建人
     */
    @ApiModelProperty(value = "创建人")
    private String createUser;
    /**
     * 修改时间
     */
    @ApiModelProperty(value = "修改时间")
    private Date updateTime;
    /**
     * 修改人
     */
    @ApiModelProperty(value = "修改人")
    private String updateUser;
    /**
     * 是否删除
     */
    @ApiModelProperty(value = "是否删除")
    private Integer isDelete;
}
