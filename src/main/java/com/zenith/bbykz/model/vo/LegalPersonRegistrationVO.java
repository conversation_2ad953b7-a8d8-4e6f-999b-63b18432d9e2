package com.zenith.bbykz.model.vo;

import lombok.Data;

import java.util.Date;

/**
 * 事业单位法人登记
 *
 * <AUTHOR>
 * @date 2023/9/13
 */
@Data
public class LegalPersonRegistrationVO {

    private String unifiedSocialCreditCode;

    private String certificateNo;

    private String name;

    private String legalRepresentative;

    private String residence;

    private String creater;

    private Date createTime;

    private Date uptimeTime;

    private Date deleteTime;

    private Integer isDelete;

    private String organizer;

    private String fundingSource;

    private String initialFunds;

    private String purpose;

    private String status;

    private String secondName;

    private String thirdName;

    private String otherNames;

    private String organizerCategory;

    private String approvalAuthority;

    private String practicingLicenseCertificate;

    private String certificateValidityStartTime;

    private String certificateValidityEndTime;

    private String contacts;

    private String telephone;

    private String postalCode;

    private String industryCategory;

    private String registrationAuthorityCode;

    private String annualReport;

    private String annualReportTime;

    private String registrationManagementAuthority;

    private String bdId;

}
