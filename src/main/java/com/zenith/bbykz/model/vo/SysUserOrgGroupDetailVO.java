package com.zenith.bbykz.model.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.util.Date;

/**
 * <p>
 * 用户机构组详情 VO
 * </p>
 *
 * <AUTHOR>
 * @date 2024-03-13 10:46:03
 */
@Data
@ApiModel("用户机构组详情 返回实体-SysUserOrgGroupDetailVO")
public class SysUserOrgGroupDetailVO implements Serializable {

    private static final long serialVersionUID = 2074934574363032661L;

    /**
     * 主键
     */
    @ApiModelProperty(value = "主键")
    private String id;
    /**
     * 用户机构组
     */
    @ApiModelProperty(value = "用户机构组")
    private String userOrgGroupId;
    /**
     * 单位ID
     */
    @ApiModelProperty(value = "单位ID")
    private String orgId;
    /**
     * 层级码
     */
    @ApiModelProperty(value = "层级码")
    private String orgLevelCode;
}
