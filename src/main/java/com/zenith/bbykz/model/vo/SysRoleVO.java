package com.zenith.bbykz.model.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * <p>
 * sys_role VO
 * </p>
 *
 * <AUTHOR>
 * @date 2023-09-14 17:14:26
 */
@Data
@ApiModel("sys_role 返回实体-SysRoleVO")
public class SysRoleVO {

    private static final long serialVersionUID = 7418241240504842287L;

    /**
     *
     */
    @ApiModelProperty(value = "")
    private String id;
    /**
     *
     */
    @ApiModelProperty(value = "")
    private String name;
    /**
     *
     */
    @ApiModelProperty(value = "")
    private Integer isDefault;
    /**
     *
     */
    @ApiModelProperty(value = "")
    private String systemCode;

    private List<String> menus;

    @ApiModelProperty(value = "父级角色id")
    private String parentId;
    @ApiModelProperty(value = "创建人")
    private String createUser;
    @ApiModelProperty(value = "创建时间")
    private Date createTime;
}
