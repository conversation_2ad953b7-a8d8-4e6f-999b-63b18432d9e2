package com.zenith.bbykz.model.vo.sd;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2024/5/22 10:17
 */
@Data
@ApiModel("三定-职能职责分析明细 返回实体-SdModuleCountListVO")
public class SdModuleCountListVO {
    @ApiModelProperty("职能职责明细数量")
    private String unitId;
    @ApiModelProperty("部门职能职责明细数量")
    private String unitName;
    @ApiModelProperty("涉及单位数量")
    private String unitLevelCode;
    @ApiModelProperty("涉及单位数量")
    private Integer moduleCount;
    @ApiModelProperty("涉及单位数量")
    private Integer moduleDetailCount;
    // @ApiModelProperty("涉及单位数量")
    // private Integer moduleDetailCount;
}
