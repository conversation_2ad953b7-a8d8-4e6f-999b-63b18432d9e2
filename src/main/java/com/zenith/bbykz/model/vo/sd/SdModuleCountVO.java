package com.zenith.bbykz.model.vo.sd;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2024/5/22 10:17
 */
@Data
@ApiModel("三定-职能职责分析 返回实体-SdModuleCountVO")
public class SdModuleCountVO {
    @ApiModelProperty("职能职责明细数量")
    private Integer moduleDetailCount;
    @ApiModelProperty("部门职能职责明细数量")
    private Integer dutyDetailCount;
    @ApiModelProperty("涉及单位数量")
    private Integer involveUnitCount;
    @ApiModelProperty("三定分类统计")
    private List<SdModuleTypeCountVO> typeCountList;
}
