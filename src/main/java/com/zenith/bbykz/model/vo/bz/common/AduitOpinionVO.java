package com.zenith.bbykz.model.vo.bz.common;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.util.Date;

/**
* <p>
* 核编管理-常用审批意见 VO
* </p>
*
* <AUTHOR>
* @date 2024-05-17 11:10:37
*/
@Data
@ApiModel("核编管理-常用审批意见 返回实体-AduitOpinionVO")
public class AduitOpinionVO implements Serializable {

    private static final long serialVersionUID = 6845722146665811239L;

    /**
    *主键id
    */
    @ApiModelProperty(value = "主键id")
    private String id;
    /**
    *模块名称
    */
    @ApiModelProperty(value = "模块名称")
    private String module;
    /**
    *意见
    */
    @ApiModelProperty(value = "意见")
    private String name;
    /**
    *排序
    */
    @ApiModelProperty(value = "排序")
    private Integer sort;
    /**
    *创建时间
    */
    @ApiModelProperty(value = "创建时间")
    private Date createTime;
    /**
    *创建人
    */
    @ApiModelProperty(value = "创建人")
    private String createUser;
    /**
    *修改时间
    */
    @ApiModelProperty(value = "修改时间")
    private Date updateTime;
    /**
    *修改人
    */
    @ApiModelProperty(value = "修改人")
    private String updateUser;
    /**
    *是否删除
    */
    @ApiModelProperty(value = "是否删除")
    private Integer isDelete;
}
