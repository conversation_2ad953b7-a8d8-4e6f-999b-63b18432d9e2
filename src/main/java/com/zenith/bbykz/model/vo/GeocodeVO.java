package com.zenith.bbykz.model.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * <p>
 * 区划代码 VO
 * </p>
 *
 * <AUTHOR>
 * @date 2024-03-13 09:52:37
 */
@Data
@ApiModel("区划代码 返回实体-GeocodeVO")
public class GeocodeVO implements Serializable {

    private static final long serialVersionUID = 3011011668419589204L;

    /**
     * code
     */
    @ApiModelProperty(value = "code")
    private String code;
    /**
     * 名称
     */
    @ApiModelProperty(value = "名称")
    private String name;
    /**
     * 类型，1-全国，2-省，3-市，4-区，5-区县
     */
    @ApiModelProperty(value = "类型，1-全国，2-省，3-市，4-区，5-区县")
    private String type;
    /**
     * 排序
     */
    @ApiModelProperty(value = "排序")
    private Integer sort;
    /**
     * 机构id
     */
    @ApiModelProperty(value = "机构id")
    private String orgId;
    /**
     * 层级码
     */
    @ApiModelProperty(value = "层级码")
    private String levelCode;
}
