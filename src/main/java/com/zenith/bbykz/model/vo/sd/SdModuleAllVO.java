package com.zenith.bbykz.model.vo.sd;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * <p>
 * 三定-三定类型 VO
 * </p>
 *
 * <AUTHOR>
 * @date 2024-04-11 16:28:25
 */
@Data
@ApiModel("三定-三定类型 返回实体-SdModuleAllVO")
public class SdModuleAllVO implements Serializable {

    private static final long serialVersionUID = 8288275434330607280L;

    /**
     * 主键id
     */
    @ApiModelProperty(value = "主键id")
    private String id;
    /**
     * 名称
     */
    @ApiModelProperty(value = "名称")
    private String title;
    @ApiModelProperty(value = "排序号")
    private Integer sort;

    private List<SdModuleDetailAllVO> children;
}
