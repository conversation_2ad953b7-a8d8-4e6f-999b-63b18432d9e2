package com.zenith.bbykz.model.vo.sd;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * <p>
 * 三定-三定明细 VO
 * </p>
 *
 * <AUTHOR>
 * @date 2024-04-11 16:28:25
 */
@Data
@ApiModel("三定-三定明细 返回实体-SdModuleDetailAllVO")
public class SdModuleDetailAllVO implements Serializable {

    private static final long serialVersionUID = 1868779566150536740L;

    /**
     * 主键id
     */
    @ApiModelProperty(value = "主键id")
    private String id;
    /**
     * 三定类型主键
     */
    @ApiModelProperty(value = "三定类型主键")
    private String moduleId;
    /**
     * 名称
     */
    @ApiModelProperty(value = "名称")
    private String title;
    @ApiModelProperty(value = "排序号")
    private Integer sort;
    @ApiModelProperty(value = "文号")
    private String recordNum;
    private List<SdDutyDetailAllVO> children;
}
