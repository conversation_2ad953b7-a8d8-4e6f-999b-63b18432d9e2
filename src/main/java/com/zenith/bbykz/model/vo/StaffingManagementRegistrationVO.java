package com.zenith.bbykz.model.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

/**
 * <p>
 * 用编管理登记表 VO
 * </p>
 *
 * <AUTHOR>
 * @date 2023-09-28 14:28:24
 */
@Data
@ApiModel("用编管理登记表 返回实体-StaffingManagementRegistrationVO")
public class StaffingManagementRegistrationVO {

    private static final long serialVersionUID = 234011944333493294L;

    /**
     * 主键id
     */
    @ApiModelProperty(value = "主键id")
    private String id;
    /**
     * 用编管理
     */
    @ApiModelProperty(value = "用编管理")
    private String staffingManagementId;
    /**
     * 登记时间
     */
    @ApiModelProperty(value = "登记时间")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date registrationTime;
    /**
     * 使用数量
     */
    @ApiModelProperty(value = "使用数量")
    private Integer usedNumber;

    /**
     * 备注
     */
    @ApiModelProperty(value = "备注")
    private String comments;
}
