package com.zenith.bbykz.model.vo;

import com.efficient.system.model.vo.DictCodeVO;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * <p>
 * 用编管理 VO
 * </p>
 *
 * <AUTHOR>
 * @date 2023-09-13
 */
@Data
@ApiModel("用编管理 返回实体-StaffingManagementVO")
public class StaffingManagementVO {

    private static final long serialVersionUID = 51468325193363612L;

    /**
     * 主键id
     */
    @ApiModelProperty(value = "主键id")
    private String id;
    /**
     * 标题
     */
    @ApiModelProperty(value = "标题")
    private String title;
    /**
     * 申请单位
     */
    @ApiModelProperty(value = "申请单位")
    private String applicantUnit;
    /**
     * 编制类型
     */
    @ApiModelProperty(value = "编制类型")
    private DictCodeVO dictCode;
    /**
     * 用编数量
     */
    @ApiModelProperty(value = "用编数量")
    private Integer employeesNumber;
    /**
     * 入编时间
     */
    @ApiModelProperty(value = "入编时间")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date enrollmentTime;
    /**
     * 联系人
     */
    @ApiModelProperty(value = "联系人")
    private String contacts;
    /**
     * 联系电话
     */
    @ApiModelProperty(value = "联系电话")
    private String telephone;
    /**
     * 申请时间
     */
    @ApiModelProperty(value = "申请时间")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date applicatTime;
    /**
     * 状态
     */
    @ApiModelProperty(value = "状态")
    private String status;
    /**
     * 申请人
     */
    @ApiModelProperty(value = "申请人")
    private String applicatPerson;

    /**
     * 用编管理附件人员信息
     */
    @ApiModelProperty(value = "用编管理附件人员信息")
    private List<StaffingManagementPersonsVO> persons;

    /**
     * 附件信息
     */
    @ApiModelProperty(value = "附件信息")
    private List<StaffingManagementFilesVO> filesList;

    /**
     * 核编单号
     */
    @ApiModelProperty(value = "核编单号")
    private String approvalNumber;

    /**
     * 意见
     */
    @ApiModelProperty(value = "意见")
    private String option;

    /**
     * 用编登记列表
     */
    @ApiModelProperty(value = "用编登记列表")
    private List<StaffingManagementRegistrationVO> registrationList;

}
