package com.zenith.bbykz.model.vo.efficient;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * <p>
 * 系统消息通知 VO
 * </p>
 *
 * <AUTHOR>
 * @date 2024-03-18 15:07:00
 */
@Data
@ApiModel("系统消息通知 返回实体-SysNotifyVO")
public class SysNotifyVO implements Serializable {

    private static final long serialVersionUID = 1079325838411542598L;

    /**
     * 系统消息通知 ID
     */
    @ApiModelProperty(value = "系统消息通知 ID")
    private String id;
    /**
     * 业务主键
     */
    @ApiModelProperty(value = "业务主键")
    private String bizId;
    /**
     * 批次号
     */
    @ApiModelProperty(value = "批次号")
    private String batchNum;

    /**
     * 标题
     */
    @ApiModelProperty(value = "标题")
    private String title;

    /**
     * 消息状态，1-待发送，2-已发送，3-已查看，4-已处理
     */
    @ApiModelProperty(value = "消息状态，1-待发送，2-已发送，3-已查看，4-已处理")
    private String state;
    private String stateName;
    /**
     * 创建时间
     */
    @ApiModelProperty(value = "创建时间")
    private Date createTime;
    /**
     * 业务类型
     */
    @ApiModelProperty(value = "业务类型")
    private String bizType;
    /**
     * 创建单位id
     */
    @ApiModelProperty(value = "创建单位id")
    private String createUnitId;
    /**
     * 创建单位名称
     */
    @ApiModelProperty(value = "创建单位名称")
    private String createUnitName;
    /**
     * 接收单位ID
     */
    @ApiModelProperty(value = "接收单位ID")
    private String recipientUnitId;
    /**
     * 接收单位名称
     */
    @ApiModelProperty(value = "接收单位名称")
    private String recipientUnitName;
    /**
     * 查看时间
     */
    @ApiModelProperty(value = "查看时间")
    private Date readTime;
    /**
     * 接收单位名称
     */
    @ApiModelProperty(value = "处理时间")
    private Date handleTime;
}
