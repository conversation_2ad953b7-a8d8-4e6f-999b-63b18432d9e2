package com.zenith.bbykz.model.vo.bz.bzsq;

import io.swagger.annotations.ApiModel;
import lombok.Data;

import java.io.Serializable;

/**
 * <p>
 * 核编管理-用编申请 VO
 * </p>
 *
 * <AUTHOR>
 * @date 2024-03-29 16:05:19
 */
@Data
@ApiModel("核编管理-用编申请 返回实体-BzBzsqDwonVO")
public class BzBzsqDownVO implements Serializable {

    private static final long serialVersionUID = 1700343563536741815L;

    private String id;
    /**
     * 批复号
     */
    private String approvalNum;
    /**
     * 申请单位
     */
    private String applyUnitName;
    /**
     * 用编单位
     */
    private String useBzUnitName;
    /**
     * 用编量
     */
    private String applyNum;
    /**
     * 审核单位
     */
    private String auditUnitName;
    /**
     * 审核人
     */
    private String auditUserId;
    /**
     * 审核人
     */
    private String auditTime;
}
