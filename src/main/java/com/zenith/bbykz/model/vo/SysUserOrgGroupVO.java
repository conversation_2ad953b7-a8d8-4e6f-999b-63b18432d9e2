package com.zenith.bbykz.model.vo;

import com.zenith.bbykz.model.dto.SysUserOrgGroupDetailDTO;
import com.zenith.bbykz.model.entity.SysUserOrgGroupDetail;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * <p>
 * 用户机构组 VO
 * </p>
 *
 * <AUTHOR>
 * @date 2024-03-13 10:48:03
 */
@Data
@ApiModel("用户机构组 返回实体-SysUserOrgGroupVO")
public class SysUserOrgGroupVO implements Serializable {

    private static final long serialVersionUID = 8967185709493996888L;

    /**
     *
     */
    @ApiModelProperty(value = "")
    private String id;
    /**
     * 名称
     */
    @ApiModelProperty(value = "名称")
    private String name;
    /**
     * 系统ID
     */
    @ApiModelProperty(value = "系统ID")
    private String systemId;
    /**
     * 用户id
     */
    @ApiModelProperty(value = "用户id")
    private String userId;
    /**
     * 备注
     */
    @ApiModelProperty(value = "备注")
    private String remark;
    @ApiModelProperty(value = "机构集合")
    private List<SysUserOrgGroupDetailDTO> detailList;
}
