package com.zenith.bbykz.model.vo.sd;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * <p>
 * 三定-细化职责 VO
 * </p>
 *
 * <AUTHOR>
 * @date 2024-04-11 16:28:25
 */
@Data
@ApiModel("三定-细化职责 返回实体-SdDutyDetailVO")
public class SdDutyDetailVO implements Serializable {

    private static final long serialVersionUID = 581986937337234429L;

    /**
     * 主键id
     */
    @ApiModelProperty(value = "主键id")
    private String id;
    /**
     * 三定类型主键
     */
    @ApiModelProperty(value = "三定类型主键")
    private String moduleId;
    /**
     * 三定明细主键
     */
    @ApiModelProperty(value = "三定明细主键")
    private String moduleDetailId;
    /**
     * 承担处室id
     */
    @ApiModelProperty(value = "承担处室id")
    private String takeUnitId;
    /**
     * 承担处室层级码
     */
    @ApiModelProperty(value = "承担处室层级码")
    private String takeUnitLevelCode;
    /**
     * 承担处室名称
     */
    @ApiModelProperty(value = "承担处室名称")
    private String takeUnitName;
    /**
     * 名称
     */
    @ApiModelProperty(value = "名称")
    private String title;
    /**
     * 描述
     */
    @ApiModelProperty(value = "描述")
    private String describe;
    /**
     * 备注
     */
    @ApiModelProperty(value = "备注")
    private String remark;
    /**
     * 排序号
     */
    @ApiModelProperty(value = "排序号")
    private Integer sort;
    /**
     *
     */
    @ApiModelProperty(value = "")
    private Date createTime;
    /**
     *
     */
    @ApiModelProperty(value = "")
    private String createUser;
    /**
     *
     */
    @ApiModelProperty(value = "")
    private Date updateTime;
    /**
     *
     */
    @ApiModelProperty(value = "")
    private String updateUser;
    /**
     *
     */
    @ApiModelProperty(value = "")
    private Integer isDelete;
    /**
     * 文号
     */
    @ApiModelProperty(value = "文号")
    private String recordNum;
    @ApiModelProperty(value = "单位层级")
    private String unitBelong;
}
