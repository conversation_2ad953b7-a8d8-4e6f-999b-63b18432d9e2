package com.zenith.bbykz.model.vo.bz.common;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.util.Date;

/**
* <p>
* SYSTEMCODE VO
* </p>
*
* <AUTHOR>
* @date 2024-08-27 15:52:58
*/
@Data
@ApiModel("SYSTEMCODE 返回实体-SystemcodeVO")
public class SystemcodeVO implements Serializable {

    private static final long serialVersionUID = 7691606792916126465L;

    /**
    *system_code
    */
    @ApiModelProperty(value = "system_code")
    private String systemCode;
    /**
    *system_code_name
    */
    @ApiModelProperty(value = "system_code_name")
    private String systemCodeName;
    /**
    *system_code_type
    */
    @ApiModelProperty(value = "system_code_type")
    private String systemCodeType;
    /**
    *system_code_desp
    */
    @ApiModelProperty(value = "system_code_desp")
    private String systemCodeDesp;
    /**
    *system_code_standby1
    */
    @ApiModelProperty(value = "system_code_standby1")
    private String systemCodeStandby1;
    /**
    *system_code_order
    */
    @ApiModelProperty(value = "system_code_order")
    private Integer systemCodeOrder;
}
