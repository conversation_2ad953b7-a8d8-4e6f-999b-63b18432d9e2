package com.zenith.bbykz.model.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;

/**
 * <p>
 * sys_user_role VO
 * </p>
 *
 * <AUTHOR>
 * @date 2023-09-14 17:14:26
 */
@Data
@ApiModel("sys_user_role 返回实体-SysUserRoleVO")
public class SysUserRoleVO {

    private static final long serialVersionUID = 4494398475338270304L;

    /**
     *
     */
    @ApiModelProperty(value = "")
    private String id;
    /**
     *
     */
    @ApiModelProperty(value = "")
    private String userId;
    /**
     *
     */
    @ApiModelProperty(value = "")
    private String roleId;
    /**
     *
     */
    @ApiModelProperty(value = "")
    private String systemCode;
    @ApiModelProperty(value = "")
    private String userSystemId;
}
