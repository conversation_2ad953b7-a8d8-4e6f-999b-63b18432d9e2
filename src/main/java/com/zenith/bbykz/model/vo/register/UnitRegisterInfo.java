package com.zenith.bbykz.model.vo.register;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 * @since 2024/5/7 16:43
 */
@Data
public class UnitRegisterInfo {
    @ApiModelProperty("主键")
    private String id;
    @ApiModelProperty("名称")
    private String name;
    @ApiModelProperty("统一社会信用代码")
    private String creditCode;
    @ApiModelProperty("法定代表人")
    private String legalRepresentative;
    @ApiModelProperty("经费来源")
    private String fundingSource;
    @ApiModelProperty("开办资金")
    private String initialFunds;
    @ApiModelProperty("举办单位")
    private String organizer;
    @ApiModelProperty("宗旨和业务范围")
    private String purpose;
    @ApiModelProperty("有效期限-开始时间")
    private String validStartDate;
    @ApiModelProperty("有效期限-结束时间")
    private String validEndDate;
    @ApiModelProperty("注册地址")
    private String address;

}
