package com.zenith.bbykz.model.vo.bz.qx;

import com.efficient.file.model.vo.FileVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * <p>
 * 核编管理-用编管理-区县用编申请计划 VO
 * </p>
 *
 * <AUTHOR>
 * @date 2024-05-06 17:05:40
 */
@Data
@ApiModel("核编管理-用编管理-区县用编申请计划 返回实体-BzQxBzsqVO")
public class BzQxBzsqVO implements Serializable {

    private static final long serialVersionUID = 9090992287347590686L;

    /**
     * 主键id
     */
    @ApiModelProperty(value = "主键id")
    private String id;
    /**
     * 所属区县
     */
    @ApiModelProperty(value = "所属区县")
    private String geocode;
    /**
     * 录入单位ID
     */
    @ApiModelProperty(value = "录入单位ID")
    private String recordUnitId;
    /**
     * 录入单位层级码
     */
    @ApiModelProperty(value = "录入单位层级码")
    private String recordUnitLevelCode;
    /**
     * 录入单位名称
     */
    @ApiModelProperty(value = "录入单位名称")
    private String recordUnitName;
    /**
     * 标题
     */
    @ApiModelProperty(value = "标题")
    private String title;
    /**
     * 申请单位ID
     */
    @ApiModelProperty(value = "申请单位ID")
    private String applyUnitId;
    /**
     * 申请单位名称
     */
    @ApiModelProperty(value = "申请单位名称")
    private String applyUnitName;
    /**
     * 申请单位层级码
     */
    @ApiModelProperty(value = "申请单位层级码")
    private String applyUnitLevelCode;
    /**
     * 申请时间
     */
    @ApiModelProperty(value = "申请时间")
    private Date applyTime;
    /**
     * 联系人姓名
     */
    @ApiModelProperty(value = "联系人姓名")
    private String contactName;
    /**
     * 联系人电话
     */
    @ApiModelProperty(value = "联系人电话")
    private String contactPhone;
    /**
     * 审核状态
     */
    @ApiModelProperty(value = "审核状态")
    private String status;
    private String statusName;
    @ApiModelProperty(value = "审核理由")
    private String checkReason;
    /**
     * 审核时间
     */
    @ApiModelProperty(value = "审核时间")
    private Date checkTime;
    /**
     * 审核人
     */
    @ApiModelProperty(value = "审核人")
    private String checkUserId;
    /**
     * 创建时间
     */
    @ApiModelProperty(value = "创建时间")
    private Date createTime;
    /**
     * 创建人
     */
    @ApiModelProperty(value = "创建人")
    private String createUser;
    /**
     * 修改时间
     */
    @ApiModelProperty(value = "修改时间")
    private Date updateTime;
    /**
     * 修改人
     */
    @ApiModelProperty(value = "修改人")
    private String updateUser;
    /**
     * 是否删除
     */
    @ApiModelProperty(value = "是否删除")
    private Integer isDelete;

    @ApiModelProperty(value = "申请明细")
    private List<BzQxBzsqDetailVO> detailList;

    @ApiModelProperty(value = "文件列表")
    private List<FileVO> fileList;

    @ApiModelProperty(value = "核编单号")
    private String approvalNum;
}
