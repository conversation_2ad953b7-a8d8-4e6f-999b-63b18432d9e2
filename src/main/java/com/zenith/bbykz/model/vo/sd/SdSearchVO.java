package com.zenith.bbykz.model.vo.sd;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * <p>
 * 三定-三定明细 VO
 * </p>
 *
 * <AUTHOR>
 * @date 2024-04-11 16:28:25
 */
@Data
@ApiModel("三定-搜索 返回实体-SdSearchVO")
public class SdSearchVO implements Serializable {

    private static final long serialVersionUID = 1868779566150536740L;

    /**
     * 单位ID
     */
    @ApiModelProperty(value = "单位ID")
    private String unitId;
    /**
     * 单位层级码
     */
    @ApiModelProperty(value = "单位层级码")
    private String unitLevelCode;
    /**
     * 单位名称
     */
    @ApiModelProperty(value = "单位名称")
    private String unitName;
    /**
     * 区划code
     */
    @ApiModelProperty(value = "区划code")
    private String geocode;
    @ApiModelProperty(value = "单位层级")
    private String unitBelong;
    /**
     * 三定类型主键
     */
    @ApiModelProperty(value = "三定类型主键")
    private String moduleId;
    private String moduleName;
    /**
     * 三定明细主键id
     */
    @ApiModelProperty(value = "三定明细主键id")
    private String moduleDetailId;
    /**
     * 名称
     */
    @ApiModelProperty(value = "三定明细名称")
    private String moduleDetailTitle;
    /**
     * 文号
     */
    @ApiModelProperty(value = "三定明细文号")
    private String moduleDetailRecordNum;

    /**
     * 三定明细主键id
     */
    @ApiModelProperty(value = "细化职责主键id")
    private String dutyDetailId;
    /**
     * 名称
     */
    @ApiModelProperty(value = "细化职责名称")
    private String dutyDetailTitle;
    @ApiModelProperty(value = "核心业务数量")
    private String coreBusinessNum;
    /**
     * 文号
     */
    @ApiModelProperty(value = "细化职责承担处室")
    private String dutyDetailTakeUnitId;
    @ApiModelProperty(value = "细化职责承担处室")
    private String dutyDetailTakeUnitName;
    /**
     * 文号
     */
    @ApiModelProperty(value = "细化职责文号")
    private String dutyDetailRecordNum;

}
