package com.zenith.bbykz.model.vo.sd;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @since 2024/5/22 10:21
 */
@Data
@ApiModel("三定-职能职责分析-三定分类统计 返回实体-SdModuleTypeCountVO")
public class SdModuleTypeCountVO {

    @ApiModelProperty("id")
    private String id;
    @ApiModelProperty("名称")
    private String name;
    @ApiModelProperty("数量")
    private Integer count;
    @ApiModelProperty("百分比")
    private double percent;
}
