package com.zenith.bbykz.model.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * <p>
 * sys_user VO
 * </p>
 *
 * <AUTHOR>
 * @date 2023-09-14 17:14:26
 */
@Data
@ApiModel("sys_user 返回实体-SysUserVO")
public class SysUserVO {

    private static final long serialVersionUID = 5127517705693999924L;

    /**
     *
     */
    @ApiModelProperty(value = "")
    private String id;
    /**
     *
     */
    @ApiModelProperty(value = "")
    private String account;
    /**
     *
     */
    @ApiModelProperty(value = "")
    private String zwddId;

    /**
     *
     */
    @ApiModelProperty(value = "")
    private String idCard;
    /**
     *
     */
    @ApiModelProperty(value = "")
    private String phone;

    // 用户角色
    private String roleId;

    // 用户角色
    private String orgLevelCode;

    /**
     * 是否编办机构: 0:否;1:是
     */
    @ApiModelProperty(value = "是否编办用户: 0:否;1:是")
    private Integer isBbUser;
    /**
     * 用户管理机构
     */
    @ApiModelProperty(value = "用户管理机构")
    private String manageCode;

    /**
     * 姓名
     */
    @ApiModelProperty(value = "姓名")
    private String name;

    private String orgLevelCodeName;

    private String manageName;
    /**
     * 是否锁定
     */
    @ApiModelProperty(value = "是否锁定")
    private Integer isLock;

    /**
     * 解锁时间
     */
    @ApiModelProperty(value = "解锁时间")
    private Date unlockTime;
    @ApiModelProperty(value = "区划层级")
    private String geocode;
    private String userOrgGroupId;

    private String userPostId;
    private String userSystemId;
    private String orgName;
    /**
     * 所在单位
     */
    private String ssdwId;
    private List<String> systemIdList;
}
