package com.zenith.bbykz.model.vo.sd;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * <p>
 * 三定-业务事项 VO
 * </p>
 *
 * <AUTHOR>
 * @date 2024-04-11 16:28:25
 */
@Data
@ApiModel("三定-业务事项 返回实体-SdBusinessItemVO")
public class SdBusinessItemAllVO implements Serializable {

    private static final long serialVersionUID = 2887641658364348641L;

    /**
     * 主键id
     */
    @ApiModelProperty(value = "主键id")
    private String id;
    /**
     * 三定类型主键
     */
    @ApiModelProperty(value = "三定类型主键")
    private String moduleId;
    /**
     * 三定明细主键
     */
    @ApiModelProperty(value = "三定明细主键")
    private String moduleDetailId;
    /**
     * 细化职责主键
     */
    @ApiModelProperty(value = "细化职责主键")
    private String dutyDetailId;
    /**
     * 核心业务主键
     */
    @ApiModelProperty(value = "核心业务主键")
    private String coreBusinessId;
    /**
     * 名称
     */
    @ApiModelProperty(value = "名称")
    private String title;
    /**
     * 排序号
     */
    @ApiModelProperty(value = "排序号")
    private Integer sort;

    private List<SdBusinessItemOneAllVO> children;
}
