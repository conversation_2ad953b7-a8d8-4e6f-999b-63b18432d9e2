package com.zenith.bbykz.model.vo;

import com.baomidou.mybatisplus.annotation.TableField;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

/**
 * <p>
 * 菜单表 VO
 * </p>
 *
 * <AUTHOR>
 * @date 2023-09-14 17:14:26
 */
@Data
@ApiModel("菜单表 返回实体-SysMenuVO")
public class SysMenuVO {

    private static final long serialVersionUID = 4857376149082229505L;

    /**
     *
     */
    @ApiModelProperty(value = "")
    private String id;
    /**
     *
     */
    @ApiModelProperty(value = "")
    private String code;
    /**
     *
     */
    @ApiModelProperty(value = "")
    private String parentCode;
    @TableField(exist = false)
    private String parentName;
    /**
     *
     */
    @ApiModelProperty(value = "")
    private String name;
    /**
     *
     */
    @ApiModelProperty(value = "")
    private String description;
    /**
     *
     */
    @ApiModelProperty(value = "")
    private Integer enable;
    /**
     *
     */
    @ApiModelProperty(value = "")
    private Boolean isLeaf;
    /**
     *
     */
    @ApiModelProperty(value = "")
    private Date createTime;
    /**
     *
     */
    @ApiModelProperty(value = "")
    private String systemCode;
}
