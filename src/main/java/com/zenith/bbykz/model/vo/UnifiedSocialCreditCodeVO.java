package com.zenith.bbykz.model.vo;

import com.baomidou.mybatisplus.annotation.TableField;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

/**
 * 机关事业单位统一社会信用代码
 *
 * <AUTHOR>
 * @date 2023/9/13
 */
@Data
public class UnifiedSocialCreditCodeVO {
    private String code;

    private String creater;

    private Date createTime;

    private Date uptimeTime;

    private Date deleteTime;

    private Integer isDelete;

    private String institutionName;

    private String institutionAddress;

    private String head;

    private String institutionalSpecifications;

    private String institutionalNature;

    private String institutionCategory;

    private String approvingAuthority;

    private String approvalNumber;

    private String issueTime;

    private String certificateAcquisitionTime;

    private String contacts;

    private String telephone;

    private String postalCode;
    private String bdId;
    private String geocode;
}
