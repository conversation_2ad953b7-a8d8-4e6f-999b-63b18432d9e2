package com.zenith.bbykz.model.vo.bz.bzsq;

import com.efficient.file.model.entity.SysFileInfo;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * <p>
 * 核编管理-用编申请-明细 VO
 * </p>
 *
 * <AUTHOR>
 * @date 2024-03-29 16:05:19
 */
@Data
@ApiModel("核编管理-用编申请-明细 返回实体-BzBzsqDetailVO")
public class BzBzsqDetailVO implements Serializable {

    private static final long serialVersionUID = 6535564956492804876L;

    /**
     * 主键id
     */
    @ApiModelProperty(value = "主键id")
    private String id;
    /**
     * 编制申请主键id
     */
    @ApiModelProperty(value = "编制申请主键id")
    private String bzsqId;
    /**
     * 编制类型
     */
    @ApiModelProperty(value = "编制类型")
    private String bzType;
    private String bzTypeName;
    /**
     * 用编类型
     */
    @ApiModelProperty(value = "用编类型")
    private String ybType;
    private String ybTypeName;
    /**
     * 待上编数
     */
    @ApiModelProperty(value = "待上编数")
    private Integer dsbNum;
    /**
     * 在编数
     */
    @ApiModelProperty(value = "在编数")
    private Integer zbNum;
    /**
     * 余编数
     */
    @ApiModelProperty(value = "余编数")
    private Integer ybNum;
    /**
     * 申请数
     */
    @ApiModelProperty(value = "申请数")
    private Integer applyNum;
    /**
     * 备注
     */
    @ApiModelProperty(value = "备注")
    private String remark;
    /**
     * 是否被退回，1-是，0-否
     */
    @ApiModelProperty(value = "是否被退回，1-是，0-否")
    private Integer isBack;
    @ApiModelProperty(value = "文件内容")
    private List<SysFileInfo> fileInfoList;
    /**
     * 退回原因
     */
    @ApiModelProperty(value = "退回原因")
    private String backReason;
    /**
     * 编制使用单位id
     */
    @ApiModelProperty(value = "编制使用单位id")
    private String useBzUnitId;
    /**
     * 编制使用单位名称
     */
    @ApiModelProperty(value = "编制使用单位名称")
    private String useBzUnitName;
    /**
     * 编制使用单位层级码
     */
    @ApiModelProperty(value = "编制使用单位层级码")
    private String useBzUnitLevelCode;
}
