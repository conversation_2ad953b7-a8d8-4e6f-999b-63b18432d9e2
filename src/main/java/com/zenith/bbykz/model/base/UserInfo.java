package com.zenith.bbykz.model.base;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.efficient.common.auth.RequestHolder;
import com.efficient.common.auth.UserTicket;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.zenith.bbykz.model.entity.SysUser;
import com.zenith.bbykz.model.entity.SysUserSystem;
import com.zenith.bbykz.model.vo.LoginSystem;
import com.zenith.bbykz.model.vo.LoginUnit;
import lombok.Data;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2023/9/15 11:33
 */
@Data
public class UserInfo extends UserTicket {

    private String userPostId;
    private String levelCode;
    private List<LoginUnit> unitList;
    private String token;
    private List<LoginSystem> systemList;
    private SysUser sysUser;

    public void setMenuListBySystemId(SysUserSystem sysUserSystem, List<String> menuList,SysUser sysUser) {
        if (Objects.isNull(systemList)) {
            systemList = new ArrayList<>();
            LoginSystem LoginSystem = setLoginSystem(sysUserSystem,sysUser);
            LoginSystem.setMenuList(menuList);
            systemList.add(LoginSystem);
        } else {
            List<LoginSystem> loginSystemList = systemList.stream().filter(et -> StrUtil.equals(sysUserSystem.getId(), et.getUserSystemId())).collect(Collectors.toList());
            if (CollUtil.isEmpty(loginSystemList)) {
                LoginSystem LoginSystem = setLoginSystem(sysUserSystem,sysUser);
                LoginSystem.setMenuList(menuList);
                systemList.add(LoginSystem);
            } else {
                loginSystemList.get(0).setMenuList(menuList);
            }
        }
    }

    public static LoginSystem setLoginSystem(SysUserSystem sysUserSystem, SysUser sysUser) {
        LoginSystem LoginSystem = new LoginSystem();
        LoginSystem.setSystemId(sysUserSystem.getSystemId());
        LoginSystem.setUserSystemId(sysUserSystem.getId());
        LoginSystem.setManageUnitId(sysUser.getManageCode());
        LoginSystem.setManageLevelCode(sysUser.getManageCode());
        LoginSystem.setGeocode(sysUser.getGeocode());
        LoginSystem.setIsBbUser(sysUser.getIsBbUser());
        return LoginSystem;
    }

    @JsonIgnore
    public LoginSystem getLoginSystem(String systemId) {
        Map<String, LoginSystem> loginSystemMap = this.systemList.stream().collect(Collectors.toMap(LoginSystem::getSystemId, et -> et, (k1, k2) -> k1));
        return loginSystemMap.get(systemId);
    }

    @JsonIgnore
    public LoginSystem getCurrLoginSystem() {
        Map<String, LoginSystem> loginSystemMap = this.systemList.stream().collect(Collectors.toMap(LoginSystem::getSystemId, et -> et, (k1, k2) -> k1));
        return loginSystemMap.get(RequestHolder.getCurrSystemId());
    }

    @JsonIgnore
    public List<String> getSystemIdList() {
        return this.getSystemList().stream().map(LoginSystem::getSystemId).collect(Collectors.toList());
    }
}
