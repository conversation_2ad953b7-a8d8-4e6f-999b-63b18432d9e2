package com.zenith.bbykz.model.base;

import lombok.Builder;
import lombok.Data;

import java.math.BigInteger;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2022/4/29 10:24
 */
@Data
@Builder
public class TreeNode {
    /**
     * 本级code
     */
    private String code;
    /**
     * 层级码
     */
    private String levelCode;
    /**
     * 父级层级码
     */
    private String parentLevelCode;
    /**
     * 父级code
     */
    private String parentCode;
    /**
     * 名称
     */
    private String name;
    /**
     * 类型
     */
    private String type;
    /**
     * 区划属性
     */
    private String geoType;
    /**
     * 排序
     */
    private Long order;
    /**
     * 0-顶级节点，1-12 systemcode,88，99-其他节点
     */
    private Integer orderType;
    /**
     * 系统类型
     */
    private String systemCode;
    /**
     * 是否叶子节点
     */
    private Boolean isLeaf;
    /**
     * 是否顶级节点
     */
    private Boolean isRoot;
    /**
     * 超机构预警　　0：空机构
     * 　　1：正常
     * 2：超机构
     */
    private String jgsyChaojg;
    /**
     * 超编制预警　　0：空编
     * 　　1：正常
     * 2：超编
     */
    private String jgsyChaobz;
    /**
     * 超职数预警　　0：空职数
     * 　　1：正常
     * 2：超职数
     */
    private String jgsyChaozs;
    /**
     * 是否垂直
     */
    private String jgsyIfvertical;
    /**
     * 是否政法机构
     */
    private String jgsyIfzfjg;
    /**
     * 区划
     */
    private String geoCode;

    /**
     * 子节点
     */
    private List<TreeNode> children;
}
