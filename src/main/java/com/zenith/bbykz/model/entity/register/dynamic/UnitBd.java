package com.zenith.bbykz.model.entity.register.dynamic;

import com.baomidou.mybatisplus.annotation.*;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * <p>
 * 登记信息-预警感知-信息变动 实体类
 * </p>
 *
 * <AUTHOR>
 * @date 2024-04-09 11:50:41
 */
@Data
@TableName("register_unit_bd")
@ApiModel("登记信息-预警感知-信息变动")
public class UnitBd implements Serializable {

    private static final long serialVersionUID = 3501676870503545781L;

    /**
     * 主键id
     */
    @ApiModelProperty(value = "主键id")
    @TableId(value = "id")
    private String id;
    /**
     * 变动类型
     */
    @ApiModelProperty(value = "变动类型")
    @TableField("type")
    private String type;
    /**
     * 区划
     */
    @ApiModelProperty(value = "区划")
    @TableField("geocode")
    private String geocode;
    /**
     * 单位ID
     */
    @ApiModelProperty(value = "单位ID")
    @TableField("unit_id")
    private String unitId;
    /**
     * 单位层级码
     */
    @ApiModelProperty(value = "单位层级码")
    @TableField("unit_level_code")
    private String unitLevelCode;
    /**
     * 单位名称
     */
    @ApiModelProperty(value = "单位名称")
    @TableField("unit_name")
    private String unitName;
    /**
     * 原信息
     */
    @ApiModelProperty(value = "原信息")
    @TableField("old_info")
    private String oldInfo;
    /**
     * 新信息
     */
    @ApiModelProperty(value = "新信息")
    @TableField("new_info")
    private String newInfo;
    /**
     * 是否处理
     */
    @ApiModelProperty(value = "是否处理")
    @TableField("is_handle")
    private Integer isHandle;
    /**
     * 处理时间
     */
    @ApiModelProperty(value = "处理时间")
    @TableField("handle_time")
    private Date handleTime;
    /**
     * 备注
     */
    @ApiModelProperty(value = "备注")
    @TableField("remark")
    private String remark;
    /**
     * 来源类型，1-自建，2-同步
     */
    @ApiModelProperty(value = "来源类型，1-自建，2-同步")
    @TableField("source_type")
    private Integer sourceType;
    /**
     * 来源类型，1-自建，2-同步
     */
    @ApiModelProperty(value = "来源类型，描述")
    @TableField("source_name")
    private String sourceName;
    /**
     * 创建时间
     */
    @ApiModelProperty(value = "创建时间")
    @TableField(value = "create_time", fill = FieldFill.INSERT)
    private Date createTime;
    /**
     * 变动时间
     */
    @ApiModelProperty(value = "变动时间")
    @TableField(value = "bd_time")
    private Date bdTime;
    /**
     * 创建人
     */
    @ApiModelProperty(value = "创建人")
    @TableField(value = "create_user", fill = FieldFill.INSERT)
    private String createUser;
    /**
     * 修改时间
     */
    @ApiModelProperty(value = "修改时间")
    @TableField(value = "update_time", fill = FieldFill.UPDATE)
    private Date updateTime;
    /**
     * 修改人
     */
    @ApiModelProperty(value = "修改人")
    @TableField(value = "update_user", fill = FieldFill.UPDATE)
    private String updateUser;
    /**
     * 是否删除
     */
    @ApiModelProperty(value = "是否删除")
    @TableLogic
    @TableField("is_delete")
    private Integer isDelete;
    /**
     * 是否发送,0-未发送，1-发送，发送后所属单位才能看到预警信息
     */
    @ApiModelProperty(value = "是否发送,0-未发送，1-发送，发送后所属单位才能看到预警信息")
    @TableField("is_send")
    private Integer isSend;
    @ApiModelProperty(value = "反馈内容")
    @TableField("back_content")
    private String  backContent;
    @ApiModelProperty(value = "反馈时间")
    @TableField("back_time")
    private Date backTime;
    @ApiModelProperty(value = "变动状态消息状态，1-已感知，2-已发送，3-已查看，4-已处理,5-已完成")
    @TableField("state")
    private String state;
    @ApiModelProperty(value = "统一社会信用代码")
    @TableField("credit_code")
    private String creditCode;
    @ApiModelProperty(value = "单位来源，1-事业单位，2-机关登记信息")
    @TableField("unit_source")
    private String unitSource;
    @ApiModelProperty(value = "中央code")
    @TableField("ZY_CODE")
    private String zyCode;
}
