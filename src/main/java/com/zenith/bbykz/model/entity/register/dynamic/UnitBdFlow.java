package com.zenith.bbykz.model.entity.register.dynamic;

import com.baomidou.mybatisplus.annotation.*;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
* <p>
* 登记信息-变动流程记录 实体类
* </p>
*
* <AUTHOR>
* @date 2024-04-18 10:01:33
*/
@Data
@TableName("register_unit_bd_flow")
@ApiModel("登记信息-变动流程记录")
public class UnitBdFlow implements Serializable {

    private static final long serialVersionUID = 2888095884097899404L;

    /**
    *主键id
    */
    @ApiModelProperty(value = "主键id")
    @TableId(value = "id")
    private String id;
    /**
    *业务主键
    */
    @ApiModelProperty(value = "业务主键")
    @TableField("biz_id")
    private String bizId;
    /**
    *变动code
    */
    @ApiModelProperty(value = "变动code")
    @TableField("code")
    private String code;
    /**
    *描述
    */
    @ApiModelProperty(value = "描述")
    @TableField("describe")
    private String describe;
    /**
    *
    */
    @ApiModelProperty(value = "")
    @TableField(value = "create_time", fill = FieldFill.INSERT)
    private Date createTime;
    /**
    *
    */
    @ApiModelProperty(value = "")
    @TableField(value = "create_user", fill = FieldFill.INSERT)
    private String createUser;
    /**
    *
    */
    @ApiModelProperty(value = "")
    @TableField("update_time")
    private Date updateTime;
    /**
    *
    */
    @ApiModelProperty(value = "")
    @TableField(value = "update_user", fill = FieldFill.UPDATE)
    private String updateUser;
    /**
    *
    */
    @ApiModelProperty(value = "")
    @TableLogic
    @TableField("is_delete")
    private Integer isDelete;
}
