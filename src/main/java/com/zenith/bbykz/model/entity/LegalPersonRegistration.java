package com.zenith.bbykz.model.entity;

import com.baomidou.mybatisplus.annotation.*;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * <p>
 * 事业单位法人登记表 实体类
 * </p>
 *
 * <AUTHOR>
 * @date 2024-03-08 09:54:43
 */
@Data
@TableName("legal_person_registration")
@ApiModel("事业单位法人登记表")
public class LegalPersonRegistration implements Serializable {

    private static final long serialVersionUID = 7035545514738272235L;

    /**
     * 主键id
     */
    @ApiModelProperty(value = "主键id")
    @TableId(value = "id")
    private String id;
    /**
     * 统一社会信用代码
     */
    @ApiModelProperty(value = "统一社会信用代码")
    @TableField("unified_social_credit_code")
    private String unifiedSocialCreditCode;
    /**
     * 证书号
     */
    @ApiModelProperty(value = "证书号")
    @TableField("certificate_no")
    private String certificateNo;
    /**
     * 名称
     */
    @ApiModelProperty(value = "名称")
    @TableField("name")
    private String name;
    /**
     * 设立类型
     */
    @ApiModelProperty(value = "设立类型")
    @TableField("establishment_type")
    private String establishmentType;
    /**
     * 法定代表人
     */
    @ApiModelProperty(value = "法定代表人")
    @TableField("legal_representative")
    private String legalRepresentative;
    /**
     * 住所
     */
    @ApiModelProperty(value = "住所")
    @TableField("residence")
    private String residence;
    /**
     * 创建人
     */
    @ApiModelProperty(value = "创建人")
    @TableField("creater")
    private String creater;
    /**
     * 导入时间
     */
    @ApiModelProperty(value = "导入时间")
    @TableField(value = "create_time", fill = FieldFill.INSERT)
    private Date createTime;
    /**
     * 更新时间
     */
    @ApiModelProperty(value = "更新时间")
    @TableField("uptime_time")
    private Date uptimeTime;
    /**
     * 删除时间
     */
    @ApiModelProperty(value = "删除时间")
    @TableField("delete_time")
    private Date deleteTime;
    /**
     * 是否删除
     */
    @ApiModelProperty(value = "是否删除")
    @TableLogic
    @TableField("is_delete")
    private Integer isDelete;
    /**
     * 举办单位
     */
    @ApiModelProperty(value = "举办单位")
    @TableField("organizer")
    private String organizer;
    /**
     * 经费来源
     */
    @ApiModelProperty(value = "经费来源")
    @TableField("funding_source")
    private String fundingSource;
    /**
     * 开办资金
     */
    @ApiModelProperty(value = "开办资金")
    @TableField("initial_funds")
    private String initialFunds;
    /**
     * 宗旨和业务范围
     */
    @ApiModelProperty(value = "宗旨和业务范围")
    @TableField("purpose")
    private String purpose;
    /**
     * 状态
     */
    @ApiModelProperty(value = "状态")
    @TableField("status")
    private String status;
    /**
     * 事业单位第二名称
     */
    @ApiModelProperty(value = "事业单位第二名称")
    @TableField("second_name")
    private String secondName;
    /**
     * 事业单位第三名称
     */
    @ApiModelProperty(value = "事业单位第三名称")
    @TableField("third_name")
    private String thirdName;
    /**
     * 事业单位其他名称
     */
    @ApiModelProperty(value = "事业单位其他名称")
    @TableField("other_names")
    private String otherNames;
    /**
     * 举办单位类别
     */
    @ApiModelProperty(value = "举办单位类别")
    @TableField("organizer_category")
    private String organizerCategory;
    /**
     * 固定资产
     */
    @ApiModelProperty(value = "固定资产")
    @TableField("fixed_assets")
    private String fixedAssets;
    /**
     * 流动资产
     */
    @ApiModelProperty(value = "流动资产")
    @TableField("current_assets")
    private String currentAssets;
    /**
     * 其他资金
     */
    @ApiModelProperty(value = "其他资金")
    @TableField("other_funds")
    private String otherFunds;
    /**
     * 开户银行
     */
    @ApiModelProperty(value = "开户银行")
    @TableField("deposit_bank")
    private String depositBank;
    /**
     * 银行帐号
     */
    @ApiModelProperty(value = "银行帐号")
    @TableField("bank_account")
    private String bankAccount;
    /**
     * 审批机关
     */
    @ApiModelProperty(value = "审批机关")
    @TableField("approval_authority")
    private String approvalAuthority;
    /**
     * 批准文号
     */
    @ApiModelProperty(value = "批准文号")
    @TableField("approval_number")
    private String approvalNumber;
    /**
     * 执业许可证书
     */
    @ApiModelProperty(value = "执业许可证书")
    @TableField("practicing_license_certificate")
    private String practicingLicenseCertificate;
    /**
     * 机构规格
     */
    @ApiModelProperty(value = "机构规格")
    @TableField("institutional_specifications")
    private String institutionalSpecifications;
    /**
     * 人员编制
     */
    @ApiModelProperty(value = "人员编制")
    @TableField("staff_establishing")
    private String staffEstablishing;
    /**
     * 从业人数
     */
    @ApiModelProperty(value = "从业人数")
    @TableField("employee_number")
    private Integer employeeNumber;
    /**
     * 设立时间
     */
    @ApiModelProperty(value = "设立时间")
    @TableField("establish_time")
    private String establishTime;
    /**
     * 发证人
     */
    @ApiModelProperty(value = "发证人")
    @TableField("licensor")
    private String licensor;
    /**
     * 领证人
     */
    @ApiModelProperty(value = "领证人")
    @TableField("certificate_recipient")
    private String certificateRecipient;
    /**
     * 领证日期
     */
    @ApiModelProperty(value = "领证日期")
    @TableField("certificate_acquisition_time")
    private String certificateAcquisitionTime;
    /**
     * 副本份数
     */
    @ApiModelProperty(value = "副本份数")
    @TableField("copies_number")
    private Integer copiesNumber;
    /**
     * 证书有效起始日期
     */
    @ApiModelProperty(value = "证书有效起始日期")
    @TableField("certificate_validity_start_time")
    private String certificateValidityStartTime;
    /**
     * 证书有效结束日期
     */
    @ApiModelProperty(value = "证书有效结束日期")
    @TableField("certificate_validity_end_time")
    private String certificateValidityEndTime;
    /**
     * 联系人
     */
    @ApiModelProperty(value = "联系人")
    @TableField("contacts")
    private String contacts;
    /**
     * 联系电话
     */
    @ApiModelProperty(value = "联系电话")
    @TableField("telephone")
    private String telephone;
    /**
     * 邮政编码
     */
    @ApiModelProperty(value = "邮政编码")
    @TableField("postal_code")
    private String postalCode;
    /**
     * 行业类别
     */
    @ApiModelProperty(value = "行业类别")
    @TableField("industry_category")
    private String industryCategory;
    /**
     * 登记机关区划码
     */
    @ApiModelProperty(value = "登记机关区划码")
    @TableField("registration_authority_code")
    private String registrationAuthorityCode;
    /**
     * 登记管理机关
     */
    @ApiModelProperty(value = "登记管理机关")
    @TableField("annual_report")
    private String annualReport;
    /**
     * 年度报告日期
     */
    @ApiModelProperty(value = "年度报告日期")
    @TableField("annual_report_time")
    private String annualReportTime;
    /**
     * 法定代表人电话
     */
    @ApiModelProperty(value = "法定代表人电话")
    @TableField("legal_representative_phone")
    private String legalRepresentativePhone;
    /**
     * 年度报告情况
     */
    @ApiModelProperty(value = "年度报告情况")
    @TableField("registration_management_authority")
    private String registrationManagementAuthority;
    /**
     * 版本号
     */
    @ApiModelProperty(value = "版本号")
    @TableField("version")
    private Integer version;
}
