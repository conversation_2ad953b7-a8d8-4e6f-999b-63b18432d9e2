package com.zenith.bbykz.model.entity;

import com.baomidou.mybatisplus.annotation.*;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * <p>
 * sys_role 实体类
 * </p>
 *
 * <AUTHOR>
 * @date 2023-09-14 17:14:26
 */
@Data
@TableName("sys_role")
@ApiModel("角色实体")
public class SysRole implements Serializable {

    private static final long serialVersionUID = 5523631115474911538L;

    /**
     *
     */
    @ApiModelProperty(value = "主键")
    @TableId(value = "id", type = IdType.ASSIGN_UUID)
    private String id;
    /**
     *
     */
    @ApiModelProperty(value = "角色名称")
    @TableField("name")
    private String name;
    /**
     *
     */
    @ApiModelProperty(value = "是否默认角色")
    @TableField("is_default")
    private Integer isDefault;
    /**
     *
     */
    @ApiModelProperty(value = "系统编码（1：机关事业单位登记信息，2：用编管理）")
    @TableField("system_code")
    private String systemCode;
    @ApiModelProperty(value = "父级角色id")
    @TableField("parent_id")
    private String parentId;

    @ApiModelProperty(value = "创建人")
    @TableField("create_user")
    private String createUser;
    @ApiModelProperty(value = "创建时间")
    @TableField("create_time")
    private Date createTime;
}
