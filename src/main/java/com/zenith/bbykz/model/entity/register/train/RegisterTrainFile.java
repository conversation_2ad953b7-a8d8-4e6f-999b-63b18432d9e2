package com.zenith.bbykz.model.entity.register.train;

import com.baomidou.mybatisplus.annotation.*;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * <p>
 * 登记信息-培训管理-课件管理 实体类
 * </p>
 *
 * <AUTHOR>
 * @date 2024-03-05 15:44:53
 */
@Data
@TableName("register_train_file")
@ApiModel("登记信息-培训管理-课件管理")
public class RegisterTrainFile implements Serializable {

    private static final long serialVersionUID = 1451006482531728583L;

    /**
     * 主键id
     */
    @ApiModelProperty(value = "主键id")
    @TableId(value = "id")
    private String id;
    /**
     * 名称
     */
    @ApiModelProperty(value = "名称")
    @TableField("name")
    private String name;
    /**
     * 分类ID
     */
    @ApiModelProperty(value = "分类ID")
    @TableField("type_id")
    private String typeId;
    /**
     * 分类名称
     */
    @ApiModelProperty(value = "分类名称")
    @TableField("type_name")
    private String typeName;
    /**
     * 时长
     */
    @ApiModelProperty(value = "时长")
    @TableField("mins")
    private String mins;
    /**
     * 描述
     */
    @ApiModelProperty(value = "描述")
    @TableField("describe")
    private String describe;
    /**
     * 创建时间
     */
    @ApiModelProperty(value = "创建时间")
    @TableField(value = "create_time", fill = FieldFill.INSERT)
    private Date createTime;
    /**
     * 创建人
     */
    @ApiModelProperty(value = "创建人")
    @TableField(value = "create_user", fill = FieldFill.INSERT)
    private String createUser;
    /**
     * 修改时间
     */
    @ApiModelProperty(value = "修改时间")
    @TableField("update_time")
    private Date updateTime;
    /**
     * 修改人
     */
    @ApiModelProperty(value = "修改人")
    @TableField(value = "update_user", fill = FieldFill.UPDATE)
    private String updateUser;
    /**
     * 是否删除
     */
    @ApiModelProperty(value = "是否删除")
    @TableLogic
    @TableField("is_delete")
    private Integer isDelete;
}
