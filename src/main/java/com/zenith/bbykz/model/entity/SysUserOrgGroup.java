package com.zenith.bbykz.model.entity;

import com.baomidou.mybatisplus.annotation.*;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * <p>
 * 用户机构组 实体类
 * </p>
 *
 * <AUTHOR>
 * @date 2024-03-13 10:48:03
 */
@Data
@TableName("sys_user_org_group")
@ApiModel("用户机构组")
public class SysUserOrgGroup implements Serializable {

    private static final long serialVersionUID = 4856491405320669955L;

    /**
     *
     */
    @ApiModelProperty(value = "")
    @TableId(value = "id")
    private String id;
    /**
     * 名称
     */
    @ApiModelProperty(value = "名称")
    @TableField("name")
    private String name;
    /**
     * 系统ID
     */
    @ApiModelProperty(value = "系统ID")
    @TableField("system_id")
    private String systemId;
    /**
     * 用户id
     */
    @ApiModelProperty(value = "用户id")
    @TableField("user_id")
    private String userId;
    /**
     * 备注
     */
    @ApiModelProperty(value = "备注")
    @TableField("remark")
    private String remark;
    /**
     *
     */
    @ApiModelProperty(value = "")
    @TableField(value = "create_time", fill = FieldFill.INSERT)
    private Date createTime;
    /**
     *
     */
    @ApiModelProperty(value = "")
    @TableField(value = "create_user", fill = FieldFill.INSERT)
    private String createUser;
    /**
     *
     */
    @ApiModelProperty(value = "")
    @TableField("update_time")
    private Date updateTime;
    /**
     *
     */
    @ApiModelProperty(value = "")
    @TableField(value = "update_user", fill = FieldFill.UPDATE)
    private String updateUser;
    @ApiModelProperty(value = "请求参数")
    @TableField(value = "request_params")
    private String requestParams;
    /**
     *
     */
    @ApiModelProperty(value = "")
    @TableLogic
    @TableField("is_delete")
    private Integer isDelete;
}
