package com.zenith.bbykz.model.entity;

import com.baomidou.mybatisplus.annotation.*;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * <p>
 * sys_user_role 实体类
 * </p>
 *
 * <AUTHOR>
 * @date 2023-09-14 17:14:26
 */
@Data
@TableName("sys_user_role")
@ApiModel("sys_user_role")
public class SysUserRole implements Serializable {

    private static final long serialVersionUID = 4360313750481815817L;

    /**
     *
     */
    @ApiModelProperty(value = "")
    @TableId(value = "id", type = IdType.ASSIGN_UUID)
    private String id;
    /**
     *
     */
    @ApiModelProperty(value = "")
    @TableField("user_id")
    private String userId;
    /**
     *
     */
    @ApiModelProperty(value = "")
    @TableField("role_id")
    private String roleId;
    /**
     *
     */
    @ApiModelProperty(value = "")
    @TableField("system_code")
    private String systemCode;
    @ApiModelProperty(value = "")
    @TableField("user_system_id")
    private String userSystemId;
}
