package com.zenith.bbykz.model.entity.bz.rybd;

import com.baomidou.mybatisplus.annotation.*;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
* <p>
* 核编管理-动态信息维护 实体类
* </p>
*
* <AUTHOR>
* @date 2024-04-22 11:21:43
*/
@Data
@TableName("bz_unit_bd")
@ApiModel("核编管理-动态信息维护")
public class BzUnitBd implements Serializable {

    private static final long serialVersionUID = 4434860109300416221L;

    /**
    *主键id
    */
    @ApiModelProperty(value = "主键id")
    @TableId(value = "id")
    private String id;
    /**
    *标题
    */
    @ApiModelProperty(value = "标题")
    @TableField("title")
    private String title;
    /**
    *单位主键
    */
    @ApiModelProperty(value = "单位主键")
    @TableField("unit_id")
    private String unitId;
    /**
    *单位层级码
    */
    @ApiModelProperty(value = "单位层级码")
    @TableField("unit_level_code")
    private String unitLevelCode;
    /**
    *单位名称
    */
    @ApiModelProperty(value = "单位名称")
    @TableField("unit_name")
    private String unitName;
    /**
    *实名制单位code
    */
    @ApiModelProperty(value = "实名制单位code")
    @TableField("smz_unit_code")
    private String smzUnitCode;
    /**
    *统一社会信用代码
    */
    @ApiModelProperty(value = "统一社会信用代码")
    @TableField("credit_code")
    private String creditCode;
    /**
    *变动类型，BZ_UNIT_BD_TYPE，默认值
    */
    @ApiModelProperty(value = "变动类型，BZ_UNIT_BD_TYPE，默认值")
    @TableField("bd_type")
    private String bdType;
    /**
    *备注
    */
    @ApiModelProperty(value = "备注")
    @TableField("remark")
    private String remark;
    /**
    *感知时间
    */
    @ApiModelProperty(value = "感知时间")
    @TableField("change_time")
    private Date changeTime;
    /**
    *
    */
    @ApiModelProperty(value = "")
    @TableField(value = "create_time", fill = FieldFill.INSERT)
    private Date createTime;
    /**
    *
    */
    @ApiModelProperty(value = "")
    @TableField(value = "create_user", fill = FieldFill.INSERT)
    private String createUser;
    /**
    *
    */
    @ApiModelProperty(value = "")
    @TableField("update_time")
    private Date updateTime;
    /**
    *
    */
    @ApiModelProperty(value = "")
    @TableField(value = "update_user", fill = FieldFill.UPDATE)
    private String updateUser;
    /**
    *
    */
    @ApiModelProperty(value = "")
    @TableLogic
    @TableField("is_delete")
    private Integer isDelete;
}
