package com.zenith.bbykz.model.entity.bz.bzsq;

import com.baomidou.mybatisplus.annotation.*;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * <p>
 * 核编管理-用编申请 实体类
 * </p>
 *
 * <AUTHOR>
 * @date 2024-03-29 16:05:18
 */
@Data
@TableName("bz_bzsq")
@ApiModel("核编管理-用编申请")
public class BzBzsq implements Serializable {

    private static final long serialVersionUID = 4311899611448590228L;

    /**
     * 主键id
     */
    @ApiModelProperty(value = "主键id")
    @TableId(value = "id")
    private String id;
    /**
     * 所属区划
     */
    @ApiModelProperty(value = "所属区划")
    @TableField("geocode")
    private String geocode;
    /**
     * 是否编办单位申请
     */
    @ApiModelProperty(value = "是否编办单位申请")
    @TableField("is_bb")
    private Integer isBb;
    /**
     *
     */
    @ApiModelProperty(value = "")
    @TableField("org_id")
    private String orgId;
    /**
     * 提交单位层级码
     */
    @ApiModelProperty(value = "提交单位层级码")
    @TableField("org_level_code")
    private String orgLevelCode;
    /**
     * 标题
     */
    @ApiModelProperty(value = "标题")
    @TableField("title")
    private String title;
    /**
     * 申请单位名称
     */
    @ApiModelProperty(value = "申请单位名称")
    @TableField("apply_unit_name")
    private String applyUnitName;
    /**
     * 申请时间
     */
    @ApiModelProperty(value = "申请时间")
    @TableField("apply_time")
    private Date applyTime;
    /**
     * 联系人
     */
    @ApiModelProperty(value = "联系人")
    @TableField("concat_name")
    private String concatName;
    /**
     * 联系电话
     */
    @ApiModelProperty(value = "联系电话")
    @TableField("concat_phone")
    private String concatPhone;
    /**
     * 审批状态，字典表 BZ_HBZT
     */
    @ApiModelProperty(value = "审批状态，字典表 BZ_HBZT")
    @TableField("status")
    private String status;
    @ApiModelProperty(value = "意见")
    @TableField("opinion")
    private String opinion;
    /**
     * 有效期限
     */
    @ApiModelProperty(value = "有效期限")
    @TableField("deadline")
    private Date deadline;
    /**
     * 批复单号
     */
    @ApiModelProperty(value = "批复单号")
    @TableField("approval_num")
    private String approvalNum;
    /**
     * 创建时间
     */
    @ApiModelProperty(value = "创建时间")
    @TableField(value = "create_time", fill = FieldFill.INSERT)
    private Date createTime;
    /**
     * 创建人
     */
    @ApiModelProperty(value = "创建人")
    @TableField(value = "create_user", fill = FieldFill.INSERT)
    private String createUser;
    /**
     * 修改时间
     */
    @ApiModelProperty(value = "修改时间")
    @TableField("update_time")
    private Date updateTime;
    /**
     * 修改人
     */
    @ApiModelProperty(value = "修改人")
    @TableField(value = "update_user", fill = FieldFill.UPDATE)
    private String updateUser;
    /**
     * 是否删除
     */
    @ApiModelProperty(value = "是否删除")
    @TableLogic
    @TableField("is_delete")
    private Integer isDelete;
    @ApiModelProperty(value = "合并ID")
    @TableField(value = "merge_id")
    private String mergeId;
    @ApiModelProperty(value = "编制使用单位id")
    @TableField(value = "use_bz_unit_id")
    private String useBzUnitId;
    @ApiModelProperty(value = "编制使用单位名称")
    @TableField(value = "use_bz_unit_name")
    private String useBzUnitName;
    @ApiModelProperty(value = "备注")
    @TableField(value = "remark")
    private String remark;
}
