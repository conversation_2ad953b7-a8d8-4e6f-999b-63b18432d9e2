package com.zenith.bbykz.model.entity.register.interior;

import com.baomidou.mybatisplus.annotation.*;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * <p>
 * 登记信息-内部事务-法人库日志 实体类
 * </p>
 *
 * <AUTHOR>
 * @date 2024-03-12 17:24:37
 */
@Data
@TableName("register_legal_person_log")
@ApiModel("登记信息-内部事务-法人库日志")
public class RegisterLegalPersonLog implements Serializable {

    private static final long serialVersionUID = 8232222029480639234L;

    /**
     * 主键id
     */
    @ApiModelProperty(value = "主键id")
    @TableId(value = "id")
    private String id;
    /**
     * 名称
     */
    @ApiModelProperty(value = "名称")
    @TableField("name")
    private String name;
    /**
     * 身份证
     */
    @ApiModelProperty(value = "身份证")
    @TableField("id_card")
    private String idCard;
    /**
     * 单位名称
     */
    @ApiModelProperty(value = "单位名称")
    @TableField("unit_name")
    private String unitName;
    /**
     * 是否通过
     */
    @ApiModelProperty(value = "是否通过")
    @TableField("is_success")
    private Integer isSuccess;
    /**
     * 描述
     */
    @ApiModelProperty(value = "描述")
    @TableField("description")
    private String description;
    /**
     * 创建时间
     */
    @ApiModelProperty(value = "创建时间")
    @TableField(value = "create_time", fill = FieldFill.INSERT)
    private Date createTime;
    /**
     * 创建人
     */
    @ApiModelProperty(value = "创建人")
    @TableField(value = "create_user", fill = FieldFill.INSERT)
    private String createUser;
    /**
     * 修改时间
     */
    @ApiModelProperty(value = "修改时间")
    @TableField("update_time")
    private Date updateTime;
    /**
     * 修改人
     */
    @ApiModelProperty(value = "修改人")
    @TableField(value = "update_user", fill = FieldFill.UPDATE)
    private String updateUser;
    /**
     * 是否删除
     */
    @ApiModelProperty(value = "是否删除")
    @TableLogic
    @TableField("is_delete")
    private Integer isDelete;


}
