package com.zenith.bbykz.model.entity;

import com.baomidou.mybatisplus.annotation.*;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * <p>
 * 用户机构组详情 实体类
 * </p>
 *
 * <AUTHOR>
 * @date 2024-03-13 10:46:02
 */
@Data
@TableName("sys_user_org_group_detail")
@ApiModel("用户机构组详情")
public class SysUserOrgGroupDetail implements Serializable {

    private static final long serialVersionUID = 4708913523578193571L;

    /**
     * 主键
     */
    @ApiModelProperty(value = "主键")
    @TableId(value = "id")
    private String id;
    /**
     * 用户机构组
     */
    @ApiModelProperty(value = "用户机构组")
    @TableField("user_org_group_id")
    private String userOrgGroupId;
    /**
     * 单位ID
     */
    @ApiModelProperty(value = "单位ID")
    @TableField("org_id")
    private String orgId;
    /**
     * 层级码
     */
    @ApiModelProperty(value = "层级码")
    @TableField("org_level_code")
    private String orgLevelCode;
}
