package com.zenith.bbykz.model.entity.bz.qx;

import com.baomidou.mybatisplus.annotation.*;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
* <p>
* 核编管理-用编管理-区县用编申请计划 实体类
* </p>
*
* <AUTHOR>
* @date 2024-05-06 17:05:40
*/
@Data
@TableName("bz_qx_bzsq")
@ApiModel("核编管理-用编管理-区县用编申请计划")
public class BzQxBzsq implements Serializable {

    private static final long serialVersionUID = 3136739002027586135L;

    /**
    *主键id
    */
    @ApiModelProperty(value = "主键id")
    @TableId(value = "id")
    private String id;
    /**
    *所属区县
    */
    @ApiModelProperty(value = "所属区县")
    @TableField("geocode")
    private String geocode;
    /**
    *录入单位ID
    */
    @ApiModelProperty(value = "录入单位ID")
    @TableField("record_unit_id")
    private String recordUnitId;
    /**
    *录入单位层级码
    */
    @ApiModelProperty(value = "录入单位层级码")
    @TableField("record_unit_level_code")
    private String recordUnitLevelCode;
    /**
    *录入单位名称
    */
    @ApiModelProperty(value = "录入单位名称")
    @TableField("record_unit_name")
    private String recordUnitName;
    @ApiModelProperty(value = "核编单号")
    @TableField("approval_num")
    private String approvalNum;
    /**
    *标题
    */
    @ApiModelProperty(value = "标题")
    @TableField("title")
    private String title;
    /**
    *申请单位ID
    */
    @ApiModelProperty(value = "申请单位ID")
    @TableField("apply_unit_id")
    private String applyUnitId;
    /**
    *申请单位名称
    */
    @ApiModelProperty(value = "申请单位名称")
    @TableField("apply_unit_name")
    private String applyUnitName;
    /**
    *申请单位层级码
    */
    @ApiModelProperty(value = "申请单位层级码")
    @TableField("apply_unit_level_code")
    private String applyUnitLevelCode;
    /**
    *申请时间
    */
    @ApiModelProperty(value = "申请时间")
    @TableField("apply_time")
    private Date applyTime;
    /**
    *联系人姓名
    */
    @ApiModelProperty(value = "联系人姓名")
    @TableField("contact_name")
    private String contactName;
    /**
    *联系人电话
    */
    @ApiModelProperty(value = "联系人电话")
    @TableField("contact_phone")
    private String contactPhone;
    /**
    *审核状态
    */
    @ApiModelProperty(value = "审核状态")
    @TableField("status")
    private String status;
    @ApiModelProperty(value = "审核理由")
    @TableField("check_reason")
    private String checkReason;
    /**
    *审核时间
    */
    @ApiModelProperty(value = "审核时间")
    @TableField("check_time")
    private Date checkTime;
    /**
    *审核人
    */
    @ApiModelProperty(value = "审核人")
    @TableField("check_user_id")
    private String checkUserId;
    /**
    *创建时间
    */
    @ApiModelProperty(value = "创建时间")
    @TableField(value = "create_time", fill = FieldFill.INSERT)
    private Date createTime;
    /**
    *创建人
    */
    @ApiModelProperty(value = "创建人")
    @TableField(value = "create_user", fill = FieldFill.INSERT)
    private String createUser;
    /**
    *修改时间
    */
    @ApiModelProperty(value = "修改时间")
    @TableField("update_time")
    private Date updateTime;
    /**
    *修改人
    */
    @ApiModelProperty(value = "修改人")
    @TableField(value = "update_user", fill = FieldFill.UPDATE)
    private String updateUser;
    /**
    *是否删除
    */
    @ApiModelProperty(value = "是否删除")
    @TableLogic
    @TableField("is_delete")
    private Integer isDelete;
}
