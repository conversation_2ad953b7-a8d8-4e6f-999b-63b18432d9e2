package com.zenith.bbykz.model.entity.bz.bzsq;

import com.baomidou.mybatisplus.annotation.*;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * <p>
 * 核编管理-用编申请-明细 实体类
 * </p>
 *
 * <AUTHOR>
 * @date 2024-03-29 16:05:19
 */
@Data
@TableName("bz_bzsq_detail")
@ApiModel("核编管理-用编申请-明细")
public class BzBzsqDetail implements Serializable {

    private static final long serialVersionUID = 2015216296094944255L;

    /**
     * 主键id
     */
    @ApiModelProperty(value = "主键id")
    @TableId(value = "id")
    private String id;
    /**
     * 编制申请主键id
     */
    @ApiModelProperty(value = "编制申请主键id")
    @TableField("bzsq_id")
    private String bzsqId;
    /**
     * 编制类型
     */
    @ApiModelProperty(value = "编制类型")
    @TableField("bz_type")
    private String bzType;
    /**
     * 用编类型
     */
    @ApiModelProperty(value = "用编类型")
    @TableField("yb_type")
    private String ybType;
    /**
     * 待上编数
     */
    @ApiModelProperty(value = "待上编数")
    @TableField("dsb_num")
    private Integer dsbNum;
    /**
     * 在编数
     */
    @ApiModelProperty(value = "在编数")
    @TableField("zb_num")
    private Integer zbNum;
    /**
     * 余编数
     */
    @ApiModelProperty(value = "余编数")
    @TableField("yb_num")
    private Integer ybNum;
    /**
     * 申请数
     */
    @ApiModelProperty(value = "申请数")
    @TableField("apply_num")
    private Integer applyNum;
    /**
     * 备注
     */
    @ApiModelProperty(value = "备注")
    @TableField("remark")
    private String remark;
    /**
     * 是否被退回，1-是，0-否
     */
    @ApiModelProperty(value = "是否被退回，1-是，0-否")
    @TableField("is_back")
    private Integer isBack;
    /**
     * 创建时间
     */
    @ApiModelProperty(value = "创建时间")
    @TableField(value = "create_time", fill = FieldFill.INSERT)
    private Date createTime;
    /**
     * 创建人
     */
    @ApiModelProperty(value = "创建人")
    @TableField(value = "create_user", fill = FieldFill.INSERT)
    private String createUser;
    /**
     * 修改时间
     */
    @ApiModelProperty(value = "修改时间")
    @TableField("update_time")
    private Date updateTime;
    /**
     * 修改人
     */
    @ApiModelProperty(value = "修改人")
    @TableField(value = "update_user", fill = FieldFill.UPDATE)
    private String updateUser;
    /**
     * 是否删除
     */
    @ApiModelProperty(value = "是否删除")
    @TableLogic
    @TableField("is_delete")
    private Integer isDelete;
    /**
     * 退回原因
     */
    @ApiModelProperty(value = "退回原因")
    @TableField("back_reason")
    private String backReason;
    /**
     * 编制使用单位id
     */
    @ApiModelProperty(value = "编制使用单位id")
    @TableField("use_bz_unit_id")
    private String useBzUnitId;
    /**
     * 编制使用单位名称
     */
    @ApiModelProperty(value = "编制使用单位名称")
    @TableField("use_bz_unit_name")
    private String useBzUnitName;
    /**
     * 编制使用单位层级码
     */
    @ApiModelProperty(value = "编制使用单位层级码")
    @TableField("use_bz_unit_level_code")
    private String useBzUnitLevelCode;
}
