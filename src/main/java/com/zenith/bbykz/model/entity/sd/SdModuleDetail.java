package com.zenith.bbykz.model.entity.sd;

import com.baomidou.mybatisplus.annotation.*;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * <p>
 * 三定-三定明细 实体类
 * </p>
 *
 * <AUTHOR>
 * @date 2024-04-11 16:28:25
 */
@Data
@TableName("sd_module_detail")
@ApiModel("三定-三定明细")
public class SdModuleDetail implements Serializable {

    private static final long serialVersionUID = 8060103161803553865L;

    /**
     * 主键id
     */
    @ApiModelProperty(value = "主键id")
    @TableId(value = "id")
    private String id;
    /**
     * 三定类型主键
     */
    @ApiModelProperty(value = "三定类型主键")
    @TableField("module_id")
    private String moduleId;
    /**
     * 名称
     */
    @ApiModelProperty(value = "名称")
    @TableField("title")
    private String title;
    /**
     * 描述
     */
    @ApiModelProperty(value = "描述")
    @TableField("describe")
    private String describe;
    /**
     * 备注
     */
    @ApiModelProperty(value = "备注")
    @TableField("remark")
    private String remark;
    /**
     * 排序号
     */
    @ApiModelProperty(value = "排序号")
    @TableField("sort")
    private Integer sort;
    /**
     *
     */
    @ApiModelProperty(value = "")
    @TableField(value = "create_time", fill = FieldFill.INSERT)
    private Date createTime;
    /**
     *
     */
    @ApiModelProperty(value = "")
    @TableField(value = "create_user", fill = FieldFill.INSERT)
    private String createUser;
    /**
     *
     */
    @ApiModelProperty(value = "")
    @TableField("update_time")
    private Date updateTime;
    /**
     *
     */
    @ApiModelProperty(value = "")
    @TableField(value = "update_user", fill = FieldFill.UPDATE)
    private String updateUser;
    /**
     *
     */
    @ApiModelProperty(value = "")
    @TableLogic
    @TableField("is_delete")
    private Integer isDelete;
    /**
     * 单位ID
     */
    @ApiModelProperty(value = "单位ID")
    @TableField("unit_id")
    private String unitId;
    /**
     * 单位层级码
     */
    @ApiModelProperty(value = "单位层级码")
    @TableField("unit_level_code")
    private String unitLevelCode;
    /**
     * 单位名称
     */
    @ApiModelProperty(value = "单位名称")
    @TableField("unit_name")
    private String unitName;
    /**
     * 区划code
     */
    @ApiModelProperty(value = "区划code")
    @TableField("geocode")
    private String geocode;

    /**
     * 文号
     */
    @ApiModelProperty(value = "文号")
    @TableField("record_num")
    private String recordNum;
}
