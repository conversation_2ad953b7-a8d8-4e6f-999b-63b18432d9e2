package com.zenith.bbykz.model.entity.sd;

import com.baomidou.mybatisplus.annotation.*;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * <p>
 * 三定-一件事 实体类
 * </p>
 *
 * <AUTHOR>
 * @date 2024-04-11 16:28:25
 */
@Data
@TableName("sd_one_thing")
@ApiModel("三定-一件事")
public class SdOneThing implements Serializable {

    private static final long serialVersionUID = 6315714459906981610L;

    /**
     * 主键id
     */
    @ApiModelProperty(value = "主键id")
    @TableId(value = "id")
    private String id;
    /**
     * 一件事类型
     */
    @ApiModelProperty(value = "一件事类型")
    @TableField("one_type")
    private String oneType;
    /**
     * 名称
     */
    @ApiModelProperty(value = "名称")
    @TableField("title")
    private String title;
    /**
     * 描述
     */
    @ApiModelProperty(value = "描述")
    @TableField("describe")
    private String describe;
    /**
     * 备注
     */
    @ApiModelProperty(value = "备注")
    @TableField("remark")
    private String remark;
    /**
     * 排序号
     */
    @ApiModelProperty(value = "排序号")
    @TableField("sort")
    private Integer sort;
    /**
     *
     */
    @ApiModelProperty(value = "")
    @TableField(value = "create_time", fill = FieldFill.INSERT)
    private Date createTime;
    /**
     *
     */
    @ApiModelProperty(value = "")
    @TableField(value = "create_user", fill = FieldFill.INSERT)
    private String createUser;
    /**
     *
     */
    @ApiModelProperty(value = "")
    @TableField("update_time")
    private Date updateTime;
    /**
     *
     */
    @ApiModelProperty(value = "")
    @TableField(value = "update_user", fill = FieldFill.UPDATE)
    private String updateUser;
    /**
     *
     */
    @ApiModelProperty(value = "")
    @TableLogic
    @TableField("is_delete")
    private Integer isDelete;
}
