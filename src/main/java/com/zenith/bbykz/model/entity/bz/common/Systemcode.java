package com.zenith.bbykz.model.entity.bz.common;

import com.baomidou.mybatisplus.annotation.*;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
* <p>
* SYSTEMCODE 实体类
* </p>
*
* <AUTHOR>
* @date 2024-08-27 15:52:58
*/
@Data
@TableName("SYSTEMCODE")
@ApiModel("SYSTEMCODE")
public class Systemcode implements Serializable {

    private static final long serialVersionUID = 8326180568761282828L;

    /**
    *system_code
    */
    @ApiModelProperty(value = "system_code")
    @TableId(value = "SYSTEM_CODE")
    private String systemCode;
    /**
    *system_code_name
    */
    @ApiModelProperty(value = "system_code_name")
    @TableField("SYSTEM_CODE_NAME")
    private String systemCodeName;
    /**
    *system_code_type
    */
    @ApiModelProperty(value = "system_code_type")
    @TableField("SYSTEM_CODE_TYPE")
    private String systemCodeType;
    /**
    *system_code_desp
    */
    @ApiModelProperty(value = "system_code_desp")
    @TableField("SYSTEM_CODE_DESP")
    private String systemCodeDesp;
    /**
    *system_code_standby1
    */
    @ApiModelProperty(value = "system_code_standby1")
    @TableField("SYSTEM_CODE_STANDBY1")
    private String systemCodeStandby1;
    /**
    *system_code_order
    */
    @ApiModelProperty(value = "system_code_order")
    @TableField("SYSTEM_CODE_ORDER")
    private Integer systemCodeOrder;
}
