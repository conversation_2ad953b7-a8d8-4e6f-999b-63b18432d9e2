package com.zenith.bbykz.model.entity.register.reconnaissance;

import com.baomidou.mybatisplus.annotation.*;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * <p>
 * 登记信息-内部事务-实地勘察 实体类
 * </p>
 *
 * <AUTHOR>
 * @date 2024-03-28 11:28:37
 */
@Data
@TableName("register_reconnaissance")
@ApiModel("登记信息-内部事务-实地勘察")
public class RegisterReconnaissance implements Serializable {

    private static final long serialVersionUID = 5295226519379555463L;

    /**
     * 主键id
     */
    @ApiModelProperty(value = "主键id")
    @TableId(value = "id")
    private String id;
    /**
     * 名称
     */
    @ApiModelProperty(value = "名称")
    @TableField("unit_name")
    private String unitName;
    /**
     * 统一社会信用代码
     */
    @ApiModelProperty(value = "统一社会信用代码")
    @TableField("credit_code")
    private String creditCode;
    /**
     * 法定代表人
     */
    @ApiModelProperty(value = "法定代表人")
    @TableField("legal_representative")
    private String legalRepresentative;
    /**
     * 经费来源
     */
    @ApiModelProperty(value = "经费来源")
    @TableField("source_of_funds")
    private String sourceOfFunds;
    /**
     * 开办资金
     */
    @ApiModelProperty(value = "开办资金")
    @TableField("start_up_capital")
    private String startUpCapital;
    /**
     * 举办单位
     */
    @ApiModelProperty(value = "举办单位")
    @TableField("hold_organizer")
    private String holdOrganizer;
    /**
     * 宗旨和业务范围
     */
    @ApiModelProperty(value = "宗旨和业务范围")
    @TableField("business_scope")
    private String businessScope;
    /**
     * 开始时间
     */
    @ApiModelProperty(value = "开始时间")
    @TableField("start_time")
    private Date startTime;
    /**
     * 结束时间
     */
    @ApiModelProperty(value = "结束时间")
    @TableField("end_time")
    private Date endTime;
    /**
     * 勘察时间
     */
    @ApiModelProperty(value = "勘察时间")
    @TableField("check_date")
    private Date checkDate;
    /**
     * 勘察原因
     */
    @ApiModelProperty(value = "勘察原因")
    @TableField("check_reason")
    private String checkReason;
    /**
     * 勘察结果 JGSY_SDKC_STATUS
     */
    @ApiModelProperty(value = "勘察结果 JGSY_SDKC_STATUS")
    @TableField("check_result")
    private String checkResult;
    /**
     * 勘察单位
     */
    @ApiModelProperty(value = "勘察单位")
    @TableField("check_unit")
    private String checkUnit;
    /**
     * 勘察人
     */
    @ApiModelProperty(value = "勘察人")
    @TableField("check_user")
    private String checkUser;
    /**
     * 勘察人电话
     */
    @ApiModelProperty(value = "勘察人电话")
    @TableField("check_user_phone")
    private String checkUserPhone;
    /**
     * 勘察记录
     */
    @ApiModelProperty(value = "勘察记录")
    @TableField("check_record")
    private String checkRecord;
    /**
     * 创建时间
     */
    @ApiModelProperty(value = "创建时间")
    @TableField(value = "create_time", fill = FieldFill.INSERT)
    private Date createTime;
    /**
     * 创建人
     */
    @ApiModelProperty(value = "创建人")
    @TableField(value = "create_user", fill = FieldFill.INSERT)
    private String createUser;
    /**
     * 修改时间
     */
    @ApiModelProperty(value = "修改时间")
    @TableField("update_time")
    private Date updateTime;
    /**
     * 修改人
     */
    @ApiModelProperty(value = "修改人")
    @TableField(value = "update_user", fill = FieldFill.UPDATE)
    private String updateUser;
    /**
     * 是否删除
     */
    @ApiModelProperty(value = "是否删除")
    @TableLogic
    @TableField("is_delete")
    private Integer isDelete;
    @ApiModelProperty(value = "证书编号")
    @TableField(value = "certificate_num")
    private String certificateNum;
    @ApiModelProperty(value = "住所")
    @TableField(value = "domicile")
    private String domicile;
    @ApiModelProperty(value = "机构id")
    @TableField(value = "org_id")
    private String orgId;
    @ApiModelProperty(value = "机构层级码")
    @TableField(value = "org_level_code")
    private String orgLevelCode;
}
