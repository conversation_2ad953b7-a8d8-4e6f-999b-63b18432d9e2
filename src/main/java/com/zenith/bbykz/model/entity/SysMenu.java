package com.zenith.bbykz.model.entity;

import com.baomidou.mybatisplus.annotation.*;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * <p>
 * 菜单表 实体类
 * </p>
 *
 * <AUTHOR>
 * @date 2023-09-14 17:14:26
 */
@Data
@TableName("sys_menu")
@ApiModel("菜单表")
public class SysMenu implements Serializable {

    private static final long serialVersionUID = 192257008283793540L;

    /**
     *
     */
    @ApiModelProperty(value = "")
    @TableId(value = "id", type = IdType.ASSIGN_UUID)
    private String id;
    /**
     *
     */
    @ApiModelProperty(value = "")
    @TableField("code")
    private String code;
    /**
     *
     */
    @ApiModelProperty(value = "")
    @TableField("parent_code")
    private String parentCode;

    @TableField(exist = false)
    private String parentName;
    /**
     *
     */
    @ApiModelProperty(value = "")
    @TableField("name")
    private String name;
    /**
     *
     */
    @ApiModelProperty(value = "")
    @TableField("description")
    private String description;
    /**
     *
     */
    @ApiModelProperty(value = "")
    @TableField("enable")
    private Integer enable;
    /**
     *
     */
    @ApiModelProperty(value = "")
    @TableField("is_leaf")
    private Boolean isLeaf;
    /**
     *
     */
    @ApiModelProperty(value = "")
    @TableField("create_time")
    private Date createTime;
    /**
     *
     */
    @ApiModelProperty(value = "")
    @TableField("system_code")
    private String systemCode;
}
