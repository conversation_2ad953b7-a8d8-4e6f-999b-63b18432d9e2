package com.zenith.bbykz.model.entity.register.random;

import com.baomidou.mybatisplus.annotation.*;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
* <p>
* 登记信息-内部事务-双随机检查 实体类
* </p>
*
* <AUTHOR>
* @date 2024-05-06 15:48:28
*/
@Data
@TableName("register_double_random")
@ApiModel("登记信息-内部事务-双随机检查")
public class DoubleRandom implements Serializable {

    private static final long serialVersionUID = 6982184370364595033L;

    /**
    *主键id
    */
    @ApiModelProperty(value = "主键id")
    @TableId(value = "id")
    private String id;
    /**
    *录入单位ID
    */
    @ApiModelProperty(value = "录入单位ID")
    @TableField("record_unit_id")
    private String recordUnitId;
    /**
    *录入单位层级码
    */
    @ApiModelProperty(value = "录入单位层级码")
    @TableField("record_unit_level_code")
    private String recordUnitLevelCode;
    /**
    *录入单位名称
    */
    @ApiModelProperty(value = "录入单位名称")
    @TableField("record_unit_name")
    private String recordUnitName;
    /**
    *名称
    */
    @ApiModelProperty(value = "名称")
    @TableField("unit_name")
    private String unitName;
    /**
    *统一社会信用代码
    */
    @ApiModelProperty(value = "统一社会信用代码")
    @TableField("credit_code")
    private String creditCode;
    /**
    *证书编号
    */
    @ApiModelProperty(value = "证书编号")
    @TableField("certificate_num")
    private String certificateNum;
    /**
    *法定代表人
    */
    @ApiModelProperty(value = "法定代表人")
    @TableField("legal_representative")
    private String legalRepresentative;
    /**
    *经费来源
    */
    @ApiModelProperty(value = "经费来源")
    @TableField("source_of_funds")
    private String sourceOfFunds;
    /**
    *开办资金
    */
    @ApiModelProperty(value = "开办资金")
    @TableField("start_up_capital")
    private String startUpCapital;
    /**
    *举办单位
    */
    @ApiModelProperty(value = "举办单位")
    @TableField("hold_organizer")
    private String holdOrganizer;
    /**
    *宗旨和业务范围
    */
    @ApiModelProperty(value = "宗旨和业务范围")
    @TableField("business_scope")
    private String businessScope;
    /**
    *住所
    */
    @ApiModelProperty(value = "住所")
    @TableField("domicile")
    private String domicile;
    /**
    *开始时间
    */
    @ApiModelProperty(value = "开始时间")
    @TableField("start_time")
    private Date startTime;
    /**
    *结束时间
    */
    @ApiModelProperty(value = "结束时间")
    @TableField("end_time")
    private Date endTime;
    /**
    *
    */
    @ApiModelProperty(value = "")
    @TableField("org_id")
    private String orgId;
    /**
    *
    */
    @ApiModelProperty(value = "")
    @TableField("org_level_code")
    private String orgLevelCode;
    /**
    *抽取时间
    */
    @ApiModelProperty(value = "抽取时间")
    @TableField("check_date")
    private Date checkDate;
    /**
    *抽取方式
    */
    @ApiModelProperty(value = "抽取方式")
    @TableField("check_type")
    private String checkType;
    /**
    *检查结果
    */
    @ApiModelProperty(value = "检查结果")
    @TableField("check_result")
    private String checkResult;
    /**
    *检查单位
    */
    @ApiModelProperty(value = "检查单位")
    @TableField("check_unit")
    private String checkUnit;
    /**
    *联系人
    */
    @ApiModelProperty(value = "联系人")
    @TableField("check_user")
    private String checkUser;
    /**
    *联系电话
    */
    @ApiModelProperty(value = "联系电话")
    @TableField("check_user_phone")
    private String checkUserPhone;
    /**
    *检查记录
    */
    @ApiModelProperty(value = "检查记录")
    @TableField("check_record")
    private String checkRecord;
    /**
    *创建时间
    */
    @ApiModelProperty(value = "创建时间")
    @TableField(value = "create_time", fill = FieldFill.INSERT)
    private Date createTime;
    /**
    *创建人
    */
    @ApiModelProperty(value = "创建人")
    @TableField(value = "create_user", fill = FieldFill.INSERT)
    private String createUser;
    /**
    *修改时间
    */
    @ApiModelProperty(value = "修改时间")
    @TableField("update_time")
    private Date updateTime;
    /**
    *修改人
    */
    @ApiModelProperty(value = "修改人")
    @TableField(value = "update_user", fill = FieldFill.UPDATE)
    private String updateUser;
    /**
    *是否删除
    */
    @ApiModelProperty(value = "是否删除")
    @TableLogic
    @TableField("is_delete")
    private Integer isDelete;
}
