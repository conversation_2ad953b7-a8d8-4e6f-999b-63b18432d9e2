package com.zenith.bbykz.model.entity;

import com.baomidou.mybatisplus.annotation.*;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * <p>
 * sys_role_menu 实体类
 * </p>
 *
 * <AUTHOR>
 * @date 2023-09-14 17:14:26
 */
@Data
@TableName("sys_role_menu")
@ApiModel("sys_role_menu")
public class SysRoleMenu implements Serializable {

    private static final long serialVersionUID = 3510153899409230242L;

    /**
     *
     */
    @ApiModelProperty(value = "")
    @TableId(value = "id", type = IdType.ASSIGN_UUID)
    private String id;
    /**
     *
     */
    @ApiModelProperty(value = "")
    @TableField("role_id")
    private String roleId;
    /**
     *
     */
    @ApiModelProperty(value = "")
    @TableField("menu_id")
    private String menuId;
    @ApiModelProperty(value = "")
    @TableField("menu_code")
    private String menuCode;
}
