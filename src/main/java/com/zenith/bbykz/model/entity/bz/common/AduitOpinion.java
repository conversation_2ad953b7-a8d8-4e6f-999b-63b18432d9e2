package com.zenith.bbykz.model.entity.bz.common;

import com.baomidou.mybatisplus.annotation.*;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
* <p>
* 核编管理-常用审批意见 实体类
* </p>
*
* <AUTHOR>
* @date 2024-05-17 11:10:37
*/
@Data
@TableName("bz_aduit_opinion")
@ApiModel("核编管理-常用审批意见")
public class AduitOpinion implements Serializable {

    private static final long serialVersionUID = 3871875885837563293L;

    /**
    *主键id
    */
    @ApiModelProperty(value = "主键id")
    @TableId(value = "id")
    private String id;
    /**
    *模块名称
    */
    @ApiModelProperty(value = "模块名称")
    @TableField("module")
    private String module;
    /**
    *意见
    */
    @ApiModelProperty(value = "意见")
    @TableField("name")
    private String name;
    /**
    *排序
    */
    @ApiModelProperty(value = "排序")
    @TableField("sort")
    private Integer sort;
    /**
    *创建时间
    */
    @ApiModelProperty(value = "创建时间")
    @TableField(value = "create_time", fill = FieldFill.INSERT)
    private Date createTime;
    /**
    *创建人
    */
    @ApiModelProperty(value = "创建人")
    @TableField(value = "create_user", fill = FieldFill.INSERT)
    private String createUser;
    /**
    *修改时间
    */
    @ApiModelProperty(value = "修改时间")
    @TableField("update_time")
    private Date updateTime;
    /**
    *修改人
    */
    @ApiModelProperty(value = "修改人")
    @TableField(value = "update_user", fill = FieldFill.UPDATE)
    private String updateUser;
    /**
    *是否删除
    */
    @ApiModelProperty(value = "是否删除")
    @TableLogic
    @TableField("is_delete")
    private Integer isDelete;
}
