package com.zenith.bbykz.model.entity.bz.bzsq;

import com.baomidou.mybatisplus.annotation.*;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * <p>
 * 核编管理-用编审核 实体类
 * </p>
 *
 * <AUTHOR>
 * @date 2024-04-02 11:51:22
 */
@Data
@TableName("bz_bzsq_flow")
@ApiModel("核编管理-用编审核")
public class BzBzsqFlow implements Serializable {

    private static final long serialVersionUID = 8310685606356272498L;

    /**
     * 主键id
     */
    @ApiModelProperty(value = "主键id")
    @TableId(value = "id")
    private String id;
    /**
     * 编制申请主键id
     */
    @ApiModelProperty(value = "编制申请主键id")
    @TableField("bzsq_id")
    private String bzsqId;
    /**
     * 当前状态
     */
    @ApiModelProperty(value = "当前状态")
    @TableField("bzsq_status")
    private String bzsqStatus;
    /**
     * 状态描述
     */
    @ApiModelProperty(value = "状态描述")
    @TableField("status_desc")
    private String statusDesc;
    /**
     * 是否合并审核
     */
    @ApiModelProperty(value = "是否合并审核")
    @TableField("is_merge")
    private Integer isMerge;
    /**
     * 意见
     */
    @ApiModelProperty(value = "意见")
    @TableField("opinion")
    private String opinion;
    /**
     * 是否同意
     */
    @ApiModelProperty(value = "是否同意")
    @TableField("is_agree")
    private Integer isAgree;
    /**
     * 是否结束
     */
    @ApiModelProperty(value = "是否结束")
    @TableField("is_finish")
    private Integer isFinish;
    /**
     * 创建时间
     */
    @ApiModelProperty(value = "创建时间")
    @TableField(value = "create_time", fill = FieldFill.INSERT)
    private Date createTime;
    /**
     * 创建人
     */
    @ApiModelProperty(value = "创建人")
    @TableField(value = "create_user", fill = FieldFill.INSERT)
    private String createUser;
    /**
     * 修改时间
     */
    @ApiModelProperty(value = "修改时间")
    @TableField("update_time")
    private Date updateTime;
    /**
     * 修改人
     */
    @ApiModelProperty(value = "修改人")
    @TableField(value = "update_user", fill = FieldFill.UPDATE)
    private String updateUser;
    /**
     * 是否删除
     */
    @ApiModelProperty(value = "是否删除")
    @TableLogic
    @TableField("is_delete")
    private Integer isDelete;
}
