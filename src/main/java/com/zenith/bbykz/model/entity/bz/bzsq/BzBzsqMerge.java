package com.zenith.bbykz.model.entity.bz.bzsq;

import com.baomidou.mybatisplus.annotation.*;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * <p>
 * 核编管理-用编审核-合并审核 实体类
 * </p>
 *
 * <AUTHOR>
 * @date 2024-04-03 15:26:58
 */
@Data
@TableName("bz_bzsq_merge")
@ApiModel("核编管理-用编审核-合并审核")
public class BzBzsqMerge implements Serializable {

    private static final long serialVersionUID = 1282162421587263787L;

    /**
     * 主键id
     */
    @ApiModelProperty(value = "主键id")
    @TableId(value = "id")
    private String id;
    /**
     *
     */
    @ApiModelProperty(value = "")
    @TableField("title")
    private String title;
    /**
     * 标题
     */
    @ApiModelProperty(value = "标题")
    @TableField("status")
    private String status;
    @ApiModelProperty(value = "区划")
    @TableField("geocode")
    private String geocode;
    /**
     * 是否同意
     */
    @ApiModelProperty(value = "是否同意")
    @TableField("is_agree")
    private Integer isAgree;
    /**
     * 是否合并审核
     */
    @ApiModelProperty(value = "是否合并审核")
    @TableField("bzsq_id_list")
    private String bzsqIdList;
    /**
     * 创建人姓名
     */
    @ApiModelProperty(value = "创建人姓名")
    @TableField("create_user_name")
    private String createUserName;
    @ApiModelProperty(value = "意见")
    @TableField("opinion")
    private String opinion;
    /**
     * 创建时间
     */
    @ApiModelProperty(value = "创建时间")
    @TableField(value = "create_time", fill = FieldFill.INSERT)
    private Date createTime;
    /**
     * 创建人
     */
    @ApiModelProperty(value = "创建人")
    @TableField(value = "create_user", fill = FieldFill.INSERT)
    private String createUser;
    /**
     * 修改时间
     */
    @ApiModelProperty(value = "修改时间")
    @TableField("update_time")
    private Date updateTime;
    /**
     * 修改人
     */
    @ApiModelProperty(value = "修改人")
    @TableField(value = "update_user", fill = FieldFill.UPDATE)
    private String updateUser;
    /**
     * 是否删除
     */
    @ApiModelProperty(value = "是否删除")
    @TableLogic
    @TableField("is_delete")
    private Integer isDelete;
    @ApiModelProperty(value = "备注")
    @TableField(value = "remark")
    private String remark;
}
