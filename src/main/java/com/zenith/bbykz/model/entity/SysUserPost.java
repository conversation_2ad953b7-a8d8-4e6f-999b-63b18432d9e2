package com.zenith.bbykz.model.entity;

import com.baomidou.mybatisplus.annotation.*;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * <p>
 * 用户职位信息 实体类
 * </p>
 *
 * <AUTHOR>
 * @date 2024-03-26 15:04:24
 */
@Data
@TableName("sys_user_post")
@ApiModel("用户职位信息")
public class SysUserPost implements Serializable {

    private static final long serialVersionUID = 622568481084980359L;

    /**
     * 主键
     */
    @ApiModelProperty(value = "主键")
    @TableId(value = "id")
    private String id;
    /**
     * 用户id
     */
    @ApiModelProperty(value = "用户id")
    @TableField("user_id")
    private String userId;
    /**
     * 部门ID
     */
    @ApiModelProperty(value = "部门ID")
    @TableField("dept_id")
    private String deptId;
    /**
     * 部门层级码
     */
    @ApiModelProperty(value = "部门层级码")
    @TableField("dept_level_code")
    private String deptLevelCode;
    /**
     * 单位ID
     */
    @ApiModelProperty(value = "单位ID")
    @TableField("unit_id")
    private String unitId;
    /**
     * 单位层级码
     */
    @ApiModelProperty(value = "单位层级码")
    @TableField("unit_level_code")
    private String unitLevelCode;
    /**
     * 是否主职务
     */
    @ApiModelProperty(value = "是否主职务")
    @TableField("main_job")
    private Integer mainJob;
    /**
     *
     */
    @ApiModelProperty(value = "")
    @TableField("join_date")
    private Date joinDate;
    /**
     * 管理单位ID
     */
    @ApiModelProperty(value = "管理单位ID")
    @TableField("manage_id")
    private String manageId;
    /**
     * 管理单位层级码
     */
    @ApiModelProperty(value = "管理单位层级码")
    @TableField("manage_code")
    private String manageCode;
    /**
     * 用户机构组
     */
    @ApiModelProperty(value = "用户机构组")
    @TableField("user_group_org_id")
    private String userGroupOrgId;
    /**
     * 排序
     */
    @ApiModelProperty(value = "排序")
    @TableField("sort")
    private Long sort;
    /**
     * 职务
     */
    @ApiModelProperty(value = "职务")
    @TableField("post_name")
    private String postName;
    /**
     *
     */
    @ApiModelProperty(value = "")
    @TableField(value = "create_time", fill = FieldFill.INSERT)
    private Date createTime;
    /**
     *
     */
    @ApiModelProperty(value = "")
    @TableField("update_time")
    private Date updateTime;
    /**
     *
     */
    @ApiModelProperty(value = "")
    @TableLogic
    @TableField("is_delete")
    private Integer isDelete;
    /**
     * 拉取时间
     */
    @ApiModelProperty(value = "拉取时间")
    @TableField("pull_time")
    private Date pullTime;
}
