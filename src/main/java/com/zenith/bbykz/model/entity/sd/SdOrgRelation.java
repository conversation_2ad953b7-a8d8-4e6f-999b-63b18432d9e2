package com.zenith.bbykz.model.entity.sd;

import com.baomidou.mybatisplus.annotation.*;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * <p>
 * 三定-三定机构关联关系 实体类
 * </p>
 *
 * <AUTHOR>
 * @date 2024-04-11 16:28:25
 */
@Data
@TableName("sd_org_relation")
@ApiModel("三定-三定机构关联关系")
public class SdOrgRelation implements Serializable {

    private static final long serialVersionUID = 507886537974396033L;

    /**
     * 主键id
     */
    @ApiModelProperty(value = "主键id")
    @TableId(value = "id")
    private String id;
    /**
     * 单位ID
     */
    @ApiModelProperty(value = "单位ID")
    @TableField("unit_id")
    private String unitId;
    /**
     * 单位层级码
     */
    @ApiModelProperty(value = "单位层级码")
    @TableField("unit_level_code")
    private String unitLevelCode;
    /**
     * 三定类型数量
     */
    @ApiModelProperty(value = "三定类型数量")
    @TableField("module_count")
    private Integer moduleCount;
    /**
     * 三定明细数量
     */
    @ApiModelProperty(value = "三定明细数量")
    @TableField("module_detail_count")
    private Integer moduleDetailCount;
    /**
     * 细化职责数量
     */
    @ApiModelProperty(value = "细化职责数量")
    @TableField("duty_detail_count")
    private Integer dutyDetailCount;
    /**
     * 核心业务数量
     */
    @ApiModelProperty(value = "核心业务数量")
    @TableField("core_business_count")
    private Integer coreBusinessCount;
    /**
     * 业务事项数量
     */
    @ApiModelProperty(value = "业务事项数量")
    @TableField("business_item_count")
    private Integer businessItemCount;
    /**
     * 一件事数量
     */
    @ApiModelProperty(value = "一件事数量")
    @TableField("one_thing_count")
    private Integer oneThingCount;
    /**
     *
     */
    @ApiModelProperty(value = "")
    @TableField(value = "create_time", fill = FieldFill.INSERT)
    private Date createTime;
    /**
     *
     */
    @ApiModelProperty(value = "")
    @TableField(value = "create_user", fill = FieldFill.INSERT)
    private String createUser;
    /**
     *
     */
    @ApiModelProperty(value = "")
    @TableField("update_time")
    private Date updateTime;
    /**
     *
     */
    @ApiModelProperty(value = "")
    @TableField(value = "update_user", fill = FieldFill.UPDATE)
    private String updateUser;
    /**
     *
     */
    @ApiModelProperty(value = "")
    @TableLogic
    @TableField("is_delete")
    private Integer isDelete;
}
