package com.zenith.bbykz.model.entity.bz.qx;

import com.baomidou.mybatisplus.annotation.*;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
* <p>
* 核编管理-用编管理-区县用编申请计划明细 实体类
* </p>
*
* <AUTHOR>
* @date 2024-05-06 17:05:40
*/
@Data
@TableName("bz_qx_bzsq_detail")
@ApiModel("核编管理-用编管理-区县用编申请计划明细")
public class BzQxBzsqDetail implements Serializable {

    private static final long serialVersionUID = 2435720792813911871L;

    /**
    *主键id
    */
    @ApiModelProperty(value = "主键id")
    @TableId(value = "id")
    private String id;
    /**
    *录入单位ID
    */
    @ApiModelProperty(value = "录入单位ID")
    @TableField("qx_bzsq_id")
    private String qxBzsqId;
    /**
    *编制类型，字典表-BZ_BZLX
    */
    @ApiModelProperty(value = "编制类型，字典表-BZ_BZLX")
    @TableField("bz_type")
    private String bzType;
    /**
    *用编类型，字典表-BZ_YBLX
    */
    @ApiModelProperty(value = "用编类型，字典表-BZ_YBLX")
    @TableField("yb_type")
    private String ybType;
    /**
    *申请用途
    */
    @ApiModelProperty(value = "申请用途")
    @TableField("apply_used")
    private String applyUsed;
    /**
    *有效期限
    */
    @ApiModelProperty(value = "有效期限")
    @TableField("valid_time")
    private Date validTime;
    /**
    *申请数量
    */
    @ApiModelProperty(value = "申请数量")
    @TableField("apply_num")
    private Integer applyNum;
    /**
    *备注
    */
    @ApiModelProperty(value = "备注")
    @TableField("remark")
    private String remark;
    /**
    *创建时间
    */
    @ApiModelProperty(value = "创建时间")
    @TableField(value = "create_time", fill = FieldFill.INSERT)
    private Date createTime;
    /**
    *创建人
    */
    @ApiModelProperty(value = "创建人")
    @TableField(value = "create_user", fill = FieldFill.INSERT)
    private String createUser;
    /**
    *修改时间
    */
    @ApiModelProperty(value = "修改时间")
    @TableField("update_time")
    private Date updateTime;
    /**
    *修改人
    */
    @ApiModelProperty(value = "修改人")
    @TableField(value = "update_user", fill = FieldFill.UPDATE)
    private String updateUser;
    /**
    *是否删除
    */
    @ApiModelProperty(value = "是否删除")
    @TableLogic
    @TableField("is_delete")
    private Integer isDelete;
}
