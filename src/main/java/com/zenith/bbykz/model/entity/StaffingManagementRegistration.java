package com.zenith.bbykz.model.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * <p>
 * 用编管理登记表 实体类
 * </p>
 *
 * <AUTHOR>
 * @date 2023-09-28 14:28:24
 */
@Data
@TableName("STAFFING_MANAGEMENT_REGISTRATION")
@ApiModel("用编管理登记表")
public class StaffingManagementRegistration implements Serializable {

    private static final long serialVersionUID = 6230492837201987252L;

    /**
     * 主键id
     */
    @ApiModelProperty(value = "主键id")
    @TableId(value = "ID", type = IdType.ASSIGN_UUID)
    private String id;
    /**
     * 用编管理
     */
    @ApiModelProperty(value = "用编管理")
    @TableField("STAFFING_MANAGEMENT_ID")
    private String staffingManagementId;
    /**
     * 登记时间
     */
    @ApiModelProperty(value = "登记时间")
    @TableField("REGISTRATION_TIME")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date registrationTime;
    /**
     * 使用数量
     */
    @ApiModelProperty(value = "使用数量")
    @TableField("USED_NUMBER")
    private Integer usedNumber;

    /**
     * 备注
     */
    @ApiModelProperty(value = "备注")
    @TableField("COMMENTS")
    private String comments;
}
