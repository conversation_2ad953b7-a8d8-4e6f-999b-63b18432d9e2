package com.zenith.bbykz.model.entity.register.interior;

import com.baomidou.mybatisplus.annotation.*;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * <p>
 * 登记信息-内部事务-法人库 实体类
 * </p>
 *
 * <AUTHOR>
 * @date 2024-03-12 14:55:18
 */
@Data
@TableName("register_legal_person_lib")
@ApiModel("登记信息-内部事务-法人库")
public class RegisterLegalPersonLib implements Serializable {

    private static final long serialVersionUID = 5738583931061816566L;

    /**
     * 主键id
     */
    @ApiModelProperty(value = "主键id")
    @TableId(value = "id")
    private String id;
    /**
     * 名称
     */
    @ApiModelProperty(value = "名称")
    @TableField("name")
    private String name;
    /**
     * 身份证
     */
    @ApiModelProperty(value = "身份证")
    @TableField("id_card")
    private String idCard;
    /**
     * 单位名称
     */
    @ApiModelProperty(value = "单位名称")
    @TableField("unit_name")
    private String unitName;
    /**
     * 注册时间
     */
    @ApiModelProperty(value = "注册时间")
    @TableField("register_time")
    private Date registerTime;
    /**
     * 有效期开始时间
     */
    @ApiModelProperty(value = "有效期开始时间")
    @TableField("start_time")
    private Date startTime;
    /**
     * 有效期结束时间
     */
    @ApiModelProperty(value = "有效期结束时间")
    @TableField("end_time")
    private Date endTime;
    @ApiModelProperty(value = "备注")
    @TableField("remark")
    private String remark;
    /**
     * 版本号
     */
    @ApiModelProperty(value = "版本号")
    @TableField("version")
    private Integer version;
    /**
     * 创建时间
     */
    @ApiModelProperty(value = "创建时间")
    @TableField(value = "create_time", fill = FieldFill.INSERT)
    private Date createTime;
    /**
     * 创建人
     */
    @ApiModelProperty(value = "创建人")
    @TableField(value = "create_user", fill = FieldFill.INSERT)
    private String createUser;
    /**
     * 修改时间
     */
    @ApiModelProperty(value = "修改时间")
    @TableField("update_time")
    private Date updateTime;
    /**
     * 修改人
     */
    @ApiModelProperty(value = "修改人")
    @TableField(value = "update_user", fill = FieldFill.UPDATE)
    private String updateUser;
    /**
     * 是否删除
     */
    @ApiModelProperty(value = "是否删除")
    @TableLogic
    @TableField("is_delete")
    private Integer isDelete;
}
