package com.zenith.bbykz.model.entity;

import com.baomidou.mybatisplus.annotation.*;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * <p>
 * 机构信息 实体类
 * </p>
 *
 * <AUTHOR>
 * @date 2023-09-19 14:29:03
 */
@Data
@TableName("org_info")
@ApiModel("机构信息")
public class OrgInfo implements Serializable {

    private static final long serialVersionUID = 4375832555365579696L;

    /**
     *
     */
    @ApiModelProperty(value = "主键")
    @TableId(value = "id", type = IdType.ASSIGN_UUID)
    private String id;
    /**
     *
     */
    @ApiModelProperty(value = "层级码")
    @TableField("level_code")
    private String levelCode;
    /**
     *
     */
    @ApiModelProperty(value = "机构名称")
    @TableField("name")
    private String name;
    /**
     *
     */
    @ApiModelProperty(value = "机构简称")
    @TableField("short_name")
    private String shortName;
    /**
     *
     */
    @ApiModelProperty(value = "上级层级码")
    @TableField("parent_code")
    private String parentCode;
    @ApiModelProperty(value = "所属机构")
    @TableField(exist = false)
    private String parentName;

    /**
     *
     */
    @ApiModelProperty(value = "类型")
    @TableField("type_code")
    private String typeCode;
    /**
     *
     */
    @ApiModelProperty(value = "是否删除")
    @TableLogic
    @TableField("is_delete")
    private Integer isDelete;
    /**
     *
     */
    @ApiModelProperty(value = "创建时间")
    @TableField(value = "create_time", fill = FieldFill.INSERT)
    private Date createTime;
    /**
     *
     */
    @ApiModelProperty(value = "修改时间")
    @TableField(value = "update_time", fill = FieldFill.UPDATE)
    private Date updateTime;
    /**
     *
     */
    @ApiModelProperty(value = "删除时间")
    @TableField("delete_time")
    private Date deleteTime;
    /**
     * 排序
     */
    @ApiModelProperty(value = "排序")
    @TableField("sort")
    private Long sort;
    /**
     * 实名制机构表主键
     */
    @ApiModelProperty(value = "实名制机构表主键")
    @TableField("jgsy_code")
    private String jgsyCode;
    /**
     * 其他全称
     */
    @ApiModelProperty(value = "其他全称")
    @TableField("jgsy_other_name")
    private String jgsyOtherName;
    /**
     * 规范简称
     */
    @ApiModelProperty(value = "规范简称")
    @TableField("gfjc")
    private String gfjc;
    /**
     * 习惯简称
     */
    @ApiModelProperty(value = "习惯简称")
    @TableField("xgjc")
    private String xgjc;
    /**
     * 机构规格
     */
    @ApiModelProperty(value = "机构规格")
    @TableField("jggg")
    private String jggg;
    /**
     * 机构性质
     */
    @ApiModelProperty(value = "机构性质")
    @TableField("jgxz")
    private String jgxz;
    /**
     * 系统类别
     */
    @ApiModelProperty(value = "系统类别")
    @TableField("jgsy_system_code")
    private String jgsySystemCode;
    /**
     * 机构类别
     */
    @ApiModelProperty(value = "机构类别")
    @TableField("jglb")
    private String jglb;
    /**
     * 机构编码
     */
    @ApiModelProperty(value = "机构编码")
    @TableField("jgbm")
    private String jgbm;
    /**
     * 批准设立文号
     */
    @ApiModelProperty(value = "批准设立文号")
    @TableField("doc_no")
    private String docNo;
    /**
     * 批准设立时间
     */
    @ApiModelProperty(value = "批准设立时间")
    @TableField("pzslsj")
    private Date pzslsj;
    /**
     * 是否园区管理机构
     */
    @ApiModelProperty(value = "是否园区管理机构")
    @TableField("sfskfq")
    private String sfskfq;
    /**
     * 编制使用层级
     */
    @ApiModelProperty(value = "编制使用层级")
    @TableField("jgsy_bzsycj")
    private String jgsyBzsycj;
    /**
     * 统一社会信用代码
     */
    @ApiModelProperty(value = "统一社会信用代码")
    @TableField("unify_code")
    private String unifyCode;
    /**
     * 是否执法队伍
     */
    @ApiModelProperty(value = "是否执法队伍")
    @TableField("jgsy_ifzfdw")
    private String jgsyIfzfdw;
    /**
     * 是否特殊机构: 0:否;1:是(非实名制机构)
     */
    @ApiModelProperty(value = "是否特殊机构: 0:否;1:是(非实名制机构)")
    @TableField("jgsy_ifts")
    private String jgsyIfts;

    /**
     * 是否编办机构: 0:否;1:是
     */
    @ApiModelProperty(value = "是否编办机构: 0:否;1:是")
    @TableField("is_bb_org")
    private Integer isBbOrg;
    /**
     * 是否编办机构: 0:否;1:是
     */
    @ApiModelProperty(value = "是否市级编办审核: 0:否;1:是")
    @TableField("is_sjbb_audit")
    private Integer isSjbbAudit;
    /**
     * 机构区划能新增: 0:否;1:是
     */
    @ApiModelProperty(value = "机构区划能新增: 0:否;1:是")
    @TableField("org_qh")
    private Integer orgQh;
    @ApiModelProperty(value = "父级id")
    @TableField("parent_id")
    private String parentId;
    @ApiModelProperty(value = "渝快政机构编码")
    @TableField("org_code")
    private String orgCode;
    @ApiModelProperty(value = "渝快政机构编码")
    @TableField("parent_org_code")
    private String parentOrgCode;
    @ApiModelProperty(value = "区划代码")
    @TableField("geocode")
    private String geocode;
    @ApiModelProperty(value = "所属层级")
    @TableField("belong")
    private String belong;
    @ApiModelProperty(value = "渝快政单位类型")
    @TableField("unit_type")
    private String unitType;
    @ApiModelProperty(value = "统一社会信用代码")
    @TableField("credit_code")
    private String creditCode;
    @ApiModelProperty(value = "单位来源，1-事业单位，2-机关登记信息")
    @TableField("unit_source")
    private String unitSource;
}
