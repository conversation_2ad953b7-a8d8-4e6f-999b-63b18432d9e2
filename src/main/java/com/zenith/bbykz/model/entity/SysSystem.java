package com.zenith.bbykz.model.entity;

import com.baomidou.mybatisplus.annotation.*;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * <p>
 * sys_system 实体类
 * </p>
 *
 * <AUTHOR>
 * @date 2024-02-29 11:43:55
 */
@Data
@TableName("sys_system")
@ApiModel("sys_system")
public class SysSystem implements Serializable {

    private static final long serialVersionUID = 4381530033699833299L;

    /**
     *
     */
    @ApiModelProperty(value = "")
    @TableId(value = "id")
    private String id;
    /**
     *
     */
    @ApiModelProperty(value = "")
    @TableField("name")
    private String name;
    /**
     * 排序
     */
    @ApiModelProperty(value = "排序")
    @TableField("sort")
    private Integer sort;
    /**
     * 备注
     */
    @ApiModelProperty(value = "备注")
    @TableField("remark")
    private String remark;
}
