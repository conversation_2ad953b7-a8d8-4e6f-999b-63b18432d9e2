package com.zenith.bbykz.model.entity;

import com.baomidou.mybatisplus.annotation.*;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * <p>
 * 区划代码 实体类
 * </p>
 *
 * <AUTHOR>
 * @date 2024-03-13 09:52:37
 */
@Data
@TableName("geocode")
@ApiModel("区划代码")
public class Geocode implements Serializable {

    private static final long serialVersionUID = 1189969887772232799L;

    /**
     * code
     */
    @ApiModelProperty(value = "code")
    @TableId(value = "code")
    private String code;
    /**
     * 名称
     */
    @ApiModelProperty(value = "名称")
    @TableField("name")
    private String name;
    /**
     * 类型，1-全国，2-省，3-市，4-区，5-区县
     */
    @ApiModelProperty(value = "类型，1-全国，2-省，3-市，4-区，5-区县")
    @TableField("type")
    private String type;
    /**
     * 排序
     */
    @ApiModelProperty(value = "排序")
    @TableField("sort")
    private Integer sort;
    /**
     * 机构id
     */
    @ApiModelProperty(value = "机构id")
    @TableField("org_id")
    private String orgId;
    /**
     * 层级码
     */
    @ApiModelProperty(value = "层级码")
    @TableField("level_code")
    private String levelCode;
}
