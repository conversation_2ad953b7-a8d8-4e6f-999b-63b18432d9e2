package com.zenith.bbykz.model.entity;

import com.baomidou.mybatisplus.annotation.*;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * <p>
 * 用户系统表 实体类
 * </p>
 *
 * <AUTHOR>
 * @date 2024-02-29 10:36:56
 */
@Data
@TableName("sys_user_system")
@ApiModel("用户系统表")
public class SysUserSystem implements Serializable {

    private static final long serialVersionUID = 7121249518511344229L;

    /**
     * 主键id
     */
    @ApiModelProperty(value = "主键id")
    @TableId(value = "id")
    private String id;
    /**
     * 用户id
     */
    @ApiModelProperty(value = "用户id")
    @TableField("user_id")
    private String userId;
    /**
     * 系统id
     */
    @ApiModelProperty(value = "系统id")
    @TableField("system_id")
    private String systemId;
    /**
     * 是否锁定
     */
    @ApiModelProperty(value = "是否锁定")
    @TableField("is_lock")
    private Integer isLock;
    /**
     * 解锁时间
     */
    @ApiModelProperty(value = "解锁时间")
    @TableField(value = "unlock_time", updateStrategy = FieldStrategy.IGNORED)
    private Date unlockTime;
    /**
     * 创建时间
     */
    @ApiModelProperty(value = "创建时间")
    @TableField(value = "create_time", fill = FieldFill.INSERT)
    private Date createTime;
    /**
     * 创建人
     */
    @ApiModelProperty(value = "创建人")
    @TableField(value = "create_user", fill = FieldFill.INSERT)
    private String createUser;
    /**
     * 修改时间
     */
    @ApiModelProperty(value = "修改时间")
    @TableField("update_time")
    private Date updateTime;
    /**
     * 修改人
     */
    @ApiModelProperty(value = "修改人")
    @TableField(value = "update_user", fill = FieldFill.UPDATE)
    private String updateUser;
    /**
     * 是否删除
     */
    @ApiModelProperty(value = "是否删除")
    @TableLogic
    @TableField("is_delete")
    private Integer isDelete;
    @ApiModelProperty(value = "是否默认角色")
    @TableField("is_default")
    private Integer isDefault;
    @ApiModelProperty(value = "所属机构ID")
    @TableField("org_id")
    private String orgId;
    @ApiModelProperty(value = "所属机构层级码")
    @TableField("org_level_code")
    private String orgLevelCode;
    @ApiModelProperty(value = "是否编办用户")
    @TableField("is_bb_user")
    private Integer isBbUser;
    @ApiModelProperty(value = "管理层级")
    @TableField("manage_code")
    private String manageCode;
    @ApiModelProperty(value = "区划层级")
    @TableField("geocode")
    private String geocode;
    @ApiModelProperty(value = "用户机构组")
    @TableField(value = "user_org_group_id", updateStrategy = FieldStrategy.IGNORED)
    private String userOrgGroupId;
    @ApiModelProperty(value = "是否主职务")
    @TableField("main_job")
    private Integer mainJob;
    @ApiModelProperty(value = "部门ID")
    @TableField("dept_id")
    private String deptId;
    @ApiModelProperty(value = "部门层级码")
    @TableField("dept_level_code")
    private String deptLevelCode;
    @ApiModelProperty(value = "单位ID")
    @TableField("unit_id")
    private String unitId;
    @ApiModelProperty(value = "单位层级码")
    @TableField("unit_level_code")
    private String unitLevelCode;
    @ApiModelProperty(value = "职务")
    @TableField("post_name")
    private String postName;
    @ApiModelProperty(value = "职务id")
    @TableField("user_post_id")
    private String userPostId;
}
