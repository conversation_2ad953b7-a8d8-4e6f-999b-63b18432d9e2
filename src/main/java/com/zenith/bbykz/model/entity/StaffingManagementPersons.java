package com.zenith.bbykz.model.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * <p>
 * 用编管理人员信息 实体类
 * </p>
 *
 * <AUTHOR>
 * @date 2023-09-13
 */
@Data
@TableName("STAFFING_MANAGEMENT_PERSONS")
@ApiModel("用编管理人员信息")
public class StaffingManagementPersons implements Serializable {

    private static final long serialVersionUID = 2400623848681094714L;

    /**
     * 主键id
     */
    @ApiModelProperty(value = "主键id")
    @TableId(value = "ID", type = IdType.ASSIGN_ID)
    private String id;
    /**
     * 用编管理id
     */
    @ApiModelProperty(value = "用编管理id")
    @TableField("STAFFING_MANAGEMENT_ID")
    private String staffingManagementId;
    /**
     * 姓名
     */
    @ApiModelProperty(value = "姓名")
    @TableField("NAME")
    private String name;
    /**
     * 性别
     */
    @ApiModelProperty(value = "性别")
    @TableField("SEX")
    private String sex;
    /**
     * 年龄
     */
    @ApiModelProperty(value = "年龄")
    @TableField("AGE")
    private Integer age;
    /**
     * 学历学位
     */
    @ApiModelProperty(value = "学历学位")
    @TableField("EDUCATION")
    private String education;
    /**
     * 现工作单位及职务职级
     */
    @ApiModelProperty(value = "现工作单位及职务职级")
    @TableField("WORK_UNIT_DUTIES")
    private String workUnitDuties;
    /**
     * 拟安排工作岗位
     */
    @ApiModelProperty(value = "拟安排工作岗位")
    @TableField("PROPOSED_JOB")
    private String proposedJob;
    /**
     * 备注
     */
    @ApiModelProperty(value = "备注")
    @TableField("COMMENTS")
    private String comments;
}
