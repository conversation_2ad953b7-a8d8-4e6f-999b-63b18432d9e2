package com.zenith.bbykz.model.entity.sd;

import com.baomidou.mybatisplus.annotation.*;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * <p>
 * 三定-细化职责 实体类
 * </p>
 *
 * <AUTHOR>
 * @date 2024-04-11 16:28:24
 */
@Data
@TableName("sd_duty_detail")
@ApiModel("三定-细化职责")
public class SdDutyDetail implements Serializable {

    private static final long serialVersionUID = 9111942434776661616L;

    /**
     * 主键id
     */
    @ApiModelProperty(value = "主键id")
    @TableId(value = "id")
    private String id;
    /**
     * 三定类型主键
     */
    @ApiModelProperty(value = "三定类型主键")
    @TableField("module_id")
    private String moduleId;
    /**
     * 三定明细主键
     */
    @ApiModelProperty(value = "三定明细主键")
    @TableField("module_detail_id")
    private String moduleDetailId;
    /**
     * 承担处室id
     */
    @ApiModelProperty(value = "承担处室id")
    @TableField("take_unit_id")
    private String takeUnitId;
    /**
     * 承担处室层级码
     */
    @ApiModelProperty(value = "承担处室层级码")
    @TableField("take_unit_level_code")
    private String takeUnitLevelCode;
    /**
     * 承担处室名称
     */
    @ApiModelProperty(value = "承担处室名称")
    @TableField("take_unit_name")
    private String takeUnitName;
    /**
     * 名称
     */
    @ApiModelProperty(value = "名称")
    @TableField("title")
    private String title;
    /**
     * 描述
     */
    @ApiModelProperty(value = "描述")
    @TableField("describe")
    private String describe;
    /**
     * 备注
     */
    @ApiModelProperty(value = "备注")
    @TableField("remark")
    private String remark;
    /**
     * 排序号
     */
    @ApiModelProperty(value = "排序号")
    @TableField("sort")
    private Integer sort;
    /**
     *
     */
    @ApiModelProperty(value = "")
    @TableField(value = "create_time", fill = FieldFill.INSERT)
    private Date createTime;
    /**
     *
     */
    @ApiModelProperty(value = "")
    @TableField(value = "create_user", fill = FieldFill.INSERT)
    private String createUser;
    /**
     *
     */
    @ApiModelProperty(value = "")
    @TableField("update_time")
    private Date updateTime;
    /**
     *
     */
    @ApiModelProperty(value = "")
    @TableField(value = "update_user", fill = FieldFill.UPDATE)
    private String updateUser;
    /**
     *
     */
    @ApiModelProperty(value = "")
    @TableLogic
    @TableField("is_delete")
    private Integer isDelete;

    /**
     * 文号
     */
    @ApiModelProperty(value = "文号")
    @TableField("record_num")
    private String recordNum;
}
