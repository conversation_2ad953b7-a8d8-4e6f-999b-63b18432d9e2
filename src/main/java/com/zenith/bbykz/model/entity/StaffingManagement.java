package com.zenith.bbykz.model.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * <p>
 * 用编管理 实体类
 * </p>
 *
 * <AUTHOR>
 * @date 2023-09-13
 */
@Data
@TableName("STAFFING_MANAGEMENT")
@ApiModel("用编管理")
public class StaffingManagement implements Serializable {

    private static final long serialVersionUID = 6884645709498239987L;

    /**
     * 主键id
     */
    @ApiModelProperty(value = "主键id")
    @TableId(value = "ID", type = IdType.ASSIGN_UUID)
    private String id;
    /**
     * 标题
     */
    @ApiModelProperty(value = "标题")
    @TableField("TITLE")
    private String title;
    /**
     * 申请单位
     */
    @ApiModelProperty(value = "申请单位")
    @TableField("APPLICANT_UNIT")
    private String applicantUnit;
    /**
     * 编制类型
     */
    @ApiModelProperty(value = "编制类型")
    @TableField("PREPARATION_TYPE")
    private String preparationType;
    /**
     * 用编数量
     */
    @ApiModelProperty(value = "用编数量")
    @TableField("EMPLOYEES_NUMBER")
    private Integer employeesNumber;
    /**
     * 入编时间
     */
    @ApiModelProperty(value = "入编时间")
    @TableField("ENROLLMENT_TIME")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date enrollmentTime;
    /**
     * 联系人
     */
    @ApiModelProperty(value = "联系人")
    @TableField("CONTACTS")
    private String contacts;
    /**
     * 联系电话
     */
    @ApiModelProperty(value = "联系电话")
    @TableField("TELEPHONE")
    private String telephone;
    /**
     * 申请时间
     */
    @ApiModelProperty(value = "申请时间")
    @TableField("APPLICAT_TIME")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date applicatTime;
    /**
     * 状态
     */
    @ApiModelProperty(value = "状态")
    @TableField("STATUS")
    private String status;
    /**
     * 申请人
     */
    @ApiModelProperty(value = "申请人")
    @TableField("APPLICAT_PERSON")
    private String applicatPerson;
    /**
     * 附件信息
     */
    @ApiModelProperty(value = "附件信息")
    @TableField("FILES")
    private String files;
    /**
     * 核编单号
     */
    @ApiModelProperty(value = "核编单号")
    @TableField("APPROVAL_NUMBER")
    private String approvalNumber;
    /**
     * 回复函地址
     */
    @ApiModelProperty(value = "回复函地址")
    @TableField("REPLIES_ADDRESS")
    private String repliesAddress;

    /**
     * 回复函地址
     */
    @ApiModelProperty(value = "回复函文件名字和id")
    @TableField("REPLIES_FILE")
    private String repliesFile;

    /**
     * 机构
     */
    @ApiModelProperty(value = "机构")
    @TableField("ORG_CODE")
    private String orgCode;

    /**
     * 审批人
     */
    @ApiModelProperty(value = "审批人")
    @TableField("APPROVED_PERSON")
    private String approvedPerson;

    /**
     * 所属编办机构
     */
    @ApiModelProperty(value = "所属编办机构")
    @TableField("BB_ORG")
    private String bbOrg;

    /**
     * 是否有权审批
     */
    @TableField(exist = false)
    @ApiModelProperty(value = "是否有权审批")
    private Boolean canApproved;

    /**
     * 意见
     */
    @ApiModelProperty(value = "意见")
    @TableField("APPROVE_OPTION")
    private String option;

    /**
     * 使用数量
     */
    @TableField(exist = false)
    @ApiModelProperty(value = "使用数量")
    private Integer usedNumber;

    /**
     * 剩余数量
     */
    @TableField(exist = false)
    @ApiModelProperty(value = "剩余数量")
    private Integer remainingNumber;
}
