package com.zenith.bbykz.model.entity;

import com.baomidou.mybatisplus.annotation.*;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import io.swagger.models.auth.In;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * <p>
 * sys_user 实体类
 * </p>
 *
 * <AUTHOR>
 * @date 2023-09-14 18:00:59
 */
@Data
@TableName("sys_user")
@ApiModel("用户实体")
public class SysUser implements Serializable {

    private static final long serialVersionUID = 2575550400126367789L;

    /**
     *
     */
    @ApiModelProperty(value = "主键")
    @TableId(value = "id", type = IdType.ASSIGN_UUID)
    private String id;
    /**
     *
     */
    @ApiModelProperty(value = "账号")
    @TableField("account")
    private String account;
    /**
     *
     */
    @ApiModelProperty(value = "密码")
    @TableField("password")
    private String password;
    /**
     *
     */
    @ApiModelProperty(value = "政务钉ID")
    @TableField("zwdd_id")
    private String zwddId;
    /**
     *
     */
    @ApiModelProperty(value = "开放ID")
    @TableField("open_id")
    private String openId;
    /**
     *
     */
    @ApiModelProperty(value = "是否删除")
    @TableField("is_delete")
    @TableLogic
    private Integer isDelete;
    /**
     *
     */
    @ApiModelProperty(value = "创建时间")
    @TableField("create_time")
    private Date createTime;
    /**
     *
     */
    @ApiModelProperty(value = "修改时间")
    @TableField("update_time")
    private Date updateTime;
    /**
     *
     */
    @ApiModelProperty(value = "删除时间")
    @TableField("delete_time")
    private Date deleteTime;
    /**
     *
     */
    @ApiModelProperty(value = "身份证号")
    @TableField("id_card")
    private String idCard;
    /**
     *
     */
    @ApiModelProperty(value = "手机号")
    @TableField("phone")
    private String phone;
    /**
     *
     */
    @ApiModelProperty(value = "机构ID")
    @TableField("org_id")
    private String orgId;
    /**
     *
     */
    @ApiModelProperty(value = "机构层级码")
    @TableField("org_level_code")
    private String orgLevelCode;

    /**
     * 是否编办机构: 0:否;1:是
     */
    @ApiModelProperty(value = "是否编办用户: 0:否;1:是")
    @TableField("is_bb_user")
    private Integer isBbUser;
    /**
     * 用户管理机构
     */
    @ApiModelProperty(value = "用户管理机构")
    @TableField("manage_code")
    private String manageCode;

    /**
     * 姓名
     */
    @ApiModelProperty(value = "姓名")
    @TableField("name")
    private String name;

    /**
     * 是否锁定
     */
    @ApiModelProperty(value = "是否锁定")
    @TableField("is_lock")
    private Integer isLock;

    /**
     * 解锁时间
     */
    @ApiModelProperty(value = "解锁时间")
    @TableField("unlock_time")
    private Date unlockTime;
    @ApiModelProperty(value = "区划层级")
    @TableField("geocode")
    private String geocode;
    @TableField("is_builtin")
    private Integer isBuiltin;
    /**
     * 所在单位
     */
    @TableField("SSDW_ID")
    private String ssdwId;
}
