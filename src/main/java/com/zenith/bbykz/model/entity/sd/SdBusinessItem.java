package com.zenith.bbykz.model.entity.sd;

import com.baomidou.mybatisplus.annotation.*;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * <p>
 * 三定-业务事项 实体类
 * </p>
 *
 * <AUTHOR>
 * @date 2024-04-11 16:28:25
 */
@Data
@TableName("sd_business_item")
@ApiModel("三定-业务事项")
public class SdBusinessItem implements Serializable {

    private static final long serialVersionUID = 5811381393530178867L;

    /**
     * 主键id
     */
    @ApiModelProperty(value = "主键id")
    @TableId(value = "id")
    private String id;
    /**
     * 三定类型主键
     */
    @ApiModelProperty(value = "三定类型主键")
    @TableField("module_id")
    private String moduleId;
    /**
     * 三定明细主键
     */
    @ApiModelProperty(value = "三定明细主键")
    @TableField("module_detail_id")
    private String moduleDetailId;
    /**
     * 细化职责主键
     */
    @ApiModelProperty(value = "细化职责主键")
    @TableField("duty_detail_id")
    private String dutyDetailId;
    /**
     * 核心业务主键
     */
    @ApiModelProperty(value = "核心业务主键")
    @TableField("core_business_id")
    private String coreBusinessId;
    /**
     * 名称
     */
    @ApiModelProperty(value = "名称")
    @TableField("title")
    private String title;
    /**
     * 描述
     */
    @ApiModelProperty(value = "描述")
    @TableField("describe")
    private String describe;
    /**
     * 备注
     */
    @ApiModelProperty(value = "备注")
    @TableField("remark")
    private String remark;
    /**
     * 排序号
     */
    @ApiModelProperty(value = "排序号")
    @TableField("sort")
    private Integer sort;
    /**
     *
     */
    @ApiModelProperty(value = "")
    @TableField(value = "create_time", fill = FieldFill.INSERT)
    private Date createTime;
    /**
     *
     */
    @ApiModelProperty(value = "")
    @TableField(value = "create_user", fill = FieldFill.INSERT)
    private String createUser;
    /**
     *
     */
    @ApiModelProperty(value = "")
    @TableField("update_time")
    private Date updateTime;
    /**
     *
     */
    @ApiModelProperty(value = "")
    @TableField(value = "update_user", fill = FieldFill.UPDATE)
    private String updateUser;
    /**
     *
     */
    @ApiModelProperty(value = "")
    @TableLogic
    @TableField("is_delete")
    private Integer isDelete;
}
