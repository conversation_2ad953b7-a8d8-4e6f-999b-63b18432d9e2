package com.zenith.bbykz.model.entity.efficient;

import com.baomidou.mybatisplus.annotation.*;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * <p>
 * 系统消息通知 实体类
 * </p>
 *
 * <AUTHOR>
 * @date 2024-03-18 15:07:00
 */
@Data
@TableName("efficient_sys_notify")
@ApiModel("系统消息通知")
public class SysNotify implements Serializable {

    private static final long serialVersionUID = 3015285578971827588L;

    /**
     * 系统消息通知 ID
     */
    @ApiModelProperty(value = "系统消息通知 ID")
    @TableId(value = "id")
    private String id;
    /**
     * 业务主键
     */
    @ApiModelProperty(value = "业务主键")
    @TableField("biz_id")
    private String bizId;
    /**
     * 批次号
     */
    @ApiModelProperty(value = "批次号")
    @TableField("batch_num")
    private String batchNum;
    /**
     * 系统标识
     */
    @ApiModelProperty(value = "系统标识")
    @TableField("system_id")
    private String systemId;
    /**
     * 菜单ID
     */
    @ApiModelProperty(value = "菜单ID")
    @TableField("menu_id")
    private String menuId;
    /**
     * 通知类型，1-普通消息，2-短信，3-邮件，4-待办，5-工作通知，6-ding消息，7-公告，9-其他
     */
    @ApiModelProperty(value = "通知类型，1-普通消息，2-短信，3-邮件，4-待办，5-工作通知，6-ding消息，7-公告，9-其他")
    @TableField("notify_type")
    private String notifyType;
    /**
     * 接收系统，1-本系统，2-渝快政，9-其他系统
     */
    @ApiModelProperty(value = "接收系统，1-本系统，2-渝快政，9-其他系统")
    @TableField("recipient_system_type")
    private String recipientSystemType;
    /**
     * 创建用户id
     */
    @ApiModelProperty(value = "创建用户id")
    @TableField("create_user_id")
    private String createUserId;
    /**
     * 创建用户政务钉钉id
     */
    @ApiModelProperty(value = "创建用户政务钉钉id")
    @TableField("create_zwdd_id")
    private String createZwddId;
    /**
     * 标题
     */
    @ApiModelProperty(value = "标题")
    @TableField("title")
    private String title;
    /**
     * 内容
     */
    @ApiModelProperty(value = "内容")
    @TableField("content")
    private String content;
    /**
     * 备注
     */
    @ApiModelProperty(value = "备注")
    @TableField("remark")
    private String remark;
    /**
     * pc路由
     */
    @ApiModelProperty(value = "pc路由")
    @TableField("pc_url")
    private String pcUrl;
    /**
     * app端路由
     */
    @ApiModelProperty(value = "app端路由")
    @TableField("app_url")
    private String appUrl;
    /**
     * 接收人ID
     */
    @ApiModelProperty(value = "接收人ID")
    @TableField("recipient_user_id")
    private String recipientUserId;
    /**
     * 接收人政务钉钉id
     */
    @ApiModelProperty(value = "接收人政务钉钉id")
    @TableField("recipient_zwdd_id")
    private String recipientZwddId;
    /**
     * 接收系统消息ID
     */
    @ApiModelProperty(value = "接收系统消息ID")
    @TableField("recipient_msg_id")
    private String recipientMsgId;
    /**
     * 消息状态，1-待发送，2-已发送，3-已查看，4-已处理
     */
    @ApiModelProperty(value = "消息状态，1-待发送，2-已发送，3-已查看，4-已处理")
    @TableField("state")
    private String state;
    /**
     * 创建时间
     */
    @ApiModelProperty(value = "创建时间")
    @TableField(value = "create_time", fill = FieldFill.INSERT)
    private Date createTime;
    /**
     *
     */
    @ApiModelProperty(value = "")
    @TableField(value = "create_user", fill = FieldFill.INSERT)
    private String createUser;
    /**
     * 修改时间
     */
    @ApiModelProperty(value = "修改时间")
    @TableField("update_time")
    private Date updateTime;
    /**
     *
     */
    @ApiModelProperty(value = "")
    @TableField(value = "update_user", fill = FieldFill.UPDATE)
    private String updateUser;
    /**
     * 是否删除，1-是，0-否
     */
    @ApiModelProperty(value = "是否删除，1-是，0-否")
    @TableLogic
    @TableField("is_delete")
    private Integer isDelete;
    /**
     * 业务类型
     */
    @ApiModelProperty(value = "业务类型")
    @TableField("biz_type")
    private String bizType;
    /**
     * 创建单位id
     */
    @ApiModelProperty(value = "创建单位id")
    @TableField("create_unit_id")
    private String createUnitId;
    /**
     * 创建单位名称
     */
    @ApiModelProperty(value = "创建单位名称")
    @TableField("create_unit_name")
    private String createUnitName;
    /**
     * 接收单位ID
     */
    @ApiModelProperty(value = "接收单位ID")
    @TableField("recipient_unit_id")
    private String recipientUnitId;
    /**
     * 接收单位名称
     */
    @ApiModelProperty(value = "接收单位名称")
    @TableField("recipient_unit_name")
    private String recipientUnitName;
    /**
     * 查看时间
     */
    @ApiModelProperty(value = "查看时间")
    @TableField("read_time")
    private Date readTime;
    /**
     * 接收单位名称
     */
    @ApiModelProperty(value = "处理时间")
    @TableField("handle_time")
    private Date handleTime;
}
