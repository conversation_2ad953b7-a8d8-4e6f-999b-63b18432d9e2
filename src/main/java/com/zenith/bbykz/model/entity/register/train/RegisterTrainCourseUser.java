package com.zenith.bbykz.model.entity.register.train;

import com.baomidou.mybatisplus.annotation.*;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * <p>
 * 登记信息-培训管理-用户课程 实体类
 * </p>
 *
 * <AUTHOR>
 * @date 2024-03-12 10:11:36
 */
@Data
@TableName("register_train_course_user")
@ApiModel("登记信息-培训管理-用户课程")
public class RegisterTrainCourseUser implements Serializable {

    private static final long serialVersionUID = 1677023018156602478L;

    /**
     * 主键id
     */
    @ApiModelProperty(value = "主键id")
    @TableId(value = "id")
    private String id;
    /**
     * 课程主键
     */
    @ApiModelProperty(value = "课程主键")
    @TableField("course_id")
    private String courseId;
    /**
     * 用户id
     */
    @ApiModelProperty(value = "用户id")
    @TableField("user_id")
    private String userId;
    /**
     * 课件时长
     */
    @ApiModelProperty(value = "课件时长")
    @TableField("mins")
    private Integer mins;
    /**
     * 学习时长
     */
    @ApiModelProperty(value = "学习时长")
    @TableField("view_mins")
    private Integer viewMins;
    /**
     * 是否完成
     */
    @ApiModelProperty(value = "是否完成")
    @TableField("is_finish")
    private Integer isFinish;
    /**
     * 是否关注
     */
    @ApiModelProperty(value = "是否关注")
    @TableField("is_attention")
    private Integer isAttention;
    /**
     * 进度
     */
    @ApiModelProperty(value = "进度")
    @TableField("progress")
    private java.math.BigDecimal progress;
    /**
     * 创建时间
     */
    @ApiModelProperty(value = "创建时间")
    @TableField(value = "create_time", fill = FieldFill.INSERT)
    private Date createTime;
    /**
     * 创建人
     */
    @ApiModelProperty(value = "创建人")
    @TableField(value = "create_user", fill = FieldFill.INSERT)
    private String createUser;
    /**
     * 修改时间
     */
    @ApiModelProperty(value = "修改时间")
    @TableField("update_time")
    private Date updateTime;
    /**
     * 修改人
     */
    @ApiModelProperty(value = "修改人")
    @TableField(value = "update_user", fill = FieldFill.UPDATE)
    private String updateUser;
    /**
     * 是否删除
     */
    @ApiModelProperty(value = "是否删除")
    @TableLogic
    @TableField("is_delete")
    private Integer isDelete;
}
