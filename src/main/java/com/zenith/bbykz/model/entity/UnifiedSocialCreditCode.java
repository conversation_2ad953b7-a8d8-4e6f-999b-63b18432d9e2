package com.zenith.bbykz.model.entity;

import com.baomidou.mybatisplus.annotation.*;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * <p>
 * 机关事业单位统一社会信用代码 实体类
 * </p>
 *
 * <AUTHOR>
 * @date 2024-03-08 09:55:19
 */
@Data
@TableName("unified_social_credit_code")
@ApiModel("机关事业单位统一社会信用代码")
public class UnifiedSocialCreditCode implements Serializable {

    private static final long serialVersionUID = 6715514409186772036L;

    /**
     * 主键id
     */
    @ApiModelProperty(value = "主键id")
    @TableId(value = "id")
    private String id;
    /**
     * 统一社会信用代码
     */
    @ApiModelProperty(value = "统一社会信用代码")
    @TableField("code")
    private String code;
    /**
     * 编号
     */
    @ApiModelProperty(value = "编号")
    @TableField("num")
    private String num;
    /**
     * 创建人
     */
    @ApiModelProperty(value = "创建人")
    @TableField("creater")
    private String creater;
    /**
     * 导入时间
     */
    @ApiModelProperty(value = "导入时间")
    @TableField(value = "create_time", fill = FieldFill.INSERT)
    private Date createTime;
    /**
     * 更新时间
     */
    @ApiModelProperty(value = "更新时间")
    @TableField("uptime_time")
    private Date uptimeTime;
    /**
     * 删除时间
     */
    @ApiModelProperty(value = "删除时间")
    @TableField("delete_time")
    private Date deleteTime;
    /**
     * 是否删除
     */
    @ApiModelProperty(value = "是否删除")
    @TableLogic
    @TableField("is_delete")
    private Integer isDelete;
    /**
     * 机构名称
     */
    @ApiModelProperty(value = "机构名称")
    @TableField("institution_name")
    private String institutionName;
    /**
     * 机构状态
     */
    @ApiModelProperty(value = "机构状态")
    @TableField("institutional_status")
    private String institutionalStatus;
    /**
     * 机构地址
     */
    @ApiModelProperty(value = "机构地址")
    @TableField("institution_address")
    private String institutionAddress;
    /**
     * 负责人
     */
    @ApiModelProperty(value = "负责人")
    @TableField("head")
    private String head;
    /**
     * 机构规格
     */
    @ApiModelProperty(value = "机构规格")
    @TableField("institutional_specifications")
    private String institutionalSpecifications;
    /**
     * 机构分类
     */
    @ApiModelProperty(value = "机构分类")
    @TableField("institutional_type")
    private String institutionalType;
    /**
     * 机构性质
     */
    @ApiModelProperty(value = "机构性质")
    @TableField("institutional_nature")
    private String institutionalNature;
    /**
     * 机构类别
     */
    @ApiModelProperty(value = "机构类别")
    @TableField("institution_category")
    private String institutionCategory;
    /**
     * 批准机构
     */
    @ApiModelProperty(value = "批准机构")
    @TableField("approving_authority")
    private String approvingAuthority;
    /**
     * 批准文号
     */
    @ApiModelProperty(value = "批准文号")
    @TableField("approval_number")
    private String approvalNumber;
    /**
     * 证书份数
     */
    @ApiModelProperty(value = "证书份数")
    @TableField("certificate_number")
    private Integer certificateNumber;
    /**
     * 发证人
     */
    @ApiModelProperty(value = "发证人")
    @TableField("licensor")
    private String licensor;
    /**
     * 颁发日期
     */
    @ApiModelProperty(value = "颁发日期")
    @TableField("issue_time")
    private String issueTime;
    /**
     * 证书截止日期
     */
    @ApiModelProperty(value = "证书截止日期")
    @TableField("certificate_expiration_time")
    private String certificateExpirationTime;
    /**
     * 领证人
     */
    @ApiModelProperty(value = "领证人")
    @TableField("certificate_recipient")
    private String certificateRecipient;
    /**
     * 领证日期
     */
    @ApiModelProperty(value = "领证日期")
    @TableField("certificate_acquisition_time")
    private String certificateAcquisitionTime;
    /**
     * 联系人
     */
    @ApiModelProperty(value = "联系人")
    @TableField("contacts")
    private String contacts;
    /**
     * 联系电话
     */
    @ApiModelProperty(value = "联系电话")
    @TableField("telephone")
    private String telephone;
    /**
     * 邮政编码
     */
    @ApiModelProperty(value = "邮政编码")
    @TableField("postal_code")
    private String postalCode;
    /**
     * 版本号
     */
    @ApiModelProperty(value = "版本号")
    @TableField("version")
    private Integer version;
    @ApiModelProperty(value = "区划")
    @TableField("GEOCODE")
    private String geocode;

}
