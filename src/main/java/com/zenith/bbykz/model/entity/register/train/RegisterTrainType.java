package com.zenith.bbykz.model.entity.register.train;

import com.baomidou.mybatisplus.annotation.*;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * <p>
 * 登记信息-培训管理-课程分类 实体类
 * </p>
 *
 * <AUTHOR>
 * @date 2024-03-05 15:05:10
 */
@Data
@TableName("register_train_type")
@ApiModel("登记信息-培训管理-课程分类")
public class RegisterTrainType implements Serializable {

    private static final long serialVersionUID = 8116999839616645846L;

    /**
     * 主键id
     */
    @ApiModelProperty(value = "主键id")
    @TableId(value = "id")
    private String id;
    /**
     * 分类名称
     */
    @ApiModelProperty(value = "分类名称")
    @TableField("name")
    private String name;
    /**
     * 父级ID
     */
    @ApiModelProperty(value = "父级ID")
    @TableField("parent_id")
    private String parentId;
    /**
     * 是否启用
     */
    @ApiModelProperty(value = "是否启用")
    @TableField("enabled")
    private Integer enabled;
    /**
     * 序号
     */
    @ApiModelProperty(value = "序号")
    @TableField("sort")
    private Integer sort;
    /**
     * 创建时间
     */
    @ApiModelProperty(value = "创建时间")
    @TableField(value = "create_time", fill = FieldFill.INSERT)
    private Date createTime;
    /**
     * 创建人
     */
    @ApiModelProperty(value = "创建人")
    @TableField(value = "create_user", fill = FieldFill.INSERT)
    private String createUser;
    /**
     * 修改时间
     */
    @ApiModelProperty(value = "修改时间")
    @TableField("update_time")
    private Date updateTime;
    /**
     * 修改人
     */
    @ApiModelProperty(value = "修改人")
    @TableField(value = "update_user", fill = FieldFill.UPDATE)
    private String updateUser;
    /**
     * 是否删除
     */
    @ApiModelProperty(value = "是否删除")
    @TableLogic
    @TableField("is_delete")
    private Integer isDelete;
}
