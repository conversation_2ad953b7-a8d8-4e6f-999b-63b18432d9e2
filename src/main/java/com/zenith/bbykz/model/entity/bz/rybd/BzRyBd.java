package com.zenith.bbykz.model.entity.bz.rybd;

import com.baomidou.mybatisplus.annotation.*;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
* <p>
* 核编管理-动态信息维护-人员详情 实体类
* </p>
*
* <AUTHOR>
* @date 2024-04-22 11:21:43
*/
@Data
@TableName("bz_ry_bd")
@ApiModel("核编管理-动态信息维护-人员详情")
public class BzRyBd implements Serializable {

    private static final long serialVersionUID = 2396238065866523325L;

    /**
    *主键id
    */
    @ApiModelProperty(value = "主键id")
    @TableId(value = "id")
    private String id;
    /**
    *变动信息主键
    */
    @ApiModelProperty(value = "变动信息主键")
    @TableField("unit_bd_id")
    private String unitBdId;
    /**
    *名称
    */
    @ApiModelProperty(value = "名称")
    @TableField("name")
    private String name;
    /**
    *身份证
    */
    @ApiModelProperty(value = "身份证")
    @TableField("id_card")
    private String idCard;
    /**
    *人员变动类型，BZ_RY_BD_TYPE
    */
    @ApiModelProperty(value = "人员变动类型，BZ_RY_BD_TYPE")
    @TableField("bd_type")
    private String bdType;
    /**
    *现单位ID
    */
    @ApiModelProperty(value = "现单位ID")
    @TableField("old_unit_id")
    private String oldUnitId;
    /**
    *现单位层级码
    */
    @ApiModelProperty(value = "现单位层级码")
    @TableField("old_unit_level_code")
    private String oldUnitLevelCode;
    /**
    *现单位名称
    */
    @ApiModelProperty(value = "现单位名称")
    @TableField("old_unit_name")
    private String oldUnitName;
    /**
    *现处室ID
    */
    @ApiModelProperty(value = "现处室ID")
    @TableField("old_dept_id")
    private String oldDeptId;
    /**
    *现处室层级码
    */
    @ApiModelProperty(value = "现处室层级码")
    @TableField("old_dept_level_code")
    private String oldDeptLevelCode;
    /**
    *现处室层名称
    */
    @ApiModelProperty(value = "现处室层名称")
    @TableField("old_dept_name")
    private String oldDeptName;
    /**
    *现编制类型，BZ_BZLX
    */
    @ApiModelProperty(value = "现编制类型，BZ_BZLX")
    @TableField("old_bz_type")
    private String oldBzType;
    /**
    *现用编类型，BZ_JFXX
    */
    @ApiModelProperty(value = "现用编类型，BZ_JFXX")
    @TableField("old_yb_type")
    private String oldYbType;
    /**
    *现职务
    */
    @ApiModelProperty(value = "现职务")
    @TableField("old_post_name")
    private String oldPostName;
    /**
    *现实名制单位code
    */
    @ApiModelProperty(value = "现实名制单位code")
    @TableField("old_smz_unit_code")
    private String oldSmzUnitCode;
    /**
    *现单位统一社会信用代码
    */
    @ApiModelProperty(value = "现单位统一社会信用代码")
    @TableField("old_credit_code")
    private String oldCreditCode;
    /**
    *
    */
    @ApiModelProperty(value = "")
    @TableField("new_unit_id")
    private String newUnitId;
    /**
    *
    */
    @ApiModelProperty(value = "")
    @TableField("new_unit_level_code")
    private String newUnitLevelCode;
    /**
    *
    */
    @ApiModelProperty(value = "")
    @TableField("new_unit_name")
    private String newUnitName;
    /**
    *
    */
    @ApiModelProperty(value = "")
    @TableField("new_dept_id")
    private String newDeptId;
    /**
    *
    */
    @ApiModelProperty(value = "")
    @TableField("new_dept_level_code")
    private String newDeptLevelCode;
    /**
    *
    */
    @ApiModelProperty(value = "")
    @TableField("new_dept_name")
    private String newDeptName;
    /**
    *
    */
    @ApiModelProperty(value = "")
    @TableField("new_bz_type")
    private String newBzType;
    /**
    *
    */
    @ApiModelProperty(value = "")
    @TableField("new_yb_type")
    private String newYbType;
    /**
    *
    */
    @ApiModelProperty(value = "")
    @TableField("new_post_name")
    private String newPostName;
    /**
    *
    */
    @ApiModelProperty(value = "")
    @TableField("new_smz_unit_code")
    private String newSmzUnitCode;
    /**
    *
    */
    @ApiModelProperty(value = "")
    @TableField("new_credit_code")
    private String newCreditCode;
    /**
    *变动时间
    */
    @ApiModelProperty(value = "变动时间")
    @TableField("bd_time")
    private Date bdTime;
    /**
    *备注
    */
    @ApiModelProperty(value = "备注")
    @TableField("remark")
    private String remark;
    /**
    *
    */
    @ApiModelProperty(value = "")
    @TableField(value = "create_time", fill = FieldFill.INSERT)
    private Date createTime;
    /**
    *
    */
    @ApiModelProperty(value = "")
    @TableField(value = "create_user", fill = FieldFill.INSERT)
    private String createUser;
    /**
    *
    */
    @ApiModelProperty(value = "")
    @TableField("update_time")
    private Date updateTime;
    /**
    *
    */
    @ApiModelProperty(value = "")
    @TableField(value = "update_user", fill = FieldFill.UPDATE)
    private String updateUser;
    /**
    *
    */
    @ApiModelProperty(value = "")
    @TableLogic
    @TableField("is_delete")
    private Integer isDelete;
}
