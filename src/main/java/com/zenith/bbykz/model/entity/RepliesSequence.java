package com.zenith.bbykz.model.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * <p>
 * REPLIES_SEQUENCE 实体类
 * </p>
 *
 * <AUTHOR>
 * @date 2023-09-14 18:34:42
 */
@Data
@TableName("REPLIES_SEQUENCE")
@ApiModel("REPLIES_SEQUENCE")
public class RepliesSequence implements Serializable {

    private static final long serialVersionUID = 2596701170968674061L;

    /**
     * 主键id
     */
    @ApiModelProperty(value = "主键id")
    @TableId(value = "ID", type = IdType.ASSIGN_ID)
    private String id;
    /**
     * 序列号
     */
    @ApiModelProperty(value = "序列号")
    @TableField("CODE")
    private String code;

    /**
     * 当天日期
     */
    @ApiModelProperty(value = "当天日期")
    @TableField("CURR_DATE")
    private String currDate;
}
