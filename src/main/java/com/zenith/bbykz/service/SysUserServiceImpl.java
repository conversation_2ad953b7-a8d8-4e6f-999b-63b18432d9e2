package com.zenith.bbykz.service;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUnit;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.efficient.auth.api.LoginService;
import com.efficient.auth.constant.AuthConstant;
import com.efficient.auth.constant.AuthResultEnum;
import com.efficient.auth.constant.LoginTypeEnum;
import com.efficient.auth.properties.AuthProperties;
import com.efficient.auth.util.AuthUtil;
import com.efficient.auth.util.JwtUtil;
import com.efficient.auth.util.TokenUtil;
import com.efficient.cache.api.CacheUtil;
import com.efficient.common.auth.RequestHolder;
import com.efficient.common.constant.CommonConstant;
import com.efficient.common.constant.DbConstant;
import com.efficient.common.result.Result;
import com.efficient.common.result.ResultEnum;
import com.efficient.common.util.WebUtil;
import com.zenith.bbykz.api.*;
import com.zenith.bbykz.common.constant.BbCommonConstant;
import com.zenith.bbykz.common.enums.SystemIdEnum;
import com.zenith.bbykz.common.result.BbResultEnum;
import com.zenith.bbykz.dao.SysUserMapper;
import com.zenith.bbykz.dao.SysUserRoleMapper;
import com.zenith.bbykz.model.base.UserInfo;
import com.zenith.bbykz.model.converter.SysUserConverter;
import com.zenith.bbykz.model.dto.LoginDTO;
import com.zenith.bbykz.model.dto.SysUserDTO;
import com.zenith.bbykz.model.dto.SysUserListDTO;
import com.zenith.bbykz.model.entity.*;
import com.zenith.bbykz.model.vo.LoginSystem;
import com.zenith.bbykz.model.vo.SysRoleVO;
import com.zenith.bbykz.model.vo.SysUserSystemVO;
import com.zenith.bbykz.model.vo.SysUserVO;
import com.zenith.bbykz.service.efficient.CustomDictService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.nio.charset.StandardCharsets;
import java.util.*;
import java.util.stream.Collectors;

import static com.efficient.common.constant.CommonConstant.TRUE_INT;

/**
 * <p>
 * sys_user 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @date 2023-09-14 17:14:26
 */
@Service
@Slf4j
public class SysUserServiceImpl extends ServiceImpl<SysUserMapper, SysUser> implements SysUserService {

    @Autowired
    private SysUserConverter sysUserConverter;
    @Autowired
    private SysUserMapper sysUserMapper;
    @Autowired
    private SysUserRoleMapper sysUserRoleMapper;
    @Autowired
    private SysUserRoleService sysUserRoleService;
    @Autowired
    private SysRoleMenuService sysRoleMenuService;
    @Autowired
    private SysMenuService sysMenuService;
    @Autowired
    private SysRoleService sysRoleService;
    @Autowired
    private SysUserService sysUserService;

    @Autowired
    private CacheUtil cacheUtil;
    @Autowired
    private OrgInfoService orgInfoService;
    @Autowired
    private AuthUtil authUtil;
    @Autowired
    private SysUserSystemService sysUserSystemService;
    @Autowired
    private SysUserPostService sysUserPostService;
    @Autowired
    private LoginService loginService;
    @Autowired
    private JwtUtil jwtUtil;
    @Autowired
    private AuthProperties authProperties;
    @Autowired
    private CustomDictService customDictService;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Result save(SysUserDTO dto) {
        if (StrUtil.isEmpty(dto.getAccount())) {
            return Result.build(BbResultEnum.THE_USER_NOT_EXIST.getCode(), BbResultEnum.THE_USER_NOT_EXIST.getMsg(), null);
        }
        SysUser sysUser = this.getByAccount(dto.getAccount());
        if (ObjectUtil.isNotNull(sysUser)) {
            return Result.fail(BbResultEnum.THE_USER_ACCOUNT_IS_EXIST.getCode(), BbResultEnum.THE_USER_ACCOUNT_IS_EXIST.getMsg(), null);
        }
        SysUser byZwddId = this.findByZwddId(dto.getZwddId());
        if (ObjectUtil.isNotNull(byZwddId)) {
            return Result.fail(BbResultEnum.THE_ZWD_ID_IS_EXIST.getCode(), BbResultEnum.THE_ZWD_ID_IS_EXIST.getMsg(), null);
        }
        // 是编办用户，选管理单位，非编办用户，就是普通的所属机构
        String orgLevelCode = dto.getOrgLevelCode();
        if (ObjectUtil.equal(dto.getIsBbUser(), 0)) {
            dto.setManageCode(orgLevelCode);
        }
        SysUser entity = sysUserConverter.dto2Entity(dto);
        entity.setCreateTime(new Date());
        entity.setIsDelete(BbCommonConstant.FALSE_INT);
        entity.setPassword(authUtil.encrypt(dto.getPassword()));
        boolean flag = this.save(entity);
        // 设置职务表
        OrgInfo orgInfo = orgInfoService.getByLevelCode(orgLevelCode);
        SysUserPost sysUserPost = sysUserPostService.findByUserIdAndDeptId(entity.getId(), orgInfo.getId());
        if (Objects.isNull(sysUserPost)) {
            sysUserPost = new SysUserPost();
            sysUserPost.setUserId(entity.getId());
            sysUserPost.setDeptId(orgInfo.getId());
            sysUserPost.setDeptLevelCode(orgInfo.getLevelCode());
            OrgInfo info = orgInfoService.getUnitByDeptId(orgInfo.getId());
            if (Objects.nonNull(info)) {
                sysUserPost.setUnitId(info.getId());
                sysUserPost.setUnitLevelCode(info.getLevelCode());
            } else {
                sysUserPost.setUnitId(sysUserPost.getDeptId());
                sysUserPost.setUnitLevelCode(sysUserPost.getDeptLevelCode());
            }

            sysUserPost.setMainJob(1);
            sysUserPost.setSort(1L);
            sysUserPost.setIsDelete(CommonConstant.FALSE_INT);
            sysUserPostService.save(sysUserPost);
        }
        String userPostId = sysUserPost.getId();
        if (ObjectUtil.isNotNull(dto.getRoleId())) {
            SysRole role = sysRoleService.getById(dto.getRoleId());
            SysUserRole sysUserRole = new SysUserRole();
            sysUserRole.setUserId(entity.getId());
            sysUserRole.setRoleId(role.getId());
            sysUserRole.setSystemCode(role.getSystemCode());
            sysUserRoleService.save(sysUserRole);
        }
        String systemId = RequestHolder.getCurrSystemId();
        if (StrUtil.equalsAny(systemId, SystemIdEnum.JGSY.getId(), SystemIdEnum.YBGL.getId())) {
            SysUserSystem sysUserSystem = new SysUserSystem();
            sysUserSystem.setUserId(entity.getId());
            sysUserSystem.setUserPostId(userPostId);
            sysUserSystem.setSystemId(systemId);
            sysUserSystem.setIsLock(CommonConstant.FALSE_INT);
            sysUserSystem.setUserOrgGroupId(dto.getUserOrgGroupId());
            sysUserSystemService.save(sysUserSystem);
        } else if (StrUtil.equals(systemId, SystemIdEnum.XTSZ.getId())) {
            List<SysUserSystem> list = new ArrayList<>();
            List<String> systemIdList = Arrays.asList(SystemIdEnum.JGSY.getId(), SystemIdEnum.YBGL.getId());
            systemIdList.forEach(et -> {
                SysUserSystem sysUserSystem = new SysUserSystem();
                sysUserSystem.setUserId(entity.getId());
                sysUserSystem.setUserPostId(userPostId);
                sysUserSystem.setSystemId(et);
                sysUserSystem.setIsLock(CommonConstant.FALSE_INT);
                list.add(sysUserSystem);
            });

            sysUserSystemService.saveBatch(list);
        }
        return Result.ok(entity);
    }

    @Override
    public SysUserVO findById(String id, String userPostId) {
        SysUser entity = this.getById(id);
        SysUserVO sysUserVO = sysUserConverter.entity2Vo(entity);
        SysUserPost sysUserPost = sysUserPostService.getById(userPostId);
        sysUserVO.setUserPostId(userPostId);
        String currSystemId = RequestHolder.getCurrSystemId();
        List<SysUserSystem> sysUserSystemList = sysUserSystemService.findByUserPostId(userPostId);
        Map<String, SysUserSystem> userSystemMap = sysUserSystemList.stream().collect(Collectors.toMap(SysUserSystem::getSystemId, et -> et, (k1, k2) -> k1));
        if (StrUtil.equals(currSystemId, SystemIdEnum.XTSZ.getId())) {
            // 系统管理
            sysUserVO.setOrgLevelCode(sysUserPost.getDeptLevelCode());
            OrgInfo orgInfoVO = orgInfoService.getById(sysUserPost.getDeptId());
            sysUserVO.setOrgLevelCodeName(Objects.nonNull(orgInfoVO) ? orgInfoVO.getName() : null);
            List<String> systemIdList = new ArrayList<>(userSystemMap.keySet());
            sysUserVO.setSystemIdList(systemIdList);
            return sysUserVO;
        }

        SysUserSystem sysUserSystem = userSystemMap.get(currSystemId);
        List<SysUserRole> userRoleList = sysUserRoleService.findByUserSystemId(sysUserSystem.getUserId(), sysUserSystem.getSystemId());

        sysUserVO.setManageCode(sysUserVO.getManageCode());
        sysUserVO.setOrgLevelCode(sysUserVO.getSsdwId());
        // 查询所属单位，管理单位名称
        List<String> orgCodeList = Arrays.asList(sysUserVO.getManageCode(), sysUserVO.getSsdwId());
        List<OrgInfo> listByCodes = orgInfoService.findByIdList(orgCodeList);
        List<Geocode> geocodeList = customDictService.getGeocodes();
        Map<String, String> geoCodeMap = geocodeList.stream().collect(Collectors.toMap(et -> et.getCode(), et -> et.getName(), (k1, k2) -> k1));
        Map<String, String> orgMap = listByCodes.stream().collect(Collectors.toMap(OrgInfo::getId, OrgInfo::getName, (k1, k2) -> k2));
        sysUserVO.setOrgLevelCodeName(orgMap.getOrDefault(sysUserVO.getOrgLevelCode(), null));
        sysUserVO.setManageName(orgMap.getOrDefault(sysUserVO.getManageCode(), null));
        if (StrUtil.isNotBlank(sysUserVO.getManageCode()) && sysUserVO.getManageCode().length() == 6) {
            sysUserVO.setManageName(geoCodeMap.getOrDefault(sysUserVO.getManageCode(), null));
        }

        if (CollUtil.isNotEmpty(userRoleList)) {
            sysUserVO.setRoleId(userRoleList.get(0).getRoleId());
        }
        sysUserVO.setUserOrgGroupId(sysUserSystem.getUserOrgGroupId());

        return sysUserVO;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Result update(SysUserDTO dto) {
        if (StrUtil.isEmpty(dto.getAccount())) {
            return Result.fail(BbResultEnum.THE_USER_NOT_EXIST.getCode(), BbResultEnum.THE_USER_NOT_EXIST.getMsg(), null);
        }
        SysUser sysUser = this.getByAccountNeId(dto.getAccount(), dto.getId());
        if (ObjectUtil.isNotNull(sysUser)) {
            return Result.fail(BbResultEnum.THE_USER_ACCOUNT_IS_EXIST.getCode(), BbResultEnum.THE_USER_ACCOUNT_IS_EXIST.getMsg(), null);
        }
        // 是编办用户，选管理单位，非编办用户，就是普通的所属机构
//        if (ObjectUtil.equal(dto.getIsBbUser(), 0)) {
//            dto.setManageCode(dto.getOrgLevelCode());
//        }
        String currSystemId = RequestHolder.getCurrSystemId();
        boolean u = this.updateById(sysUserConverter.dto2Entity(dto));
        SysUserSystem sysUserSystem = sysUserSystemService.findByUserIdAndSystemId(dto.getId(), currSystemId, dto.getUserPostId());
        sysUserSystem.setUserOrgGroupId(dto.getUserOrgGroupId());
        OrgInfo orgInfo;
        if (ObjectUtil.equal(dto.getIsBbUser(), 0)) {
            orgInfo = orgInfoService.getByLevelCode(dto.getOrgLevelCode());
        } else {
            orgInfo = orgInfoService.getByLevelCode(dto.getManageCode());
            sysUserSystem.setGeocode(orgInfo.getGeocode());
        }
        sysUserSystem.setOrgId(orgInfo.getId());
        sysUserSystem.setOrgLevelCode(orgInfo.getLevelCode());
        sysUserSystem.setIsBbUser(dto.getIsBbUser());
        sysUserSystem.setGeocode(orgInfo.getGeocode());

        sysUserSystemService.updateById(sysUserSystem);
        LambdaQueryWrapper<SysUserRole> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(SysUserRole::getUserSystemId, sysUserSystem.getId());
        // 删除用户角色关联表后再新增
        sysUserRoleService.remove(wrapper);
//        sysUserSystemService.updateUserOrgGroup(currSystemId, dto.getId(), dto.getUserOrgGroupId());
        if (ObjectUtil.isNotNull(dto.getRoleId())) {
            SysRoleVO role = sysRoleService.findById(dto.getRoleId());
            SysUserRole sysUserRole = new SysUserRole();
            sysUserRole.setUserId(dto.getId());
            sysUserRole.setRoleId(role.getId());
            sysUserRole.setSystemCode(role.getSystemCode());
            sysUserRole.setUserSystemId(sysUserSystem.getId());
            boolean s = sysUserRoleService.save(sysUserRole);
            return u && s ? Result.ok() : Result.fail();
        }

        return u ? Result.ok() : Result.fail();
    }

    @Override
    public Boolean delete(String id) {
        LambdaUpdateWrapper<SysUser> wrapper = new LambdaUpdateWrapper<>();
        wrapper.set(SysUser::getIsDelete, 1);
        wrapper.set(SysUser::getDeleteTime, new Date());
        wrapper.eq(SysUser::getId, id);
        wrapper.eq(SysUser::getIsDelete, 0);
        return this.update(wrapper);
    }

    @Override
    public Page<SysUserVO> list(SysUserListDTO dto) {
        // 编办管理员角色，排除系统管理员账户
        String currSystemId = RequestHolder.getCurrSystemId();
        if (!StrUtil.equals(currSystemId, SystemIdEnum.XTSZ.getId())) {
            dto.setSystemId(currSystemId);
        }
        dto.setUserId(RequestHolder.getCurrUser().getUserId());
        Page<SysUserVO> page = baseMapper.getPage(new Page<>(dto.getPageNum(), dto.getPageSize()), dto);
        return page;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Result login(LoginDTO dto) {
        SysUser sysUser = null;
        if (!dto.isSsoLogin()) {
            // 账号登录
            String account = dto.getAccount();
            sysUser = this.getByAccount(account);
            String password = sysUser.getPassword();
            if (!StrUtil.equals(authUtil.encrypt(dto.getPassword()), password)) {
                return Result.build(AuthResultEnum.ACCOUNT_FAIL);
            }
        } else {
            final String zwddId = String.valueOf(dto.getZwddId());
            sysUser = this.findByZwddId(zwddId);
            if (Objects.isNull(sysUser)) {
                return Result.build(AuthResultEnum.ACCOUNT_FAIL);
            }
        }

        UserInfo userInfo = new UserInfo();
        List<SysUserSystemVO> byUserId = sysUserSystemService.findByUserId(sysUser.getId());
        if (CollUtil.isEmpty(byUserId)) {
            return Result.build(BbResultEnum.USER_SYSTEM_NULL);
        }
        Date date = new Date();
        long unlockCount = byUserId.stream().filter(et -> Objects.equals(et.getIsLock(), BbCommonConstant.FALSE_INT) || (Objects.equals(et.getIsLock(), BbCommonConstant.TRUE_INT) && Objects.nonNull(et.getUnlockTime()) && DateUtil.between(date, et.getUnlockTime(), DateUnit.SECOND, false) < 0)).count();
        if (unlockCount < 1) {
            return Result.build(BbResultEnum.USER_LOCK);
        }
        List<String> systemIdList = byUserId.stream().map(SysUserSystemVO::getSystemId).collect(Collectors.toList());
//        userInfo.setSystemIdList(systemIdList);
        userInfo.setUserId(sysUser.getId());
        userInfo.setAccount(sysUser.getAccount());
        userInfo.setUsername(sysUser.getName());
//        userInfo.setUser(sysUser);
        // 获取角色，菜单权限
        // SysUserRole userRole = sysUserRoleService.findByUserId(sysUser.getId());

        // if (ObjectUtil.isNotNull(userRole)) {
        //     userInfo.setRoleId(userRole.getRoleId());
        //     SysRoleVO sysRole = sysRoleService.findById(userRole.getRoleId());
        //     userInfo.setRoleName(sysRole.getName());
        //     List<String> menuCodes = sysRoleMenuService.findByRoleId(userRole.getRoleId());
        //     userInfo.setMenus(menuCodes);
        // }

        // userInfo.setOrgCode(sysUser.getManageCode());
        // userInfo.setOrgLevelCode(sysUser.getOrgLevelCode());
        RequestHolder.set(userInfo);
        final long nanoTime = System.currentTimeMillis();
        String token = TokenUtil.createToken(String.valueOf(sysUser.getId()), nanoTime, LoginTypeEnum.LOGIN.getCode(), RequestHolder.getCurrRequest());
        userInfo.setToken(token);
        String loginIp = WebUtil.getIP(RequestHolder.getCurrRequest());
        userInfo.setLoginIp(loginIp);
        userInfo.setCreateTime(nanoTime);
        // 存入缓存
        cacheUtil.put(AuthConstant.TOKEN_CACHE, AuthConstant.TOKEN_CACHE + token, userInfo);
        return Result.ok(cacheUtil.get(AuthConstant.TOKEN_CACHE, AuthConstant.TOKEN_CACHE + token));
    }

    @Override
    public SysUser getByAccount(String account) {
        LambdaQueryWrapper<SysUser> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(SysUser::getAccount, account);
        wrapper.eq(SysUser::getIsDelete, 0);
        wrapper.last("limit 1");
        return baseMapper.selectOne(wrapper);
    }

    @Override
    public SysUser getByZwddId(String zwddId) {
        LambdaQueryWrapper<SysUser> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(SysUser::getZwddId, zwddId);
        wrapper.eq(SysUser::getIsDelete, 0);
        wrapper.last("limit 1");
        return baseMapper.selectOne(wrapper);
    }

//    @Override
//    @Transactional(rollbackFor = Exception.class)
//    public UserInfo login(DdUser ddUser) {
//        SysUser sysUser = null;
//        if (ddUser.getIsLocal()) {
//            // 模拟账号登录
//            String account = ddUser.getAccount();
//            sysUser = this.getByAccount(account);
//            if (Objects.isNull(sysUser)) {
//                // todo 测试需要
//                sysUser = new SysUser();
//                sysUser.setOpenId(ddUser.getOpenid());
//                sysUser.setAccount(account);
//                sysUser.setZwddId(sysUser.getZwddId());
//                sysUser.setPhone(sysUser.getPhone());
//                sysUser.setIsDelete(0);
//                this.save(sysUser);
//                // 保存角色信息
//                SysUserRole sysUserRole = new SysUserRole();
//                sysUserRole.setUserId(sysUser.getId());
//                // 默认为普通用户角色
//                sysUserRole.setRoleId("eb71c69dfbaf281fe151ea4340b3fd1f");
//                sysUserRoleService.save(sysUserRole);
//            }
//            String password = sysUser.getPassword();
//            if (!StrUtil.equals(authUtil.encrypt(ddUser.getPassword()), password)) {
//                return null;
//            }
//        } else {
//            final String accountId = String.valueOf(ddUser.getAccountId());
//            sysUser = this.findByZwddId(accountId);
//            if (Objects.isNull(sysUser)) {
//                // todo 测试需要
//                sysUser = new SysUser();
//                sysUser.setOpenId(ddUser.getOpenid());
//                sysUser.setAccount(accountId);
//                sysUser.setZwddId(sysUser.getZwddId());
//                sysUser.setPhone(sysUser.getPhone());
//                sysUser.setIsDelete(0);
//                this.save(sysUser);
//                // 保存角色信息
//                SysUserRole sysUserRole = new SysUserRole();
//                sysUserRole.setUserId(sysUser.getId());
//                // 默认为普通用户角色
//                sysUserRole.setRoleId("eb71c69dfbaf281fe151ea4340b3fd1f");
//                sysUserRoleService.save(sysUserRole);
//            }
//        }
//
//        UserInfo userInfo = new UserInfo();
//        List<SysUserSystemVO> byUserId = sysUserSystemService.findByUserId(userInfo.getUserId());
//        Date date = new Date();
//        long unlockCount = byUserId.stream().filter(et -> Objects.equals(et.getIsLock(), BbCommonConstant.FALSE_INT) || (Objects.equals(et.getIsLock(), BbCommonConstant.TRUE_INT) && Objects.nonNull(et.getUnlockTime()) && DateUtil.between(date, et.getUnlockTime(), DateUnit.SECOND, false) < 0)).count();
//        if (unlockCount < 1) {
//            return null;
//        }
//        List<String> systemIdList = byUserId.stream().map(SysUserSystemVO::getSystemId).collect(Collectors.toList());
//        userInfo.setSystemIdList(systemIdList);
//        userInfo.setUserId(sysUser.getId());
//        userInfo.setAccount(sysUser.getAccount());
//        userInfo.setUsername(sysUser.getName());
//        userInfo.setUser(sysUser);
//        // 获取角色，菜单权限
//        // SysUserRole userRole = sysUserRoleService.findByUserId(sysUser.getId());
//
//        // if (ObjectUtil.isNotNull(userRole)) {
//        //     userInfo.setRoleId(userRole.getRoleId());
//        //     SysRoleVO sysRole = sysRoleService.findById(userRole.getRoleId());
//        //     userInfo.setRoleName(sysRole.getName());
//        //     List<String> menuCodes = sysRoleMenuService.findByRoleId(userRole.getRoleId());
//        //     userInfo.setMenus(menuCodes);
//        // }
//
//        // userInfo.setOrgCode(sysUser.getManageCode());
//        // userInfo.setOrgLevelCode(sysUser.getOrgLevelCode());
//        RequestHolder.set(userInfo);
//        final long nanoTime = System.currentTimeMillis();
//        String token = TokenUtil.createToken(String.valueOf(sysUser.getId()), nanoTime, RequestHolder.getCurrRequest());
//        userInfo.setToken(token);
//        String loginIp = WebUtil.getIP(RequestHolder.getCurrRequest());
//        userInfo.setLoginIp(loginIp);
//        userInfo.setCreateTime(nanoTime);
//        // 存入缓存
//        cacheUtil.put(AuthConstant.TOKEN_CACHE, AuthConstant.TOKEN_CACHE + token, userInfo);
//        return cacheUtil.get(AuthConstant.TOKEN_CACHE, AuthConstant.TOKEN_CACHE + token);
//    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Result updateZwd(String zwddId, String accountId) {
        SysUser sysUser = this.getById(accountId);
        if (Objects.isNull(sysUser)) {
            return Result.build(BbResultEnum.DATA_NOT_EXIST);
        }
        SysUser byZwddId = this.findByZwddIdNeId(zwddId, sysUser.getId());
        if (ObjectUtil.isNotNull(byZwddId)) {
            return Result.fail(BbResultEnum.THE_ZWD_ID_IS_EXIST.getCode(), BbResultEnum.THE_ZWD_ID_IS_EXIST.getMsg(), null);
        }
        sysUser.setZwddId(zwddId);
        boolean b = this.updateById(sysUser);
        return b ? Result.ok() : Result.fail();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Result updatePwd(String id, String password) {
        SysUser sysUser = this.getById(id);
        if (ObjectUtil.isNull(sysUser)) {
            return Result.build(BbResultEnum.DATA_NOT_EXIST);
        }
        sysUser.setPassword(authUtil.createPassword(new String(Base64.getDecoder().decode(password.getBytes(StandardCharsets.UTF_8)))));
        boolean b = this.updateById(sysUser);
        return b ? Result.ok() : Result.fail();
    }

    @Override
    public SysUser getByAccountNeId(String account, String id) {
        LambdaQueryWrapper<SysUser> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(SysUser::getAccount, account);
        wrapper.ne(SysUser::getId, id);
        wrapper.eq(SysUser::getIsDelete, 0);
        wrapper.last("limit 1");
        return baseMapper.selectOne(wrapper);
    }

    @Override
    public Result getPermission(String systemId, String userPostId) {
        UserInfo userInfo = (UserInfo) RequestHolder.getCurrUser();
        String userId = userInfo.getUserId();
        SysUser sysUser = sysUserService.getById(userId);
        if (StrUtil.equals(SystemIdEnum.XTSZ.getId(), systemId)) {
            List<SysMenu> menuList = sysMenuService.getListBySystemId(systemId);
            Map<String, Object> map = new HashMap<>();
            map.put("menuList", menuList.stream().map(SysMenu::getCode).collect(Collectors.toList()));
            return Result.ok(map);
        }
        SysUserSystem sysUserSystem = sysUserSystemService.findByUserIdAndSystemId(userId, systemId, userPostId);
        if (Objects.isNull(sysUserSystem)) {
            return Result.build(ResultEnum.NOT_PERMISSION);
        }
        List<SysUserRole> userRoleList = sysUserRoleService.findByUserId(userId, systemId);
        if (CollUtil.isEmpty(userRoleList)) {
            return Result.build(ResultEnum.NOT_PERMISSION);
        }

        List<String> roleList = userRoleList.stream().map(SysUserRole::getRoleId).collect(Collectors.toList());
        List<SysRoleMenu> roleMenuList = sysRoleMenuService.findByRoleList(roleList);
        List<String> menuList = roleMenuList.stream().map(SysRoleMenu::getMenuCode).collect(Collectors.toList());
        userInfo.setMenuListBySystemId(sysUserSystem, menuList, sysUser);
        loginService.putCacheUser(userInfo.getToken(), userInfo);
        Map<String, Object> map = new HashMap<>();
        map.put("menuList", menuList);
        return Result.ok(map);
    }

    @Override
    public List<SysUser> getByAccountList(List<String> accountList) {
        LambdaQueryWrapper<SysUser> queryWrapper = new LambdaQueryWrapper<>(SysUser.class);
        queryWrapper.in(SysUser::getAccount, accountList);
        return this.list(queryWrapper);
    }

    @Override
    public Page<SysUserVO> sysList(SysUserListDTO dto) {
        Page<SysUserVO> page = baseMapper.sysList(new Page<>(dto.getPageNum(), dto.getPageSize()), dto);
        return page;
    }

    @Override
    public String findZwddIdById(String userId) {
        LambdaQueryWrapper<SysUser> queryWrapper = new LambdaQueryWrapper<>(SysUser.class);
        queryWrapper.select(SysUser::getZwddId, SysUser::getId);
        queryWrapper.eq(SysUser::getId, userId);
        queryWrapper.last(DbConstant.LIMIT_ONE);
        SysUser sysUser = this.getOne(queryWrapper);
        return Objects.isNull(sysUser) ? null : sysUser.getZwddId();
    }

    @Override
    public Result changeUnit(String userPostId) {
        UserInfo currUser = (UserInfo) RequestHolder.getCurrUser();
        long timeMillis = System.currentTimeMillis();
        String newToken = TokenUtil.createToken(currUser.getUserId(), timeMillis, LoginTypeEnum.LOGIN.getCode(), RequestHolder.getCurrRequest());
        currUser.setToken(newToken);
        String jwtToken = jwtUtil.createToken(currUser);
        currUser.setUserPostId(userPostId);
        currUser.setUserUnitId(userPostId);
        SysUser sysUser = sysUserService.getById(currUser.getUserId());
        List<SysUserSystem> sysUserSystemList = sysUserSystemService.findByUserPostId(userPostId);
        if (CollUtil.isNotEmpty(sysUserSystemList)) {
            List<LoginSystem> systemList = new ArrayList<>();
            for (SysUserSystem sysUserSystem : sysUserSystemList) {
                systemList.add(UserInfo.setLoginSystem(sysUserSystem, sysUser));
            }
            currUser.setSystemList(systemList);
        } else {
            currUser.setSystemList(null);
        }

        int tokenLive = authProperties.getLogin().getTokenLive();
        cacheUtil.put(AuthConstant.AUTH_CACHE, AuthConstant.TOKEN_CACHE + newToken, jwtToken, tokenLive);
        return Result.ok(currUser);
    }

    @Override
    public Result sysUpdate(SysUserDTO dto) {
        String userPostId = dto.getUserPostId();
        String id = dto.getId();
        List<String> systemIdList = dto.getSystemIdList();
        if (CollUtil.isEmpty(systemIdList)) {
            return Result.fail(ResultEnum.PARA_ERROR);
        }
        List<SysUserSystem> userSystemList = sysUserSystemService.findByUserPostId(userPostId);

        if (CollUtil.isEmpty(userSystemList)) {
            userSystemList = new ArrayList<>();
        }
        List<String> deleteList = userSystemList.stream().filter(et -> !systemIdList.contains(et.getSystemId())).map(et -> et.getId()).collect(Collectors.toList());
        List<SysUserSystem> saveList = new ArrayList<>();
        Map<String, SysUserSystem> sysUserSystemMap = userSystemList.stream().collect(Collectors.toMap(et -> et.getSystemId(), et -> et, (k1, k2) -> k1));
        for (String systemId : systemIdList) {
            SysUserSystem sysUserSystem = sysUserSystemMap.get(systemId);
            if (Objects.isNull(sysUserSystem)) {
                sysUserSystem = new SysUserSystem();
                sysUserSystem.setUserId(id);
                sysUserSystem.setSystemId(systemId);
                sysUserSystem.setUserPostId(userPostId);
            }
            if (StrUtil.equals(systemId, SystemIdEnum.XTSZ.getId())) {
                OrgInfo orgInfo = orgInfoService.findTop();
                sysUserSystem.setOrgId(orgInfo.getId());
                sysUserSystem.setOrgLevelCode(orgInfo.getLevelCode());
            }
            saveList.add(sysUserSystem);
        }

        if (CollUtil.isNotEmpty(deleteList)) {
            sysUserSystemService.removeByIds(deleteList);
        }
        if (CollUtil.isNotEmpty(saveList)) {
            sysUserSystemService.saveOrUpdateBatch(saveList);
        }

        return Result.ok();
    }

    @Override
    public List<SysUser> findUserByDeptIdAndSystemId(String unitId, String currSystemId) {
        return sysUserMapper.findUserByDeptIdAndSystemId(unitId, currSystemId);
    }

    @Override
    public OrgInfo findOrgInfoByUserAndSystemId(String userId, String systemId) {
        return sysUserMapper.findOrgInfoByUserAndSystemId(userId, systemId);
    }

    @Override
    public OrgInfo findOrgInfoByUserAndSystemIdAndPostId(String userId, String systemId, String userPostId) {
        return sysUserMapper.findOrgInfoByUserAndSystemIdAndPostId(userId, systemId, userPostId);
    }

    @Override
    public OrgInfo findBbUnitByUserId(String userId, String systemId) {
        return sysUserMapper.findBbUnitByUserId(userId, systemId);
    }

    @Override
    public void deleteAll() {
        sysUserMapper.deleteUserAll();
        sysUserMapper.deleteUserPostAll();
        sysUserMapper.deleteUserRoleAll();
        sysUserMapper.deleteUserSystemAll();
    }

    @Override
    public List<SysUser> findBySzdwId(String unitId) {
        LambdaQueryWrapper<SysUser> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(SysUser::getSsdwId, unitId);
        queryWrapper.orderByAsc(SysUser::getId);
        // queryWrapper.last(DbConstant.LIMIT_ONE);
        return this.list(queryWrapper);
    }

    @Override
    public List<SysUser> findBbUserByGeoCode(String geocode) {
        LambdaQueryWrapper<SysUser> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(SysUser::getManageCode, geocode);
        queryWrapper.eq(SysUser::getIsBbUser, TRUE_INT);
        queryWrapper.orderByAsc(SysUser::getId);
        return this.list(queryWrapper);
    }

    private SysUser findByZwddIdNeId(String accountId, String id) {
        LambdaQueryWrapper<SysUser> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(SysUser::getZwddId, accountId);
        queryWrapper.ne(SysUser::getId, id);
        queryWrapper.eq(SysUser::getIsDelete, 0);
        queryWrapper.last("limit 1");
        return this.getOne(queryWrapper);
    }

    private SysUser findByZwddId(String accountId) {
        LambdaQueryWrapper<SysUser> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(SysUser::getZwddId, accountId);
        queryWrapper.eq(SysUser::getIsDelete, 0);
        queryWrapper.last("limit 1");
        return this.getOne(queryWrapper);
    }
}
