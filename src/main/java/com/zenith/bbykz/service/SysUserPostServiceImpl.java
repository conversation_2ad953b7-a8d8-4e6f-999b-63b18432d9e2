package com.zenith.bbykz.service;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.efficient.common.constant.CommonConstant;
import com.efficient.common.constant.DbConstant;
import com.zenith.bbykz.api.SysUserPostService;
import com.zenith.bbykz.dao.SysUserPostMapper;
import com.zenith.bbykz.model.converter.SysUserPostConverter;
import com.zenith.bbykz.model.entity.SysUserPost;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <p>
 * 用户职位信息 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @date 2024-03-26 15:04:24
 */
@Service
public class SysUserPostServiceImpl extends ServiceImpl<SysUserPostMapper, SysUserPost> implements SysUserPostService {

    @Autowired
    private SysUserPostConverter sysUserPostConverter;
    @Autowired
    private SysUserPostMapper sysUserPostMapper;

    @Override
    public List<SysUserPost> findByUserId(String userId) {
        LambdaQueryWrapper<SysUserPost> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(SysUserPost::getUserId, userId);
        queryWrapper.eq(SysUserPost::getIsDelete, CommonConstant.FALSE_INT);
        queryWrapper.orderByDesc(SysUserPost::getMainJob);
        queryWrapper.orderByAsc(SysUserPost::getDeptId);
        return this.list(queryWrapper);
    }

    @Override
    public SysUserPost findByUserIdAndDeptId(String userId, String deptId) {
        LambdaQueryWrapper<SysUserPost> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(SysUserPost::getUserId, userId);
        queryWrapper.eq(SysUserPost::getIsDelete, CommonConstant.FALSE_INT);
        queryWrapper.eq(SysUserPost::getDeptId, deptId);
        queryWrapper.last(DbConstant.LIMIT_ONE);
        return this.sysUserPostMapper.selectOne(queryWrapper);
    }

}
