package com.zenith.bbykz.service;

import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.efficient.common.auth.RequestHolder;
import com.efficient.common.constant.CommonConstant;
import com.efficient.common.result.Result;
import com.efficient.common.util.JackSonUtil;
import com.efficient.common.util.ThreadUtil;
import com.zenith.bbykz.api.OrgInfoService;
import com.zenith.bbykz.api.SysUserOrgGroupDetailService;
import com.zenith.bbykz.api.SysUserOrgGroupService;
import com.zenith.bbykz.dao.SysUserOrgGroupMapper;
import com.zenith.bbykz.model.converter.SysUserOrgGroupConverter;
import com.zenith.bbykz.model.dto.SysUserOrgGroupDTO;
import com.zenith.bbykz.model.dto.SysUserOrgGroupDetailDTO;
import com.zenith.bbykz.model.dto.SysUserOrgGroupListDTO;
import com.zenith.bbykz.model.entity.OrgInfo;
import com.zenith.bbykz.model.entity.SysUserOrgGroup;
import com.zenith.bbykz.model.entity.SysUserOrgGroupDetail;
import com.zenith.bbykz.model.vo.SysUserOrgGroupVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Objects;
import java.util.concurrent.CopyOnWriteArrayList;
import java.util.stream.Collectors;

/**
 * <p>
 * 用户机构组 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @date 2024-03-13 10:48:03
 */
@Service
public class SysUserOrgGroupServiceImpl extends ServiceImpl<SysUserOrgGroupMapper, SysUserOrgGroup> implements SysUserOrgGroupService {

    @Autowired
    private SysUserOrgGroupConverter sysUserOrgGroupConverter;
    @Autowired
    private SysUserOrgGroupMapper sysUserOrgGroupMapper;
    @Autowired
    private SysUserOrgGroupDetailService sysUserOrgGroupDetailService;
    @Autowired
    private OrgInfoService orgInfoService;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Result<SysUserOrgGroup> save(SysUserOrgGroupDTO dto) {
        SysUserOrgGroup entity = sysUserOrgGroupConverter.dto2Entity(dto);
        entity.setRequestParams(JackSonUtil.toJson(dto.getDetailList()));
        boolean flag = this.save(entity);
        this.saveDeatilList(entity.getId(), dto.getDetailList());
        return Result.ok(entity);
    }

    @Override
    public Result<SysUserOrgGroupVO> findById(String id) {
        SysUserOrgGroup entity = this.getById(id);
        SysUserOrgGroupVO vo = sysUserOrgGroupConverter.entity2Vo(entity);
//        List<SysUserOrgGroupDetail> detailList = sysUserOrgGroupDetailService.findByGroupId(vo.getId());
        vo.setDetailList(JackSonUtil.toObjectList(entity.getRequestParams(), SysUserOrgGroupDetailDTO.class));
        return Result.ok(vo);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Result<Boolean> update(SysUserOrgGroupDTO dto) {
        SysUserOrgGroup sysUserOrgGroup = sysUserOrgGroupConverter.dto2Entity(dto);
        sysUserOrgGroup.setRequestParams(JackSonUtil.toJson(dto.getDetailList()));
        boolean flag = this.updateById(sysUserOrgGroup);
        this.saveDeatilList(dto.getId(), dto.getDetailList());
        return flag ? Result.ok() : Result.fail();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Result<Boolean> delete(String id) {
        boolean flag = this.removeById(id);
        sysUserOrgGroupDetailService.deleteByGroupId(id);
        return flag ? Result.ok() : Result.fail();
    }

    @Override
    public Page<SysUserOrgGroup> list(SysUserOrgGroupListDTO dto) {
        LambdaQueryWrapper<SysUserOrgGroup> queryWrapper = new LambdaQueryWrapper<>(SysUserOrgGroup.class);
        queryWrapper.like(StrUtil.isNotBlank(dto.getKeyword()), SysUserOrgGroup::getName, dto.getKeyword());
        String userId = RequestHolder.getCurrUser().getUserId();
        queryWrapper.eq(SysUserOrgGroup::getUserId, userId);
        queryWrapper.orderByDesc(SysUserOrgGroup::getCreateTime);
        final Page<SysUserOrgGroup> page = sysUserOrgGroupMapper.selectPage(new Page<>(dto.getPageNum(), dto.getPageSize()), queryWrapper);
        return page;
    }

    @Override
    public void saveDeatilList(String id, List<SysUserOrgGroupDetailDTO> detailList) {
        ThreadUtil.EXECUTOR_SERVICE.execute(() -> {

            List<SysUserOrgGroupDetail> saveList = new CopyOnWriteArrayList<>();
            detailList.stream().filter(et -> Objects.equals(et.getAllSelect(), CommonConstant.FALSE_INT)).forEach(et -> {
                SysUserOrgGroupDetail detail = new SysUserOrgGroupDetail();
                detail.setUserOrgGroupId(id);
                detail.setOrgId(et.getOrgId());
                detail.setOrgLevelCode(et.getOrgLevelCode());
                saveList.add(detail);
            });
            List<String> levelCodeList = detailList.stream().filter(et -> Objects.equals(et.getAllSelect(), CommonConstant.TRUE_INT)).map(SysUserOrgGroupDetailDTO::getOrgLevelCode).collect(Collectors.toList());
            List<OrgInfo> orgInfoList = orgInfoService.findByLevelCodeList(levelCodeList);
            orgInfoList.parallelStream().forEach(et -> {
                SysUserOrgGroupDetail detail = new SysUserOrgGroupDetail();
                detail.setUserOrgGroupId(id);
                detail.setOrgId(et.getId());
                detail.setOrgLevelCode(et.getLevelCode());
                saveList.add(detail);
            });
            sysUserOrgGroupDetailService.saveByGroupId(id, saveList);
        });
    }
}
