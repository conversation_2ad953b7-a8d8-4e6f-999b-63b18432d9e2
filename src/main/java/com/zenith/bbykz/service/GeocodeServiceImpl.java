package com.zenith.bbykz.service;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.zenith.bbykz.api.GeocodeService;
import com.zenith.bbykz.dao.GeocodeMapper;
import com.zenith.bbykz.model.converter.GeocodeConverter;
import com.zenith.bbykz.model.entity.Geocode;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.List;

/**
 * <p>
 * 区划代码 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @date 2024-03-13 09:52:37
 */
@Service
public class GeocodeServiceImpl extends ServiceImpl<GeocodeMapper, Geocode> implements GeocodeService {

    @Autowired
    private GeocodeConverter geocodeConverter;
    @Autowired
    private GeocodeMapper geocodeMapper;

    @Override
    public List<Geocode> findAll() {
        LambdaQueryWrapper<Geocode> queryWrapper = new LambdaQueryWrapper<>();
        return this.list(queryWrapper);
    }
}
