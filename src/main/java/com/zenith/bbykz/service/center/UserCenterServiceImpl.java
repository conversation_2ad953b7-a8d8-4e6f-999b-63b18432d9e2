package com.zenith.bbykz.service.center;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.http.HttpResponse;
import cn.hutool.http.HttpUtil;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.efficient.cache.api.CacheUtil;
import com.efficient.common.constant.DbConstant;
import com.efficient.common.result.Result;
import com.efficient.common.util.JackSonUtil;
import com.zenith.bbykz.api.*;
import com.zenith.bbykz.api.center.UserCenterService;
import com.zenith.bbykz.dao.OrgInfoMapper;
import com.zenith.bbykz.model.dto.center.CenterPage;
import com.zenith.bbykz.model.dto.center.GbpUser;
import com.zenith.bbykz.model.dto.center.JgsyCommon;
import com.zenith.bbykz.model.entity.*;
import com.zenith.bbykz.model.entity.bz.common.Systemcode;
import com.zenith.bbykz.service.efficient.CustomDictService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

import static com.zenith.bbykz.common.constant.BbCommonConstant.*;

/**
 * <AUTHOR>
 * @since 2024/8/26 10:40
 */
@Service
@Slf4j
public class UserCenterServiceImpl implements UserCenterService {

    private final int httpSuccessCode = 200;
    @Value("${com.smz.http-url}")
    private String smzHttpUrl;
    @Autowired
    private OrgInfoService orgInfoService;
    @Autowired
    private OrgInfoMapper orgInfoMapper;
    @Autowired
    private GeocodeService geocodeService;
    @Autowired
    private SysUserService sysUserService;
    @Autowired
    private SysSystemService sysSystemService;
    @Autowired
    private SysUserPostService sysUserPostService;
    @Autowired
    private SysUserSystemService sysUserSystemService;
    @Autowired
    private SysUserRoleService sysUserRoleService;
    @Autowired
    private CacheUtil cacheUtil;
    private Map<String, String> geoCodeMap;
    @Autowired
    private CustomDictService customDictService;

    public static String getOrderStr(String order) {
        if (StrUtil.isBlank(order)) {
            return "9999";
        }
        if (order.length() > 10) {
            order = order.substring(0, 10);
        }
        final String[] str = order.trim().split(" ");
        // final String replaceAll = order.replaceAll(" ", "");
        return str[str.length - 1].replaceAll("[^0-9]", "");
    }

    public static String jgsyCode2LevelCode(String jgsyCode, String depCode, Map<String, String> geoCodeMap) {
        String[] split = jgsyCode.split("-");
        if (StrUtil.isBlank(depCode)) {
            depCode = split[0];
            String bd = BIANDONG_QX_GEOCODE.get(depCode);
            if (StrUtil.isNotBlank(bd)) {
                depCode = bd;
            }
        }

        String levelCode = CQ_CODE;
        for (int i = 0; i < split.length; i++) {
            if (i == 0) {
                String type = geoCodeMap.get(depCode);
                if (StrUtil.equalsAny(type, "4", "5")) {
                    switch (type) {
                        case "4":
                            levelCode = levelCode + "-" + SXQ_CODE;
                            break;
                        case "5":
                            levelCode = levelCode + "-" + X_CODE;
                            break;
                    }
                }
                levelCode = levelCode + "-" + depCode;
                continue;
            }
            levelCode = levelCode + "-" + split[i];
        }
        return levelCode;
    }

    @Override
    // @Transactional(rollbackFor = Exception.class,timeout = 12000)
    public Result syncSmzJg() {
        CenterPage centerPage = new CenterPage();
        centerPage.setPageSize(1000);
        centerPage.setPageNum(1);
        List<Geocode> geocodeList = customDictService.getGeocodes();
        geoCodeMap = geocodeList.stream().collect(Collectors.toMap(Geocode::getCode, Geocode::getType, (k1, k2) -> k1));
        List<OrgInfo> orgInfoList = new ArrayList<>();
        boolean flag = this.handlerJgCommon(centerPage, orgInfoList);
        if (CollUtil.isNotEmpty(orgInfoList)) {
            orgInfoService.deleteAll();
            // orgInfoService.saveBatch(orgInfoList, DbConstant.BATCH_LITTLE_SIZE);
            // orgInfoService.saveBatch(orgInfoList, 100);
            // orgInfoService.saveBatch(orgInfoList, 100);
            for (OrgInfo orgInfo : orgInfoList) {
                orgInfoService.save(orgInfo);
            }
        }
        // 处理层级码
        orgInfoMapper.flushErrorLevelCode();
        return flag ? Result.ok("本次同步机构数据为：" + orgInfoList.size() + "条") : Result.fail();
    }

    @Override
    // @Transactional(rollbackFor = Exception.class,timeout = 12000)
    public Result syncSmzUser() {
        CenterPage centerPage = new CenterPage();
        centerPage.setPageSize(1000);
        centerPage.setPageNum(1);
        List<SysUser> orgInfoList = new ArrayList<>();
        boolean flag = this.handlerUser(centerPage, orgInfoList);
        if (CollUtil.isNotEmpty(orgInfoList)) {
            List<SysSystem> list = sysSystemService.list();

            List<SysUserPost> userPostList = new ArrayList<>();
            List<SysUserSystem> userSystemList = new ArrayList<>();
            List<SysUserRole> userRoleList = new ArrayList<>();

            orgInfoList.forEach(et -> {
                SysUserPost sysUserPost = new SysUserPost();
                sysUserPost.setId(et.getId());
                sysUserPost.setUserId(et.getId());
                sysUserPost.setDeptId(et.getSsdwId());
                sysUserPost.setMainJob(1);
                sysUserPost.setIsDelete(0);
                sysUserPost.setPullTime(new Date());
                userPostList.add(sysUserPost);
                list.forEach(system -> {
                    if (!StrUtil.equals(system.getId(), "3") || (StrUtil.equals(system.getId(), "3") && et.getIsBbUser() == 1)) {
                        SysUserSystem sysUserSystem = new SysUserSystem();
                        sysUserSystem.setUserId(et.getId());
                        sysUserSystem.setSystemId(system.getId());
                        sysUserSystem.setIsDelete(0);
                        sysUserSystem.setIsDefault(0);
                        sysUserSystem.setUserPostId(et.getId());
                        userSystemList.add(sysUserSystem);

                        SysUserRole sysUserRole = new SysUserRole();
                        sysUserRole.setUserId(et.getId());
                        sysUserRole.setRoleId("system" + system.getId() + "-" + (et.getIsBbUser() == 1 ? "2" : "3"));
                        sysUserRole.setSystemCode(system.getId());
                        sysUserRole.setUserSystemId(system.getId());
                        userRoleList.add(sysUserRole);
                    }
                });
            });
            sysUserService.deleteAll();
            sysUserService.saveBatch(orgInfoList, DbConstant.BATCH_SIZE);
            sysUserPostService.saveBatch(userPostList, DbConstant.BATCH_SIZE);
            sysUserSystemService.saveBatch(userSystemList, DbConstant.BATCH_SIZE);
            sysUserRoleService.saveBatch(userRoleList, DbConstant.BATCH_SIZE);
        }
        return flag ? Result.ok("本次同步人员数据为：" + orgInfoList.size() + "条") : Result.fail();
    }

    @Override
    public Result orgBelong() {
        List<OrgInfo> orgInfoList = orgInfoService.list();
        Map<String, OrgInfo> orgInfoMap = orgInfoList.stream().collect(Collectors.toMap(et -> et.getId(), et -> et, (k1, k2) -> k1));
        List<OrgInfo> orgInfoSaveList = new ArrayList<>();
        List<Geocode> geocodeList = customDictService.getGeocodes();
        Map<String, String> geoCodeMap = geocodeList.stream().collect(Collectors.toMap(Geocode::getCode, Geocode::getName, (k1, k2) -> k1));
        List<Systemcode> systemcodeList = customDictService.getSystemCodes();
        Map<String, String> systemCodeMap = systemcodeList.stream().collect(Collectors.toMap(Systemcode::getSystemCode, Systemcode::getSystemCodeName, (k1, k2) -> k1));
        orgInfoList.forEach(et -> {
            OrgInfo orgInfo = new OrgInfo();
            orgInfo.setId(et.getId());
            orgInfo.setBelong(this.createBelong(orgInfoMap, et, geoCodeMap, systemCodeMap));

            orgInfoSaveList.add(orgInfo);
        });
        if (CollUtil.isNotEmpty(orgInfoSaveList)) {
            // orgInfoService.saveBatch(orgInfoList, DbConstant.BATCH_LITTLE_SIZE);
            orgInfoService.updateBatchById(orgInfoSaveList, 100);
        }

        return Result.ok();
    }

    private String createBelong(Map<String, OrgInfo> orgInfoMap, OrgInfo orgInfo, Map<String, String> geoCodeMap, Map<String, String> systemCodeMap) {
        List<String> belongList = new ArrayList<>();
        belongList.add("重庆市");
        String jgsyCode = orgInfo.getJgsyCode();
        String[] split = jgsyCode.split("-");
        String geocode = orgInfo.getGeocode();
        if (!StrUtil.equals(geocode, CQ_CODE)) {
            belongList.add(geoCodeMap.get(geocode));
        }
        belongList.add(systemCodeMap.get(split[1]));

        List<String> orgNameList = new ArrayList<>();
        orgNameList.add(orgInfo.getName());
        if (!StrUtil.equals(orgInfo.getJgsyCode(), orgInfo.getParentCode())) {
            this.findOrgParent(orgInfoMap, orgNameList, orgInfo.getParentCode());
        }
        for (int i = orgNameList.size() - 1; i >= 0; i--) {
            belongList.add(orgNameList.get(i));
        }
        return String.join("-", belongList);
    }

    private void findOrgParent(Map<String, OrgInfo> orgInfoMap, List<String> orgNameList, String jgsyCode) {
        OrgInfo orgInfo = orgInfoMap.get(jgsyCode);
        if (Objects.isNull(orgInfo)) {
            return;
        }
        orgNameList.add(orgInfo.getName());
        if (!StrUtil.equals(orgInfo.getJgsyCode(), orgInfo.getParentCode())) {
            this.findOrgParent(orgInfoMap, orgNameList, orgInfo.getParentCode());
        }
    }

    private boolean handlerUser(CenterPage centerPage, List<SysUser> orgInfoList) {
        log.info("开始获取人员数据：pageNum:{},pageSize:{}", centerPage.getPageNum(), centerPage.getPageSize());
        HttpResponse httpResponse = HttpUtil.createPost(smzHttpUrl + "/userCenter/syncUser")
                .body(JackSonUtil.toJson(centerPage)).execute();
        int status = httpResponse.getStatus();
        if (status != httpSuccessCode) {
            return false;
        }
        String body = httpResponse.body();
        Result result = JackSonUtil.toObject(body, Result.class);
        if (result == null || result.getCode() != httpSuccessCode) {
            return false;
        }
        Object data = result.getData();
        Page page = JackSonUtil.toObject(JackSonUtil.toJson(data), Page.class);
        List<GbpUser> records = JackSonUtil.toObjectList(JackSonUtil.toJson(page.getRecords()), GbpUser.class);
        if (CollUtil.isNotEmpty(records)) {
            records.forEach(et -> {
                orgInfoList.add(this.changeToSysUser(et));
            });
        }
        boolean b = page.hasNext();
        if (b) {
            CenterPage centerPageNext = new CenterPage();
            centerPageNext.setPageNum(centerPage.getPageNum() + 1);
            centerPageNext.setPageSize(centerPage.getPageSize());
            return this.handlerUser(centerPageNext, orgInfoList);
        }

        return true;
    }

    private SysUser changeToSysUser(GbpUser et) {
        SysUser sysUser = new SysUser();

        sysUser.setId(String.valueOf(et.getId()));
        sysUser.setAccount(et.getName());
        sysUser.setPassword(et.getPassword());
        sysUser.setZwddId(null);
        sysUser.setOpenId(null);
        sysUser.setIsDelete(0);
        sysUser.setCreateTime(null);
        sysUser.setUpdateTime(null);
        sysUser.setDeleteTime(null);
        sysUser.setIdCard(null);
        sysUser.setPhone(null);
        sysUser.setOrgId(null);
        sysUser.setOrgLevelCode(null);
        sysUser.setIsBbUser(StrUtil.equals(et.getIsBbuser(), "2") ? 1 : 0);
        sysUser.setManageCode(et.getDepCode());
        sysUser.setName(et.getRealname());
        sysUser.setIsLock(null);
        sysUser.setUnlockTime(null);
        if (sysUser.getIsBbUser() == 0) {
            OrgInfo orgInfo = orgInfoService.findByJgsyCode(et.getDepCode());
            if (Objects.nonNull(orgInfo)) {
                sysUser.setGeocode(orgInfo.getGeocode());
            }
            sysUser.setSsdwId(et.getDepCode());
        } else {
            sysUser.setGeocode(et.getDepCode());
            sysUser.setSsdwId(et.getSsdwid());
        }

        sysUser.setIsBuiltin(0);
        return sysUser;
    }

    private boolean handlerJgCommon(CenterPage centerPage, List<OrgInfo> orgInfoList) {
        log.info("开始获取机构数据：pageNum:{},pageSize:{}", centerPage.getPageNum(), centerPage.getPageSize());
        HttpResponse httpResponse = HttpUtil.createPost(smzHttpUrl + "/userCenter/syncJgsyCommon")
                .body(JackSonUtil.toJson(centerPage)).execute();
        int status = httpResponse.getStatus();
        if (status != httpSuccessCode) {
            return false;
        }
        String body = httpResponse.body();
        Result result = JackSonUtil.toObject(body, Result.class);
        if (result == null || result.getCode() != httpSuccessCode) {
            return false;
        }
        Object data = result.getData();
        Page page = JackSonUtil.toObject(JackSonUtil.toJson(data), Page.class);
        List<JgsyCommon> records = JackSonUtil.toObjectList(JackSonUtil.toJson(page.getRecords()), JgsyCommon.class);
        if (CollUtil.isNotEmpty(records)) {
            records.forEach(et -> {
                orgInfoList.add(this.changeToOrgInfo(et));
            });
        }
        boolean b = page.hasNext();
        if (b) {
            CenterPage centerPageNext = new CenterPage();
            centerPageNext.setPageNum(centerPage.getPageNum() + 1);
            centerPageNext.setPageSize(centerPage.getPageSize());
            return this.handlerJgCommon(centerPageNext, orgInfoList);
        }

        return true;
    }

    private OrgInfo changeToOrgInfo(JgsyCommon jgsyCommon) {
        OrgInfo orgInfo = new OrgInfo();
        String jgsyCode = jgsyCommon.getJgsyCode();
        orgInfo.setId(jgsyCode);
        String depCode = jgsyCommon.getDepCode();
        String levelCode = UserCenterServiceImpl.jgsyCode2LevelCode(jgsyCode, depCode, geoCodeMap);
        orgInfo.setLevelCode(levelCode);
        orgInfo.setName(jgsyCommon.getJgsyName());
        orgInfo.setShortName(jgsyCommon.getJgsyName());
        orgInfo.setParentCode(jgsyCommon.getJgsyParentCode());
        orgInfo.setParentName(null);
        orgInfo.setTypeCode(jgsyCommon.getJgsyType());
        orgInfo.setIsDelete(0);
        orgInfo.setCreateTime(jgsyCommon.getJgszsj());
        orgInfo.setUpdateTime(jgsyCommon.getLastUpdateTime());
        orgInfo.setDeleteTime(null);
        orgInfo.setSort(new Long(this.getOrderStr(jgsyCommon.getJgsyCodeOrder())));
        orgInfo.setJgsyCode(jgsyCode);
        orgInfo.setJgsyOtherName(jgsyCommon.getJgsyName());
        orgInfo.setGfjc(null);
        orgInfo.setXgjc(null);
        orgInfo.setJggg(null);
        orgInfo.setJgxz(null);
        orgInfo.setJgsySystemCode(jgsyCommon.getJgsySystemCode());
        orgInfo.setJglb(null);
        orgInfo.setJgbm(null);
        orgInfo.setDocNo(null);
        orgInfo.setPzslsj(null);
        orgInfo.setSfskfq(null);
        orgInfo.setJgsyBzsycj(null);
        orgInfo.setUnifyCode(jgsyCommon.getUnifyCode());
        orgInfo.setJgsyIfzfdw(null);
        orgInfo.setJgsyIfts(null);
        if (StrUtil.contains(jgsyCommon.getJgsyName(), "机构编制委员会办公室")) {
            orgInfo.setIsBbOrg(1);
        } else {
            orgInfo.setIsBbOrg(0);
        }
        orgInfo.setIsSjbbAudit(null);
        orgInfo.setOrgQh(null);
        orgInfo.setParentId(jgsyCommon.getJgsyParentCode());
        orgInfo.setOrgCode(null);
        orgInfo.setParentOrgCode(null);
        orgInfo.setGeocode(depCode);
        orgInfo.setBelong(null);
        orgInfo.setUnitType(jgsyCommon.getJgsyType());
        orgInfo.setCreditCode(jgsyCommon.getUnifyCode());
        orgInfo.setUnitSource(null);
        return orgInfo;
    }

}
