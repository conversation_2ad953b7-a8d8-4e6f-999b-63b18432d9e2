package com.zenith.bbykz.service;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.zenith.bbykz.api.SysUserOrgGroupDetailService;
import com.zenith.bbykz.dao.SysUserOrgGroupDetailMapper;
import com.zenith.bbykz.model.converter.SysUserOrgGroupDetailConverter;
import com.zenith.bbykz.model.entity.SysUserOrgGroupDetail;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <p>
 * 用户机构组详情 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @date 2024-03-13 10:46:03
 */
@Service
public class SysUserOrgGroupDetailServiceImpl extends ServiceImpl<SysUserOrgGroupDetailMapper, SysUserOrgGroupDetail> implements SysUserOrgGroupDetailService {

    @Autowired
    private SysUserOrgGroupDetailConverter sysUserOrgGroupDetailConverter;
    @Autowired
    private SysUserOrgGroupDetailMapper sysUserOrgGroupDetailMapper;

    @Override
    public boolean saveByGroupId(String groupId, List<SysUserOrgGroupDetail> saveList) {
        sysUserOrgGroupDetailMapper.deleteByGroupId(groupId);
        return this.saveBatch(saveList);
    }

    @Override
    public List<SysUserOrgGroupDetail> findByGroupId(String groupId) {
        LambdaQueryWrapper<SysUserOrgGroupDetail> queryWrapper = new LambdaQueryWrapper<>(SysUserOrgGroupDetail.class);
        queryWrapper.eq(SysUserOrgGroupDetail::getUserOrgGroupId, groupId);
        queryWrapper.orderByDesc(SysUserOrgGroupDetail::getId);
        return this.list(queryWrapper);
    }

    @Override
    public void deleteByGroupId(String groupId) {
        sysUserOrgGroupDetailMapper.deleteByGroupId(groupId);
    }
}
