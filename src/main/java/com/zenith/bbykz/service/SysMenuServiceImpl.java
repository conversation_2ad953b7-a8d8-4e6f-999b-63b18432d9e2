package com.zenith.bbykz.service;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.efficient.common.auth.RequestHolder;
import com.efficient.common.constant.CommonConstant;
import com.zenith.bbykz.api.SysMenuService;
import com.zenith.bbykz.api.SysRoleMenuService;
import com.zenith.bbykz.common.constant.BbCommonConstant;
import com.zenith.bbykz.model.base.BbUserAuthInfo;
import com.zenith.bbykz.model.base.UserInfo;
import com.zenith.bbykz.model.converter.SysMenuConverter;
import com.zenith.bbykz.dao.SysMenuMapper;
import com.zenith.bbykz.model.dto.SysMenuDTO;
import com.zenith.bbykz.model.dto.SysMenuListDTO;
import com.zenith.bbykz.model.entity.SysMenu;
import com.zenith.bbykz.model.vo.SysMenuVO;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <p>
 * 菜单表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @date 2023-09-14 17:14:26
 */
@Service
public class SysMenuServiceImpl extends ServiceImpl<SysMenuMapper, SysMenu> implements SysMenuService {

    @Autowired
    private SysMenuConverter sysMenuConverter;
    @Autowired
    private SysMenuMapper sysMenuMapper;
    @Autowired
    private SysRoleMenuService sysRoleMenuService;

    @Override
    public SysMenu save(SysMenuDTO dto) {
        SysMenu entity = sysMenuConverter.dto2Entity(dto);
        boolean flag = this.save(entity);
        return entity;
    }

    @Override
    public SysMenuVO findById(String id) {
        SysMenu entity = this.getById(id);
        // 获取父级名称
        LambdaQueryWrapper<SysMenu> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(SysMenu::getCode, entity.getParentCode());
        SysMenu one = this.getOne(wrapper);
        SysMenuVO sysMenuVO = sysMenuConverter.entity2Vo(entity);
        sysMenuVO.setParentName(ObjectUtil.isNull(one) ? "无" : one.getName());
        return sysMenuVO;
    }

    @Override
    public Boolean update(SysMenuDTO dto) {
        return this.updateById(sysMenuConverter.dto2Entity(dto));
    }

    @Override
    public Boolean delete(String id) {
        return this.removeById(id);
    }

    @Override
    public Page<SysMenu> list(SysMenuListDTO dto) {
        dto.setSystemId(RequestHolder.getCurrSystemId());
        UserInfo userTicket = (UserInfo) RequestHolder.getCurrUser();
//        BbUserAuthInfo userAuthInfo = userTicket.getSystemUserAuthMap().get(RequestHolder.getCurrSystemId());
//        List<String> menuCodeList = userAuthInfo.getMenuList();
        LambdaQueryWrapper<SysMenu> queryWrapper = new LambdaQueryWrapper<SysMenu>();
        queryWrapper.eq(SysMenu::getSystemCode, dto.getSystemId());
        queryWrapper.eq(SysMenu::getEnable, BbCommonConstant.TRUE_INT);
//        Integer isDefault = userAuthInfo.getUserSystem().getIsDefault();
//        queryWrapper.in(!Objects.equals(isDefault, CommonConstant.TRUE_INT),SysMenu::getCode, menuCodeList);
        queryWrapper.orderByAsc(SysMenu::getCode);

        final Page<SysMenu> page = sysMenuMapper.selectPage(new Page<>(dto.getPageNum(), dto.getPageSize()), queryWrapper);
        List<SysMenu> records = page.getRecords();
        if (CollUtil.isNotEmpty(records)) {
            List<String> parentCodes = records.stream().map(SysMenu::getParentCode).collect(Collectors.toList());
            List<SysMenu> menuList = this.getListByParentCodes(parentCodes);
            Map<String, String> menuNameMap = menuList.stream().collect(Collectors.toMap(SysMenu::getCode, SysMenu::getName, (k1, k2) -> k2));
            records.forEach(data -> {
                data.setParentName(menuNameMap.getOrDefault(data.getParentCode(), "无"));
            });
        }
        return page;
    }

    private List<SysMenu> getListByParentCodes(List<String> parentCodes) {
        LambdaQueryWrapper<SysMenu> wrapper = new LambdaQueryWrapper<>();
        wrapper.in(SysMenu::getParentCode, parentCodes);
        return this.list(wrapper);
    }

    @Override
    public List<SysMenu> getListBySystemId(String systemId) {
        LambdaQueryWrapper<SysMenu> queryWrapper = new LambdaQueryWrapper<SysMenu>();
        queryWrapper.eq(SysMenu::getSystemCode, systemId);
        queryWrapper.eq(SysMenu::getEnable, BbCommonConstant.TRUE_INT);
        queryWrapper.orderByAsc(SysMenu::getCode);
        return this.list(queryWrapper);
    }
}
