package com.zenith.bbykz.service;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.zenith.bbykz.api.SysSystemService;
import com.zenith.bbykz.dao.SysSystemMapper;
import com.zenith.bbykz.model.converter.SysSystemConverter;
import com.zenith.bbykz.model.entity.SysSystem;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * <p>
 * sys_system 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @date 2024-02-29 11:43:56
 */
@Service
public class SysSystemServiceImpl extends ServiceImpl<SysSystemMapper, SysSystem> implements SysSystemService {

    @Autowired
    private SysSystemConverter sysSystemConverter;
    @Autowired
    private SysSystemMapper sysSystemMapper;
}
