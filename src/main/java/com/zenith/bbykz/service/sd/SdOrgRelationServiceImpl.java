package com.zenith.bbykz.service.sd;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.efficient.common.constant.DbConstant;
import com.efficient.common.result.Result;
import com.zenith.bbykz.api.OrgInfoService;
import com.zenith.bbykz.api.sd.*;
import com.zenith.bbykz.dao.sd.*;
import com.zenith.bbykz.model.converter.sd.SdOrgRelationConverter;
import com.zenith.bbykz.model.dto.sd.SdOrgRelationListDTO;
import com.zenith.bbykz.model.entity.OrgInfo;
import com.zenith.bbykz.model.entity.sd.SdModuleDetail;
import com.zenith.bbykz.model.entity.sd.SdOrgRelation;
import com.zenith.bbykz.model.vo.sd.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <p>
 * 三定-三定机构关联关系 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @date 2024-04-11 16:17:20
 */
@Service
public class SdOrgRelationServiceImpl extends ServiceImpl<SdOrgRelationMapper, SdOrgRelation> implements SdOrgRelationService {

    @Autowired
    private SdOrgRelationConverter sdOrgRelationConverter;
    @Autowired
    private SdOrgRelationMapper sdOrgRelationMapper;
    @Autowired
    private SdModuleService sdModuleService;
    @Autowired
    private SdModuleMapper sdModuleMapper;
    @Autowired
    private SdModuleDetailService sdModuleDetailService;
    @Autowired
    private SdModuleDetailMapper sdModuleDetailMapper;
    @Autowired
    private SdDutyDetailService sdDutyDetailService;
    @Autowired
    private SdDutyDetailMapper sdDutyDetailMapper;
    @Autowired
    private SdCoreBusinessService sdCoreBusinessService;
    @Autowired
    private SdCoreBusinessMapper sdCoreBusinessMapper;
    @Autowired
    private SdBusinessItemService sdBusinessItemService;
    @Autowired
    private SdBusinessItemMapper sdBusinessItemMapper;
    @Autowired
    private SdBusinessItemOneService sdBusinessItemOneService;
    @Autowired
    private SdBusinessItemOneMapper sdBusinessItemOneMapper;
    @Autowired
    private OrgInfoService orgInfoService;

    @Override
    public void countAllAndSet(OrgInfo orgInfo, Integer typeCode) {
        String unitId = orgInfo.getId();
        SdOrgRelation orgRelation = this.getByUnitId(unitId);
        if (Objects.isNull(orgRelation)) {
            orgRelation = new SdOrgRelation();
            orgRelation.setUnitId(unitId);
            orgRelation.setUnitLevelCode(orgInfo.getLevelCode());
        }


        // if (Objects.equals(typeCode, 1)) {
        //
        // }

        // 三定明细
        if (Objects.equals(typeCode, 2)) {
            // 三定类型
            int moduleCount = sdModuleMapper.countByUnitId(unitId);
            orgRelation.setModuleCount(moduleCount);
            int count = sdModuleDetailMapper.countByUnitId(unitId);
            orgRelation.setModuleDetailCount(count);
        }
        // 细化职责
        if (Objects.equals(typeCode, 3)) {
            int count = sdDutyDetailMapper.countByUnitId(unitId);
            orgRelation.setDutyDetailCount(count);
        }
        // 核心业务
        if (Objects.equals(typeCode, 4)) {
            int count = sdCoreBusinessMapper.countByUnitId(unitId);
            orgRelation.setCoreBusinessCount(count);
        }
        // 业务事项
        if (Objects.equals(typeCode, 5)) {
            int count = sdBusinessItemMapper.countByUnitId(unitId);
            orgRelation.setBusinessItemCount(count);
        }
        // 一件事
        if (Objects.equals(typeCode, 6)) {
            int count = sdBusinessItemOneMapper.countByUnitId(unitId);
            orgRelation.setOneThingCount(count);
        }

        this.saveOrUpdate(orgRelation);
    }

    @Override
    public SdOrgRelation findByUnitId(String unitId) {
        LambdaQueryWrapper<SdOrgRelation> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.eq(SdOrgRelation::getUnitId, unitId);
        lambdaQueryWrapper.last(DbConstant.LIMIT_ONE);
        return this.getOne(lambdaQueryWrapper);
    }

    @Override
    public Page<SdOrgRelationVO> list(SdOrgRelationListDTO dto) {
        Page<SdOrgRelationVO> page = new Page<>(dto.getPageNum(), dto.getPageSize());
        return sdBusinessItemOneMapper.list(page, dto);
    }

    @Override
    public void countAllAndSetByModuleDetailId(String moduleId, Integer typeCode) {
        SdModuleDetail sdModuleDetail = sdModuleDetailMapper.selectById(moduleId);
        String unitId = sdModuleDetail.getUnitId();
        OrgInfo orgInfo = orgInfoService.getById(unitId);
        this.countAllAndSet(orgInfo, typeCode);
    }

    @Override
    public SdOrgRelation getByUnitId(String unitId) {
        LambdaQueryWrapper<SdOrgRelation> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.eq(SdOrgRelation::getUnitId, unitId);
        lambdaQueryWrapper.last(DbConstant.LIMIT_ONE);

        return this.getOne(lambdaQueryWrapper);
    }

    @Override
    public Result<SdAllVO> findAll(String unitId) {
        SdOrgRelation sdOrgRelation = this.getByUnitId(unitId);
        if (Objects.isNull(sdOrgRelation)) {
            return Result.ok();
        }
        SdAllVO allVO = new SdAllVO();
        allVO.setUnitId(sdOrgRelation.getUnitId());
        List<SdSqlVO> sqlVOList = sdOrgRelationMapper.findAll(unitId);
        Map<String, List<SdSqlVO>> moduleMap = sqlVOList.stream().filter(vo -> Objects.nonNull(vo.getModuleId())).collect(Collectors.groupingBy(SdSqlVO::getModuleId));
        List<SdModuleAllVO> moduleList = new ArrayList<>();
        // 三定类型
        moduleMap.forEach((k, v) -> {
            SdModuleAllVO moduleAllVO = new SdModuleAllVO();
            SdSqlVO sdSqlVO = v.get(0);
            moduleAllVO.setId(sdSqlVO.getModuleId());
            moduleAllVO.setTitle(sdSqlVO.getModuleTile());
            moduleAllVO.setSort(sdSqlVO.getModuleSort());
            Map<String, List<SdSqlVO>> moduleDetailMap = v.stream().filter(vo -> Objects.nonNull(vo.getModuleDetailId())).collect(Collectors.groupingBy(SdSqlVO::getModuleDetailId));
            // 三定明细
            List<SdModuleDetailAllVO> moduleDetailList = new ArrayList<>();
            moduleDetailMap.forEach((mdk, mdv) -> {
                SdModuleDetailAllVO moduleDetailAllVO = new SdModuleDetailAllVO();
                SdSqlVO mdVo = mdv.get(0);
                moduleDetailAllVO.setId(mdVo.getModuleDetailId());
                moduleDetailAllVO.setModuleId(mdVo.getModuleId());
                moduleDetailAllVO.setTitle(mdVo.getModuleDetailTile());
                moduleDetailAllVO.setSort(mdVo.getModuleDetailSort());
                moduleDetailAllVO.setRecordNum(mdVo.getModuleDetailRecordNum());
                // 细化职责
                List<SdDutyDetailAllVO> dutyDetailList = new ArrayList<>();
                Map<String, List<SdSqlVO>> dutyDetailMap = mdv.stream().filter(vo -> Objects.nonNull(vo.getDutyDetailId())).collect(Collectors.groupingBy(SdSqlVO::getDutyDetailId));
                dutyDetailMap.forEach((ddk, ddv) -> {
                    SdDutyDetailAllVO dutyDetailAllVO = new SdDutyDetailAllVO();
                    SdSqlVO ddVO = ddv.get(0);
                    dutyDetailAllVO.setId(ddVO.getDutyDetailId());
                    dutyDetailAllVO.setModuleId(ddVO.getModuleId());
                    dutyDetailAllVO.setModuleDetailId(ddVO.getModuleDetailId());
                    dutyDetailAllVO.setTitle(ddVO.getDutyDetailTile());
                    dutyDetailAllVO.setSort(ddVO.getDutyDetailSort());
                    dutyDetailAllVO.setTakeUnitName(ddVO.getDutyDetailTakeUnitName());
                    dutyDetailAllVO.setRecordNum(ddVO.getDutyDetailRecordNum());

                    // 核心业务
                    Map<String, List<SdSqlVO>> coreBusinessMap = ddv.stream().filter(vo -> Objects.nonNull(vo.getCoreBusinessId())).collect(Collectors.groupingBy(SdSqlVO::getCoreBusinessId));
                    List<SdCoreBusinessAllVO> coreBusinessAllList = new ArrayList<>();
                    coreBusinessMap.forEach((cbk, cbv) -> {
                        SdCoreBusinessAllVO sdCoreBusinessAllVO = new SdCoreBusinessAllVO();
                        SdSqlVO cbVO = cbv.get(0);
                        sdCoreBusinessAllVO.setId(cbVO.getCoreBusinessId());
                        sdCoreBusinessAllVO.setModuleId(cbVO.getModuleId());
                        sdCoreBusinessAllVO.setModuleDetailId(cbVO.getModuleDetailId());
                        sdCoreBusinessAllVO.setDutyDetailId(cbVO.getDutyDetailId());
                        sdCoreBusinessAllVO.setTitle(cbVO.getCoreBusinessTile());
                        sdCoreBusinessAllVO.setSort(cbVO.getCoreBusinessSort());

                        // 业务事项
                        Map<String, List<SdSqlVO>> businessItemMap = cbv.stream().filter(vo -> Objects.nonNull(vo.getBusinessItemId())).collect(Collectors.groupingBy(SdSqlVO::getBusinessItemId));
                        List<SdBusinessItemAllVO> businessItemAllList = new ArrayList<>();
                        businessItemMap.forEach((bik, biv) -> {
                            SdBusinessItemAllVO sdBusinessItemAllVO = new SdBusinessItemAllVO();
                            SdSqlVO biVo = biv.get(0);
                            sdBusinessItemAllVO.setId(biVo.getBusinessItemId());
                            sdBusinessItemAllVO.setModuleId(biVo.getModuleId());
                            sdBusinessItemAllVO.setModuleDetailId(biVo.getModuleDetailId());
                            sdBusinessItemAllVO.setDutyDetailId(biVo.getDutyDetailId());
                            sdBusinessItemAllVO.setCoreBusinessId(biVo.getCoreBusinessId());
                            sdBusinessItemAllVO.setTitle(biVo.getBusinessItemTile());
                            sdBusinessItemAllVO.setSort(biVo.getBusinessItemSort());
                            // 一件事
                            Map<String, List<SdSqlVO>> oneMap = biv.stream().filter(vo -> Objects.nonNull(vo.getOneId())).collect(Collectors.groupingBy(SdSqlVO::getOneId));
                            List<SdBusinessItemOneAllVO> businessItemOneAllList = new ArrayList<>();
                            oneMap.forEach((biok, biov) -> {
                                SdBusinessItemOneAllVO sdBusinessItemOneAllVO = new SdBusinessItemOneAllVO();
                                SdSqlVO bioVo = biov.get(0);
                                sdBusinessItemOneAllVO.setItemId(bik);
                                sdBusinessItemOneAllVO.setOneId(bioVo.getOneId());
                                sdBusinessItemOneAllVO.setOneName(bioVo.getOneName());

                                businessItemOneAllList.add(sdBusinessItemOneAllVO);
                            });
                            sdBusinessItemAllVO.setChildren(businessItemOneAllList);
                            businessItemAllList.add(sdBusinessItemAllVO);
                        });
                        List<SdBusinessItemAllVO> sortBusinessItemAllList = businessItemAllList.stream().sorted(Comparator.comparing(SdBusinessItemAllVO::getSort, Comparator.nullsLast(Comparator.naturalOrder())).thenComparing(SdBusinessItemAllVO::getId)).collect(Collectors.toList());
                        sdCoreBusinessAllVO.setChildren(sortBusinessItemAllList);

                        coreBusinessAllList.add(sdCoreBusinessAllVO);
                    });
                    List<SdCoreBusinessAllVO> sortCoreBusinessAllList = coreBusinessAllList.stream().sorted(Comparator.comparing(SdCoreBusinessAllVO::getSort, Comparator.nullsLast(Comparator.naturalOrder())).thenComparing(SdCoreBusinessAllVO::getId)).collect(Collectors.toList());
                    dutyDetailAllVO.setChildren(sortCoreBusinessAllList);
                    dutyDetailList.add(dutyDetailAllVO);
                });
                List<SdDutyDetailAllVO> sortDutyDetailList = dutyDetailList.stream().sorted(Comparator.comparing(SdDutyDetailAllVO::getSort, Comparator.nullsLast(Comparator.naturalOrder())).thenComparing(SdDutyDetailAllVO::getId)).collect(Collectors.toList());
                moduleDetailAllVO.setChildren(sortDutyDetailList);
                moduleDetailList.add(moduleDetailAllVO);
            });
            List<SdModuleDetailAllVO> sortModuleDetailList = moduleDetailList.stream().sorted(Comparator.comparing(SdModuleDetailAllVO::getSort, Comparator.nullsLast(Comparator.naturalOrder())).thenComparing(SdModuleDetailAllVO::getId)).collect(Collectors.toList());
            moduleAllVO.setChildren(sortModuleDetailList);
            moduleList.add(moduleAllVO);
        });
        List<SdModuleAllVO> sortModuleList = moduleList.stream().sorted(Comparator.comparing(SdModuleAllVO::getSort, Comparator.nullsLast(Comparator.naturalOrder())).thenComparing(SdModuleAllVO::getId)).collect(Collectors.toList());
        allVO.setChildren(sortModuleList);
        return Result.ok(allVO);
    }

    @Override
    public Page<SdSearchVO> dutyList(SdOrgRelationListDTO dto) {
        Page<SdSearchVO> page = new Page<>(dto.getPageNum(), dto.getPageSize());
        Page<SdSearchVO> pageList = sdOrgRelationMapper.dutyList(page, dto);
        Map<String, String> mapByType = sdModuleService.findMap();
        List<SdSearchVO> records = pageList.getRecords();
        records.forEach(et -> {
            et.setModuleName(mapByType.get(et.getModuleId()));
        });
        return pageList;
    }
}
