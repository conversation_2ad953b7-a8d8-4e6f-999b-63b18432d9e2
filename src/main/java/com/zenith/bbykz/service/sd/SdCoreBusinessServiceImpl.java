package com.zenith.bbykz.service.sd;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.efficient.common.result.Result;
import com.zenith.bbykz.api.sd.SdCoreBusinessService;
import com.zenith.bbykz.api.sd.SdOrgRelationService;
import com.zenith.bbykz.dao.sd.SdBusinessItemMapper;
import com.zenith.bbykz.dao.sd.SdCoreBusinessMapper;
import com.zenith.bbykz.model.converter.sd.SdCoreBusinessConverter;
import com.zenith.bbykz.model.dto.sd.SdCoreBusinessDTO;
import com.zenith.bbykz.model.dto.sd.SdCoreBusinessListDTO;
import com.zenith.bbykz.model.dto.sd.SdSaveCoreBusinessDTO;
import com.zenith.bbykz.model.entity.sd.SdCoreBusiness;
import com.zenith.bbykz.model.vo.sd.SdCoreBusinessVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;

/**
 * <p>
 * 三定-核心业务 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @date 2024-04-11 16:17:20
 */
@Service
public class SdCoreBusinessServiceImpl extends ServiceImpl<SdCoreBusinessMapper, SdCoreBusiness> implements SdCoreBusinessService {

    @Autowired
    private SdCoreBusinessConverter sdCoreBusinessConverter;
    @Autowired
    private SdCoreBusinessMapper sdCoreBusinessMapper;
    @Autowired
    private SdOrgRelationService sdOrgRelationService;
    @Autowired
    private SdBusinessItemMapper sdBusinessItemMapper;

    @Override
    public Result<SdCoreBusiness> save(SdSaveCoreBusinessDTO dto) {
        List<SdCoreBusinessDTO> dtoList = dto.getDtoList();
        List<SdCoreBusiness> entityList = new ArrayList<>();
        String moduleDetailId = dtoList.get(0).getModuleDetailId();
        for (SdCoreBusinessDTO sdCoreBusinessDTO : dtoList) {
            SdCoreBusiness entity = sdCoreBusinessConverter.dto2Entity(sdCoreBusinessDTO);
            entityList.add(entity);
        }
        this.saveOrUpdateBatch(entityList);
        sdOrgRelationService.countAllAndSetByModuleDetailId(moduleDetailId, 4);
        return Result.ok();
    }

    @Override
    public Result<SdCoreBusinessVO> findById(String id) {
        SdCoreBusiness entity = this.getById(id);
        SdCoreBusinessVO vo = sdCoreBusinessConverter.entity2Vo(entity);
        return Result.ok(vo);
    }

    @Override
    public Result<Boolean> update(SdCoreBusinessDTO dto) {
        boolean flag = this.updateById(sdCoreBusinessConverter.dto2Entity(dto));
        return flag ? Result.ok() : Result.fail();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Result<Boolean> delete(String id) {
        SdCoreBusiness sdCoreBusiness = this.getById(id);
        boolean flag = this.removeById(id);
        sdOrgRelationService.countAllAndSetByModuleDetailId(sdCoreBusiness.getModuleDetailId(), 4);
        return flag ? Result.ok() : Result.fail();
    }

    @Override
    public Page<SdCoreBusiness> list(SdCoreBusinessListDTO dto) {
        LambdaQueryWrapper<SdCoreBusiness> queryWrapper = new LambdaQueryWrapper<>(SdCoreBusiness.class);
        queryWrapper.eq(SdCoreBusiness::getDutyDetailId, dto.getDutyDetailId());
        queryWrapper.orderByAsc(SdCoreBusiness::getSort).orderByDesc(SdCoreBusiness::getCreateTime);
        final Page<SdCoreBusiness> page = sdCoreBusinessMapper.selectPage(new Page<>(dto.getPageNum(), dto.getPageSize()), queryWrapper);
        return page;
    }

    @Override
    public void countBusinessItem(String coreBusinessId) {
        SdCoreBusiness sdCoreBusiness = this.getById(coreBusinessId);
        int count = sdBusinessItemMapper.countByCoreBusinessId(coreBusinessId);
        sdCoreBusiness.setBusinessItemCount(count);
        this.updateById(sdCoreBusiness);
    }

    @Override
    public Page<SdCoreBusinessVO> coreList(SdCoreBusinessListDTO dto) {
        Page<SdCoreBusinessVO> page = new Page<>(dto.getPageNum(), dto.getPageSize());
        Page<SdCoreBusinessVO> businessVOPage = sdBusinessItemMapper.coreList(page, dto);
        return businessVOPage;
    }
}
