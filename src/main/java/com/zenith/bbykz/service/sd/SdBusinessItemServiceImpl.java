package com.zenith.bbykz.service.sd;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.efficient.common.result.Result;
import com.zenith.bbykz.api.sd.*;
import com.zenith.bbykz.dao.sd.SdBusinessItemMapper;
import com.zenith.bbykz.model.converter.sd.SdBusinessItemConverter;
import com.zenith.bbykz.model.dto.sd.SdBusinessItemDTO;
import com.zenith.bbykz.model.dto.sd.SdBusinessItemListDTO;
import com.zenith.bbykz.model.dto.sd.SdOneThingDTO;
import com.zenith.bbykz.model.dto.sd.SdSaveBusinessItemDTO;
import com.zenith.bbykz.model.entity.sd.SdBusinessItem;
import com.zenith.bbykz.model.entity.sd.SdBusinessItemOne;
import com.zenith.bbykz.model.vo.sd.SdBusinessItemVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <p>
 * 三定-业务事项 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @date 2024-04-11 16:17:20
 */
@Service
public class SdBusinessItemServiceImpl extends ServiceImpl<SdBusinessItemMapper, SdBusinessItem> implements SdBusinessItemService {

    @Autowired
    private SdBusinessItemConverter sdBusinessItemConverter;
    @Autowired
    private SdBusinessItemMapper sdBusinessItemMapper;
    @Autowired
    private SdBusinessItemOneService sdBusinessItemOneService;
    @Autowired
    private SdOrgRelationService sdOrgRelationService;
    @Autowired
    private SdCoreBusinessService sdCoreBusinessService;
    @Autowired
    private SdOneThingService sdOneThingService;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Result<SdBusinessItem> save(SdSaveBusinessItemDTO dto) {
        List<SdBusinessItemDTO> dtoList = dto.getDtoList();
        String moduleDetailId = dtoList.get(0).getModuleDetailId();
        String coreBusinessId = dtoList.get(0).getCoreBusinessId();
        for (SdBusinessItemDTO sdBusinessItemDTO : dtoList) {
            SdBusinessItem entity = sdBusinessItemConverter.dto2Entity(sdBusinessItemDTO);
            boolean flag = this.save(entity);
            List<SdOneThingDTO> oneThingList = sdBusinessItemDTO.getOneThingList();
            sdBusinessItemOneService.saveByItemId(entity.getId(), oneThingList);
        }
        sdOrgRelationService.countAllAndSetByModuleDetailId(moduleDetailId, 5);
        sdOrgRelationService.countAllAndSetByModuleDetailId(moduleDetailId, 6);
        sdCoreBusinessService.countBusinessItem(coreBusinessId);
        return Result.ok();
    }

    @Override
    public Result<SdBusinessItemVO> findById(String id) {
        SdBusinessItem entity = this.getById(id);
        SdBusinessItemVO vo = sdBusinessItemConverter.entity2Vo(entity);
        List<SdBusinessItemOne> oneList= sdBusinessItemOneService.findByItemId(id);
        if(Objects.nonNull(oneList)){
            vo.setOneThingList(sdOneThingService.listByIds(oneList.stream().map(et->et.getOneId()).collect(Collectors.toList())));
        }

        return Result.ok(vo);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Result<Boolean> update(SdBusinessItemDTO dto) {
        String moduleDetailId = dto.getModuleDetailId();
        String coreBusinessId = dto.getCoreBusinessId();
        SdBusinessItem entity = sdBusinessItemConverter.dto2Entity(dto);
        boolean flag = this.updateById(entity);
        List<SdOneThingDTO> oneThingList = dto.getOneThingList();
        sdBusinessItemOneService.saveByItemId(entity.getId(), oneThingList);

        sdOrgRelationService.countAllAndSetByModuleDetailId(moduleDetailId, 5);
        sdOrgRelationService.countAllAndSetByModuleDetailId(moduleDetailId, 6);
        sdCoreBusinessService.countBusinessItem(coreBusinessId);
        return flag ? Result.ok() : Result.fail();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Result<Boolean> delete(String id) {
        SdBusinessItem sdBusinessItem = this.getById(id);
        boolean flag = this.removeById(id);
        sdBusinessItemOneService.deleteByItemId(id);
        sdOrgRelationService.countAllAndSetByModuleDetailId(sdBusinessItem.getModuleDetailId(), 5);
        return flag ? Result.ok() : Result.fail();
    }

    @Override
    public Page<SdBusinessItem> list(SdBusinessItemListDTO dto) {
        LambdaQueryWrapper<SdBusinessItem> queryWrapper = new LambdaQueryWrapper<>(SdBusinessItem.class);
        queryWrapper.eq(SdBusinessItem::getCoreBusinessId, dto.getCoreBusinessId());
        queryWrapper.orderByAsc(SdBusinessItem::getSort).orderByDesc(SdBusinessItem::getCreateTime);
        final Page<SdBusinessItem> page = sdBusinessItemMapper.selectPage(new Page<>(dto.getPageNum(), dto.getPageSize()), queryWrapper);
        return page;
    }
}
