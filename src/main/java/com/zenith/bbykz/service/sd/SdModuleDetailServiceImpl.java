package com.zenith.bbykz.service.sd;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.efficient.common.result.Result;
import com.efficient.common.result.ResultEnum;
import com.efficient.configs.util.PageUtil;
import com.efficient.system.api.DictCodeService;
import com.zenith.bbykz.api.OrgInfoService;
import com.zenith.bbykz.api.sd.SdModuleDetailService;
import com.zenith.bbykz.api.sd.SdModuleService;
import com.zenith.bbykz.api.sd.SdOrgRelationService;
import com.zenith.bbykz.dao.sd.SdModuleDetailMapper;
import com.zenith.bbykz.model.converter.sd.SdModuleDetailConverter;
import com.zenith.bbykz.model.dto.sd.SdModuleDetailDTO;
import com.zenith.bbykz.model.dto.sd.SdModuleDetailListDTO;
import com.zenith.bbykz.model.dto.sd.SdSaveModuleDetailDTO;
import com.zenith.bbykz.model.entity.OrgInfo;
import com.zenith.bbykz.model.entity.sd.SdModuleDetail;
import com.zenith.bbykz.model.vo.sd.SdModuleDetailVO;
import com.zenith.bbykz.model.vo.sd.SdSearchVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * <p>
 * 三定-三定明细 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @date 2024-04-11 16:17:20
 */
@Service
public class SdModuleDetailServiceImpl extends ServiceImpl<SdModuleDetailMapper, SdModuleDetail> implements SdModuleDetailService {

    @Autowired
    private SdModuleDetailConverter sdModuleDetailConverter;
    @Autowired
    private SdModuleDetailMapper sdModuleDetailMapper;
    @Autowired
    private OrgInfoService orgInfoService;
    @Autowired
    private SdOrgRelationService sdOrgRelationService;
    @Autowired
    private DictCodeService dictCodeService;
    @Autowired
    private SdModuleService sdModuleService;

    @Override
    public Result<SdModuleDetail> save(SdSaveModuleDetailDTO dto) {
        List<SdModuleDetail> detailList = new ArrayList<>();
        List<SdModuleDetailDTO> dtoList = dto.getDtoList();
        SdModuleDetailDTO sdModuleDetailDTO = dtoList.get(0);
        String unitId = sdModuleDetailDTO.getUnitId();
        OrgInfo orgInfo = orgInfoService.getById(unitId);
        if (Objects.isNull(orgInfo)) {
            return Result.build(ResultEnum.PARA_ERROR);
        }
        for (SdModuleDetailDTO detailDTO : dtoList) {
            SdModuleDetail entity = sdModuleDetailConverter.dto2Entity(detailDTO);
            entity.setUnitName(orgInfo.getName());
            entity.setUnitLevelCode(orgInfo.getLevelCode());
            entity.setGeocode(orgInfo.getGeocode());
            detailList.add(entity);
        }
        this.saveOrUpdateBatch(detailList);
        sdOrgRelationService.countAllAndSet(orgInfo, 2);
        return Result.ok();
    }

    @Override
    public Result<SdModuleDetailVO> findById(String id) {
        SdModuleDetail entity = this.getById(id);
        SdModuleDetailVO vo = sdModuleDetailConverter.entity2Vo(entity);
        // Map<String, String> mapByType = dictCodeService.findMapByType(DictConstant.SD_MODULE_TYPE);
        Map<String, String> mapByType = sdModuleService.findMap();
        vo.setModuleName(mapByType.get(vo.getModuleId()));
        return Result.ok(vo);
    }

    @Override
    public Result<Boolean> update(SdModuleDetailDTO dto) {
        boolean flag = this.updateById(sdModuleDetailConverter.dto2Entity(dto));
        return flag ? Result.ok() : Result.fail();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Result<Boolean> delete(String id) {
        SdModuleDetail sdModuleDetail = this.getById(id);
        boolean flag = this.removeById(id);
        OrgInfo orgInfo = orgInfoService.getById(sdModuleDetail.getUnitId());
        sdOrgRelationService.countAllAndSet(orgInfo, 2);
        return flag ? Result.ok() : Result.fail();
    }

    @Override
    public Page<SdModuleDetailVO> list(SdModuleDetailListDTO dto) {
        LambdaQueryWrapper<SdModuleDetail> queryWrapper = new LambdaQueryWrapper<>(SdModuleDetail.class);
        queryWrapper.eq(SdModuleDetail::getUnitId, dto.getUnitId());
        String moduleId = dto.getModuleId();
        if (StrUtil.isNotBlank(moduleId)) {
            queryWrapper.eq(SdModuleDetail::getModuleId, moduleId);
        }
        queryWrapper.orderByAsc(SdModuleDetail::getSort).orderByAsc(SdModuleDetail::getCreateTime);
        final Page<SdModuleDetail> page = sdModuleDetailMapper.selectPage(new Page<>(dto.getPageNum(), dto.getPageSize()), queryWrapper);
        List<SdModuleDetailVO> voList = new ArrayList<>();
        List<SdModuleDetail> records = page.getRecords();
        if (CollUtil.isEmpty(records)) {
            return PageUtil.change(page, voList);
        }
        Map<String, String> mapByType = sdModuleService.findMap();

        records.forEach(et -> {
            SdModuleDetailVO vo = sdModuleDetailConverter.entity2Vo(et);
            vo.setModuleName(mapByType.get(vo.getModuleId()));
            voList.add(vo);
        });
        return PageUtil.change(page, voList);
    }

    @Override
    public Page<SdSearchVO> searchList(SdModuleDetailListDTO dto) {
        Page<SdSearchVO> page = new Page<>(dto.getPageNum(), dto.getPageSize());
        Page<SdSearchVO> voPage = sdModuleDetailMapper.searchList(page, dto);
        Map<String, String> mapByType = sdModuleService.findMap();
        List<SdSearchVO> records = voPage.getRecords();
        records.forEach(et -> {
            et.setModuleName(mapByType.get(et.getModuleId()));
        });
        return voPage;
    }
}
