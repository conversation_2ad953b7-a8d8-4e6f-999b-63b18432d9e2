package com.zenith.bbykz.service.sd;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.efficient.common.result.Result;
import com.efficient.common.util.CountUtil;
import com.zenith.bbykz.api.OrgInfoService;
import com.zenith.bbykz.api.sd.SdModuleService;
import com.zenith.bbykz.api.sd.SdOrgRelationService;
import com.zenith.bbykz.dao.sd.SdModuleMapper;
import com.zenith.bbykz.model.converter.sd.SdModuleConverter;
import com.zenith.bbykz.model.dto.sd.SdModuleCountDTO;
import com.zenith.bbykz.model.dto.sd.SdModuleDTO;
import com.zenith.bbykz.model.dto.sd.SdModuleListDTO;
import com.zenith.bbykz.model.entity.sd.SdModule;
import com.zenith.bbykz.model.vo.sd.SdModuleCountVO;
import com.zenith.bbykz.model.vo.sd.SdModuleTypeCountVO;
import com.zenith.bbykz.model.vo.sd.SdModuleVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <p>
 * 三定-三定类型 服务实现类
 * </p>
 * <p>
 * -- sd_org_relation  三定机构关系
 * -- sd_module   三定类型
 * -- sd_module_detail  三定明细
 * -- sd_duty_detail  细化职责     处室
 * -- sd_core_business  核心业务
 * -- sd_business_item  业务事项
 * -- sd_business_item_one  业务事项一件事关联表
 * -- sd_one_thing  一件事
 *
 * <AUTHOR>
 * @date 2024-04-11 16:17:20
 */
@Service
public class SdModuleServiceImpl extends ServiceImpl<SdModuleMapper, SdModule> implements SdModuleService {

    @Autowired
    private SdModuleConverter sdModuleConverter;
    @Autowired
    private SdModuleMapper sdModuleMapper;
    @Autowired
    private OrgInfoService orgInfoService;
    @Autowired
    private SdOrgRelationService sdOrgRelationService;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Result<SdModule> save(SdModuleDTO dto) {
        SdModule entity = sdModuleConverter.dto2Entity(dto);
        // OrgInfo orgInfo = orgInfoService.getById(dto.getUnitId());
        // if (Objects.isNull(orgInfo)) {
        //     return Result.build(ResultEnum.PARA_ERROR);
        // }
        // entity.setUnitName(orgInfo.getName());
        // entity.setUnitLevelCode(orgInfo.getLevelCode());
        // entity.setGeocode(orgInfo.getGeocode());
        boolean flag = this.save(entity);
        // sdOrgRelationService.countAllAndSet(orgInfo, 1);
        return Result.ok(entity);
    }

    @Override
    public Result<SdModuleVO> findById(String id) {
        SdModule entity = this.getById(id);
        SdModuleVO vo = sdModuleConverter.entity2Vo(entity);
        return Result.ok(vo);
    }

    @Override
    public Result<Boolean> update(SdModuleDTO dto) {
        boolean flag = this.updateById(sdModuleConverter.dto2Entity(dto));
        return flag ? Result.ok() : Result.fail();
    }

    @Override
    public Result<Boolean> delete(String id) {
        // SdModule sdModule = this.getById(id);
        // SdOrgRelation sdOrgRelation = sdOrgRelationService.findByUnitId(sdModule.getUnitId());
        // if (Objects.nonNull(sdOrgRelation) && sdOrgRelation.getModuleDetailCount() > 0) {
        //     return Result.build(BbResultEnum.SD_LOWER_DATA);
        // }
        boolean flag = this.removeById(id);
        return flag ? Result.ok() : Result.fail();
    }

    @Override
    public Page<SdModule> list(SdModuleListDTO dto) {
        LambdaQueryWrapper<SdModule> queryWrapper = new LambdaQueryWrapper<>(SdModule.class);
        Integer isEnable = dto.getIsEnable();
        if (Objects.nonNull(isEnable)) {
            queryWrapper.eq(SdModule::getIsEnable, isEnable);
        }

        queryWrapper.orderByAsc(SdModule::getSort).orderByDesc(SdModule::getCreateTime);
        final Page<SdModule> page = sdModuleMapper.selectPage(new Page<>(dto.getPageNum(), dto.getPageSize()), queryWrapper);
        return page;
    }

    @Override
    public Map<String, String> findMap() {
        LambdaQueryWrapper<SdModule> queryWrapper = new LambdaQueryWrapper<>();

        List<SdModule> list = this.list(queryWrapper);
        return list.stream().collect(Collectors.toMap(SdModule::getId, SdModule::getTitle, (k1, k2) -> k1));

    }

    @Override
    public SdModuleCountVO analysis(SdModuleCountDTO dto) {
        SdModuleCountVO vo = new SdModuleCountVO();
        Integer moduleDetailCount = sdModuleMapper.analysisModuleCount(dto);
        vo.setModuleDetailCount(moduleDetailCount);
        Integer dutyDetailCount = sdModuleMapper.analysisDutyDetailCount(dto);
        vo.setDutyDetailCount(dutyDetailCount);

        Integer involveUnitCount = sdModuleMapper.analysisInvolveUnitCount(dto);
        vo.setInvolveUnitCount(involveUnitCount);

        List<SdModuleTypeCountVO> typeCountList = sdModuleMapper.analysisTypeCountList(dto);
        int total = typeCountList.stream().mapToInt(SdModuleTypeCountVO::getCount).sum();
        typeCountList.forEach(et -> {
            double percentage = CountUtil.calculatePercentage(et.getCount(), total);
            et.setPercent(percentage);
        });
        vo.setTypeCountList(typeCountList);
        return vo;
    }

}
