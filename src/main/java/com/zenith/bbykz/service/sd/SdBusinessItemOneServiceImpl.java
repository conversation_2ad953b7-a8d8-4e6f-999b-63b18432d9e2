package com.zenith.bbykz.service.sd;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.efficient.common.constant.CommonConstant;
import com.efficient.common.constant.DbConstant;
import com.efficient.common.result.Result;
import com.zenith.bbykz.api.sd.SdBusinessItemOneService;
import com.zenith.bbykz.api.sd.SdBusinessItemService;
import com.zenith.bbykz.dao.sd.SdBusinessItemMapper;
import com.zenith.bbykz.dao.sd.SdBusinessItemOneMapper;
import com.zenith.bbykz.model.converter.sd.SdBusinessItemOneConverter;
import com.zenith.bbykz.model.dto.sd.SdBusinessItemOneDTO;
import com.zenith.bbykz.model.dto.sd.SdOneThingDTO;
import com.zenith.bbykz.model.entity.sd.SdBusinessItem;
import com.zenith.bbykz.model.entity.sd.SdBusinessItemOne;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

/**
 * <p>
 * 三定-业务事项一件事关联表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @date 2024-04-11 16:17:20
 */
@Service
public class SdBusinessItemOneServiceImpl extends ServiceImpl<SdBusinessItemOneMapper, SdBusinessItemOne> implements SdBusinessItemOneService {

    @Autowired
    private SdBusinessItemOneConverter sdBusinessItemOneConverter;
    @Autowired
    private SdBusinessItemOneMapper sdBusinessItemOneMapper;
    @Autowired
    private SdBusinessItemMapper sdBusinessItemMapper;
    @Autowired
    private SdBusinessItemService sdBusinessItemService;

    @Override
    public List<SdBusinessItemOne> findByOneId(String oneId) {
        LambdaQueryWrapper<SdBusinessItemOne> queryWrapper = new LambdaQueryWrapper<>(SdBusinessItemOne.class);
        queryWrapper.eq(SdBusinessItemOne::getOneId, oneId);
        return this.list(queryWrapper);
    }

    @Override
    public void saveByItemId(String itemId, List<SdOneThingDTO> oneThingList) {
        sdBusinessItemOneMapper.deleteByItemId(itemId);
        if (CollUtil.isEmpty(oneThingList)) {
            return;
        }
        List<SdBusinessItemOne> oneList = new ArrayList<>();
        oneThingList.forEach(et -> {
            SdBusinessItemOne one = new SdBusinessItemOne();
            one.setItemId(itemId);
            one.setOneId(et.getId());
            oneList.add(one);
        });
        this.saveBatch(oneList);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Result<Boolean> updateItemOne(SdBusinessItemOneDTO dto) {
        String itemId = dto.getItemId();
        String oneId = dto.getOneId();
        SdBusinessItemOne itemOne = this.findByItemIdAndOneId(itemId, oneId);
        if (itemOne == null) {
            // 新增
            itemOne = new SdBusinessItemOne();
            itemOne.setItemId(itemId);
            itemOne.setOneId(oneId);
            itemOne.setIsDelete(CommonConstant.FALSE_INT);
        }
        // 修改业务事项说明
        SdBusinessItem sdBusinessItem = sdBusinessItemService.getById(dto.getItemId());
        sdBusinessItem.setRemark(dto.getItemRemark());
        sdBusinessItem.setTitle(dto.getItemName());
        sdBusinessItemService.saveOrUpdate(sdBusinessItem);
        // 移除之前关系
        sdBusinessItemOneMapper.deleteByItemId(dto.getItemId());
        this.saveOrUpdate(itemOne);
        return Result.ok();
    }

    @Override
    public SdBusinessItemOne findByItemIdAndOneId(String itemId, String oneId) {
        LambdaQueryWrapper<SdBusinessItemOne> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(SdBusinessItemOne::getItemId, itemId);
        queryWrapper.eq(SdBusinessItemOne::getOneId, oneId);
        queryWrapper.last(DbConstant.LIMIT_ONE);
        return this.getOne(queryWrapper);
    }

    @Override
    public void deleteByItemId(String itemId) {
        sdBusinessItemOneMapper.deleteByItemId(itemId);
    }

    @Override
    public List<SdBusinessItemOne> findByItemId(String itemId) {
        LambdaQueryWrapper<SdBusinessItemOne> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(SdBusinessItemOne::getItemId, itemId);
        queryWrapper.orderByAsc(SdBusinessItemOne::getCreateTime);
        return this.list(queryWrapper);
    }
}
