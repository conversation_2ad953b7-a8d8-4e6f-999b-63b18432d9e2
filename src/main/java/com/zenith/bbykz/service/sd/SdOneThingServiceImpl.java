package com.zenith.bbykz.service.sd;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.efficient.common.result.Result;
import com.efficient.configs.util.PageUtil;
import com.efficient.system.api.DictCodeService;
import com.zenith.bbykz.api.sd.SdBusinessItemOneService;
import com.zenith.bbykz.api.sd.SdOneThingService;
import com.zenith.bbykz.common.constant.DictConstant;
import com.zenith.bbykz.common.result.BbResultEnum;
import com.zenith.bbykz.dao.sd.SdOneThingMapper;
import com.zenith.bbykz.model.converter.sd.SdOneThingConverter;
import com.zenith.bbykz.model.dto.sd.SdOneThingDTO;
import com.zenith.bbykz.model.dto.sd.SdOneThingListDTO;
import com.zenith.bbykz.model.entity.sd.SdBusinessItemOne;
import com.zenith.bbykz.model.entity.sd.SdOneThing;
import com.zenith.bbykz.model.vo.sd.SdBusinessItemOneVO;
import com.zenith.bbykz.model.vo.sd.SdOneThingVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * <p>
 * 三定-一件事 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @date 2024-04-11 16:17:20
 */
@Service
public class SdOneThingServiceImpl extends ServiceImpl<SdOneThingMapper, SdOneThing> implements SdOneThingService {

    @Autowired
    private SdOneThingConverter sdOneThingConverter;
    @Autowired
    private SdOneThingMapper sdOneThingMapper;
    @Autowired
    private DictCodeService dictCodeService;
    @Autowired
    private SdBusinessItemOneService sdBusinessItemOneService;

    @Override
    public Result<SdOneThing> save(SdOneThingDTO dto) {
        SdOneThing entity = sdOneThingConverter.dto2Entity(dto);
        boolean flag = this.save(entity);
        return Result.ok(entity);
    }

    @Override
    public Result<SdOneThingVO> findById(String id) {
        SdOneThing entity = this.getById(id);
        SdOneThingVO vo = sdOneThingConverter.entity2Vo(entity);
        Map<String, String> mapByType = dictCodeService.findMapByType(DictConstant.SD_ONE_TYPE);
        vo.setOneTypeName(mapByType.get(vo.getOneType()));
        return Result.ok(vo);

    }

    @Override
    public Result<Boolean> update(SdOneThingDTO dto) {
        boolean flag = this.updateById(sdOneThingConverter.dto2Entity(dto));
        return flag ? Result.ok() : Result.fail();
    }

    @Override
    public Result<Boolean> delete(String id) {
        List<SdBusinessItemOne> oneList = sdBusinessItemOneService.findByOneId(id);
        if (CollUtil.isNotEmpty(oneList)) {
            return Result.build(BbResultEnum.SD_USE_DATA);
        }
        boolean flag = this.removeById(id);
        return flag ? Result.ok() : Result.fail();
    }

    @Override
    public Page<SdOneThingVO> list(SdOneThingListDTO dto) {
        LambdaQueryWrapper<SdOneThing> queryWrapper = new LambdaQueryWrapper<>(SdOneThing.class);

        String oneType = dto.getOneType();
        if (StrUtil.isNotBlank(oneType)) {
            queryWrapper.eq(SdOneThing::getOneType, oneType);
        }

        String oneName = dto.getOneName();
        if (StrUtil.isNotBlank(oneName)) {
            queryWrapper.like(SdOneThing::getTitle, oneName);
        }
        queryWrapper.orderByAsc(SdOneThing::getSort).orderByDesc(SdOneThing::getCreateTime);
        final Page<SdOneThing> page = sdOneThingMapper.selectPage(new Page<>(dto.getPageNum(), dto.getPageSize()), queryWrapper);
        List<SdOneThingVO> voList = new ArrayList<>();
        List<SdOneThing> records = page.getRecords();
        if (CollUtil.isEmpty(records)) {
            return PageUtil.change(page, voList);
        }
        Map<String, String> mapByType = dictCodeService.findMapByType(DictConstant.SD_ONE_TYPE);

        records.forEach(et -> {
            SdOneThingVO vo = sdOneThingConverter.entity2Vo(et);
            vo.setOneTypeName(mapByType.get(vo.getOneType()));
            voList.add(vo);
        });
        return PageUtil.change(page, voList);
    }

    @Override
    public Page<SdBusinessItemOneVO> oneItemList(SdOneThingListDTO dto) {
        Page<SdBusinessItemOneVO> page = new Page<>(dto.getPageNum(), dto.getPageSize());
        Page<SdBusinessItemOneVO> itemOneVOPage = sdOneThingMapper.oneItemList(page, dto);
        List<SdBusinessItemOneVO> records = itemOneVOPage.getRecords();
        if (CollUtil.isEmpty(records)) {
            return itemOneVOPage;
        }
        Map<String, String> mapByType = dictCodeService.findMapByType(DictConstant.SD_ONE_TYPE);
        records.forEach(et -> {
            et.setOneTypeName(mapByType.get(et.getOneType()));
        });
        return itemOneVOPage;
    }
}
