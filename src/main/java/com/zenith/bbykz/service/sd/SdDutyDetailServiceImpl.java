package com.zenith.bbykz.service.sd;

import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.efficient.common.result.Result;
import com.zenith.bbykz.api.OrgInfoService;
import com.zenith.bbykz.api.sd.SdDutyDetailService;
import com.zenith.bbykz.api.sd.SdOrgRelationService;
import com.zenith.bbykz.dao.sd.SdDutyDetailMapper;
import com.zenith.bbykz.model.converter.sd.SdDutyDetailConverter;
import com.zenith.bbykz.model.dto.sd.SdDutyDetailDTO;
import com.zenith.bbykz.model.dto.sd.SdDutyDetailListDTO;
import com.zenith.bbykz.model.dto.sd.SdSaveDutyDetailDTO;
import com.zenith.bbykz.model.entity.sd.SdDutyDetail;
import com.zenith.bbykz.model.vo.OrgInfoVO;
import com.zenith.bbykz.model.vo.sd.SdDutyDetailVO;
import com.zenith.bbykz.model.vo.sd.SdModuleDetailVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * <p>
 * 三定-细化职责 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @date 2024-04-11 16:17:19
 */
@Service
public class SdDutyDetailServiceImpl extends ServiceImpl<SdDutyDetailMapper, SdDutyDetail> implements SdDutyDetailService {

    @Autowired
    private SdDutyDetailConverter sdDutyDetailConverter;
    @Autowired
    private SdDutyDetailMapper sdDutyDetailMapper;
    @Autowired
    private SdOrgRelationService sdOrgRelationService;
    @Autowired
    private OrgInfoService orgInfoService;

    @Override
    public Result<SdDutyDetail> save(SdSaveDutyDetailDTO dto) {
        List<SdDutyDetailDTO> dtoList = dto.getDtoList();
        String moduleDetailId = dtoList.get(0).getModuleDetailId();
        List<SdDutyDetail> detailList = new ArrayList<>();
        for (SdDutyDetailDTO detailDTO : dtoList) {
            SdDutyDetail entity = sdDutyDetailConverter.dto2Entity(detailDTO);
            if (StrUtil.isNotBlank(detailDTO.getTakeUnitName())) {
                entity.setTakeUnitName(entity.getTakeUnitName().trim());
            }
            String takeUnitId = detailDTO.getTakeUnitId();
            OrgInfoVO orgInfoVO = orgInfoService.findById(takeUnitId);
            if (Objects.nonNull(orgInfoVO)) {
                entity.setTakeUnitName(orgInfoVO.getName());
                entity.setTakeUnitLevelCode(orgInfoVO.getLevelCode());
            }

            detailList.add(entity);
        }
        this.saveOrUpdateBatch(detailList);
        sdOrgRelationService.countAllAndSetByModuleDetailId(moduleDetailId, 3);
        return Result.ok();
    }

    @Override
    public Result<SdDutyDetailVO> findById(String id) {
        SdDutyDetail entity = this.getById(id);
        SdDutyDetailVO vo = sdDutyDetailConverter.entity2Vo(entity);
        return Result.ok(vo);
    }

    @Override
    public Result<Boolean> update(SdDutyDetailDTO dto) {
        SdDutyDetail sdDutyDetail = sdDutyDetailConverter.dto2Entity(dto);
        String takeUnitId = sdDutyDetail.getTakeUnitId();
        OrgInfoVO orgInfoVO = orgInfoService.findById(takeUnitId);
        if (Objects.nonNull(orgInfoVO)) {
            sdDutyDetail.setTakeUnitName(orgInfoVO.getName());
            sdDutyDetail.setTakeUnitLevelCode(orgInfoVO.getLevelCode());
        }
        boolean flag = this.updateById(sdDutyDetail);
        return flag ? Result.ok() : Result.fail();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Result<Boolean> delete(String id) {
        SdDutyDetail sdDutyDetail = this.getById(id);
        boolean flag = this.removeById(id);
        sdOrgRelationService.countAllAndSetByModuleDetailId(sdDutyDetail.getModuleDetailId(), 3);
        return flag ? Result.ok() : Result.fail();
    }

    @Override
    public Page<SdDutyDetail> list(SdDutyDetailListDTO dto) {
        LambdaQueryWrapper<SdDutyDetail> queryWrapper = new LambdaQueryWrapper<>(SdDutyDetail.class);
        queryWrapper.eq(SdDutyDetail::getModuleDetailId, dto.getModuleDetailId());
        queryWrapper.orderByAsc(SdDutyDetail::getSort).orderByDesc(SdDutyDetail::getCreateTime);
        final Page<SdDutyDetail> page = sdDutyDetailMapper.selectPage(new Page<>(dto.getPageNum(), dto.getPageSize()), queryWrapper);
        return page;
    }

    @Override
    public Page<SdDutyDetailVO> searchList(SdDutyDetailListDTO dto) {
        Page<SdDutyDetailVO> page = new Page<>(dto.getPageNum(), dto.getPageSize());
        Page<SdDutyDetailVO> voPage = sdDutyDetailMapper.searchList(page, dto);
        return voPage;
    }
}
