package com.zenith.bbykz.service.publicInstitutions;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.util.StrUtil;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.ExcelReader;
import com.alibaba.excel.read.metadata.ReadSheet;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.efficient.auth.api.LoginService;
import com.efficient.cache.api.CacheUtil;
import com.efficient.cache.util.ProgressUtil;
import com.efficient.common.auth.RequestHolder;
import com.efficient.common.constant.CommonConstant;
import com.efficient.common.constant.DbConstant;
import com.efficient.common.result.Result;
import com.efficient.common.util.ExceptionUtil;
import com.efficient.common.util.ThreadUtil;
import com.efficient.file.api.FileService;
import com.efficient.file.api.SysFileInfoService;
import com.efficient.file.properties.FileProperties;
import com.zenith.bbykz.api.OrgInfoService;
import com.zenith.bbykz.api.publicInstitutions.UnifiedSocialCreditCodeService;
import com.zenith.bbykz.api.register.dynamic.UnitBdService;
import com.zenith.bbykz.common.enums.NotifyState;
import com.zenith.bbykz.common.result.BbResultEnum;
import com.zenith.bbykz.dao.UnifiedSocialCreditCodeMapper;
import com.zenith.bbykz.model.converter.register.interior.UnifiedSocialCreditCodeConverter;
import com.zenith.bbykz.model.dto.UnifiedSocialCreditCodeDTO;
import com.zenith.bbykz.model.dto.register.dynamic.UnitBdDTO;
import com.zenith.bbykz.model.easy.excel.model.UnifiedSocialCreditCodeModel;
import com.zenith.bbykz.model.entity.LegalPersonRegistration;
import com.zenith.bbykz.model.entity.OrgInfo;
import com.zenith.bbykz.model.entity.UnifiedSocialCreditCode;
import com.zenith.bbykz.model.entity.register.dynamic.UnitBd;
import com.zenith.bbykz.model.listener.UnifiedSocialCreditCodeListener;
import com.zenith.bbykz.model.vo.UnifiedSocialCreditCodeVO;
import com.zenith.bbykz.model.vo.register.UnitRegisterInfo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.io.File;
import java.io.IOException;
import java.nio.file.Files;
import java.util.*;
import java.util.stream.Collectors;

import static com.zenith.bbykz.common.constant.BbCommonConstant.BIANDONG_QX_GEOCODE_ARR_REAL;
import static com.zenith.bbykz.common.constant.BbCommonConstant.BIANDONG_QX_GEOCODE_REAL;

@Service
@Slf4j
public class UnifiedSocialCreditCodeServiceImpl extends ServiceImpl<UnifiedSocialCreditCodeMapper, UnifiedSocialCreditCode> implements UnifiedSocialCreditCodeService {
    @Autowired
    private CacheUtil cacheUtil;

    @Autowired
    private UnifiedSocialCreditCodeMapper unifiedSocialCreditCodeMapper;
    @Autowired
    private ProgressUtil progressUtil;
    @Autowired
    private FileProperties fileProperties;
    @Autowired
    private LoginService loginService;
    @Autowired
    private SysFileInfoService sysFileInfoService;
    @Autowired
    private FileService fileService;
    @Autowired
    private OrgInfoService orgInfoService;
    @Autowired
    private UnitBdService unitBdService;

    @Autowired
    private UnifiedSocialCreditCodeConverter unifiedSocialCreditCodeConverter;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Result importFile(String fileId) throws IOException {
        String currKey = ProgressUtil.getCurrKey("UnifiedSocialCreditCodeServiceImpl.importFile", RequestHolder.getCurrUser().getToken());
        // String tempPath = fileProperties.getTempPath();
        File file = fileService.getById(fileId);
        String originalFilename = file.getName();
        // File destFile = new File(tempPath + System.currentTimeMillis() + originalFilename);
        // FileUtil.writeFromStream(file.getInputStream(), destFile);
        ThreadUtil.EXECUTOR_SERVICE.execute(() -> {
            ExcelReader excelReader = null;
            progressUtil.running("正在解析文件", 10, currKey);
            try {
                excelReader = EasyExcel.read(Files.newInputStream(file.toPath())).build();
                ReadSheet readSheet = EasyExcel
                        .readSheet()
                        .headRowNumber(1)
                        .head(UnifiedSocialCreditCodeModel.class)
                        .registerReadListener(new UnifiedSocialCreditCodeListener(this, unifiedSocialCreditCodeConverter, progressUtil, currKey))
                        .build();
                excelReader.read(readSheet);
            } catch (Exception e) {
                log.error("导入文件异常", e);
                progressUtil.fail(BbResultEnum.DEVELOP_UPLOAD_EXCEL_ERROR.getMsg(), currKey, ExceptionUtil.getStackTrace(e));
                return;
            } finally {
                if (excelReader != null) {
                    excelReader.finish();
                }
                if (file.exists()) {
                    try {
                        fileService.delete(fileId);
                    } catch (Exception e) {
                        log.error("删除文件异常", e);
                    }
                }
            }
            progressUtil.success("导入成功", currKey);
        });
        return Result.ok(currKey);
    }

    @Override
    public Result queryImportRecords(Integer pageNum, Integer pageSize, String keyWord, String key, String captcha) {
        if (!loginService.checkCaptcha(key, captcha)) {
            return Result.build(BbResultEnum.VERIFICATION_CODE_ERROR);
        }
        Page<UnifiedSocialCreditCode> page = new Page<>(pageNum,pageSize);
        String trim = StrUtil.trim(keyWord);
        LambdaQueryWrapper<UnifiedSocialCreditCode> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(UnifiedSocialCreditCode::getCode, trim)
                .eq(UnifiedSocialCreditCode::getIsDelete, CommonConstant.FALSE_INT)
                .or().like(UnifiedSocialCreditCode::getInstitutionName, trim);
        queryWrapper.orderByAsc(UnifiedSocialCreditCode::getNum);
        Page<UnifiedSocialCreditCode> creditCodePage = page(page, queryWrapper);
        if (CollUtil.isNotEmpty(creditCodePage.getRecords())) {
            return Result.ok(creditCodePage);
        } else {
            return Result.build(BbResultEnum.QEURY_FAILED);
        }
    }

    @Override
    public Page<UnifiedSocialCreditCode> list(UnifiedSocialCreditCodeDTO dto) {
        Page<UnifiedSocialCreditCode> objectPage = new Page<>(dto.getPageNum(), dto.getPageSize());
        LambdaQueryWrapper<UnifiedSocialCreditCode> queryWrapper = new LambdaQueryWrapper<>();
        String unitLevelCode = dto.getUnitLevelCode();
        if (StringUtils.isBlank(unitLevelCode)) {
            return objectPage;
        }
        if (unitLevelCode.length() == 6) {
            queryWrapper.eq(UnifiedSocialCreditCode::getGeocode, unitLevelCode);
        } else {
            List<String> creditCodeList = orgInfoService.findCreditCodeListById(unitLevelCode);
            if (CollUtil.isEmpty(creditCodeList)) {
                return objectPage;
            }
            queryWrapper.in(UnifiedSocialCreditCode::getCode, creditCodeList);
        }
        if (!StringUtils.isBlank(dto.getUnifiedSocialCreditCode())) {
            queryWrapper.eq(UnifiedSocialCreditCode::getCode, dto.getUnifiedSocialCreditCode().trim());
        }

        if (!StringUtils.isBlank(dto.getKeyword())) {
            queryWrapper.like(UnifiedSocialCreditCode::getInstitutionName, dto.getKeyword().trim());
        }
        queryWrapper.eq(UnifiedSocialCreditCode::getInstitutionalStatus, "正常");
        // UnifiedSocialCreditCode lastVersionEntity = this.findLastVersion();
        // queryWrapper.eq(UnifiedSocialCreditCode::getVersion, lastVersionEntity.getVersion());
        queryWrapper.orderByDesc(UnifiedSocialCreditCode::getCreateTime).orderByAsc(UnifiedSocialCreditCode::getId);
        return unifiedSocialCreditCodeMapper.selectPage(objectPage, queryWrapper);
    }

    @Override
    public UnifiedSocialCreditCode findLastVersion() {
        return unifiedSocialCreditCodeMapper.findLastVersion();
    }

    @Override
    public void deleteByVersion(Integer lastVersion) {
        unifiedSocialCreditCodeMapper.deleteByVersion(lastVersion);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Result rollback() {
        UnifiedSocialCreditCode lastVersionEntity = this.findLastVersion();
        if (Objects.isNull(lastVersionEntity)) {
            return Result.build(BbResultEnum.REGISTER_ROLL_BACK_VERSION_NO);
        }
        Integer version = lastVersionEntity.getVersion();
        if (Objects.isNull(version) || version == 1) {
            return Result.build(BbResultEnum.REGISTER_ROLL_BACK_VERSION_NO);
        }
        Integer lastVersion = version - 1;
        // 还原上一个版本
        unifiedSocialCreditCodeMapper.rollback(lastVersion);
        unifiedSocialCreditCodeMapper.physicallyDeleteByVersion(version);
        return Result.ok();
    }

    @Override
    public Page<UnifiedSocialCreditCodeVO> bdList(UnifiedSocialCreditCodeDTO dto) {
        Page<UnifiedSocialCreditCodeVO> page = new Page<>(dto.getPageNum(), dto.getPageSize());
        Page<UnifiedSocialCreditCodeVO> pageResult = unifiedSocialCreditCodeMapper.bdList(page, dto);

        // List<LegalPersonRegistrationVO> records = pageResult.getRecords();
        // if (CollUtil.isNotEmpty(records)) {
        //     return pageResult;
        // }

        return pageResult;
    }

    @Override
    public UnitRegisterInfo findByCreditCode(String creditCode) {
        LambdaQueryWrapper<UnifiedSocialCreditCode> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(UnifiedSocialCreditCode::getCode, creditCode);
        // queryWrapper.eq(UnifiedSocialCreditCode::getIsDelete, 0);
        // queryWrapper.orderByDesc(UnifiedSocialCreditCode::getVersion);
        queryWrapper.last(DbConstant.LIMIT_ONE);
        UnifiedSocialCreditCode legalPersonRegistration = this.getOne(queryWrapper);
        UnitRegisterInfo unitRegisterInfo = new UnitRegisterInfo();
        if (Objects.nonNull(legalPersonRegistration)) {
            unitRegisterInfo.setId(legalPersonRegistration.getId());
            unitRegisterInfo.setName(legalPersonRegistration.getInstitutionName());
            unitRegisterInfo.setCreditCode(legalPersonRegistration.getCode());
            unitRegisterInfo.setLegalRepresentative(legalPersonRegistration.getHead());
            // unitRegisterInfo.setFundingSource(legalPersonRegistration.getFundingSource());
            // unitRegisterInfo.setInitialFunds(legalPersonRegistration.getInitialFunds());
            // unitRegisterInfo.setOrganizer(legalPersonRegistration.getOrganizer());
            // unitRegisterInfo.setPurpose(legalPersonRegistration.getPurpose());
            unitRegisterInfo.setValidStartDate(legalPersonRegistration.getIssueTime());
            unitRegisterInfo.setValidEndDate(legalPersonRegistration.getCertificateExpirationTime());
            // unitRegisterInfo.setAddress(legalPersonRegistration.getResidence());
        }
        return unitRegisterInfo;
    }

    @Override
    public UnifiedSocialCreditCode findEntityByCreditCode(String creditCode) {
        LambdaQueryWrapper<UnifiedSocialCreditCode> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(UnifiedSocialCreditCode::getCode, creditCode);
        // queryWrapper.orderByDesc(UnifiedSocialCreditCode::getVersion);
        queryWrapper.last(DbConstant.LIMIT_ONE);
        return this.getOne(queryWrapper);
    }

    @Override
    public List<UnifiedSocialCreditCode> findCertificateExpiration(DateTime startDate, DateTime endDate) {
        UnifiedSocialCreditCode lastVersion = this.findLastVersion();
        return unifiedSocialCreditCodeMapper.findCertificateExpiration(lastVersion.getVersion(), startDate, endDate);
    }

    @Override
    public void createWarnInfo(UnifiedSocialCreditCode et) {
        OrgInfo orgInfo = orgInfoService.findByCreditCode(et.getCode());
        if (Objects.isNull(orgInfo)) {
            return;
        }
        UnitBdDTO dto = new UnitBdDTO();
        dto.setType("04");
        dto.setUnitId(orgInfo.getId());
        dto.setUnitLevelCode(orgInfo.getLevelCode());
        dto.setUnitName(orgInfo.getName());
        // dto.setOldInfo("证书到期时间：" + et.getCertificateExpirationTime());
        dto.setNewInfo("证书到期时间：" + et.getCertificateExpirationTime());
        dto.setRemark("证书已到期,请尽快办理！");
        dto.setBdTime(new Date());
        unitBdService.save(dto);
    }

    @Override
    public Result verifyZyInfo() {
        // UnifiedSocialCreditCode lastVersion = this.findLastVersion();
        // if (Objects.isNull(lastVersion)) {
        //     return Result.fail("未查询到导入的事业单位数据，请检查是否已进行数据导入！");
        // }
        // List<OrgInfo> orgInfoList = orgInfoService.findAllSy();
        // Map<String, OrgInfo> orgInfoNameMap = orgInfoList.stream().collect(Collectors.toMap(OrgInfo::getName, et -> et, (k1, k2) -> k1));
        //
        // List<LegalPersonRegistration> legalPersonList = legalPersonMapper.findAllByVersion(lastVersion.getVersion());
        // Map<String, LegalPersonRegistration> legalPersonNameMap = legalPersonList.stream().collect(Collectors.toMap(LegalPersonRegistration::getName, et -> et, (k1, k2) -> k1));
        //
        // List<UnitBd> saveBdList = new ArrayList<>();
        // // 1.smz中统一社会信用代码为空
        // List<OrgInfo> smzCodeNullList = orgInfoList.stream().filter(et -> StrUtil.isBlank(et.getCreditCode())||et.getCreditCode().length()!=creditCodeLength).collect(Collectors.toList());
        // smzCodeNullList.forEach(et -> {
        //     UnitBd unitBd = new UnitBd();
        //     unitBd.setType("01");
        //     unitBd.setGeocode(et.getGeocode());
        //     unitBd.setUnitId(et.getId());
        //     unitBd.setUnitLevelCode(et.getLevelCode());
        //     unitBd.setUnitName(et.getName());
        //     unitBd.setOldInfo(null);
        //     unitBd.setNewInfo("该单位在实名制系统中统一社会信用代码为空!");
        //     unitBd.setIsHandle(null);
        //     unitBd.setHandleTime(null);
        //     unitBd.setRemark(null);
        //     unitBd.setSourceType(null);
        //     unitBd.setSourceName(null);
        //     unitBd.setBdTime(new Date());
        //     unitBd.setIsDelete(0);
        //     unitBd.setIsSend(0);
        //     unitBd.setState("1");
        //     saveBdList.add(unitBd);
        // });
        //
        // // 2.zy中统一社会信用代码为空
        // List<LegalPersonRegistration> zyCodeNullList = legalPersonList.stream().filter(et -> StrUtil.isBlank(et.getUnifiedSocialCreditCode())||et.getUnifiedSocialCreditCode().length()!=creditCodeLength).collect(Collectors.toList());
        // zyCodeNullList.forEach(et -> {
        //     UnitBd unitBd = new UnitBd();
        //     unitBd.setType("02");
        //     unitBd.setGeocode(et.getRegistrationAuthorityCode());
        //     unitBd.setUnitId(et.getId());
        //     String registrationAuthorityCode = et.getRegistrationAuthorityCode();
        //     if (StrUtil.equalsAny(registrationAuthorityCode, BIANDONG_QX_GEOCODE_ARR_REAL)) {
        //         registrationAuthorityCode = BIANDONG_QX_GEOCODE_REAL.get(registrationAuthorityCode);
        //     }
        //
        //     unitBd.setUnitLevelCode(orgInfoService.findLevelCodeById(registrationAuthorityCode));
        //     unitBd.setUnitName(et.getName());
        //     unitBd.setOldInfo(null);
        //     unitBd.setNewInfo("该单位在中央事业单位法人登记信息系统中统一社会信用代码为空!");
        //     unitBd.setIsHandle(null);
        //     unitBd.setHandleTime(null);
        //     unitBd.setRemark(null);
        //     unitBd.setSourceType(null);
        //     unitBd.setSourceName(null);
        //     unitBd.setBdTime(new Date());
        //     unitBd.setIsDelete(0);
        //     unitBd.setIsSend(0);
        //     unitBd.setState("1");
        //     saveBdList.add(unitBd);
        // });
        //
        // Map<String, OrgInfo> orgInfoMap = orgInfoList.stream().filter(et -> StrUtil.isNotBlank(et.getCreditCode())&&et.getCreditCode().length()==creditCodeLength).collect(Collectors.toMap(OrgInfo::getCreditCode, et -> et, (k1, k2) -> k1));
        // Map<String, LegalPersonRegistration> legalPersonMap = legalPersonList.stream().filter(et -> StrUtil.isNotBlank(et.getUnifiedSocialCreditCode())&&et.getUnifiedSocialCreditCode().length()==creditCodeLength).collect(Collectors.toMap(LegalPersonRegistration::getUnifiedSocialCreditCode, et -> et, (k1, k2) -> k1));
        // Set<String> smzCodeSet = orgInfoMap.keySet();
        // Set<String> zyCodeSet = legalPersonMap.keySet();
        // // 3.smz有，但是zy没有
        // List<String> smzHas = smzCodeSet.stream().filter(et -> !zyCodeSet.contains(et)).collect(Collectors.toList());
        // orgInfoMap.keySet().stream().filter(smzHas::contains).forEach(et -> {
        //     OrgInfo orgInfo = orgInfoMap.get(et);
        //     LegalPersonRegistration legalPersonRegistration = legalPersonNameMap.get(orgInfo.getName());
        //     if(Objects.nonNull(legalPersonRegistration)){
        //         return;
        //     }
        //     UnitBd unitBd = new UnitBd();
        //     unitBd.setType("03");
        //     unitBd.setGeocode(orgInfo.getGeocode());
        //     unitBd.setUnitId(orgInfo.getId());
        //     unitBd.setUnitLevelCode(orgInfo.getLevelCode());
        //     unitBd.setUnitName(orgInfo.getName());
        //     unitBd.setOldInfo(null);
        //     unitBd.setNewInfo("该单位在实名制系统中存在，但是未在中央事业单位法人登记信息系统匹配到!");
        //     unitBd.setIsHandle(null);
        //     unitBd.setHandleTime(null);
        //     unitBd.setRemark(null);
        //     unitBd.setSourceType(null);
        //     unitBd.setSourceName(null);
        //     unitBd.setBdTime(new Date());
        //     unitBd.setIsDelete(0);
        //     unitBd.setIsSend(0);
        //     unitBd.setState("1");
        //     saveBdList.add(unitBd);
        // });
        // // 4.smz没有，但是zy有
        // List<String> zyHas = zyCodeSet.stream().filter(et -> !smzCodeSet.contains(et)).collect(Collectors.toList());
        // // 5.统一社会信用代码一致但是名称不一致
        // legalPersonMap.keySet().stream().filter(zyHas::contains).forEach(et -> {
        //     LegalPersonRegistration legalPersonRegistration = legalPersonMap.get(et);
        //     OrgInfo orgInfo = orgInfoNameMap.get(legalPersonRegistration.getName());
        //     if(Objects.nonNull(orgInfo)){
        //         return;
        //     }
        //     UnitBd unitBd = new UnitBd();
        //     unitBd.setType("05");
        //     unitBd.setGeocode(legalPersonRegistration.getRegistrationAuthorityCode());
        //     unitBd.setUnitId(legalPersonRegistration.getId());
        //     String registrationAuthorityCode = legalPersonRegistration.getRegistrationAuthorityCode();
        //     if (StrUtil.equalsAny(registrationAuthorityCode, BIANDONG_QX_GEOCODE_ARR_REAL)) {
        //         registrationAuthorityCode = BIANDONG_QX_GEOCODE_REAL.get(registrationAuthorityCode);
        //     }
        //
        //     unitBd.setUnitLevelCode(orgInfoService.findLevelCodeById(registrationAuthorityCode));
        //     unitBd.setUnitName(legalPersonRegistration.getName());
        //     unitBd.setOldInfo(null);
        //     unitBd.setNewInfo("该单位在中央事业单位法人登记信息系统中存在，但是未在实名制系统中匹配到!");
        //     unitBd.setIsHandle(null);
        //     unitBd.setHandleTime(null);
        //     unitBd.setRemark(null);
        //     unitBd.setSourceType(null);
        //     unitBd.setSourceName(null);
        //     unitBd.setBdTime(new Date());
        //     unitBd.setIsDelete(0);
        //     unitBd.setIsSend(0);
        //     unitBd.setState("1");
        //     saveBdList.add(unitBd);
        // });
        // smzCodeSet.stream().filter(zyCodeSet::contains).forEach(et -> {
        //     OrgInfo orgInfo = orgInfoMap.get(et);
        //     String smzName = orgInfo.getName();
        //     LegalPersonRegistration legalPersonRegistration = legalPersonMap.get(et);
        //     String zyName = legalPersonRegistration.getName();
        //     int indexOf = zyName.indexOf("（");
        //     if (indexOf != -1) {
        //         zyName = zyName.substring(0, indexOf);
        //     } else {
        //         indexOf = zyName.indexOf("(");
        //         if (indexOf != -1) {
        //             zyName = zyName.substring(0, indexOf);
        //         }
        //     }
        //     if (!StrUtil.equals(smzName, zyName)) {
        //         UnitBd unitBd = new UnitBd();
        //         unitBd.setType("06");
        //         unitBd.setGeocode(orgInfo.getGeocode());
        //         unitBd.setUnitId(orgInfo.getId());
        //         unitBd.setUnitLevelCode(orgInfo.getLevelCode());
        //         unitBd.setUnitName(orgInfo.getName());
        //         unitBd.setOldInfo(null);
        //         unitBd.setNewInfo("中央事业单位法人登记信息系统中的名称为：" + zyName);
        //         unitBd.setIsHandle(null);
        //         unitBd.setHandleTime(null);
        //         unitBd.setRemark(null);
        //         unitBd.setSourceType(null);
        //         unitBd.setSourceName(null);
        //         unitBd.setBdTime(new Date());
        //         unitBd.setIsDelete(0);
        //         unitBd.setIsSend(0);
        //         unitBd.setState("1");
        //         saveBdList.add(unitBd);
        //     }
        // });
        //
        // if (CollUtil.isNotEmpty(saveBdList)) {
        //     unitBdService.deleteAll();
        //     unitBdFlowService.deleteAll();
        //     unitBdService.saveBatch(saveBdList, DEFAULT_BATCH_SIZE);
        //     for (UnitBd unitBd : saveBdList) {
        //         unitBdFlowService.saveByBizId(unitBd.getId(), NotifyState.YGZ);
        //     }
        // }

        return Result.ok();
    }
}
