package com.zenith.bbykz.service.publicInstitutions;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.ExcelReader;
import com.alibaba.excel.read.metadata.ReadSheet;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.efficient.auth.api.LoginService;
import com.efficient.cache.api.CacheUtil;
import com.efficient.cache.util.ProgressUtil;
import com.efficient.common.auth.RequestHolder;
import com.efficient.common.constant.CommonConstant;
import com.efficient.common.constant.DbConstant;
import com.efficient.common.result.Result;
import com.efficient.common.util.ExceptionUtil;
import com.efficient.common.util.ThreadUtil;
import com.efficient.file.api.FileService;
import com.efficient.file.properties.FileProperties;
import com.zenith.bbykz.api.OrgInfoService;
import com.zenith.bbykz.api.publicInstitutions.LegalPersonService;
import com.zenith.bbykz.api.register.dynamic.UnitBdFlowService;
import com.zenith.bbykz.api.register.dynamic.UnitBdService;
import com.zenith.bbykz.common.enums.NotifyState;
import com.zenith.bbykz.common.result.BbResultEnum;
import com.zenith.bbykz.dao.LegalPersonMapper;
import com.zenith.bbykz.model.converter.register.dynamic.UnitBdConverter;
import com.zenith.bbykz.model.converter.register.interior.LegalPersonConverter;
import com.zenith.bbykz.model.dto.LegalPersonRegistrationListDTO;
import com.zenith.bbykz.model.dto.register.dynamic.UnitBdDTO;
import com.zenith.bbykz.model.easy.excel.model.LegalPersonModel;
import com.zenith.bbykz.model.entity.LegalPersonRegistration;
import com.zenith.bbykz.model.entity.OrgInfo;
import com.zenith.bbykz.model.entity.register.dynamic.UnitBd;
import com.zenith.bbykz.model.listener.LegalPersonListener;
import com.zenith.bbykz.model.vo.LegalPersonRegistrationVO;
import com.zenith.bbykz.model.vo.register.UnitRegisterInfo;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.io.File;
import java.io.IOException;
import java.nio.file.Files;
import java.util.*;
import java.util.stream.Collectors;

import static com.zenith.bbykz.common.constant.BbCommonConstant.BIANDONG_QX_GEOCODE_ARR_REAL;
import static com.zenith.bbykz.common.constant.BbCommonConstant.BIANDONG_QX_GEOCODE_REAL;

@Service
public class LegalPersonServiceImpl extends ServiceImpl<LegalPersonMapper, LegalPersonRegistration> implements LegalPersonService {

    private static final Integer creditCodeLength = 18;
    @Autowired
    private CacheUtil cacheUtil;
    @Autowired
    private LoginService loginService;
    @Autowired
    private LegalPersonMapper legalPersonMapper;
    @Autowired
    private ProgressUtil progressUtil;
    @Autowired
    private FileProperties fileProperties;
    @Autowired
    private FileService fileService;
    @Autowired
    private OrgInfoService orgInfoService;
    @Autowired
    private UnitBdConverter unitBdConverter;
    @Autowired
    private UnitBdService unitBdService;
    @Autowired
    private UnitBdFlowService unitBdFlowService;
    @Autowired
    private LegalPersonConverter legalPersonConverter;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Result importFile(String fileId) throws IOException {
        String currKey = ProgressUtil.getCurrKey("LegalPersonServiceImpl.importFile", RequestHolder.getCurrUser().getToken());
        // String tempPath = fileProperties.getTempPath();
        File file = fileService.getById(fileId);
        // String originalFilename = file.getOriginalFilename();
        // File destFile = new File(tempPath + System.currentTimeMillis() + originalFilename);
        // FileUtil.writeFromStream(file.getInputStream(), destFile);
        ThreadUtil.EXECUTOR_SERVICE.execute(() -> {
            ExcelReader excelReader = null;
            progressUtil.running("正在解析文件", 10, currKey);
            try {
                excelReader = EasyExcel.read(Files.newInputStream(file.toPath())).build();
                ReadSheet readSheet = EasyExcel.readSheet().headRowNumber(1).head(LegalPersonModel.class).registerReadListener(new LegalPersonListener(this, legalPersonConverter, progressUtil, currKey)).build();
                excelReader.read(readSheet);
            } catch (Exception e) {
                log.error("导入文件异常", e);
                progressUtil.fail(BbResultEnum.DEVELOP_UPLOAD_EXCEL_ERROR.getMsg(), currKey, ExceptionUtil.getStackTrace(e));
                return;
            } finally {
                if (excelReader != null) {
                    excelReader.finish();
                }
                if (file.exists()) {
                    try {
                        fileService.delete(fileId);
                    } catch (Exception e) {
                        log.error("删除文件异常", e);
                    }
                }
            }
            progressUtil.success("导入成功", currKey);
        });
        return Result.ok(currKey);
    }

    @Override
    public Result queryImportRecords(Integer pageNum, Integer pageSize, String keyWord, String type, String key, String captcha) {
        if (!loginService.checkCaptcha(key, captcha)) {
            return Result.build(BbResultEnum.VERIFICATION_CODE_ERROR);
        }
        Page<LegalPersonRegistration> page = new Page<>(pageNum, pageSize);
        String trim = StrUtil.trim(keyWord);
        LambdaQueryWrapper<LegalPersonRegistration> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(LegalPersonRegistration::getUnifiedSocialCreditCode, trim).or().like(LegalPersonRegistration::getName, trim).eq(LegalPersonRegistration::getIsDelete, CommonConstant.FALSE_INT);
        if (StrUtil.equals(type, "1")) {
            queryWrapper.eq(LegalPersonRegistration::getStatus, "正常");
        } else {
            queryWrapper.eq(LegalPersonRegistration::getStatus, "已注销");
        }
        queryWrapper.orderByAsc(LegalPersonRegistration::getCertificateNo);
        // queryWrapper.last("limit 1");
        Page<LegalPersonRegistration> registrationPage = page(page, queryWrapper);
        if (CollUtil.isNotEmpty(registrationPage.getRecords())) {
            // LegalPersonRegistrationVO result = new LegalPersonRegistrationVO();
            // BeanUtils.copyProperties(one, result);
            return Result.ok(registrationPage);
        } else {
            return Result.build(BbResultEnum.QEURY_FAILED);
        }
    }

    @Override
    public Page<LegalPersonRegistration> list(LegalPersonRegistrationListDTO dto) {
        Page<LegalPersonRegistration> objectPage = new Page<>(dto.getPageNum(), dto.getPageSize());
        LambdaQueryWrapper<LegalPersonRegistration> queryWrapper = new LambdaQueryWrapper<>();
        String unitLevelCode = dto.getUnitLevelCode();
        if (StringUtils.isBlank(unitLevelCode)) {
            return objectPage;
        }
        LegalPersonRegistration lastVersionEntity = this.findLastVersion();

        if (Objects.isNull(lastVersionEntity)) {
            return objectPage;
        }
        queryWrapper.eq(LegalPersonRegistration::getVersion, lastVersionEntity.getVersion());
        if (unitLevelCode.length() == 6) {
            queryWrapper.eq(LegalPersonRegistration::getRegistrationAuthorityCode, unitLevelCode);
        } else {
            List<String> creditCodeList = orgInfoService.findCreditCodeListById(unitLevelCode);
            if (CollUtil.isEmpty(creditCodeList)) {
                return objectPage;
            }
            queryWrapper.in(LegalPersonRegistration::getUnifiedSocialCreditCode, creditCodeList);
        }
        queryWrapper.eq(LegalPersonRegistration::getStatus, "正常");
        if (!StringUtils.isBlank(dto.getUnifiedSocialCreditCode())) {
            queryWrapper.eq(LegalPersonRegistration::getUnifiedSocialCreditCode, dto.getUnifiedSocialCreditCode().trim());
        }
        if (!StringUtils.isBlank(dto.getKeyword())) {
            queryWrapper.like(LegalPersonRegistration::getName, dto.getKeyword().trim());
        }

        queryWrapper.orderByAsc(LegalPersonRegistration::getUnifiedSocialCreditCode).orderByAsc(LegalPersonRegistration::getCertificateNo).orderByAsc(LegalPersonRegistration::getId);
        return legalPersonMapper.selectPage(objectPage, queryWrapper);
    }

    @Override
    public LegalPersonRegistration findLastVersion() {
        return legalPersonMapper.findLastVersion();
    }

    @Override
    public void deleteByVersion(Integer lastVersion) {
        legalPersonMapper.deleteByVersion(lastVersion);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Result rollback() {
        LegalPersonRegistration lastVersionEntity = this.findLastVersion();
        if (Objects.isNull(lastVersionEntity)) {
            return Result.build(BbResultEnum.REGISTER_ROLL_BACK_VERSION_NO);
        }
        Integer version = lastVersionEntity.getVersion();
        if (Objects.isNull(version) || version == 1) {
            return Result.build(BbResultEnum.REGISTER_ROLL_BACK_VERSION_NO);
        }
        Integer lastVersion = version - 1;
        // 还原上一个版本
        legalPersonMapper.rollback(lastVersion);
        legalPersonMapper.physicallyDeleteByVersion(version);
        return Result.ok();
    }

    @Override
    public Page<LegalPersonRegistrationVO> bdList(LegalPersonRegistrationListDTO dto) {
        Page<LegalPersonRegistrationVO> page = new Page<>(dto.getPageNum(), dto.getPageSize());
        Page<LegalPersonRegistrationVO> pageResult = legalPersonMapper.bdList(page, dto);

        // List<LegalPersonRegistrationVO> records = pageResult.getRecords();
        // if (CollUtil.isNotEmpty(records)) {
        //     return pageResult;
        // }

        return pageResult;
    }

    @Override
    public UnitRegisterInfo findByCreditCode(String creditCode) {
        LambdaQueryWrapper<LegalPersonRegistration> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(LegalPersonRegistration::getUnifiedSocialCreditCode, creditCode);
        // queryWrapper.eq(LegalPersonRegistration::getIsDelete, 0);
        // queryWrapper.orderByDesc(LegalPersonRegistration::getVersion);
        queryWrapper.last(DbConstant.LIMIT_ONE);
        LegalPersonRegistration legalPersonRegistration = this.getOne(queryWrapper);
        UnitRegisterInfo unitRegisterInfo = new UnitRegisterInfo();
        if (Objects.nonNull(legalPersonRegistration)) {
            unitRegisterInfo.setId(legalPersonRegistration.getId());
            unitRegisterInfo.setName(legalPersonRegistration.getName());
            unitRegisterInfo.setCreditCode(legalPersonRegistration.getUnifiedSocialCreditCode());
            unitRegisterInfo.setLegalRepresentative(legalPersonRegistration.getLegalRepresentative());
            unitRegisterInfo.setFundingSource(legalPersonRegistration.getFundingSource());
            unitRegisterInfo.setInitialFunds(legalPersonRegistration.getInitialFunds());
            unitRegisterInfo.setOrganizer(legalPersonRegistration.getOrganizer());
            unitRegisterInfo.setPurpose(legalPersonRegistration.getPurpose());
            unitRegisterInfo.setValidStartDate(legalPersonRegistration.getCertificateValidityStartTime());
            unitRegisterInfo.setValidEndDate(legalPersonRegistration.getCertificateValidityEndTime());
            unitRegisterInfo.setAddress(legalPersonRegistration.getResidence());
        }
        return unitRegisterInfo;
    }

    @Override
    public LegalPersonRegistration findEntityByCreditCode(String creditCode) {
        LambdaQueryWrapper<LegalPersonRegistration> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(LegalPersonRegistration::getUnifiedSocialCreditCode, creditCode);
        // queryWrapper.orderByDesc(LegalPersonRegistration::getVersion);
        queryWrapper.last(DbConstant.LIMIT_ONE);
        return this.getOne(queryWrapper);
    }

    @Override
    public List<LegalPersonRegistration> findCertificateExpiration(DateTime startDate, DateTime endDate) {
        LegalPersonRegistration lastVersion = this.findLastVersion();
        return legalPersonMapper.findCertificateExpiration(lastVersion.getVersion(), startDate, endDate);
    }

    @Override
    public UnitBd createWarnInfo(LegalPersonRegistration et) {
        // OrgInfo orgInfo = orgInfoService.findByCreditCode(et.getUnifiedSocialCreditCode());
        // if (Objects.isNull(orgInfo)) {
        //     return null;
        // }
        UnitBdDTO dto = new UnitBdDTO();
        dto.setType("04");
        dto.setUnitId(et.getId());
        String registrationAuthorityCode = et.getRegistrationAuthorityCode();
        if (StrUtil.equalsAny(registrationAuthorityCode, BIANDONG_QX_GEOCODE_ARR_REAL)) {
            registrationAuthorityCode = BIANDONG_QX_GEOCODE_REAL.get(registrationAuthorityCode);
        }

        dto.setUnitLevelCode(orgInfoService.findLevelCodeById(registrationAuthorityCode));
        dto.setUnitName(et.getName());
        // dto.setOldInfo("证书到期时间：" + et.getCertificateValidityEndTime());
        dto.setNewInfo("证书到期时间：" + et.getCertificateValidityEndTime());
        dto.setRemark("证书已到期,请尽快办理！");
        dto.setBdTime(new Date());
        dto.setZyCode(et.getCertificateNo());
        // unitBdService.save(dto);
        UnitBd entity = unitBdConverter.dto2Entity(dto);
        return entity;
    }

    @Override
    public Result verifyZyInfo() {
        LegalPersonRegistration lastVersion = this.findLastVersion();
        if (Objects.isNull(lastVersion)) {
            return Result.fail("未查询到导入的事业单位数据，请检查是否已进行数据导入！");
        }
        List<OrgInfo> orgInfoList = orgInfoService.findAllSy();
        Map<String, OrgInfo> orgInfoNameMap = orgInfoList.stream().collect(Collectors.toMap(OrgInfo::getName, et -> et, (k1, k2) -> k1));

        List<LegalPersonRegistration> legalPersonList = legalPersonMapper.findAllByVersion(lastVersion.getVersion());
        Map<String, LegalPersonRegistration> legalPersonNameMap = legalPersonList.stream().collect(Collectors.toMap(LegalPersonRegistration::getName, et -> et, (k1, k2) -> k1));

        List<UnitBd> oldUnitBdList = unitBdService.findByState("1");
        List<UnitBd> bdUpdateList = new ArrayList<>();

        List<UnitBd> saveBdList = new ArrayList<>();
        // 1.smz中统一社会信用代码为空
        List<OrgInfo> smzCodeNullList = orgInfoList.stream().filter(et -> StrUtil.isBlank(et.getCreditCode()) || et.getCreditCode().length() != creditCodeLength).collect(Collectors.toList());
        Map<String, UnitBd> oldMap01 = oldUnitBdList.stream().filter(et -> StrUtil.equals(et.getType(), "01")).collect(Collectors.toMap(et -> et.getUnitId(), et -> et, (k1, k2) -> k1));
        Set<String> bdIdSet01 = new HashSet<>();
        smzCodeNullList.forEach(et -> {
            bdIdSet01.add(et.getId());
            UnitBd unitBd = new UnitBd();
            unitBd.setType("01");
            unitBd.setGeocode(et.getGeocode());
            unitBd.setUnitId(et.getId());
            unitBd.setUnitLevelCode(et.getLevelCode());
            unitBd.setUnitName(et.getName());
            unitBd.setOldInfo(null);
            unitBd.setNewInfo("该单位在实名制系统中统一社会信用代码为空!");
            unitBd.setIsHandle(null);
            unitBd.setHandleTime(null);
            unitBd.setRemark(null);
            unitBd.setSourceType(null);
            unitBd.setSourceName(null);
            unitBd.setBdTime(new Date());
            unitBd.setIsDelete(0);
            unitBd.setIsSend(0);
            unitBd.setState("1");
            unitBd.setUnitSource("1");
            UnitBd unitBd1 = oldMap01.get(et.getId());
            if (Objects.isNull(unitBd1)) {
                saveBdList.add(unitBd);
            }
        });
        // 比对没有的就转入历史数据
        Set<String> oldSet01 = oldMap01.keySet();
        oldSet01.stream().filter(et -> !bdIdSet01.contains(et)).forEach(et -> {
            UnitBd unitBd = oldMap01.get(et);
            unitBd.setState("5");
            bdUpdateList.add(unitBd);
        });

        // 2.zy中统一社会信用代码为空
        List<LegalPersonRegistration> zyCodeNullList = legalPersonList.stream().filter(et -> StrUtil.isBlank(et.getUnifiedSocialCreditCode()) || et.getUnifiedSocialCreditCode().length() != creditCodeLength).collect(Collectors.toList());
        Map<String, UnitBd> oldMap02 = oldUnitBdList.stream().filter(et -> StrUtil.equals(et.getType(), "02")).collect(Collectors.toMap(et -> et.getZyCode(), et -> et, (k1, k2) -> k1));
        Set<String> bdIdSet02 = new HashSet<>();
        zyCodeNullList.forEach(et -> {
            bdIdSet02.add(et.getCertificateNo());
            UnitBd unitBd = new UnitBd();
            unitBd.setType("02");
            unitBd.setGeocode(et.getRegistrationAuthorityCode());
            unitBd.setUnitId(et.getId());
            String registrationAuthorityCode = et.getRegistrationAuthorityCode();
            if (StrUtil.equalsAny(registrationAuthorityCode, BIANDONG_QX_GEOCODE_ARR_REAL)) {
                registrationAuthorityCode = BIANDONG_QX_GEOCODE_REAL.get(registrationAuthorityCode);
            }

            unitBd.setUnitLevelCode(orgInfoService.findLevelCodeById(registrationAuthorityCode));
            unitBd.setUnitName(et.getName());
            unitBd.setOldInfo(null);
            unitBd.setNewInfo("该单位在中央事业单位法人登记信息系统中统一社会信用代码为空!");
            unitBd.setIsHandle(null);
            unitBd.setHandleTime(null);
            unitBd.setRemark(null);
            unitBd.setSourceType(null);
            unitBd.setSourceName(null);
            unitBd.setBdTime(new Date());
            unitBd.setIsDelete(0);
            unitBd.setIsSend(0);
            unitBd.setState("1");
            unitBd.setUnitSource("1");
            unitBd.setZyCode(et.getCertificateNo());
            UnitBd unitBd1 = oldMap02.get(et.getCertificateNo());
            if (Objects.isNull(unitBd1)) {
                saveBdList.add(unitBd);
            }
        });
        Set<String> oldSet02 = oldMap02.keySet();
        oldSet02.stream().filter(et -> !bdIdSet02.contains(et)).forEach(et -> {
            UnitBd unitBd = oldMap02.get(et);
            unitBd.setState("5");
            bdUpdateList.add(unitBd);
        });

        Map<String, OrgInfo> orgInfoMap = orgInfoList.stream().filter(et -> StrUtil.isNotBlank(et.getCreditCode()) && et.getCreditCode().length() == creditCodeLength).collect(Collectors.toMap(OrgInfo::getCreditCode, et -> et, (k1, k2) -> k1));
        Map<String, LegalPersonRegistration> legalPersonMap = legalPersonList.stream().filter(et -> StrUtil.isNotBlank(et.getUnifiedSocialCreditCode()) && et.getUnifiedSocialCreditCode().length() == creditCodeLength).collect(Collectors.toMap(LegalPersonRegistration::getUnifiedSocialCreditCode, et -> et, (k1, k2) -> k1));
        Set<String> smzCodeSet = orgInfoMap.keySet();
        Set<String> zyCodeSet = legalPersonMap.keySet();
        // 3.smz有，但是zy没有
        List<String> smzHas = smzCodeSet.stream().filter(et -> !zyCodeSet.contains(et)).collect(Collectors.toList());
        Map<String, UnitBd> oldMap03 = oldUnitBdList.stream().filter(et -> StrUtil.equals(et.getType(), "03")).collect(Collectors.toMap(et -> et.getUnitId(), et -> et, (k1, k2) -> k1));
        Set<String> bdIdSet03 = new HashSet<>();
        orgInfoMap.keySet().stream().filter(smzHas::contains).forEach(et -> {
            OrgInfo orgInfo = orgInfoMap.get(et);
            LegalPersonRegistration legalPersonRegistration = legalPersonNameMap.get(orgInfo.getName());
            if (Objects.nonNull(legalPersonRegistration)) {
                return;
            }
            bdIdSet03.add(orgInfo.getId());
            UnitBd unitBd = new UnitBd();
            unitBd.setType("03");
            unitBd.setGeocode(orgInfo.getGeocode());
            unitBd.setUnitId(orgInfo.getId());
            unitBd.setUnitLevelCode(orgInfo.getLevelCode());
            unitBd.setUnitName(orgInfo.getName());
            unitBd.setOldInfo(null);
            unitBd.setNewInfo("该单位在实名制系统中存在，但是未在中央事业单位法人登记信息系统匹配到!");
            unitBd.setIsHandle(null);
            unitBd.setHandleTime(null);
            unitBd.setRemark(null);
            unitBd.setSourceType(null);
            unitBd.setSourceName(null);
            unitBd.setBdTime(new Date());
            unitBd.setIsDelete(0);
            unitBd.setIsSend(0);
            unitBd.setState("1");
            unitBd.setUnitSource("1");
            // saveBdList.add(unitBd);
            UnitBd unitBd1 = oldMap03.get(orgInfo.getId());
            if (Objects.isNull(unitBd1)) {
                saveBdList.add(unitBd);
            }
        });
        Set<String> oldSet03 = oldMap03.keySet();
        oldSet03.stream().filter(et -> !bdIdSet03.contains(et)).forEach(et -> {
            UnitBd unitBd = oldMap03.get(et);
            unitBd.setState("5");
            bdUpdateList.add(unitBd);
        });

        // 4.smz没有，但是zy有
        List<String> zyHas = zyCodeSet.stream().filter(et -> !smzCodeSet.contains(et)).collect(Collectors.toList());
        Map<String, UnitBd> oldMap05 = oldUnitBdList.stream().filter(et -> StrUtil.equals(et.getType(), "05")).collect(Collectors.toMap(et -> et.getZyCode(), et -> et, (k1, k2) -> k1));
        Set<String> bdIdSet05 = new HashSet<>();
        legalPersonMap.keySet().stream().filter(zyHas::contains).forEach(et -> {
            LegalPersonRegistration legalPersonRegistration = legalPersonMap.get(et);
            OrgInfo orgInfo = orgInfoNameMap.get(legalPersonRegistration.getName());
            if (Objects.nonNull(orgInfo)) {
                return;
            }
            UnitBd unitBd = new UnitBd();
            unitBd.setType("05");
            unitBd.setGeocode(legalPersonRegistration.getRegistrationAuthorityCode());
            unitBd.setUnitId(legalPersonRegistration.getId());
            String registrationAuthorityCode = legalPersonRegistration.getRegistrationAuthorityCode();
            if (StrUtil.equalsAny(registrationAuthorityCode, BIANDONG_QX_GEOCODE_ARR_REAL)) {
                registrationAuthorityCode = BIANDONG_QX_GEOCODE_REAL.get(registrationAuthorityCode);
            }
            bdIdSet05.add(legalPersonRegistration.getCertificateNo());
            unitBd.setUnitLevelCode(orgInfoService.findLevelCodeById(registrationAuthorityCode));
            unitBd.setUnitName(legalPersonRegistration.getName());
            unitBd.setOldInfo(null);
            unitBd.setNewInfo("该单位在中央事业单位法人登记信息系统中存在，但是未在实名制系统中匹配到!");
            unitBd.setIsHandle(null);
            unitBd.setHandleTime(null);
            unitBd.setRemark(null);
            unitBd.setSourceType(null);
            unitBd.setSourceName(null);
            unitBd.setBdTime(new Date());
            unitBd.setIsDelete(0);
            unitBd.setIsSend(0);
            unitBd.setState("1");
            unitBd.setUnitSource("1");
            unitBd.setZyCode(legalPersonRegistration.getCertificateNo());
            // saveBdList.add(unitBd);
            UnitBd unitBd1 = oldMap05.get(legalPersonRegistration.getCertificateNo());
            if (Objects.isNull(unitBd1)) {
                saveBdList.add(unitBd);
            }
        });
        Set<String> oldSet05 = oldMap05.keySet();
        oldSet05.stream().filter(et -> !bdIdSet05.contains(et)).forEach(et -> {
            UnitBd unitBd = oldMap05.get(et);
            unitBd.setState("5");
            bdUpdateList.add(unitBd);
        });

        // 5.统一社会信用代码一致但是名称不一致
        Map<String, UnitBd> oldMap06 = oldUnitBdList.stream().filter(et -> StrUtil.equals(et.getType(), "06")).collect(Collectors.toMap(et -> et.getUnitId(), et -> et, (k1, k2) -> k1));
        Set<String> bdIdSet06 = new HashSet<>();
        smzCodeSet.stream().filter(zyCodeSet::contains).forEach(et -> {
            OrgInfo orgInfo = orgInfoMap.get(et);
            String smzName = orgInfo.getName();
            LegalPersonRegistration legalPersonRegistration = legalPersonMap.get(et);
            String zyName = legalPersonRegistration.getName();
            int indexOf = zyName.indexOf("（");
            if (indexOf != -1) {
                zyName = zyName.substring(0, indexOf);
            } else {
                indexOf = zyName.indexOf("(");
                if (indexOf != -1) {
                    zyName = zyName.substring(0, indexOf);
                }
            }
            if (!StrUtil.equals(smzName, zyName)) {
                bdIdSet06.add(orgInfo.getId());
                UnitBd unitBd = new UnitBd();
                unitBd.setType("06");
                unitBd.setGeocode(orgInfo.getGeocode());
                unitBd.setUnitId(orgInfo.getId());
                unitBd.setUnitLevelCode(orgInfo.getLevelCode());
                unitBd.setUnitName(orgInfo.getName());
                unitBd.setOldInfo(null);
                unitBd.setNewInfo("中央事业单位法人登记信息系统中的名称为：" + zyName);
                unitBd.setIsHandle(null);
                unitBd.setHandleTime(null);
                unitBd.setRemark(null);
                unitBd.setSourceType(null);
                unitBd.setSourceName(null);
                unitBd.setBdTime(new Date());
                unitBd.setIsDelete(0);
                unitBd.setIsSend(0);
                unitBd.setState("1");
                unitBd.setUnitSource("1");
                // saveBdList.add(unitBd);
                UnitBd unitBd1 = oldMap06.get(orgInfo.getId());
                if (Objects.isNull(unitBd1)) {
                    saveBdList.add(unitBd);
                }
            }
        });
        Set<String> oldSet06 = oldMap06.keySet();
        oldSet06.stream().filter(et -> !bdIdSet06.contains(et)).forEach(et -> {
            UnitBd unitBd = oldMap06.get(et);
            unitBd.setState("5");
            bdUpdateList.add(unitBd);
        });

        Date date = new Date();
        DateTime startDate = DateUtil.beginOfDay(date);
        DateTime endDate = DateUtil.endOfDay(date);
        // 查询 事业单位正式到期
        Map<String, UnitBd> oldMap04 = oldUnitBdList.stream().filter(et -> StrUtil.equals(et.getType(), "04")).collect(Collectors.toMap(et -> et.getZyCode(), et -> et, (k1, k2) -> k1));
        Set<String> bdIdSet04 = new HashSet<>();
        List<LegalPersonRegistration> legalPersonRegistrationList = this.findCertificateExpiration(startDate, endDate);
        legalPersonRegistrationList.forEach(et -> {
            UnitBd warnInfo = this.createWarnInfo(et);
            bdIdSet04.add(warnInfo.getZyCode());
            UnitBd unitBd = oldMap04.get(warnInfo.getZyCode());
            if (Objects.isNull(unitBd)) {
                saveBdList.add(warnInfo);
            }
        });
        Set<String> oldSet04 = oldMap04.keySet();
        oldSet04.stream().filter(et -> !bdIdSet04.contains(et)).forEach(et -> {
            UnitBd unitBd = oldMap04.get(et);
            unitBd.setState("5");
            bdUpdateList.add(unitBd);
        });

        if (CollUtil.isNotEmpty(saveBdList)) {
            // unitBdService.deleteAll();
            // unitBdFlowService.deleteAll();
            unitBdService.saveBatch(saveBdList, DEFAULT_BATCH_SIZE);
            for (UnitBd unitBd : saveBdList) {
                unitBdFlowService.saveByBizId(unitBd.getId(), NotifyState.YGZ);
            }
        }
        if (CollUtil.isNotEmpty(bdUpdateList)) {
            // unitBdService.deleteAll();
            // unitBdFlowService.deleteAll();
            unitBdService.updateBatchById(bdUpdateList, DEFAULT_BATCH_SIZE);
            for (UnitBd unitBd : bdUpdateList) {
                unitBdFlowService.saveByBizId(unitBd.getId(), NotifyState.YWC);
            }
        }

        return Result.ok();
    }
}
