package com.zenith.bbykz.service.task;

import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import com.zenith.bbykz.api.publicInstitutions.LegalPersonService;
import com.zenith.bbykz.api.publicInstitutions.UnifiedSocialCreditCodeService;
import com.zenith.bbykz.dao.LegalPersonMapper;
import com.zenith.bbykz.model.entity.LegalPersonRegistration;
import com.zenith.bbykz.model.entity.register.dynamic.UnitBd;
import lombok.extern.slf4j.Slf4j;
import org.quartz.JobExecutionContext;
import org.quartz.JobExecutionException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.quartz.QuartzJobBean;
import org.springframework.stereotype.Component;

import java.util.Date;
import java.util.HashSet;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * 证书到期查询
 *
 * <AUTHOR>
 * @since 2024/5/16 14:52
 */
@Component
@Slf4j
public class CertificateExpirationTask extends QuartzJobBean {

    @Autowired
    private LegalPersonService legalPersonService;
    @Autowired
    private UnifiedSocialCreditCodeService unifiedSocialCreditCodeService;
    @Autowired
    private LegalPersonMapper legalPersonMapper;

    @Override
    protected void executeInternal(JobExecutionContext context) throws JobExecutionException {
        log.info("执行定时任务 CertificateExpirationTask ... 开始");
        legalPersonService.verifyZyInfo();
        unifiedSocialCreditCodeService.verifyZyInfo();
        // log.info("执行定时任务 CertificateExpirationTask ... 预警信息比较完成");
        // Date date = new Date();
        // DateTime startDate = DateUtil.beginOfDay(date);
        // DateTime endDate = DateUtil.endOfDay(date);
        // // 查询 事业单位正式到期
        // LegalPersonRegistration lastVersion = legalPersonService.findLastVersion();
        // List<LegalPersonRegistration> legalPersonList = legalPersonMapper.findAllByVersion(lastVersion.getVersion());
        // legalPersonList.stream().filter(et-> StrUtil.equals(et.gett)).collect(Collectors.toMap(et->et.getCertificateNo(), et->et,(k1, k2)->k1));
        // List<LegalPersonRegistration> legalPersonRegistrationList = legalPersonService.findCertificateExpiration(startDate, endDate);
        // Set<String> newSet = new HashSet<>();
        // legalPersonRegistrationList.forEach(et -> {
        //             UnitBd warnInfo = legalPersonService.createWarnInfo(et);
        //             newSet.add(warnInfo.getZyCode());
        //
        //         }
        // );
        // log.info("执行定时任务 CertificateExpirationTask ... 事业单位证书到期比较完成");
        // 查询机关单位证书到期
        // List<UnifiedSocialCreditCode> unifiedSocialCreditCodeList = unifiedSocialCreditCodeService.findCertificateExpiration(startDate, endDate);
        // unifiedSocialCreditCodeList.forEach(et -> unifiedSocialCreditCodeService.createWarnInfo(et));
        // log.info("执行定时任务 CertificateExpirationTask ... 机关单位证书到期比较完成");
        log.info("执行定时任务 CertificateExpirationTask ... 完成");
    }
}
