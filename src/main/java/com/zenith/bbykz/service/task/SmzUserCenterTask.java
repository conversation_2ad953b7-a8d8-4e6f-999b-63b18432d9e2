package com.zenith.bbykz.service.task;

import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import com.zenith.bbykz.api.center.UserCenterService;
import com.zenith.bbykz.api.publicInstitutions.LegalPersonService;
import com.zenith.bbykz.api.publicInstitutions.UnifiedSocialCreditCodeService;
import com.zenith.bbykz.model.entity.LegalPersonRegistration;
import com.zenith.bbykz.model.entity.UnifiedSocialCreditCode;
import lombok.extern.slf4j.Slf4j;
import org.quartz.JobExecutionContext;
import org.quartz.JobExecutionException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.quartz.QuartzJobBean;
import org.springframework.stereotype.Component;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2024/5/16 14:52
 */
@Component
@Slf4j
public class SmzUserCenterTask extends QuartzJobBean {

    @Autowired
    private UserCenterService userCenterService;


    @Override
    protected void executeInternal(JobExecutionContext context) throws JobExecutionException {
        log.info("执行定时任务 SmzUserCenterTask ... 开始");
        try {
            userCenterService.syncSmzJg();
        } catch (Exception e) {
            log.error("执行定时任务 同步smz机构数据异常",e);
        }
        log.info("执行定时任务 同步smz机构数据完成");
        userCenterService.orgBelong();
        log.info("执行定时任务 设置机构层级关系完成");
        userCenterService.syncSmzUser();
        log.info("执行定时任务 同步smz用户数据完成");
        log.info("执行定时任务 SmzUserCenterTask ... 完成");
    }
}
