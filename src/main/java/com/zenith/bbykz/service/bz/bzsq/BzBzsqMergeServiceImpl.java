package com.zenith.bbykz.service.bz.bzsq;

import cn.afterturn.easypoi.word.WordExportUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.efficient.common.auth.RequestHolder;
import com.efficient.common.constant.CommonConstant;
import com.efficient.common.result.Result;
import com.efficient.common.result.ResultEnum;
import com.efficient.configs.constant.SpringConstant;
import com.efficient.configs.util.PageUtil;
import com.efficient.file.api.SysFileInfoService;
import com.efficient.file.exception.FileException;
import com.efficient.file.model.entity.SysFileInfo;
import com.efficient.system.api.DictCodeService;
import com.zenith.bbykz.api.bz.bzsq.BzBzsqMergeService;
import com.zenith.bbykz.api.bz.bzsq.BzBzsqService;
import com.zenith.bbykz.common.constant.DictConstant;
import com.zenith.bbykz.dao.bz.bzsq.BzBzsqMergeMapper;
import com.zenith.bbykz.model.converter.bz.bzsq.BzBzsqMergeConverter;
import com.zenith.bbykz.model.dto.bz.bzsq.BzBzsqDTO;
import com.zenith.bbykz.model.dto.bz.bzsq.BzBzsqMergeDTO;
import com.zenith.bbykz.model.dto.bz.bzsq.BzBzsqMergeListDTO;
import com.zenith.bbykz.model.entity.bz.bzsq.BzBzsqMerge;
import com.zenith.bbykz.model.vo.bz.bzsq.BzBzsqDetailVO;
import com.zenith.bbykz.model.vo.bz.bzsq.BzBzsqMergeVO;
import com.zenith.bbykz.model.vo.bz.bzsq.BzBzsqMergeWordVO;
import com.zenith.bbykz.model.vo.bz.bzsq.BzBzsqVO;
import org.apache.poi.xwpf.usermodel.XWPFDocument;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.io.File;
import java.io.FileOutputStream;
import java.util.*;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * <p>
 * 核编管理-用编审核-合并审核 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @date 2024-04-03 15:26:58
 */
@Service
public class BzBzsqMergeServiceImpl extends ServiceImpl<BzBzsqMergeMapper, BzBzsqMerge> implements BzBzsqMergeService {

    @Autowired
    private BzBzsqMergeConverter bzBzsqMergeConverter;
    @Autowired
    private BzBzsqMergeMapper bzBzsqMergeMapper;
    @Autowired
    private BzBzsqService bzBzsqService;
    @Autowired
    private DictCodeService dictCodeService;
    @Autowired
    private SysFileInfoService sysFileInfoService;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Result<BzBzsqMerge> save(BzBzsqMergeDTO dto) {
        BzBzsqMerge entity = this.getEntity(dto);
        if (Objects.isNull(entity)) {
            return Result.build(ResultEnum.PARA_ERROR);
        }
        List<BzBzsqDTO> bzBzsqList = dto.getBzBzsqList();
        String mergeId = entity.getId();
        bzBzsqList.forEach(et -> {
            et.setMergeId(mergeId);
            bzBzsqService.update(et);
        });

        return Result.ok(entity);
    }

    private BzBzsqMerge getEntity(BzBzsqMergeDTO dto) {
        BzBzsqMerge entity;
        String id = dto.getId();
        List<BzBzsqDTO> bzBzsqList = dto.getBzBzsqList();
        if (CollUtil.isEmpty(bzBzsqList)) {
            return null;
        }
        entity = bzBzsqMergeConverter.dto2Entity(dto);
        if (StrUtil.isBlank(id)) {
            BzBzsqDTO bzBzsqDTO = bzBzsqList.get(0);
            entity.setGeocode(bzBzsqDTO.getGeocode());
            entity.setCreateTime(null);
            entity.setStatus("2");
            boolean flag = this.save(entity);
        } else {
            // entity = this.getById(id);
            boolean flag = this.updateById(entity);
        }
        return entity;
    }

    @Override
    public Result<BzBzsqMergeVO> findById(String id) {
        BzBzsqMerge entity = this.getById(id);
        BzBzsqMergeVO vo = bzBzsqMergeConverter.entity2Vo(entity);
        List<BzBzsqVO> voList = bzBzsqService.listByMergeId(id);
        vo.setBzBzsqList(voList);
        Map<String, String> ztMap = dictCodeService.findMapByType(DictConstant.BZ_HBZT);
        String status = vo.getStatus();
        vo.setStatusName(ztMap.get(status));
        if (StrUtil.equalsAny(status, "5", "8")) {
            vo.setIsAgree(1);
        } else if (StrUtil.equals(status, "6")) {
            vo.setIsAgree(0);
        }
        return Result.ok(vo);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Result<Boolean> delete(String id) {
        boolean flag = this.removeById(id);
        bzBzsqMergeMapper.removeListById(id);
        return flag ? Result.ok() : Result.fail();
    }

    @Override
    public Page<BzBzsqMergeVO> list(BzBzsqMergeListDTO dto) {
        LambdaQueryWrapper<BzBzsqMerge> queryWrapper = new LambdaQueryWrapper<>(BzBzsqMerge.class);
        String title = dto.getTitle();
        if (StrUtil.isNotBlank(title)) {
            queryWrapper.like(BzBzsqMerge::getTitle, title);
        }
        queryWrapper.eq(BzBzsqMerge::getCreateUser, RequestHolder.getCurrUser().getUserId());

        String status = dto.getStatus();
        if (StrUtil.isNotBlank(status)) {
            queryWrapper.eq(BzBzsqMerge::getStatus, status);
        }

        Date startTime = dto.getStartTime();
        if (Objects.nonNull(startTime)) {
            queryWrapper.ge(BzBzsqMerge::getCreateTime, DateUtil.beginOfDay(startTime));
        }

        Date endTime = dto.getEndTime();
        if (Objects.nonNull(endTime)) {
            queryWrapper.le(BzBzsqMerge::getCreateTime, DateUtil.beginOfDay(endTime));
        }
        queryWrapper.orderByDesc(BzBzsqMerge::getCreateTime);
        final Page<BzBzsqMerge> page = bzBzsqMergeMapper.selectPage(new Page<>(dto.getPageNum(), dto.getPageSize()), queryWrapper);
        List<BzBzsqMerge> records = page.getRecords();
        if (CollUtil.isEmpty(records)) {
            return PageUtil.change(page, null);
        }
        Map<String, String> ztMap = dictCodeService.findMapByType(DictConstant.BZ_HBZT);
        List<BzBzsqMergeVO> voList = new ArrayList<>();
        records.forEach(et -> {
            BzBzsqMergeVO bzBzsqMergeVO = bzBzsqMergeConverter.entity2Vo(et);
            bzBzsqMergeVO.setStatusName(ztMap.get(et.getStatus()));
            voList.add(bzBzsqMergeVO);
        });
        return PageUtil.change(page, voList);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Result approve(BzBzsqMergeDTO dto) {
        Integer isAgree = dto.getIsAgree();
        if (Objects.isNull(isAgree)) {
            return Result.build(ResultEnum.PARA_ERROR, "是否同意 不为空！");
        }
        String status = "";
        if (Objects.equals(isAgree, CommonConstant.TRUE_INT)) {
            // 同意
            status = "5";
            long count = dto.getBzBzsqList().stream().filter(et -> et.getDetailList().stream().anyMatch(et1 -> Objects.equals(et1.getIsBack(), CommonConstant.TRUE_INT))).count();
            if (count > 0) {
                status = "8";
            }
        } else {
            // 拒绝
            status = "6";
        }
        dto.setStatus(status);
        BzBzsqMerge entity = this.getEntity(dto);
        if (Objects.isNull(entity)) {
            return Result.build(ResultEnum.PARA_ERROR);
        }

        String mergeId = entity.getId();
        String opinion = entity.getOpinion();
        String remark = entity.getRemark();
        List<BzBzsqDTO> bzBzsqList = dto.getBzBzsqList();
        bzBzsqList.parallelStream().forEach(et -> {
            et.setMergeId(mergeId);
            et.setIsAgree(isAgree);
            et.setOpinion(opinion);
            et.setRemark(remark);
            bzBzsqService.approve(et);
        });
        return Result.ok();
    }

    @Override
    public String downAudit(String id) throws Exception {
        Result<BzBzsqMergeVO> bzsqMergeVOResult = this.findById(id);
        List<BzBzsqMergeWordVO> wordVOList = new ArrayList<>();
        BzBzsqMergeVO data = bzsqMergeVOResult.getData();
        Date createTime = data.getCreateTime();
        AtomicInteger count = new AtomicInteger(1);
        List<BzBzsqVO> bzBzsqList = data.getBzBzsqList();
        bzBzsqList.forEach(et -> {
            List<BzBzsqDetailVO> detailList = et.getDetailList();
            detailList.forEach(dt -> {
                if (Objects.equals(dt.getIsBack(), CommonConstant.TRUE_INT)) {
                    return;
                }
                BzBzsqMergeWordVO vo = new BzBzsqMergeWordVO();
                vo.setSort(String.valueOf(count.getAndIncrement()));
                vo.setApplyUnitName(et.getApplyUnitName());
                vo.setUseBzUnitName(dt.getUseBzUnitName());
                vo.setBzTypeName(dt.getBzTypeName());
                vo.setYbTypeName(dt.getYbTypeName());
                vo.setDsbNum(String.valueOf(dt.getDsbNum()));
                vo.setZbNum(String.valueOf(dt.getZbNum()));
                vo.setYbNum(String.valueOf(dt.getYbNum()));
                vo.setApplyNum(String.valueOf(dt.getApplyNum()));
                vo.setRemark(dt.getRemark());
                wordVOList.add(vo);
            });
        });

        Map<String, Object> dataMap = new HashMap<>();
        dataMap.put("createTime", DateUtil.formatDate(createTime));
        dataMap.put("remark", Objects.isNull(data.getRemark()) ? "" : data.getRemark());
        dataMap.put("dataList", wordVOList);
        String tempName = "/bz/bz_merge_export.docx";
        String tempPath = SpringConstant.TEMPLATES_PATH + tempName;
        SysFileInfo sysFileInfo = null;
        String filename = data.getTitle() + ".docx";
        File downFile = sysFileInfoService.getDownPath(filename);
        try (FileOutputStream fout = new FileOutputStream(downFile)) {
            XWPFDocument document = WordExportUtil.exportWord07(tempPath, dataMap);
            // 生成文件
            document.write(fout);
            sysFileInfo = sysFileInfoService.saveDownFile(downFile, id, filename, tempName);
        } catch (Exception e) {
            throw new FileException("编制使用核准审批单", e);
        }
        return sysFileInfo.getId();
    }
}
