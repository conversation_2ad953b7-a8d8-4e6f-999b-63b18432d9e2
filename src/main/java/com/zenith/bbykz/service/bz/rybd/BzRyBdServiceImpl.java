package com.zenith.bbykz.service.bz.rybd;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.efficient.common.constant.DbConstant;
import com.efficient.common.result.Result;
import com.efficient.common.result.ResultEnum;
import com.efficient.configs.util.PageUtil;
import com.zenith.bbykz.api.bz.rybd.BzRyBdService;
import com.zenith.bbykz.dao.bz.rybd.BzRyBdMapper;
import com.zenith.bbykz.model.converter.bz.rybd.BzRyBdConverter;
import com.zenith.bbykz.model.dto.bz.rybd.BzRyBdDTO;
import com.zenith.bbykz.model.dto.bz.rybd.BzRyBdListDTO;
import com.zenith.bbykz.model.entity.bz.rybd.BzRyBd;
import com.zenith.bbykz.model.vo.bz.rybd.BzRyBdVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <p>
 * 核编管理-动态信息维护-人员详情 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @date 2024-04-22 11:21:43
 */
@Service
public class BzRyBdServiceImpl extends ServiceImpl<BzRyBdMapper, BzRyBd> implements BzRyBdService {

    @Autowired
    private BzRyBdConverter bzRyBdConverter;
    @Autowired
    private BzRyBdMapper bzRyBdMapper;

    @Override
    public Result<BzRyBd> save(BzRyBdDTO dto) {
        BzRyBd entity = bzRyBdConverter.dto2Entity(dto);
        boolean flag = this.save(entity);
        return Result.ok(entity);
    }

    @Override
    public Result<BzRyBdVO> findById(String id) {
        BzRyBd entity = this.getById(id);
        if (Objects.isNull(entity)) {
            return Result.build(ResultEnum.DATA_NOT_EXIST);
        }
        BzRyBdVO vo = bzRyBdConverter.entity2Vo(entity);
        return Result.ok(vo);
    }

    @Override
    public Result<Boolean> update(BzRyBdDTO dto) {
        BzRyBd entity = bzRyBdConverter.dto2Entity(dto);
        boolean flag = this.updateById(entity);
        return flag ? Result.ok() : Result.fail();
    }

    @Override
    public Result<Boolean> delete(String id) {
        boolean flag = this.removeById(id);
        return flag ? Result.ok() : Result.fail();
    }

    @Override
    public Page<BzRyBdVO> list(BzRyBdListDTO dto) {
        LambdaQueryWrapper<BzRyBd> queryWrapper = new LambdaQueryWrapper<>(BzRyBd.class);
        final Page<BzRyBd> page = bzRyBdMapper.selectPage(new Page<>(dto.getPageNum(), dto.getPageSize()), queryWrapper);
        List<BzRyBdVO> voList = new ArrayList<>();
        List<BzRyBd> records = page.getRecords();
        if (CollUtil.isEmpty(records)) {
            return PageUtil.change(page, voList);
        }
        records.forEach(et -> {
            BzRyBdVO vo = bzRyBdConverter.entity2Vo(et);
            voList.add(vo);
        });
        return PageUtil.change(page, voList);
    }

    @Override
    public void saveByBizId(String bizId, List<BzRyBdDTO> rybdList) {
        if (CollUtil.isEmpty(rybdList)) {
            this.removeByBizId(bizId);
            return;
        }
        List<BzRyBd> bzRyBdList = this.findListByBizId(bizId);
        List<BzRyBd> saveList = new ArrayList<>();
        rybdList.forEach(et -> {
            BzRyBd entity = bzRyBdConverter.dto2Entity(et);
            entity.setUnitBdId(bizId);
            saveList.add(entity);
        });
        List<String> idList = saveList.stream().map(BzRyBd::getId).filter(Objects::nonNull).collect(Collectors.toList());
        if (CollUtil.isNotEmpty(bzRyBdList)) {
            List<String> deleteIdList = bzRyBdList.stream().map(BzRyBd::getId).filter(id -> !idList.contains(id)).collect(Collectors.toList());
            this.deleteByIdList(deleteIdList);
        }
        this.saveOrUpdateBatch(saveList, DbConstant.BATCH_LITTLE_SIZE);
    }

    @Override
    public List<BzRyBd> findListByBizId(String bizId) {
        LambdaQueryWrapper<BzRyBd> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(BzRyBd::getUnitBdId, bizId);
        queryWrapper.orderByAsc(BzRyBd::getId);
        return this.list(queryWrapper);
    }

    @Override
    public void removeByBizId(String bizId) {
        bzRyBdMapper.removeByBizId(bizId);
    }

    @Override
    public void deleteByIdList(List<String> deleteIdList) {
        this.removeByIds(deleteIdList);
    }

    @Override
    public List<BzRyBd> findListByBizIdList(List<String> idList) {
        LambdaQueryWrapper<BzRyBd> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.in(BzRyBd::getUnitBdId, idList);
        queryWrapper.orderByAsc(BzRyBd::getId);
        return this.list(queryWrapper);
    }
}
