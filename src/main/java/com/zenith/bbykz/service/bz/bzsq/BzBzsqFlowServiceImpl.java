package com.zenith.bbykz.service.bz.bzsq;

import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.efficient.system.api.DictCodeService;
import com.zenith.bbykz.api.bz.bzsq.BzBzsqFlowService;
import com.zenith.bbykz.common.constant.DictConstant;
import com.zenith.bbykz.dao.bz.bzsq.BzBzsqFlowMapper;
import com.zenith.bbykz.model.converter.bz.bzsq.BzBzsqFlowConverter;
import com.zenith.bbykz.model.entity.bz.bzsq.BzBzsqFlow;
import com.zenith.bbykz.model.vo.bz.bzsq.BzBzsqFlowVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * <p>
 * 核编管理-用编审核 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @date 2024-04-02 11:51:22
 */
@Service
public class BzBzsqFlowServiceImpl extends ServiceImpl<BzBzsqFlowMapper, BzBzsqFlow> implements BzBzsqFlowService {

    @Autowired
    private BzBzsqFlowConverter bzBzsqFlowConverter;
    @Autowired
    private BzBzsqFlowMapper bzBzsqFlowMapper;

    @Autowired
    private DictCodeService dictCodeService;

    @Override
    public List<BzBzsqFlowVO> getFlow(String id) {
        LambdaQueryWrapper<BzBzsqFlow> queryWrapper = new LambdaQueryWrapper<>(BzBzsqFlow.class);
        queryWrapper.eq(BzBzsqFlow::getBzsqId, id);
        queryWrapper.orderByAsc(BzBzsqFlow::getCreateTime);
        List<BzBzsqFlow> list = this.list(queryWrapper);
        List<BzBzsqFlowVO> voList = new ArrayList<>();

        list.forEach(et -> {
            BzBzsqFlowVO flowVO = bzBzsqFlowConverter.entity2Vo(et);
            voList.add(flowVO);
        });
        return voList;
    }

    @Override
    public void saveByBizId(String id, String status) {
        Map<String, String> mapByType = dictCodeService.findMapByType(DictConstant.BZ_HBZT);
        /**
         * 1	未提交
         * 2	未核准
         * 3	撤回
         * 4	变更申请
         * 5	核准通过
         * 6	核准未通过
         * 7	已废弃
         * 8	部分通过
         */
        BzBzsqFlow bzBzsqFlow = new BzBzsqFlow();
        if (StrUtil.equals("2", status)) {
            bzBzsqFlow.setBzsqId(id);
            bzBzsqFlow.setBzsqStatus(status);
            bzBzsqFlow.setStatusDesc("提交申请");
            bzBzsqFlow.setIsMerge(0);
            bzBzsqFlow.setIsFinish(0);
        } else {
            bzBzsqFlow.setBzsqId(id);
            bzBzsqFlow.setBzsqStatus(status);
            bzBzsqFlow.setStatusDesc(mapByType.get(status));
            bzBzsqFlow.setIsMerge(0);
            bzBzsqFlow.setIsFinish(0);
        }
        if (StrUtil.equalsAny(status, "5", "6", "8")) {
            bzBzsqFlow.setIsFinish(1);
        }
        this.save(bzBzsqFlow);

    }
}
