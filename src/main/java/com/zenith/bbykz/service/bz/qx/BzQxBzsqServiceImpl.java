package com.zenith.bbykz.service.bz.qx;

import cn.afterturn.easypoi.word.WordExportUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.efficient.common.auth.RequestHolder;
import com.efficient.common.result.Result;
import com.efficient.common.result.ResultEnum;
import com.efficient.configs.constant.SpringConstant;
import com.efficient.configs.util.PageUtil;
import com.efficient.file.api.SysFileInfoService;
import com.efficient.file.exception.FileException;
import com.efficient.file.model.entity.SysFileInfo;
import com.efficient.file.model.vo.FileVO;
import com.efficient.system.api.DictCodeService;
import com.zenith.bbykz.api.OrgInfoService;
import com.zenith.bbykz.api.bz.qx.BzQxBzsqDetailService;
import com.zenith.bbykz.api.bz.qx.BzQxBzsqService;
import com.zenith.bbykz.common.constant.DictConstant;
import com.zenith.bbykz.common.utils.IncreasingSequenceUtil;
import com.zenith.bbykz.dao.bz.qx.BzQxBzsqMapper;
import com.zenith.bbykz.model.base.UserInfo;
import com.zenith.bbykz.model.converter.bz.qx.BzQxBzsqConverter;
import com.zenith.bbykz.model.converter.bz.qx.BzQxBzsqDetailConverter;
import com.zenith.bbykz.model.dto.bz.qx.BzQxBzsqAuditDTO;
import com.zenith.bbykz.model.dto.bz.qx.BzQxBzsqDTO;
import com.zenith.bbykz.model.dto.bz.qx.BzQxBzsqDetailDTO;
import com.zenith.bbykz.model.dto.bz.qx.BzQxBzsqListDTO;
import com.zenith.bbykz.model.entity.OrgInfo;
import com.zenith.bbykz.model.entity.SysUser;
import com.zenith.bbykz.model.entity.bz.qx.BzQxBzsq;
import com.zenith.bbykz.model.entity.bz.qx.BzQxBzsqDetail;
import com.zenith.bbykz.model.vo.bz.bzsq.BzBzsqDownVO;
import com.zenith.bbykz.model.vo.bz.qx.BzQxBzsqDetailVO;
import com.zenith.bbykz.model.vo.bz.qx.BzQxBzsqVO;
import org.apache.poi.xwpf.usermodel.XWPFDocument;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.io.File;
import java.io.FileOutputStream;
import java.util.*;

import static com.zenith.bbykz.service.bz.bzsq.BzBzsqServiceImpl.DOWN_REPLY;
import static com.zenith.bbykz.service.bz.bzsq.BzBzsqServiceImpl.REPLY_PATH;

/**
 * <p>
 * 核编管理-用编管理-区县用编申请计划 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @date 2024-05-06 17:05:40
 */
@Service
public class BzQxBzsqServiceImpl extends ServiceImpl<BzQxBzsqMapper, BzQxBzsq> implements BzQxBzsqService {

    @Autowired
    private BzQxBzsqConverter bzQxBzsqConverter;
    @Autowired
    private BzQxBzsqDetailConverter bzQxBzsqDetailConverter;
    @Autowired
    private BzQxBzsqMapper bzQxBzsqMapper;
    @Autowired
    private OrgInfoService orgInfoService;
    @Autowired
    private BzQxBzsqDetailService bzQxBzsqDetailService;
    @Autowired
    private SysFileInfoService sysFileInfoService;
    @Autowired
    private DictCodeService dictCodeService;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Result<BzQxBzsq> save(BzQxBzsqDTO dto) {
        UserInfo currUser = (UserInfo) RequestHolder.getCurrUser();
        String manageUnitId = currUser.getSysUser().getSsdwId();

        OrgInfo orgInfo = orgInfoService.getById(manageUnitId);

        BzQxBzsq entity = bzQxBzsqConverter.dto2Entity(dto);
        entity.setRecordUnitId(orgInfo.getId());
        entity.setRecordUnitName(orgInfo.getGeocode());
        entity.setRecordUnitLevelCode(orgInfo.getLevelCode());
        entity.setGeocode(orgInfo.getGeocode());
        List<BzQxBzsqDetailDTO> detailList = dto.getDetailList();
        entity.setApprovalNum(IncreasingSequenceUtil.createNum(entity.getGeocode()));
        boolean flag = this.save(entity);
        List<FileVO> fileList = dto.getFileList();
        bzQxBzsqDetailService.saveByBizId(entity.getId(), detailList);
        sysFileInfoService.saveFileListByBizId(fileList, entity.getId());
        return Result.ok(entity);
    }

    @Override
    public Result<BzQxBzsqVO> findById(String id) {
        BzQxBzsq entity = this.getById(id);
        if (Objects.isNull(entity)) {
            return Result.build(ResultEnum.DATA_NOT_EXIST);
        }
        BzQxBzsqVO vo = bzQxBzsqConverter.entity2Vo(entity);
        List<BzQxBzsqDetail> bzQxBzsqDetails = bzQxBzsqDetailService.listByBiz(id);
        List<BzQxBzsqDetailVO> detailVOList = new ArrayList<>();
        Map<String, String> bzlxMap = dictCodeService.findMapByType(DictConstant.BZ_BZLX);
        Map<String, String> yblxMap = dictCodeService.findMapByType(DictConstant.BZ_YBLX);
        Map<String, String> ztType = dictCodeService.findMapByType(DictConstant.BZ_HBZT);
        vo.setStatusName(ztType.get(vo.getStatus()));
        bzQxBzsqDetails.forEach(et -> {
            BzQxBzsqDetailVO bzQxBzsqDetailVO = bzQxBzsqDetailConverter.entity2Vo(et);
            bzQxBzsqDetailVO.setBzTypeName(bzlxMap.get(bzQxBzsqDetailVO.getBzType()));
            bzQxBzsqDetailVO.setBzTypeName(bzlxMap.get(bzQxBzsqDetailVO.getBzType()));
            bzQxBzsqDetailVO.setYbTypeName(yblxMap.get(bzQxBzsqDetailVO.getYbType()));
            detailVOList.add(bzQxBzsqDetailVO);
        });
        vo.setDetailList(detailVOList);

        vo.setFileList(sysFileInfoService.findVOByBizId(id));
        return Result.ok(vo);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Result<Boolean> update(BzQxBzsqDTO dto) {
        BzQxBzsq entity = bzQxBzsqConverter.dto2Entity(dto);
        boolean flag = this.updateById(entity);
        List<BzQxBzsqDetailDTO> detailList = dto.getDetailList();
        bzQxBzsqDetailService.saveByBizId(entity.getId(), detailList);
        List<FileVO> fileList = dto.getFileList();
        sysFileInfoService.saveFileListByBizId(fileList, entity.getId());
        return flag ? Result.ok() : Result.fail();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Result<Boolean> delete(String id) {
        boolean flag = this.removeById(id);
        bzQxBzsqDetailService.deleteByBizId(id);
        sysFileInfoService.deleteByBizId(id);
        return flag ? Result.ok() : Result.fail();
    }

    @Override
    public Page<BzQxBzsqVO> list(BzQxBzsqListDTO dto) {
        LambdaQueryWrapper<BzQxBzsq> queryWrapper = new LambdaQueryWrapper<>(BzQxBzsq.class);
        String pageType = dto.getPageType();
        if (StrUtil.equals(pageType, "2")) {
            queryWrapper.in(BzQxBzsq::getStatus, Arrays.asList("2", "5", "6"));
        }else {
            String geocode = ((UserInfo) RequestHolder.getCurrUser()).getSysUser().getGeocode();
            queryWrapper.eq(BzQxBzsq::getGeocode, geocode);
        }
        String title = dto.getTitle();
        if (StrUtil.isNotBlank(title)) {
            queryWrapper.like(BzQxBzsq::getTitle, title);
        }
        String applyUnitName = dto.getApplyUnitName();
        if (StrUtil.isNotBlank(applyUnitName)) {
            queryWrapper.like(BzQxBzsq::getApplyUnitName, applyUnitName);
        }
        String status = dto.getStatus();
        if (StrUtil.isNotBlank(status)) {
            queryWrapper.eq(BzQxBzsq::getStatus, status);
        }
        Date startDate = dto.getStartDate();
        if (Objects.nonNull(startDate)) {
            queryWrapper.ge(BzQxBzsq::getApplyTime, DateUtil.beginOfDay(startDate));
        }
        Date endDate = dto.getEndDate();
        if (Objects.nonNull(endDate)) {
            queryWrapper.le(BzQxBzsq::getApplyTime, DateUtil.endOfDay(endDate));
        }

        final Page<BzQxBzsq> page = bzQxBzsqMapper.selectPage(new Page<>(dto.getPageNum(), dto.getPageSize()), queryWrapper);
        List<BzQxBzsqVO> voList = new ArrayList<>();
        List<BzQxBzsq> records = page.getRecords();
        if (CollUtil.isEmpty(records)) {
            return PageUtil.change(page, voList);
        }
        Map<String, String> ztType = dictCodeService.findMapByType(DictConstant.BZ_HBZT);
        records.forEach(et -> {
            BzQxBzsqVO vo = bzQxBzsqConverter.entity2Vo(et);
            vo.setStatusName(ztType.get(vo.getStatus()));
            voList.add(vo);
        });
        return PageUtil.change(page, voList);
    }

    @Override
    public Result<Boolean> revoke(String id) {
        LambdaUpdateWrapper<BzQxBzsq> updateWrapper = new LambdaUpdateWrapper<>(BzQxBzsq.class);
        updateWrapper.eq(BzQxBzsq::getId, id);
        updateWrapper.set(BzQxBzsq::getStatus, "3");
        boolean update = this.update(updateWrapper);
        return update ? Result.ok() : Result.fail();
    }

    @Override
    public Result<Boolean> audit(BzQxBzsqAuditDTO dto) {
        LambdaUpdateWrapper<BzQxBzsq> updateWrapper = new LambdaUpdateWrapper<>(BzQxBzsq.class);
        updateWrapper.eq(BzQxBzsq::getId, dto.getId());
        updateWrapper.set(BzQxBzsq::getStatus, dto.getStatus());
        updateWrapper.set(BzQxBzsq::getCheckReason, dto.getCheckReason());
        updateWrapper.set(BzQxBzsq::getCheckTime, new Date());
        updateWrapper.set(BzQxBzsq::getCheckUserId, RequestHolder.getCurrUser().getUserId());
        boolean update = this.update(updateWrapper);
        return update ? Result.ok() : Result.fail();
    }

    @Override
    public String downReply(String id) {
        SysFileInfo sysFileInfo = sysFileInfoService.findByBizIdAndRemark(id, DOWN_REPLY);
        if (Objects.isNull(sysFileInfo)) {
            return this.downLoadReply(id);
        } else if (!new File(sysFileInfo.getFilePath()).exists()) {
            sysFileInfoService.deleteByBizId(id);
            return this.downLoadReply(id);
        }
        return sysFileInfo.getId();
    }

    private String downLoadReply(String id) {
        String replyPath = SpringConstant.TEMPLATES_PATH + REPLY_PATH;
        SysFileInfo sysFileInfo;

        BzBzsqDownVO dwonVO = bzQxBzsqMapper.downReplyInfo(id);
        if (Objects.isNull(dwonVO)) {
            throw new FileException("审批未完成，不能下载批复函！");
        }
        dwonVO.setAuditUnitName("中共重庆市委机构编制委员会办公室");
        Map<String, Object> resultMap = new HashMap<>();
        resultMap.put("approvalNum", Objects.nonNull(dwonVO.getApprovalNum()) ? dwonVO.getApprovalNum() : "");
        resultMap.put("applyUnitName", Objects.nonNull(dwonVO.getApplyUnitName()) ? dwonVO.getApplyUnitName() : "");
        resultMap.put("useBzUnitName", Objects.nonNull(dwonVO.getUseBzUnitName()) ? dwonVO.getUseBzUnitName() : "");
        resultMap.put("applyNum", Objects.nonNull(dwonVO.getApplyNum()) ? dwonVO.getApplyNum() : "");
        resultMap.put("auditUnitName", Objects.nonNull(dwonVO.getAuditUnitName()) ? dwonVO.getAuditUnitName() : "");
        String name = dwonVO.getUseBzUnitName() + "批复函.docx";
        File downFile = sysFileInfoService.getDownPath(name);
        try (FileOutputStream fout = new FileOutputStream(downFile)) {
            XWPFDocument document = WordExportUtil.exportWord07(replyPath, resultMap);
            // 生成文件
            document.write(fout);
            sysFileInfo = sysFileInfoService.saveDownFile(downFile, id, name, DOWN_REPLY);
        } catch (Exception e) {
            throw new FileException("批复函下载异常", e);
        }
        return sysFileInfo.getId();
    }
}
