package com.zenith.bbykz.service.bz.rybd;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.write.metadata.WriteSheet;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.efficient.common.result.Result;
import com.efficient.common.result.ResultEnum;
import com.efficient.configs.util.PageUtil;
import com.efficient.file.api.SysFileInfoService;
import com.efficient.file.model.entity.SysFileInfo;
import com.efficient.system.api.DictCodeService;
import com.zenith.bbykz.api.bz.rybd.BzRyBdService;
import com.zenith.bbykz.api.bz.rybd.BzUnitBdService;
import com.zenith.bbykz.common.constant.DictConstant;
import com.zenith.bbykz.dao.bz.rybd.BzUnitBdMapper;
import com.zenith.bbykz.model.converter.bz.rybd.BzRyBdConverter;
import com.zenith.bbykz.model.converter.bz.rybd.BzUnitBdConverter;
import com.zenith.bbykz.model.dto.bz.rybd.BzRyBdDTO;
import com.zenith.bbykz.model.dto.bz.rybd.BzUnitBdDTO;
import com.zenith.bbykz.model.dto.bz.rybd.BzUnitBdExportDTO;
import com.zenith.bbykz.model.dto.bz.rybd.BzUnitBdListDTO;
import com.zenith.bbykz.model.easy.excel.model.BzRyBdExcel;
import com.zenith.bbykz.model.entity.bz.rybd.BzRyBd;
import com.zenith.bbykz.model.entity.bz.rybd.BzUnitBd;
import com.zenith.bbykz.model.vo.bz.rybd.BzRyBdVO;
import com.zenith.bbykz.model.vo.bz.rybd.BzUnitBdVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.ResourceUtils;

import java.io.File;
import java.util.*;

/**
 * <p>
 * 核编管理-动态信息维护 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @date 2024-04-22 11:21:43
 */
@Service
public class BzUnitBdServiceImpl extends ServiceImpl<BzUnitBdMapper, BzUnitBd> implements BzUnitBdService {

    @Autowired
    private BzUnitBdConverter bzUnitBdConverter;
    @Autowired
    private BzUnitBdMapper bzUnitBdMapper;
    @Autowired
    private BzRyBdService bzRyBdService;
    @Autowired
    private BzRyBdConverter bzRyBdConverter;
    @Autowired
    private DictCodeService dictCodeService;
    @Autowired
    private SysFileInfoService sysFileInfoService;

    public static void main(String[] args) {
        List<BzRyBdExcel> list = new ArrayList<>();
        BzRyBdExcel excel = new BzRyBdExcel();
        excel.setName("1");
        excel.setIdCard("2");
        excel.setBdTypeName("3");
        excel.setOldUnitName("2");
        excel.setOldDeptName("");
        excel.setOldPostName("");
        excel.setOldBzTypeName("");
        excel.setOldYbTypeName("");
        excel.setNewUnitName("");
        excel.setNewDeptName("");
        excel.setNewPostName("");
        excel.setNewBzTypeName("");
        excel.setNewYbTypeName("");
        excel.setBdTime(new Date());
        excel.setRemark("");

        list.add(excel);
        String im = "F:\\work\\project\\learn\\bb_ykz\\src\\main\\resources\\templates\\bz\\bz_rybzbd_export.xlsx";
        String export = "F:\\bb_yzk\\upload\\temp\\2024\\05\\07\\text.xlsx";
        // ExcelWriter excelWriter1 = EasyExcel.write(export).withTemplate(im).build();
        // try (ExcelWriter excelWriter =  EasyExcel.write(export).withTemplate(im).build())  {
        //     WriteSheet writeSheet = EasyExcel.writerSheet().build();
        //     excelWriter.fill(list,writeSheet);
        // }catch (Exception e){
        //
        // } finally {
        //
        // }
        try (ExcelWriter excelWriter = EasyExcel.write(export).withTemplate(im).build()) {
            WriteSheet writeSheet = EasyExcel.writerSheet().build();
            excelWriter.fill(list, writeSheet);
        }
        // ExcelWriter excelWriter = EasyExcel.write("F:\\bb_yzk\\upload\\temp\\2024\\05\\07\\text.xlsx")
        //         .withTemplate(im)
        //         .build();

        // .doFill(list);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Result<BzUnitBd> save(BzUnitBdDTO dto) {
        BzUnitBd entity = bzUnitBdConverter.dto2Entity(dto);
        boolean flag = this.save(entity);
        List<BzRyBdDTO> rybdList = dto.getRybdList();
        bzRyBdService.saveByBizId(entity.getId(), rybdList);
        return Result.ok(entity);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Result<BzUnitBdVO> findById(String id, String pageType) {
        BzUnitBd entity = this.getById(id);
        if (Objects.isNull(entity)) {
            return Result.build(ResultEnum.DATA_NOT_EXIST);
        }
        BzUnitBdVO vo = bzUnitBdConverter.entity2Vo(entity);
        List<BzRyBd> listByBizId = bzRyBdService.findListByBizId(id);
        List<BzRyBdVO> voList = new ArrayList<>();
        Map<String, String> unitBdType = dictCodeService.findMapByType(DictConstant.BZ_UNIT_BD_TYPE);
        Map<String, String> rybdType = dictCodeService.findMapByType(DictConstant.BZ_RY_BD_TYPE);
        Map<String, String> bzlxType = dictCodeService.findMapByType(DictConstant.BZ_BZLX);
        Map<String, String> yblxType = dictCodeService.findMapByType(DictConstant.BZ_JFXX);
        listByBizId.forEach(et -> {
            if (Objects.equals(pageType, "1")) {
                // 上编
                if (!StrUtil.equals(et.getBdType(), "01")) {
                    return;
                }
            } else if (Objects.equals(pageType, "2")) {
                // 调整
                if (!StrUtil.equals(et.getBdType(), "0301")) {
                    return;
                }
            } else if (Objects.equals(pageType, "3")) {
                // 下编
                if (!StrUtil.equals(et.getBdType(), "02")) {
                    return;
                }
            }
            BzRyBdVO bzRyBdVO = bzRyBdConverter.entity2Vo(et);
            bzRyBdVO.setBdTypeName(rybdType.get(bzRyBdVO.getBdType()));
            bzRyBdVO.setOldBzTypeName(bzlxType.get(bzRyBdVO.getOldBzType()));
            bzRyBdVO.setOldYbTypeName(yblxType.get(bzRyBdVO.getOldYbType()));
            bzRyBdVO.setNewBzTypeName(bzlxType.get(bzRyBdVO.getNewBzType()));
            bzRyBdVO.setNewYbTypeName(yblxType.get(bzRyBdVO.getNewYbType()));
            voList.add(bzRyBdVO);
        });
        vo.setBdTypeName(unitBdType.get(vo.getBdType()));
        vo.setRybdList(voList);
        return Result.ok(vo);
    }

    @Override
    public Result<Boolean> update(BzUnitBdDTO dto) {
        BzUnitBd entity = bzUnitBdConverter.dto2Entity(dto);
        List<BzRyBdDTO> rybdList = dto.getRybdList();
        bzRyBdService.saveByBizId(entity.getId(), rybdList);
        boolean flag = this.updateById(entity);
        return flag ? Result.ok() : Result.fail();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Result<Boolean> delete(String id) {
        boolean flag = this.removeById(id);
        bzRyBdService.removeByBizId(id);
        return flag ? Result.ok() : Result.fail();
    }

    @Override
    public Page<BzUnitBdVO> list(BzUnitBdListDTO dto) {
        LambdaQueryWrapper<BzUnitBd> queryWrapper = new LambdaQueryWrapper<>(BzUnitBd.class);
        String unitLevelCode = dto.getUnitLevelCode();
        String keyword = dto.getKeyword();
        if (StrUtil.isNotBlank(keyword)) {
            queryWrapper.like(BzUnitBd::getTitle, keyword);
        }
        queryWrapper.likeRight(BzUnitBd::getUnitLevelCode, unitLevelCode);
        queryWrapper.orderByDesc(BzUnitBd::getCreateTime);
        Map<String, String> unitBdType = dictCodeService.findMapByType(DictConstant.BZ_UNIT_BD_TYPE);
        final Page<BzUnitBd> page = bzUnitBdMapper.selectPage(new Page<>(dto.getPageNum(), dto.getPageSize()), queryWrapper);
        List<BzUnitBdVO> voList = new ArrayList<>();
        List<BzUnitBd> records = page.getRecords();
        if (CollUtil.isEmpty(records)) {
            return PageUtil.change(page, voList);
        }
        records.forEach(et -> {
            BzUnitBdVO vo = bzUnitBdConverter.entity2Vo(et);
            vo.setBdTypeName(unitBdType.get(vo.getBdType()));
            voList.add(vo);
        });
        return PageUtil.change(page, voList);
    }

    @Override
    public Page<BzUnitBdVO> bdList(BzUnitBdListDTO dto) {
        Page<BzUnitBdVO> voPage = new Page<>(dto.getPageNum(), dto.getPageSize());
        Page<BzUnitBdVO> resultPage = bzUnitBdMapper.bdList(voPage, dto);
        Map<String, String> unitBdType = dictCodeService.findMapByType(DictConstant.BZ_UNIT_BD_TYPE);
        List<BzUnitBdVO> records = resultPage.getRecords();
        if (CollUtil.isEmpty(records)) {
            return resultPage;
        }
        records.forEach(et -> {
            et.setBdTypeName(unitBdType.get(et.getBdType()));
        });
        return resultPage;
    }

    @Override
    public Result<String> exportRybd(BzUnitBdExportDTO dto) throws Exception {
        List<BzRyBdExcel> excelList = new ArrayList<>();
        List<String> idList = dto.getIdList();
        List<BzRyBd> bzRyBdList = bzRyBdService.findListByBizIdList(idList);
        Map<String, String> rybdType = dictCodeService.findMapByType(DictConstant.BZ_RY_BD_TYPE);
        Map<String, String> bzlxType = dictCodeService.findMapByType(DictConstant.BZ_BZLX);
        Map<String, String> yblxType = dictCodeService.findMapByType(DictConstant.BZ_JFXX);
        String pageType = dto.getPageType();

        bzRyBdList.forEach(et -> {
            if (Objects.equals(pageType, "1")) {
                // 上编
                if (!StrUtil.equals(et.getBdType(), "01")) {
                    return;
                }
            } else if (Objects.equals(pageType, "2")) {
                // 调整
                if (!StrUtil.equals(et.getBdType(), "0301")) {
                    return;
                }
            } else if (Objects.equals(pageType, "3")) {
                // 下编
                if (!StrUtil.equals(et.getBdType(), "02")) {
                    return;
                }
            }
            BzRyBdExcel bzRyBdExcel = bzRyBdConverter.entity2Excel(et);
            bzRyBdExcel.setBdTypeName(rybdType.get(et.getBdType()));
            bzRyBdExcel.setOldBzTypeName(bzlxType.get(et.getOldBzType()));
            bzRyBdExcel.setOldYbTypeName(yblxType.get(et.getOldYbType()));
            bzRyBdExcel.setNewBzTypeName(bzlxType.get(et.getNewBzType()));
            bzRyBdExcel.setNewYbTypeName(yblxType.get(et.getNewYbType()));
            excelList.add(bzRyBdExcel);
        });
        String prefix = StrUtil.equals("1", pageType) ? "上编人员" : StrUtil.equals("2", pageType) ? "人员调整" : "人员下编";
        String fileName = prefix + "编制变动数据包" + DateUtil.format(new Date(), "yyyyMMdd") + ".xlsx";
        File downFile = sysFileInfoService.getDownPath("编制变动数据包.xlsx");
        String basePath = ResourceUtils.getURL(ResourceUtils.CLASSPATH_URL_PREFIX).getPath() + "templates/bz/bz_rybzbd_export.xlsx";
        try (ExcelWriter excelWriter = EasyExcel.write(downFile.getAbsolutePath()).withTemplate(basePath).build()) {
            WriteSheet writeSheet = EasyExcel.writerSheet().build();
            excelWriter.fill(excelList, writeSheet);
        }
        SysFileInfo sysFileInfo = sysFileInfoService.saveDownFile(downFile, "temp", fileName, "temp");
        return Result.ok(sysFileInfo.getId());
    }
}
