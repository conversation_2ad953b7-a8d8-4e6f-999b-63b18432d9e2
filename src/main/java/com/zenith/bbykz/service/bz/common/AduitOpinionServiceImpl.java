package com.zenith.bbykz.service.bz.common;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.efficient.common.auth.RequestHolder;
import com.efficient.common.result.Result;
import com.efficient.common.result.ResultEnum;
import com.efficient.configs.util.PageUtil;
import com.zenith.bbykz.api.bz.common.AduitOpinionService;
import com.zenith.bbykz.dao.bz.common.AduitOpinionMapper;
import com.zenith.bbykz.model.converter.bz.common.AduitOpinionConverter;
import com.zenith.bbykz.model.dto.bz.common.AduitOpinionDTO;
import com.zenith.bbykz.model.dto.bz.common.AduitOpinionListDTO;
import com.zenith.bbykz.model.entity.bz.common.AduitOpinion;
import com.zenith.bbykz.model.vo.bz.common.AduitOpinionVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

/**
 * <p>
 * 核编管理-常用审批意见 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @date 2024-05-17 11:10:37
 */
@Service
public class AduitOpinionServiceImpl extends ServiceImpl<AduitOpinionMapper, AduitOpinion> implements AduitOpinionService {

    @Autowired
    private AduitOpinionConverter aduitOpinionConverter;
    @Autowired
    private AduitOpinionMapper aduitOpinionMapper;

    @Override
    public Result<AduitOpinion> save(AduitOpinionDTO dto) {
        AduitOpinion entity = aduitOpinionConverter.dto2Entity(dto);
        boolean flag = this.save(entity);
        return Result.ok(entity);
    }

    @Override
    public Result<AduitOpinionVO> findById(String id) {
        AduitOpinion entity = this.getById(id);
        if (Objects.isNull(entity)) {
            return Result.build(ResultEnum.DATA_NOT_EXIST);
        }
        AduitOpinionVO vo = aduitOpinionConverter.entity2Vo(entity);
        return Result.ok(vo);
    }

    @Override
    public Result<Boolean> update(AduitOpinionDTO dto) {
        AduitOpinion entity = aduitOpinionConverter.dto2Entity(dto);
        boolean flag = this.updateById(entity);
        return flag ? Result.ok() : Result.fail();
    }

    @Override
    public Result<Boolean> delete(String id) {
        boolean flag = this.removeById(id);
        return flag ? Result.ok() : Result.fail();
    }

    @Override
    public Page<AduitOpinionVO> list(AduitOpinionListDTO dto) {
        LambdaQueryWrapper<AduitOpinion> queryWrapper = new LambdaQueryWrapper<>(AduitOpinion.class);
        queryWrapper.eq(AduitOpinion::getCreateUser, RequestHolder.getCurrUser().getUserId());

        queryWrapper.orderByAsc(AduitOpinion::getSort).orderByDesc(AduitOpinion::getCreateTime);
        final Page<AduitOpinion> page = aduitOpinionMapper.selectPage(new Page<>(dto.getPageNum(), dto.getPageSize()), queryWrapper);
        List<AduitOpinionVO> voList = new ArrayList<>();
        List<AduitOpinion> records = page.getRecords();
        if (CollUtil.isEmpty(records)) {
            return PageUtil.change(page, voList);
        }
        records.forEach(et -> {
            AduitOpinionVO vo = aduitOpinionConverter.entity2Vo(et);
            voList.add(vo);
        });
        return PageUtil.change(page, voList);
    }
}
