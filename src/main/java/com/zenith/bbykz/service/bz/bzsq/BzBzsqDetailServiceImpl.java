package com.zenith.bbykz.service.bz.bzsq;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.efficient.common.result.Result;
import com.efficient.file.api.SysFileInfoService;
import com.efficient.file.model.entity.SysFileInfo;
import com.efficient.system.api.DictCodeService;
import com.zenith.bbykz.api.OrgInfoService;
import com.zenith.bbykz.api.bz.bzsq.BzBzsqDetailService;
import com.zenith.bbykz.common.constant.DictConstant;
import com.zenith.bbykz.dao.bz.bzsq.BzBzsqDetailMapper;
import com.zenith.bbykz.model.converter.bz.bzsq.BzBzsqDetailConverter;
import com.zenith.bbykz.model.dto.bz.bzsq.BzBzsqDetailDTO;
import com.zenith.bbykz.model.dto.bz.bzsq.BzBzsqDetailListDTO;
import com.zenith.bbykz.model.entity.bz.bzsq.BzBzsqDetail;
import com.zenith.bbykz.model.vo.OrgInfoVO;
import com.zenith.bbykz.model.vo.bz.bzsq.BzBzsqDetailVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <p>
 * 核编管理-用编申请-明细 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @date 2024-03-29 16:05:19
 */
@Service
public class BzBzsqDetailServiceImpl extends ServiceImpl<BzBzsqDetailMapper, BzBzsqDetail> implements BzBzsqDetailService {

    @Autowired
    private BzBzsqDetailConverter bzBzsqDetailConverter;
    @Autowired
    private BzBzsqDetailMapper bzBzsqDetailMapper;
    @Autowired
    private SysFileInfoService sysFileInfoService;
    @Autowired
    private DictCodeService dictCodeService;
    @Autowired
    private OrgInfoService orgInfoService;

    @Override
    public Result<Boolean> deleteByBizId(String bizId) {
        bzBzsqDetailMapper.deleteByBizId(bizId);
        return Result.ok();
    }

    @Override
    public List<BzBzsqDetail> list(BzBzsqDetailListDTO dto) {
        LambdaQueryWrapper<BzBzsqDetail> queryWrapper = new LambdaQueryWrapper<>(BzBzsqDetail.class);
        queryWrapper.eq(BzBzsqDetail::getBzsqId, dto.getBizId());
        if (Objects.nonNull(dto.getIsBack())) {
            queryWrapper.eq(BzBzsqDetail::getIsBack, dto.getIsBack());
        }

        return this.list(queryWrapper);
    }

    @Override
    public void saveListByBizId(String bizId, List<BzBzsqDetailDTO> detailList) {
        if (CollUtil.isEmpty(detailList)) {
            return;
        }
        BzBzsqDetailListDTO dto = new BzBzsqDetailListDTO();
        dto.setBizId(bizId);
        List<BzBzsqDetail> oldDetails = this.list(dto);
        List<String> newIdList = detailList.stream().map(BzBzsqDetailDTO::getId).collect(Collectors.toList());
        List<String> deleteIdList = oldDetails.stream().map(BzBzsqDetail::getId).filter(et -> !newIdList.contains(et)).collect(Collectors.toList());
        for (BzBzsqDetailDTO detailDTO : detailList) {
            BzBzsqDetail entity = bzBzsqDetailConverter.dto2Entity(detailDTO);
            String useBzUnitId = detailDTO.getUseBzUnitId();
            entity.setUseBzUnitId(useBzUnitId);
            OrgInfoVO orgInfoVO = orgInfoService.findById(useBzUnitId);
            if (Objects.nonNull(orgInfoVO)) {
                entity.setUseBzUnitName(orgInfoVO.getName());
                entity.setUseBzUnitLevelCode(orgInfoVO.getLevelCode());
            }
            entity.setBzsqId(bizId);
            this.saveOrUpdate(entity);
            sysFileInfoService.saveIdListByBizId(detailDTO.getFileIdList(), entity.getId());
        }

        if (CollUtil.isNotEmpty(deleteIdList)) {
            this.removeBatchByIds(detailList);
        }
    }

    @Override
    public List<BzBzsqDetailVO> getDetailListByBizId(String bizId, Integer isBack) {
        BzBzsqDetailListDTO dto = new BzBzsqDetailListDTO();
        dto.setBizId(bizId);
        dto.setIsBack(isBack);
        List<BzBzsqDetail> oldDetails = this.list(dto);
        List<BzBzsqDetailVO> result = new ArrayList<>();
        Map<String, String> bzlx = dictCodeService.findMapByType(DictConstant.BZ_BZLX);
        Map<String, String> jfxx = dictCodeService.findMapByType(DictConstant.BZ_YBLX);

        for (BzBzsqDetail oldDetail : oldDetails) {
            BzBzsqDetailVO vo = bzBzsqDetailConverter.entity2Vo(oldDetail);
            vo.setBzTypeName(bzlx.get(vo.getBzType()));
            vo.setYbTypeName(jfxx.get(vo.getYbType()));
            List<SysFileInfo> fileInfoList = sysFileInfoService.findByBizId(oldDetail.getId());
            vo.setFileInfoList(fileInfoList);
            result.add(vo);
        }
        return result;
    }
}
