package com.zenith.bbykz.service.bz.qx;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.efficient.common.constant.DbConstant;
import com.efficient.common.result.Result;
import com.efficient.common.result.ResultEnum;
import com.efficient.configs.util.PageUtil;
import com.zenith.bbykz.api.bz.qx.BzQxBzsqDetailService;
import com.zenith.bbykz.dao.bz.qx.BzQxBzsqDetailMapper;
import com.zenith.bbykz.model.converter.bz.qx.BzQxBzsqDetailConverter;
import com.zenith.bbykz.model.dto.bz.qx.BzQxBzsqDetailDTO;
import com.zenith.bbykz.model.dto.bz.qx.BzQxBzsqDetailListDTO;
import com.zenith.bbykz.model.entity.bz.qx.BzQxBzsqDetail;
import com.zenith.bbykz.model.vo.bz.qx.BzQxBzsqDetailVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <p>
 * 核编管理-用编管理-区县用编申请计划明细 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @date 2024-05-06 17:05:40
 */
@Service
public class BzQxBzsqDetailServiceImpl extends ServiceImpl<BzQxBzsqDetailMapper, BzQxBzsqDetail> implements BzQxBzsqDetailService {

    @Autowired
    private BzQxBzsqDetailConverter bzQxBzsqDetailConverter;
    @Autowired
    private BzQxBzsqDetailMapper bzQxBzsqDetailMapper;

    @Override
    public Result<BzQxBzsqDetail> save(BzQxBzsqDetailDTO dto) {
        BzQxBzsqDetail entity = bzQxBzsqDetailConverter.dto2Entity(dto);
        boolean flag = this.save(entity);
        return Result.ok(entity);
    }

    @Override
    public Result<BzQxBzsqDetailVO> findById(String id) {
        BzQxBzsqDetail entity = this.getById(id);
        if (Objects.isNull(entity)) {
            return Result.build(ResultEnum.DATA_NOT_EXIST);
        }
        BzQxBzsqDetailVO vo = bzQxBzsqDetailConverter.entity2Vo(entity);
        return Result.ok(vo);
    }

    @Override
    public Result<Boolean> update(BzQxBzsqDetailDTO dto) {
        BzQxBzsqDetail entity = bzQxBzsqDetailConverter.dto2Entity(dto);
        boolean flag = this.updateById(entity);
        return flag ? Result.ok() : Result.fail();
    }

    @Override
    public Result<Boolean> delete(String id) {
        boolean flag = this.removeById(id);
        return flag ? Result.ok() : Result.fail();
    }

    @Override
    public Page<BzQxBzsqDetailVO> list(BzQxBzsqDetailListDTO dto) {
        LambdaQueryWrapper<BzQxBzsqDetail> queryWrapper = new LambdaQueryWrapper<>(BzQxBzsqDetail.class);
        final Page<BzQxBzsqDetail> page = bzQxBzsqDetailMapper.selectPage(new Page<>(dto.getPageNum(), dto.getPageSize()), queryWrapper);
        List<BzQxBzsqDetailVO> voList = new ArrayList<>();
        List<BzQxBzsqDetail> records = page.getRecords();
        if (CollUtil.isEmpty(records)) {
            return PageUtil.change(page, voList);
        }
        records.forEach(et -> {
            BzQxBzsqDetailVO vo = bzQxBzsqDetailConverter.entity2Vo(et);
            voList.add(vo);
        });
        return PageUtil.change(page, voList);
    }

    @Override
    public void saveByBizId(String bizId, List<BzQxBzsqDetailDTO> detailList) {
        if (CollUtil.isEmpty(detailList)) {
            return;
        }

        List<BzQxBzsqDetail> oldDetails = this.listByBiz(bizId);
        List<String> newIdList = detailList.stream().map(BzQxBzsqDetailDTO::getId).collect(Collectors.toList());
        List<String> deleteIdList = oldDetails.stream().map(BzQxBzsqDetail::getId).filter(et -> !newIdList.contains(et)).collect(Collectors.toList());

        List<BzQxBzsqDetail> saveList = new ArrayList<>();
        for (BzQxBzsqDetailDTO detailDTO : detailList) {
            BzQxBzsqDetail entity = bzQxBzsqDetailConverter.dto2Entity(detailDTO);
            entity.setQxBzsqId(bizId);
            saveList.add(entity);
        }

        if (CollUtil.isNotEmpty(saveList)) {
            this.saveOrUpdateBatch(saveList, DbConstant.BATCH_LITTLE_SIZE);
        }
        if (CollUtil.isNotEmpty(deleteIdList)) {
            this.removeBatchByIds(detailList);
        }

    }

    @Override
    public List<BzQxBzsqDetail> listByBiz(String bizId) {
        LambdaQueryWrapper<BzQxBzsqDetail> queryWrapper = new LambdaQueryWrapper<>(BzQxBzsqDetail.class);
        queryWrapper.eq(BzQxBzsqDetail::getQxBzsqId, bizId);
        return this.list(queryWrapper);
    }

    @Override
    public void deleteByBizId(String bizId) {
        bzQxBzsqDetailMapper.deleteByBizId(bizId);
    }
}
