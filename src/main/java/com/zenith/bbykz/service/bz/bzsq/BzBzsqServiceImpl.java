package com.zenith.bbykz.service.bz.bzsq;

import cn.afterturn.easypoi.word.WordExportUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.efficient.common.auth.RequestHolder;
import com.efficient.common.constant.CommonConstant;
import com.efficient.common.result.Result;
import com.efficient.configs.constant.SpringConstant;
import com.efficient.configs.util.PageUtil;
import com.efficient.file.api.SysFileInfoService;
import com.efficient.file.exception.FileException;
import com.efficient.file.model.entity.SysFileInfo;
import com.efficient.system.api.DictCodeService;
import com.zenith.bbykz.api.OrgInfoService;
import com.zenith.bbykz.api.SysUserService;
import com.zenith.bbykz.api.bz.bzsq.BzBzsqDetailService;
import com.zenith.bbykz.api.bz.bzsq.BzBzsqFlowService;
import com.zenith.bbykz.api.bz.bzsq.BzBzsqService;
import com.zenith.bbykz.common.constant.DictConstant;
import com.zenith.bbykz.common.utils.IncreasingSequenceUtil;
import com.zenith.bbykz.dao.bz.bzsq.BzBzsqMapper;
import com.zenith.bbykz.model.base.UserInfo;
import com.zenith.bbykz.model.converter.bz.bzsq.BzBzsqConverter;
import com.zenith.bbykz.model.dto.bz.bzsq.BzBzsqDTO;
import com.zenith.bbykz.model.dto.bz.bzsq.BzBzsqDetailDTO;
import com.zenith.bbykz.model.dto.bz.bzsq.BzBzsqListDTO;
import com.zenith.bbykz.model.entity.OrgInfo;
import com.zenith.bbykz.model.entity.bz.bzsq.BzBzsq;
import com.zenith.bbykz.model.vo.LoginSystem;
import com.zenith.bbykz.model.vo.bz.bzsq.BzBzsqDetailVO;
import com.zenith.bbykz.model.vo.bz.bzsq.BzBzsqDownVO;
import com.zenith.bbykz.model.vo.bz.bzsq.BzBzsqMergeWordVO;
import com.zenith.bbykz.model.vo.bz.bzsq.BzBzsqVO;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.xwpf.usermodel.XWPFDocument;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.io.File;
import java.io.FileOutputStream;
import java.util.*;
import java.util.concurrent.atomic.AtomicInteger;

import static com.zenith.bbykz.common.constant.BbCommonConstant.CQ_GEOCODE;

/**
 * <p>
 * 核编管理-用编申请 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @date 2024-03-29 16:05:19
 */
@Service
@Slf4j
public class BzBzsqServiceImpl extends ServiceImpl<BzBzsqMapper, BzBzsq> implements BzBzsqService {

    public static final String DOWN_REPLY = "downReply";
    public static final String REPLY_PATH = "bz/repliesWord.docx";
    @Autowired
    private BzBzsqConverter bzBzsqConverter;
    @Autowired
    private BzBzsqMapper bzBzsqMapper;
    @Autowired
    private BzBzsqDetailService bzBzsqDetailService;
    @Autowired
    private DictCodeService dictCodeService;
    @Autowired
    private OrgInfoService orgInfoService;
    @Autowired
    private BzBzsqFlowService bzBzsqFlowService;
    @Autowired
    private SysFileInfoService sysFileInfoService;
    @Autowired
    private SysUserService sysUserService;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Result<BzBzsq> save(BzBzsqDTO dto) {
        OrgInfo orgInfo = orgInfoService.getById(dto.getOrgLevelCode());
        dto.setOrgId(orgInfo.getId());
        dto.setOrgLevelCode(orgInfo.getLevelCode());
        BzBzsq entity = bzBzsqConverter.dto2Entity(dto);
        UserInfo currUser = (UserInfo) RequestHolder.getCurrUser();
        LoginSystem loginSystem = currUser.getLoginSystem(RequestHolder.getCurrSystemId());
        entity.setIsBb(loginSystem.getIsBbUser());
        entity.setGeocode(loginSystem.getGeocode());
        entity.setApprovalNum(IncreasingSequenceUtil.createNum(entity.getGeocode()));
        boolean flag = this.save(entity);
        List<BzBzsqDetailDTO> detailList = dto.getDetailList();
        bzBzsqDetailService.saveListByBizId(entity.getId(), detailList);
        return Result.ok(entity);
    }

    @Override
    public Result<BzBzsqVO> findById(String id, Integer isBack) {
        BzBzsq entity = this.getById(id);
        BzBzsqVO vo = this.getVo(entity, isBack);
        OrgInfo orgInfo = orgInfoService.getById(vo.getUseBzUnitId());
        Optional.ofNullable(orgInfo).ifPresent(et -> {
            vo.setUseBzUnitLevelCode(et.getLevelCode());
        });

        return Result.ok(vo);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Result<Boolean> update(BzBzsqDTO dto) {
        BzBzsq entity = bzBzsqConverter.dto2Entity(dto);
        boolean flag = this.updateById(entity);
        List<BzBzsqDetailDTO> detailList = dto.getDetailList();
        bzBzsqDetailService.saveListByBizId(entity.getId(), detailList);
        return flag ? Result.ok() : Result.fail();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Result<Boolean> delete(String id) {
        boolean flag = this.removeById(id);
        bzBzsqDetailService.deleteByBizId(id);
        return flag ? Result.ok() : Result.fail();
    }

    @Override
    public Page<BzBzsqVO> list(BzBzsqListDTO dto) {
        LambdaQueryWrapper<BzBzsq> queryWrapper = new LambdaQueryWrapper<>(BzBzsq.class);

        String title = dto.getTitle();
        if (StrUtil.isNotBlank(title)) {
            queryWrapper.like(BzBzsq::getTitle, title);
        }
        Integer pageType = dto.getPageType();
        if (Objects.equals(pageType, 1)) {
            // queryWrapper.likeRight(BzBzsq::getOrgLevelCode, getUserLevelCode());
        } else if (Objects.equals(pageType, 2)) {
            UserInfo currUser = (UserInfo) RequestHolder.getCurrUser();
            queryWrapper.notIn(BzBzsq::getStatus, "1", "3", "7");
            String geocode = currUser.getCurrLoginSystem().getGeocode();

            if (StrUtil.equals(geocode, CQ_GEOCODE)) {
                queryWrapper.eq(BzBzsq::getIsBb, CommonConstant.TRUE_INT);
            } else {
                queryWrapper.eq(BzBzsq::getGeocode, geocode);
            }
        }

        String applyUnitName = dto.getApplyUnitName();
        if (StrUtil.isNotBlank(applyUnitName)) {
            queryWrapper.like(BzBzsq::getApplyUnitName, applyUnitName);
        }

        String status = dto.getStatus();
        if (StrUtil.isNotBlank(status)) {
            queryWrapper.eq(BzBzsq::getStatus, status);
        }
        String unitLevelCode = dto.getUnitLevelCode();
        if (StrUtil.isNotBlank(unitLevelCode)) {
            queryWrapper.likeRight(BzBzsq::getOrgLevelCode, unitLevelCode);
        }

        Date startTime = dto.getStartTime();
        if (Objects.nonNull(startTime)) {
            queryWrapper.ge(BzBzsq::getApplyTime, DateUtil.beginOfDay(startTime));
        }

        Date endTime = dto.getEndTime();
        if (Objects.nonNull(endTime)) {
            queryWrapper.le(BzBzsq::getApplyTime, DateUtil.beginOfDay(endTime));
        }
        // queryWrapper.orderByDesc(BzBzsq::getApplyTime);
        queryWrapper.orderByDesc(BzBzsq::getCreateTime);
        final Page<BzBzsq> page = bzBzsqMapper.selectPage(new Page<>(dto.getPageNum(), dto.getPageSize()), queryWrapper);
        List<BzBzsq> records = page.getRecords();
        if (CollUtil.isEmpty(records)) {
            return PageUtil.change(page, null);
        }
        List<BzBzsqVO> result = new ArrayList<>();
        Map<String, String> ztMap = dictCodeService.findMapByType(DictConstant.BZ_HBZT);
        for (BzBzsq record : records) {
            BzBzsqVO bzBzsqVO = bzBzsqConverter.entity2Vo(record);
            bzBzsqVO.setStatusName(ztMap.get(bzBzsqVO.getStatus()));
            result.add(bzBzsqVO);
        }

        return PageUtil.change(page, result);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Result changeStatus(String id, String status) {
        BzBzsq bzBzsq = this.getById(id);
        bzBzsq.setStatus(status);
        boolean updatedById = this.updateById(bzBzsq);
        bzBzsqFlowService.saveByBizId(id, status);
        return updatedById ? Result.ok() : Result.fail();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Result approve(BzBzsqDTO dto) {
        Integer isAgree = dto.getIsAgree();
        String status = "";
        if (Objects.equals(isAgree, CommonConstant.TRUE_INT)) {
            // 同意
            status = "5";
            long count = dto.getDetailList().stream().filter(et -> Objects.equals(et.getIsBack(), CommonConstant.TRUE_INT)).count();
            if (count > 0) {
                status = "8";
            }
        } else {
            // 拒绝
            status = "6";
        }

        dto.setStatus(status);
        bzBzsqFlowService.saveByBizId(dto.getId(), status);
        return this.update(dto);
    }

    @Override
    public List<BzBzsqVO> listByMergeId(String mergeId) {
        LambdaQueryWrapper<BzBzsq> queryWrapper = new LambdaQueryWrapper<>(BzBzsq.class);
        // queryWrapper.select(BzBzsq::getId);
        queryWrapper.eq(BzBzsq::getMergeId, mergeId);
        queryWrapper.orderByDesc(BzBzsq::getApplyTime);
        List<BzBzsq> list = this.list(queryWrapper);
        List<BzBzsqVO> voList = new ArrayList<>();
        list.forEach(et -> {
            BzBzsqVO vo = this.getVo(et, null);
            voList.add(vo);
        });

        return voList;
    }

    private BzBzsqVO getVo(BzBzsq entity, Integer isBack) {
        BzBzsqVO vo = bzBzsqConverter.entity2Vo(entity);
        String status = entity.getStatus();
        if (StrUtil.equalsAny(status, "5", "8")) {
            vo.setIsAgree(1);
        } else if (StrUtil.equals(status, "6")) {
            vo.setIsAgree(0);
        }
        Map<String, String> ztMap = dictCodeService.findMapByType(DictConstant.BZ_HBZT);
        vo.setStatusName(ztMap.get(vo.getStatus()));

        vo.setDetailList(bzBzsqDetailService.getDetailListByBizId(entity.getId(), isBack));
        return vo;
    }

    @Override
    public String downReply(String id) {
        SysFileInfo sysFileInfo = sysFileInfoService.findByBizIdAndRemark(id, DOWN_REPLY);
        if (Objects.isNull(sysFileInfo)) {
            return this.downLoadReply(id);
        } else if (!new File(sysFileInfo.getFilePath()).exists()) {
            sysFileInfoService.deleteByBizId(id);
            return this.downLoadReply(id);
        }
        return sysFileInfo.getId();
    }

    private String downLoadReply(String id) {
        String replyPath = SpringConstant.TEMPLATES_PATH + REPLY_PATH;
        String name;
        SysFileInfo sysFileInfo;

        BzBzsqDownVO dwonVO = bzBzsqMapper.downReplyInfo(id);
        if (Objects.isNull(dwonVO)) {
            log.warn("审批未完成，不能下载批复函！");
            // throw new FileException("审批未完成，不能下载批复函！");
            dwonVO = new BzBzsqDownVO();
        }
        OrgInfo orgInfo = sysUserService.findBbUnitByUserId(dwonVO.getAuditUserId(), RequestHolder.getCurrSystemId());
        BzBzsqDownVO finalDwonVO = dwonVO;
        Optional.ofNullable(orgInfo).ifPresent(et -> {
            finalDwonVO.setAuditUnitName(orgInfo.getName());
        });
        if (Objects.nonNull(dwonVO.getUseBzUnitName())) {
            name = dwonVO.getUseBzUnitName() + "批复函.docx";
        } else {
            name = dwonVO.getApplyUnitName() + "批复函.docx";
        }
        Map<String, Object> resultMap = new HashMap<>();
        resultMap.put("approvalNum", Objects.nonNull(dwonVO.getApprovalNum()) ? dwonVO.getApprovalNum() : "");
        resultMap.put("applyUnitName", Objects.nonNull(dwonVO.getApplyUnitName()) ? dwonVO.getApplyUnitName() : "");
        resultMap.put("useBzUnitName", Objects.nonNull(dwonVO.getUseBzUnitName()) ? dwonVO.getUseBzUnitName() : "");
        resultMap.put("applyNum", Objects.nonNull(dwonVO.getApplyNum()) ? dwonVO.getApplyNum() : "");
        resultMap.put("auditUnitName", Objects.nonNull(dwonVO.getAuditUnitName()) ? dwonVO.getAuditUnitName() : "");
        File downFile = sysFileInfoService.getDownPath(name);
        try (FileOutputStream fout = new FileOutputStream(downFile)) {
            XWPFDocument document = WordExportUtil.exportWord07(replyPath, resultMap);
            // 生成文件
            document.write(fout);
            sysFileInfo = sysFileInfoService.saveDownFile(downFile, id, name, DOWN_REPLY);
        } catch (Exception e) {
            throw new FileException("批复函下载异常", e);
        }
        return sysFileInfo.getId();
    }

    @Override
    public String downAudit(String id) {
        Result<BzBzsqVO> bzsqVOResult = this.findById(id, null);
        List<BzBzsqMergeWordVO> wordVOList = new ArrayList<>();
        BzBzsqVO data = bzsqVOResult.getData();
        Date createTime = data.getCreateTime();
        AtomicInteger count = new AtomicInteger(1);
        List<BzBzsqDetailVO> bzBzsqList = data.getDetailList();
        bzBzsqList.forEach(et -> {
            BzBzsqMergeWordVO vo = new BzBzsqMergeWordVO();
            vo.setSort(String.valueOf(count.getAndIncrement()));
            vo.setApplyUnitName(data.getApplyUnitName());
            vo.setUseBzUnitName(et.getUseBzUnitName());
            vo.setBzTypeName(et.getBzTypeName());
            vo.setYbTypeName(et.getYbTypeName());
            vo.setDsbNum(String.valueOf(et.getDsbNum()));
            vo.setZbNum(String.valueOf(et.getZbNum()));
            vo.setYbNum(String.valueOf(et.getYbNum()));
            vo.setApplyNum(String.valueOf(et.getApplyNum()));
            vo.setRemark(et.getRemark());
            wordVOList.add(vo);
        });

        Map<String, Object> dataMap = new HashMap<>();
        dataMap.put("createTime", DateUtil.formatDate(createTime));
        dataMap.put("remark", Objects.isNull(data.getRemark()) ? "" : data.getRemark());
        dataMap.put("dataList", wordVOList);
        String tempName = "/bz/bz_aduit_export.docx";
        String tempPath = SpringConstant.TEMPLATES_PATH + tempName;
        SysFileInfo sysFileInfo = null;
        String filename = data.getTitle() + ".docx";
        File downFile = sysFileInfoService.getDownPath(filename);
        try (FileOutputStream fout = new FileOutputStream(downFile)) {
            XWPFDocument document = WordExportUtil.exportWord07(tempPath, dataMap);
            // 生成文件
            document.write(fout);
            sysFileInfo = sysFileInfoService.saveDownFile(downFile, id, filename, tempName);
        } catch (Exception e) {
            throw new FileException("编制使用核准审批单", e);
        }
        return sysFileInfo.getId();
    }
}
