package com.zenith.bbykz.service.bz.common;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.efficient.common.result.Result;
import com.efficient.common.result.ResultEnum;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.zenith.bbykz.api.bz.common.SystemcodeService;
import com.zenith.bbykz.model.converter.bz.common.SystemcodeConverter;
import com.zenith.bbykz.dao.bz.common.SystemcodeMapper;
import com.zenith.bbykz.model.dto.bz.common.SystemcodeDTO;
import com.zenith.bbykz.model.dto.bz.common.SystemcodeListDTO;
import com.zenith.bbykz.model.entity.bz.common.Systemcode;
import com.zenith.bbykz.model.vo.bz.common.SystemcodeVO;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.Objects;
import java.util.ArrayList;
import java.util.List;

/**
* <p>
* SYSTEMCODE 服务实现类
* </p>
*
* <AUTHOR>
* @date 2024-08-27 15:52:58
*/
@Service
public class SystemcodeServiceImpl extends ServiceImpl<SystemcodeMapper, Systemcode> implements SystemcodeService {

    @Autowired
    private SystemcodeConverter systemcodeConverter;
    @Autowired
    private SystemcodeMapper systemcodeMapper;



    @Override
    public List<Systemcode> getAll() {
        LambdaQueryWrapper<Systemcode> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.orderByAsc(Systemcode::getSystemCodeType);
        queryWrapper.orderByAsc(Systemcode::getSystemCodeOrder);
        return this.list(queryWrapper);
    }
}
