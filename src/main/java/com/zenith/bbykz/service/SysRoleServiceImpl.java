package com.zenith.bbykz.service;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.efficient.common.auth.RequestHolder;
import com.efficient.common.constant.CommonConstant;
import com.efficient.common.result.Result;
import com.zenith.bbykz.api.*;
import com.zenith.bbykz.common.result.BbResultEnum;
import com.zenith.bbykz.dao.SysRoleMapper;
import com.zenith.bbykz.model.base.UserInfo;
import com.zenith.bbykz.model.converter.SysRoleConverter;
import com.zenith.bbykz.model.dto.SysRoleDTO;
import com.zenith.bbykz.model.dto.SysRoleListDTO;
import com.zenith.bbykz.model.entity.SysRole;
import com.zenith.bbykz.model.entity.SysRoleMenu;
import com.zenith.bbykz.model.entity.SysUser;
import com.zenith.bbykz.model.entity.SysUserRole;
import com.zenith.bbykz.model.vo.SysMenuVO;
import com.zenith.bbykz.model.vo.SysRoleVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <p>
 * sys_role 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @date 2023-09-14 17:14:26
 */
@Service
public class SysRoleServiceImpl extends ServiceImpl<SysRoleMapper, SysRole> implements SysRoleService {

    @Autowired
    private SysRoleConverter sysRoleConverter;
    @Autowired
    private SysRoleMapper sysRoleMapper;
    @Autowired
    private SysRoleMenuService sysRoleMenuService;
    @Autowired
    private SysUserRoleService sysUserRoleService;
    @Autowired
    private SysMenuService sysMenuService;
    @Autowired
    private SysUserService sysUserService;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Result save(SysRoleDTO dto) {
        // 查询角色名称是否重名
        // LambdaQueryWrapper<SysRole> wrapper = new LambdaQueryWrapper<>();
        // wrapper.eq(SysRole::getName, dto.getName());
        // SysRole one = this.getOne(wrapper);
        //
        // if (ObjectUtil.isNotNull(one)) {
        //     return Result.fail(BbResultEnum.THE_ROLE_NAME_IS_REPEAT.getCode(), BbResultEnum.THE_ROLE_NAME_IS_REPEAT.getMsg(), null);
        // }
        SysRole entity = sysRoleConverter.dto2Entity(dto);
        UserInfo ticket = (UserInfo) RequestHolder.getCurrUser();
        entity.setCreateTime(new Date());
        entity.setCreateUser(ticket.getUserId());
        entity.setSystemCode(RequestHolder.getCurrSystemId());
        boolean flag = this.save(entity);
        if (CollUtil.isNotEmpty(dto.getMenus())) {
            dto.setId(entity.getId());
            boolean b = sysRoleMenuService.saveMenus(dto);
            return flag && b ? Result.ok(entity) : Result.fail();
        }
        return flag ? Result.ok(entity) : Result.fail();
    }

    @Override
    public SysRoleVO findById(String id) {
        SysRole entity = this.getById(id);
        List<SysMenuVO> menuVOS = baseMapper.findMenuListByRoleId(id, entity.getSystemCode());
        SysRoleVO sysRoleVO = sysRoleConverter.entity2Vo(entity);
        List<String> menus = menuVOS.stream().map(SysMenuVO::getCode).collect(Collectors.toList());
        sysRoleVO.setMenus(menus);
        return sysRoleVO;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Result update(SysRoleDTO dto) {
        LambdaQueryWrapper<SysRole> wrapper1 = new LambdaQueryWrapper<>();
        wrapper1.eq(SysRole::getName, dto.getName());
        wrapper1.eq(SysRole::getSystemCode, RequestHolder.getCurrSystemId());
        wrapper1.ne(SysRole::getId, dto.getId());
        SysRole one = this.getOne(wrapper1);

        if (ObjectUtil.isNotNull(one)) {
            return Result.fail(BbResultEnum.THE_ROLE_NAME_IS_REPEAT.getCode(), BbResultEnum.THE_ROLE_NAME_IS_REPEAT.getMsg(), null);
        }
        boolean u = this.updateById(sysRoleConverter.dto2Entity(dto));
        LambdaQueryWrapper<SysRoleMenu> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(SysRoleMenu::getRoleId, dto.getId());
        // 删除用户角色关联表后再新增
        sysRoleMenuService.remove(wrapper);
        if (CollUtil.isNotEmpty(dto.getMenus())) {
            boolean b = sysRoleMenuService.saveMenus(dto);
            return u && b ? Result.ok() : Result.fail();
        }
        return u ? Result.ok() : Result.fail();
    }

    @Override
    public Boolean delete(String id) {
        return this.removeById(id);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Result deleteRole(String id) {
        // 删除角色时校核是否存在关联数据
        //List<SysRoleMenu> roleMenus = sysRoleMenuService.list(new LambdaQueryWrapper<SysRoleMenu>().eq(SysRoleMenu::getRoleId, id));
        List<SysUserRole> userRoles = sysUserRoleService.getList(id);
        if (CollUtil.isNotEmpty(userRoles)) {
            return Result.fail(BbResultEnum.THE_ROLE_IS_USED.getCode(), BbResultEnum.THE_ROLE_IS_USED.getMsg(), null);
        }
        boolean b = this.removeById(id);
        return b ? Result.ok() : Result.fail();
    }

    @Override
    public Page<SysRole> list(SysRoleListDTO dto) {
        LambdaQueryWrapper<SysRole> wrapper = new LambdaQueryWrapper<>();
        if (StrUtil.isNotEmpty(dto.getName())) {
            wrapper.like(SysRole::getName, "%" + dto.getName() + "%");
        }
        String systemId = RequestHolder.getCurrSystemId();
        dto.setSystemCode(systemId);
        if (StrUtil.isNotEmpty(dto.getSystemCode())) {
            wrapper.eq(SysRole::getSystemCode, dto.getSystemCode());
        }
        wrapper.orderByAsc(SysRole::getId);

        // 编办管理员角色，排除系统管理员角色权限
        // UserInfo currUser = (UserInfo) RequestHolder.getCurrUser();
        // SysUser sysUser = sysUserService.getById(currUser.getUserId());
        // Integer isBuiltin = sysUser.getIsBuiltin();
        // if (Objects.equals(isBuiltin, CommonConstant.TRUE_INT)) {
        //     wrapper.and(qw -> qw.eq(SysRole::getCreateUser, currUser.getUserId()).or().isNull(SysRole::getCreateUser));
        // } else {
        //     wrapper.eq(SysRole::getCreateUser, currUser.getUserId());
        // }

        final Page<SysRole> page = sysRoleMapper.selectPage(new Page<>(dto.getPageNum(), dto.getPageSize()), wrapper);
        return page;
    }
}
