package com.zenith.bbykz.service;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.zenith.bbykz.api.SysUserRoleService;
import com.zenith.bbykz.dao.SysUserRoleMapper;
import com.zenith.bbykz.model.converter.SysUserRoleConverter;
import com.zenith.bbykz.model.dto.SysUserRoleDTO;
import com.zenith.bbykz.model.dto.SysUserRoleListDTO;
import com.zenith.bbykz.model.entity.SysUserRole;
import com.zenith.bbykz.model.vo.SysUserRoleVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <p>
 * sys_user_role 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @date 2023-09-14 17:14:26
 */
@Service
public class SysUserRoleServiceImpl extends ServiceImpl<SysUserRoleMapper, SysUserRole> implements SysUserRoleService {

    @Autowired
    private SysUserRoleConverter sysUserRoleConverter;
    @Autowired
    private SysUserRoleMapper sysUserRoleMapper;

    @Override
    public SysUserRole save(SysUserRoleDTO dto) {
        SysUserRole entity = sysUserRoleConverter.dto2Entity(dto);
        boolean flag = this.save(entity);
        return entity;
    }

    @Override
    public SysUserRoleVO findById(String id) {
        SysUserRole entity = this.getById(id);
        return sysUserRoleConverter.entity2Vo(entity);
    }

    @Override
    public Boolean update(SysUserRoleDTO dto) {
        return this.updateById(sysUserRoleConverter.dto2Entity(dto));
    }

    @Override
    public Boolean delete(String id) {
        return this.removeById(id);
    }

    @Override
    public Page<SysUserRole> list(SysUserRoleListDTO dto) {
        final Page<SysUserRole> page = sysUserRoleMapper.selectPage(new Page<>(dto.getPageNum(), dto.getPageSize()), new QueryWrapper<>());
        return page;
    }

    @Override
    public List<SysUserRole> findByUserId(String id, String systemId) {
        LambdaQueryWrapper<SysUserRole> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(SysUserRole::getUserId, id);
        wrapper.eq(SysUserRole::getSystemCode, systemId);
        wrapper.orderByAsc(SysUserRole::getRoleId);
        return this.list(wrapper);
    }

    @Override
    public List<SysUserRole> getByIsDefault(Integer isDefault) {
        return sysUserRoleMapper.getByIsDefault(isDefault);
    }

    @Override
    public List<SysUserRole> getList(String id) {
        return sysUserRoleMapper.getList(id);
    }

    @Override
    public List<SysUserRole> findByUserSystemId(String userId,String systemId) {
        LambdaQueryWrapper<SysUserRole> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(SysUserRole::getSystemCode, systemId);
        wrapper.eq(SysUserRole::getUserId, userId);
        wrapper.orderByAsc(SysUserRole::getRoleId);
        return this.list(wrapper);
    }
}
