package com.zenith.bbykz.service.register.train;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.efficient.common.constant.DbConstant;
import com.efficient.common.result.Result;
import com.efficient.configs.util.PageUtil;
import com.zenith.bbykz.api.register.train.RegisterTrainCourseService;
import com.zenith.bbykz.api.register.train.RegisterTrainCourseUserService;
import com.zenith.bbykz.api.register.train.RegisterTrainUserInfoService;
import com.zenith.bbykz.dao.register.train.RegisterTrainUserInfoMapper;
import com.zenith.bbykz.model.converter.register.train.RegisterTrainUserInfoConverter;
import com.zenith.bbykz.model.dto.register.train.RegisterTrainUserInfoDTO;
import com.zenith.bbykz.model.dto.register.train.RegisterTrainUserInfoListDTO;
import com.zenith.bbykz.model.entity.register.train.RegisterTrainCourse;
import com.zenith.bbykz.model.entity.register.train.RegisterTrainUserInfo;
import com.zenith.bbykz.model.vo.register.train.RegisterTrainUserInfoVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <p>
 * 登记信息-培训管理-学习进度 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @date 2024-03-06 14:39:04
 */
@Service
public class RegisterTrainUserInfoServiceImpl extends ServiceImpl<RegisterTrainUserInfoMapper, RegisterTrainUserInfo> implements RegisterTrainUserInfoService {

    @Autowired
    private RegisterTrainUserInfoConverter registerTrainUserInfoConverter;
    @Autowired
    private RegisterTrainUserInfoMapper registerTrainUserInfoMapper;
    @Autowired
    private RegisterTrainCourseService registerTrainCourseService;
    @Autowired
    private RegisterTrainCourseUserService registerTrainCourseUserService;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Result<RegisterTrainUserInfo> save(RegisterTrainUserInfoDTO dto) {
        RegisterTrainUserInfo registerTrainUserInfo = this.findByUserIdAndCourseId(dto.getUserId(), dto.getCourseId(), dto.getCourseDetailId(), dto.getTrainFileId(), dto.getFileId());
        BigDecimal progress = Objects.isNull(dto.getProgress()) ? new BigDecimal(0) : dto.getProgress();
        int comparisonResult = progress.compareTo(new BigDecimal("1"));
        int isFinish = comparisonResult >= 0 ? 1 : 0;
        if (Objects.isNull(registerTrainUserInfo)) {
            registerTrainUserInfo = registerTrainUserInfoConverter.dto2Entity(dto);
            registerTrainUserInfo.setIsFinish(isFinish);
            boolean flag = this.save(registerTrainUserInfo);
        } else {
            registerTrainUserInfo.setMins(dto.getMins());
            registerTrainUserInfo.setViewMins(dto.getViewMins());
            registerTrainUserInfo.setProgress(progress);
            registerTrainUserInfo.setIsFinish(isFinish);
            this.updateById(registerTrainUserInfo);
        }
        // 更新总进度条
        this.syncCourseUser(dto.getUserId(), dto.getCourseId());
        return Result.ok(registerTrainUserInfo);
    }

    @Override
    public Result<RegisterTrainUserInfoVO> findById(String id) {
        RegisterTrainUserInfo entity = this.getById(id);
        RegisterTrainUserInfoVO vo = registerTrainUserInfoConverter.entity2Vo(entity);
        return Result.ok(vo);
    }

    @Override
    public Result<Boolean> update(RegisterTrainUserInfoDTO dto) {
        boolean flag = this.updateById(registerTrainUserInfoConverter.dto2Entity(dto));
        return flag ? Result.ok() : Result.fail();
    }

    @Override
    public Result<Boolean> delete(String id) {
        boolean flag = this.removeById(id);
        return flag ? Result.ok() : Result.fail();
    }

    @Override
    public Page<RegisterTrainUserInfoVO> list(RegisterTrainUserInfoListDTO dto) {
        LambdaQueryWrapper<RegisterTrainUserInfo> queryWrapper = new LambdaQueryWrapper<>(RegisterTrainUserInfo.class);
        queryWrapper.eq(RegisterTrainUserInfo::getUserId, dto.getUserId());
        queryWrapper.eq(Objects.nonNull(dto.getIsFinish()), RegisterTrainUserInfo::getIsFinish, dto.getIsFinish());
        queryWrapper.eq(Objects.nonNull(dto.getIsAttention()), RegisterTrainUserInfo::getIsAttention, dto.getIsFinish());
        queryWrapper.orderByAsc(RegisterTrainUserInfo::getUpdateTime);
        final Page<RegisterTrainUserInfo> page = registerTrainUserInfoMapper.selectPage(new Page<>(dto.getPageNum(), dto.getPageSize()), queryWrapper);

        List<RegisterTrainUserInfo> records = page.getRecords();
        if (CollUtil.isEmpty(records)) {
            return PageUtil.change(page, null);
        }
        List<String> courseIdList = records.stream().map(RegisterTrainUserInfo::getCourseId).collect(Collectors.toList());
        List<RegisterTrainCourse> registerTrainCourses = registerTrainCourseService.listByIds(courseIdList);
        Map<String, String> collect = registerTrainCourses.stream().collect(Collectors.toMap(RegisterTrainCourse::getId, RegisterTrainCourse::getName, (k1, k2) -> k1));

        List<RegisterTrainUserInfoVO> voList = new ArrayList<>();
        records.forEach(et -> {
            RegisterTrainUserInfoVO vo = registerTrainUserInfoConverter.entity2Vo(et);
            vo.setCourseName(collect.get(vo.getCourseId()));
            voList.add(vo);
        });

        return PageUtil.change(page, voList);
    }

    @Override
    public RegisterTrainUserInfo findByUserIdAndCourseId(String userId, String courseId, String courseDetailId, String trainFileId, String fileId) {
        LambdaQueryWrapper<RegisterTrainUserInfo> queryWrapper = new LambdaQueryWrapper<>(RegisterTrainUserInfo.class);
        queryWrapper.eq(RegisterTrainUserInfo::getUserId, userId);
        queryWrapper.eq(RegisterTrainUserInfo::getCourseId, courseId);
        queryWrapper.eq(RegisterTrainUserInfo::getCourseDetailId, courseDetailId);
        queryWrapper.eq(RegisterTrainUserInfo::getTrainFileId, trainFileId);
        queryWrapper.eq(RegisterTrainUserInfo::getFileId, fileId);
        queryWrapper.last(DbConstant.LIMIT_ONE);

        return this.getOne(queryWrapper);
    }

    @Override
    public List<RegisterTrainUserInfo> findCourseUserInfo(String userId, String courseId) {
        LambdaQueryWrapper<RegisterTrainUserInfo> queryWrapper = new LambdaQueryWrapper<>(RegisterTrainUserInfo.class);
        queryWrapper.eq(RegisterTrainUserInfo::getUserId, userId);
        queryWrapper.eq(RegisterTrainUserInfo::getCourseId, courseId);
        queryWrapper.orderByAsc(RegisterTrainUserInfo::getId);
        return this.list(queryWrapper);
    }

    @Override
    public void syncCourseUser(String userId, String courseId) {
        List<RegisterTrainUserInfo> courseUserInfo = findCourseUserInfo(userId, courseId);
        int size = courseUserInfo.size();
        BigDecimal sum = BigDecimal.ZERO;
        for (RegisterTrainUserInfo userInfo : courseUserInfo) {
            if (userInfo.getProgress() != null) {
                sum = sum.add(userInfo.getProgress());
            }
        }
        BigDecimal average = size > 0 ? sum.divide(BigDecimal.valueOf(size), 2, RoundingMode.HALF_UP) : BigDecimal.ZERO;
        registerTrainCourseUserService.updateByUserIdAndCourseId(userId, courseId, average);
    }
}
