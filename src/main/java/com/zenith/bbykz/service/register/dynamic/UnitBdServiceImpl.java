package com.zenith.bbykz.service.register.dynamic;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.efficient.common.auth.RequestHolder;
import com.efficient.common.constant.CommonConstant;
import com.efficient.common.result.Result;
import com.efficient.common.result.ResultEnum;
import com.efficient.common.util.IdUtil;
import com.efficient.configs.util.PageUtil;
import com.efficient.system.api.DictCodeService;
import com.zenith.bbykz.api.OrgInfoService;
import com.zenith.bbykz.api.SysUserService;
import com.zenith.bbykz.api.efficient.SysNotifyService;
import com.zenith.bbykz.api.publicInstitutions.LegalPersonService;
import com.zenith.bbykz.api.publicInstitutions.UnifiedSocialCreditCodeService;
import com.zenith.bbykz.api.register.dynamic.UnitBdFlowService;
import com.zenith.bbykz.api.register.dynamic.UnitBdService;
import com.zenith.bbykz.common.constant.DictConstant;
import com.zenith.bbykz.common.enums.NotifyState;
import com.zenith.bbykz.common.enums.efficient.NotifyRecipientSystemEnum;
import com.zenith.bbykz.common.enums.efficient.NotifyTypeEnum;
import com.zenith.bbykz.dao.efficient.SysNotifyMapper;
import com.zenith.bbykz.dao.register.dynamic.UnitBdMapper;
import com.zenith.bbykz.model.base.UserInfo;
import com.zenith.bbykz.model.converter.efficient.SysNotifyConverter;
import com.zenith.bbykz.model.converter.efficient.SysNotifyConverterImpl;
import com.zenith.bbykz.model.converter.register.dynamic.UnitBdConverter;
import com.zenith.bbykz.model.dto.efficient.SysNotifyDTO;
import com.zenith.bbykz.model.dto.register.dynamic.UnitBdDTO;
import com.zenith.bbykz.model.dto.register.dynamic.UnitBdFlowDTO;
import com.zenith.bbykz.model.dto.register.dynamic.UnitBdListDTO;
import com.zenith.bbykz.model.entity.LegalPersonRegistration;
import com.zenith.bbykz.model.entity.OrgInfo;
import com.zenith.bbykz.model.entity.SysUser;
import com.zenith.bbykz.model.entity.UnifiedSocialCreditCode;
import com.zenith.bbykz.model.entity.efficient.SysNotify;
import com.zenith.bbykz.model.entity.register.dynamic.UnitBd;
import com.zenith.bbykz.model.entity.register.dynamic.UnitBdFlow;
import com.zenith.bbykz.model.vo.register.dynamic.UnitBdFlowVO;
import com.zenith.bbykz.model.vo.register.dynamic.UnitBdVO;
import com.zenith.bbykz.model.vo.register.dynamic.UnitBdWarnVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;

/**
 * <p>
 * 登记信息-预警感知-信息变动 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @date 2024-04-09 11:50:41
 */
@Service
public class UnitBdServiceImpl extends ServiceImpl<UnitBdMapper, UnitBd> implements UnitBdService {

    @Autowired
    private UnitBdConverter unitBdConverter;
    @Autowired
    private UnitBdMapper unitBdMapper;
    @Autowired
    private OrgInfoService orgInfoService;
    @Autowired
    private DictCodeService dictCodeService;
    @Autowired
    private SysNotifyService sysNotifyService;
    @Autowired
    private SysUserService sysUserService;
    @Autowired
    private UnitBdFlowService unitBdFlowService;
    @Autowired
    private SysNotifyConverter sysNotifyConverter;
    @Autowired
    private LegalPersonService legalPersonService;
    @Autowired
    private UnifiedSocialCreditCodeService unifiedSocialCreditCodeService;
    @Autowired
    private SysNotifyMapper sysNotifyMapper;
    @Autowired
    private SysNotifyConverterImpl sysNotifyConverterImpl;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Result<UnitBd> save(UnitBdDTO dto) {
        dto.setId(null);
        UnitBd entity = unitBdConverter.dto2Entity(dto);
        entity.setSourceType(CommonConstant.FALSE_INT);
        entity.setSourceName("本系统");
        OrgInfo orgInfo = orgInfoService.getById(dto.getUnitId());
        Optional.ofNullable(orgInfo).ifPresent(et -> {
            entity.setGeocode(et.getGeocode());
            entity.setCreditCode(et.getCreditCode());
            entity.setUnitSource(et.getUnitSource());
        });
        entity.setState(NotifyState.YGZ.getCode());
        boolean flag = this.save(entity);
        unitBdFlowService.saveByBizId(entity.getId(), NotifyState.YGZ);
        return Result.ok(entity);
    }

    @Override
    public Result<UnitBdVO> findById(String id) {
        UnitBd entity = this.getById(id);
        UnitBdVO vo = unitBdConverter.entity2Vo(entity);
        Map<String, String> mapByType = dictCodeService.findMapByType(DictConstant.JGSY_BD_TYPE);
        vo.setTypeName(mapByType.get(vo.getType()));
        return Result.ok(vo);
    }

    @Override
    public Result<Boolean> update(UnitBdDTO dto) {
        boolean flag = this.updateById(unitBdConverter.dto2Entity(dto));
        return flag ? Result.ok() : Result.fail();
    }

    @Override
    public Result<Boolean> delete(String id) {
        boolean flag = this.removeById(id);
        return flag ? Result.ok() : Result.fail();
    }

    @Override
    public Page<UnitBdVO> list(UnitBdListDTO dto) {
        Map<String, String> mapByType = dictCodeService.findMapByType(DictConstant.JGSY_BD_TYPE);
        LambdaQueryWrapper<UnitBd> queryWrapper = new LambdaQueryWrapper<>(UnitBd.class);
        String orgLevelCode = dto.getOrgLevelCode();
        queryWrapper.likeRight(UnitBd::getUnitLevelCode, orgLevelCode);
        Date startDate = dto.getStartDate();
        Date endDate = dto.getEndDate();
        if (Objects.nonNull(startDate)) {
            queryWrapper.ge(UnitBd::getBdTime, DateUtil.beginOfDay(startDate));
        }
        if (Objects.nonNull(endDate)) {
            queryWrapper.le(UnitBd::getBdTime, DateUtil.endOfDay(endDate));
        }
        String type = dto.getType();
        if (StrUtil.isNotBlank(type)) {
            queryWrapper.eq(UnitBd::getType, type);
        }
        String creditCode = dto.getCreditCode();
        if (StrUtil.isNotBlank(creditCode)) {
            queryWrapper.eq(UnitBd::getCreditCode, creditCode)
                    .eq(UnitBd::getState, "5");
        }
        queryWrapper.orderByDesc(UnitBd::getCreateTime);
        final Page<UnitBd> page = unitBdMapper.selectPage(new Page<>(dto.getPageNum(), dto.getPageSize()), queryWrapper);
        List<UnitBdVO> voList = new ArrayList<>();
        List<UnitBd> records = page.getRecords();
        if (CollUtil.isEmpty(records)) {
            return PageUtil.change(page, voList);
        }
        records.forEach(et -> {
            UnitBdVO unitBdVO = unitBdConverter.entity2Vo(et);
            unitBdVO.setTypeName(mapByType.get(unitBdVO.getType()));
            voList.add(unitBdVO);
        });
        return PageUtil.change(page, voList);
    }

    @Override
    public Page<UnitBdWarnVO> warnList(UnitBdListDTO dto) {
        Map<String, String> mapByType = dictCodeService.findMapByType(DictConstant.JGSY_BD_TYPE);
        LambdaQueryWrapper<UnitBd> queryWrapper = new LambdaQueryWrapper<>(UnitBd.class);
        String orgLevelCode = dto.getOrgLevelCode();
        queryWrapper.likeRight(UnitBd::getUnitLevelCode, orgLevelCode);
        Date startDate = dto.getStartDate();
        Date endDate = dto.getEndDate();
        String keyword = dto.getKeyword();
        if (StrUtil.isNotBlank(keyword)) {
            queryWrapper.and(qw -> qw.like(UnitBd::getUnitName, keyword).or().like(UnitBd::getOldInfo, keyword).or().like(UnitBd::getNewInfo, keyword));
        }
        if (Objects.nonNull(startDate)) {
            queryWrapper.ge(UnitBd::getBdTime, DateUtil.beginOfDay(startDate));
        }
        if (Objects.nonNull(endDate)) {
            queryWrapper.le(UnitBd::getBdTime, DateUtil.endOfDay(endDate));
        }
        UserInfo currUser = (UserInfo) RequestHolder.getCurrUser();
        String type = dto.getType();
        if (StrUtil.isNotBlank(type)) {
            queryWrapper.eq(UnitBd::getType, type);
        }


        Integer isBbUser = currUser.getCurrLoginSystem().getIsBbUser();
        if (!Objects.equals(isBbUser, CommonConstant.TRUE_INT)) {
            queryWrapper.eq(UnitBd::getIsSend, CommonConstant.TRUE_INT);
        }
        Integer pageType = dto.getPageType();
        if (Objects.equals(pageType, 1)) {
            queryWrapper.ne(UnitBd::getState, NotifyState.YWC.getCode());
            queryWrapper.orderByDesc(UnitBd::getCreateTime);
        } else {
            queryWrapper.eq(UnitBd::getState, NotifyState.YWC.getCode());
            queryWrapper.orderByDesc(UnitBd::getBackTime).orderByDesc(UnitBd::getCreateTime);
        }

        final Page<UnitBd> page = unitBdMapper.selectPage(new Page<>(dto.getPageNum(), dto.getPageSize()), queryWrapper);
        List<UnitBdWarnVO> voList = new ArrayList<>();
        List<UnitBd> records = page.getRecords();
        if (CollUtil.isEmpty(records)) {
            return PageUtil.change(page, voList);
        }
        records.forEach(et -> {
            UnitBdWarnVO unitBdVO = unitBdConverter.entity2Warn(et);
            unitBdVO.setTypeName(mapByType.get(unitBdVO.getType()));
            unitBdVO.setState(et.getState());
            unitBdVO.setStateName(NotifyState.getNameByCode(et.getState()));
            voList.add(unitBdVO);
        });
        return PageUtil.change(page, voList);
    }

    @Override
    public Result sendMsg(UnitBdDTO dto) {
        String unitId = dto.getUnitId();
        String id = dto.getId();
        List<SysNotify> sysNotifyList = sysNotifyService.findListByBizId(id);
        if (CollUtil.isNotEmpty(sysNotifyList)) {
            return Result.fail("消息已发送，请不要重复发送！");
        }
        OrgInfo orgInfo = orgInfoService.getById(unitId);
        List<SysUser> sendUserList = new ArrayList<>();
        List<SysUser> ssdwList = sysUserService.findBySzdwId(unitId);
        // List<SysUser> bbList = sysUserService.findBbUserByGeoCode(orgInfo.getGeocode());
        if (CollUtil.isNotEmpty(ssdwList)) {
            sendUserList.addAll(ssdwList);
        }
        // if(CollUtil.isNotEmpty(bbList)){
        //     sendUserList.addAll(bbList);
        // }
        if (CollUtil.isEmpty(sendUserList)) {
            return Result.fail("该单位下未绑定用户无法发送消息！");
        }
        UnitBd unitBd = this.getById(id);
        Map<String, String> mapByType = dictCodeService.findMapByType(DictConstant.JGSY_BD_TYPE);
        String batchNum = IdUtil.generateBatchNumber();
        sysNotifyList = new ArrayList<>();
        String userId = RequestHolder.getCurrUser().getUserId();
        UserInfo userInfo = (UserInfo) RequestHolder.getCurrUser();
        SysUser currUser = userInfo.getSysUser();
        String currSystemId = RequestHolder.getCurrSystemId();
        OrgInfo currOrgInfo = orgInfoService.getById(currUser.getSsdwId());
        for (SysUser sysUser : sendUserList) {
            SysNotifyDTO notifyDTO = new SysNotifyDTO();
            notifyDTO.setBizId(id);
            notifyDTO.setSystemId(currSystemId);
            notifyDTO.setNotifyType(NotifyTypeEnum.PTXX.getCode());
            notifyDTO.setRecipientUserId(sysUser.getId());
            notifyDTO.setCreateUserId(userId);
            OrgInfo orgInfoFor = orgInfoService.getById(sysUser.getSsdwId());
            if (Objects.nonNull(orgInfoFor)) {
                notifyDTO.setRecipientUnitId(orgInfoFor.getId());
                notifyDTO.setRecipientUnitName(orgInfoFor.getName());
            }
            if (Objects.nonNull(currOrgInfo)) {
                notifyDTO.setCreateUnitId(currOrgInfo.getId());
                notifyDTO.setCreateUnitName(currOrgInfo.getName());
            }
            // notifyDTO.setRecipientZwddId(sysUser.getZwddId());
            String title = "经数据变动预警告知，单位(" + orgInfo.getName() + ")信息已发生变动（" + mapByType.get(dto.getType()) + "），请尽快办理！";
            notifyDTO.setTitle(title);
            notifyDTO.setState("2");
            // notifyDTO.setPcUrl(dto.getPcUrl());
            // notifyDTO.setAppUrl(notifyDTO.getPcUrl());
            notifyDTO.setMenuId(dto.getMenuId());
            notifyDTO.setBizType(dto.getBizType());
            String content = "经数据变动预警告知，单位(" + orgInfo.getName() + ")信息已发生变动,变动类型：%s，原信息：%s，现信息：%s，请尽快办理！";
            notifyDTO.setContent(String.format(content, mapByType.get(dto.getType()), dto.getOldInfo(), dto.getNewInfo()));
            notifyDTO.setBatchNum(batchNum);
            notifyDTO.setRecipientSystemType(NotifyRecipientSystemEnum.OWN.getCode());
            sysNotifyList.add(sysNotifyConverter.dto2Entity(notifyDTO));
        }
        sysNotifyService.saveBatch(sysNotifyList);
        unitBd.setIsSend(CommonConstant.TRUE_INT);
        unitBd.setState(NotifyState.YFS.getCode());
        this.updateById(unitBd);
        unitBdFlowService.saveByBizId(id, NotifyState.YFS);

        return Result.ok();
    }

    @Override
    public Result<List<UnitBdFlowVO>> findFlow(String id) {
        List<UnitBdFlow> list = unitBdFlowService.findByBizId(id);
        List<UnitBdFlowVO> resultList = new ArrayList<>();
        list.forEach(et -> {
            UnitBdFlowVO vo1 = new UnitBdFlowVO();
            vo1.setCode(et.getCode());
            vo1.setDate(et.getCreateTime());
            resultList.add(vo1);
        });
        return Result.ok(resultList);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Result changeState(UnitBdFlowDTO dto) {
        String bizId = dto.getId();
        UnitBd unitBd = this.getById(bizId);
        if (StrUtil.equals(dto.getState(), NotifyState.YCL.getCode())) {
            if (Objects.isNull(dto.getBackContent()) || Objects.isNull(dto.getBackTime())) {
                return Result.build(ResultEnum.PARA_ERROR);
            }
            unitBd.setBackContent(dto.getBackContent());
            unitBd.setBackTime(dto.getBackTime());
            unitBdFlowService.saveByBizId(bizId, NotifyState.YCL);
        } else if (StrUtil.equals(dto.getState(), NotifyState.YWC.getCode())) {
            unitBdFlowService.saveByBizId(bizId, NotifyState.YWC);
            unitBd.setRemark(dto.getRemark());
            unitBd.setBackTime(new Date());
            // 已完成，进行数据变更
            // String unitSource = unitBd.getUnitSource();
            // String creditCode = unitBd.getCreditCode();
            // String type = unitBd.getType();
            // String newInfo = unitBd.getNewInfo();
            // if (StrUtil.equals(unitSource, "1")) {
            //     LegalPersonRegistration entity = legalPersonService.findEntityByCreditCode(creditCode);
            //     if (StrUtil.equals(type, "0201")) {
            //         //  名称
            //         entity.setName(newInfo);
            //     } else if (StrUtil.equals(type, "0205")) {
            //         // 法人变动
            //         entity.setLegalRepresentative(newInfo);
            //     }
            //     legalPersonService.updateById(entity);
            // } else if (StrUtil.equals(unitSource, "2")) {
            //     UnifiedSocialCreditCode entity = unifiedSocialCreditCodeService.findEntityByCreditCode(creditCode);
            //     if (StrUtil.equals(type, "0201")) {
            //         //  名称
            //         entity.setInstitutionName(newInfo);
            //     } else if (StrUtil.equals(type, "0205")) {
            //         // 法人变动
            //         entity.setHead(newInfo);
            //     }
            //     unifiedSocialCreditCodeService.updateById(entity);
            // }
        } else {
            UnitBdFlow unitBdFlow = unitBdFlowService.findByBizIdAndState(bizId, dto.getState());
            if (Objects.isNull(unitBdFlow)) {
                if(StrUtil.equals(dto.getState(), NotifyState.YCK.getCode())){
                    sysNotifyService.changeState(dto.getState(), dto.getNotifyId());
                }
                unitBdFlowService.saveByBizId(bizId, NotifyState.getByCode(dto.getState()));
            }
        }
        unitBd.setState(dto.getState());
        this.updateById(unitBd);
        return Result.ok();
    }

    @Transactional(rollbackFor = Exception.class)
    public Result sendMsgYkz(UnitBdDTO dto) {
        String unitId = dto.getUnitId();
        String id = dto.getId();

        List<SysUser> sysUserList = sysUserService.findUserByDeptIdAndSystemId(unitId, RequestHolder.getCurrSystemId());
        if (CollUtil.isEmpty(sysUserList)) {
            return Result.fail("该单位下未绑定用户无法发送消息！");
        }
        UnitBd unitBd = this.getById(id);

        Map<String, String> mapByType = dictCodeService.findMapByType(DictConstant.JGSY_BD_TYPE);
        SysUser sysUser = sysUserList.get(0);
        SysNotifyDTO notifyDTO = new SysNotifyDTO();
        notifyDTO.setBizId(id);
        notifyDTO.setNotifyType(NotifyTypeEnum.DB.getCode());
        notifyDTO.setRecipientUserId(sysUser.getId());
        notifyDTO.setRecipientZwddId(sysUser.getZwddId());
        String title = "经数据变动预警告知，你单位信息已发生变动（" + mapByType.get(dto.getType()) + "），请尽快办理！";
        notifyDTO.setTitle(title);
        notifyDTO.setPcUrl(dto.getPcUrl());
        notifyDTO.setAppUrl(notifyDTO.getPcUrl());
        notifyDTO.setMenuId(dto.getMenuId());
        notifyDTO.setBizType(dto.getBizType());
        String content = "经数据变动预警告知，你单位信息已发生变动,变动类型：%s，原信息：%s，现信息：%s，请尽快办理！";

        notifyDTO.setContent(String.format(content, mapByType.get(dto.getType()), dto.getOldInfo(), dto.getNewInfo()));
        Result result = sysNotifyService.sendTodo(notifyDTO);

        if (Objects.equals(result.getCode(), Result.ok().getCode())) {

            unitBd.setIsSend(CommonConstant.TRUE_INT);
            unitBd.setState(NotifyState.YFS.getCode());
            this.updateById(unitBd);
            unitBdFlowService.saveByBizId(id, NotifyState.YFS);
        }
        return result;
    }

    @Override
    public void deleteAll() {
        unitBdMapper.deleteAll();
    }

    @Override
    public List<UnitBd> findByState(String state) {
        LambdaQueryWrapper<UnitBd> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(UnitBd::getState, state);
        queryWrapper.orderByAsc(UnitBd::getId);
        return this.list(queryWrapper);
    }
}
