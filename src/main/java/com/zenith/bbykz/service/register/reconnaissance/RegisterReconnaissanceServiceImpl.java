package com.zenith.bbykz.service.register.reconnaissance;

import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.efficient.common.result.Result;
import com.efficient.configs.util.PageUtil;
import com.efficient.system.api.DictCodeService;
import com.zenith.bbykz.api.OrgInfoService;
import com.zenith.bbykz.api.register.reconnaissance.RegisterReconnaissanceService;
import com.zenith.bbykz.common.constant.DictConstant;
import com.zenith.bbykz.dao.register.reconnaissance.RegisterReconnaissanceMapper;
import com.zenith.bbykz.model.converter.register.reconnaissance.RegisterReconnaissanceConverter;
import com.zenith.bbykz.model.dto.register.reconnaissance.RegisterReconnaissanceDTO;
import com.zenith.bbykz.model.dto.register.reconnaissance.RegisterReconnaissanceListDTO;
import com.zenith.bbykz.model.entity.OrgInfo;
import com.zenith.bbykz.model.entity.register.reconnaissance.RegisterReconnaissance;
import com.zenith.bbykz.model.vo.register.reconnaissance.RegisterReconnaissanceVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * <p>
 * 登记信息-内部事务-实地勘察 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @date 2024-03-28 11:28:37
 */
@Service
public class RegisterReconnaissanceServiceImpl extends ServiceImpl<RegisterReconnaissanceMapper, RegisterReconnaissance> implements RegisterReconnaissanceService {

    @Autowired
    private RegisterReconnaissanceConverter registerReconnaissanceConverter;
    @Autowired
    private RegisterReconnaissanceMapper registerReconnaissanceMapper;
    @Autowired
    private DictCodeService dictCodeService;
    @Autowired
    private OrgInfoService orgInfoService;

    @Override
    public Result<RegisterReconnaissance> save(RegisterReconnaissanceDTO dto) {
        RegisterReconnaissance entity = registerReconnaissanceConverter.dto2Entity(dto);
        OrgInfo orgInfo = orgInfoService.findByCreditCode(dto.getCreditCode());
        if(Objects.isNull(orgInfo)){
            entity.setOrgLevelCode(orgInfoService.findLevelCodeById(dto.getOrgLevelCode()));
        }else{
            entity.setOrgLevelCode(orgInfo.getLevelCode());
        }
        boolean flag = this.save(entity);
        return Result.ok(entity);
    }

    @Override
    public Result<RegisterReconnaissanceVO> findById(String id) {
        RegisterReconnaissance entity = this.getById(id);
        Map<String, String> mapByType = dictCodeService.findMapByType(DictConstant.JGSY_SDKC_STATUS);
        RegisterReconnaissanceVO vo = registerReconnaissanceConverter.entity2Vo(entity);
        vo.setCheckResultName(mapByType.get(vo.getCheckResult()));
        return Result.ok(vo);
    }

    @Override
    public Result<Boolean> update(RegisterReconnaissanceDTO dto) {
        boolean flag = this.updateById(registerReconnaissanceConverter.dto2Entity(dto));
        return flag ? Result.ok() : Result.fail();
    }

    @Override
    public Result<Boolean> delete(String id) {
        boolean flag = this.removeById(id);
        return flag ? Result.ok() : Result.fail();
    }

    @Override
    public Page<RegisterReconnaissanceVO> list(RegisterReconnaissanceListDTO dto) {
        LambdaQueryWrapper<RegisterReconnaissance> queryWrapper = new LambdaQueryWrapper<>(RegisterReconnaissance.class);
        String unitName = dto.getUnitName();
        String creditCode = dto.getCreditCode();
        String certificateNum = dto.getCertificateNum();
        String orgLevelCode = dto.getOrgLevelCode();
        queryWrapper.likeRight(RegisterReconnaissance::getOrgLevelCode, orgLevelCode);
        queryWrapper.like(StrUtil.isNotBlank(unitName), RegisterReconnaissance::getUnitName, unitName);
        queryWrapper.like(StrUtil.isNotBlank(creditCode), RegisterReconnaissance::getCreditCode, creditCode);
        queryWrapper.like(StrUtil.isNotBlank(certificateNum), RegisterReconnaissance::getCertificateNum, certificateNum);
        queryWrapper.orderByDesc(RegisterReconnaissance::getCreateTime);
        final Page<RegisterReconnaissance> page = registerReconnaissanceMapper.selectPage(new Page<>(dto.getPageNum(), dto.getPageSize()), queryWrapper);
        List<RegisterReconnaissance> records = page.getRecords();
        Map<String, String> mapByType = dictCodeService.findMapByType(DictConstant.JGSY_SDKC_STATUS);
        List<RegisterReconnaissanceVO> list = new ArrayList<>();
        records.forEach(et -> {
            RegisterReconnaissanceVO vo = registerReconnaissanceConverter.entity2Vo(et);
            vo.setCheckResultName(mapByType.get(vo.getCheckResult()));
            list.add(vo);
        });

        return PageUtil.change(page, list);
    }
}
