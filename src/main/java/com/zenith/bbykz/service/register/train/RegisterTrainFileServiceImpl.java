package com.zenith.bbykz.service.register.train;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.efficient.common.result.Result;
import com.efficient.file.api.SysFileInfoService;
import com.efficient.file.model.entity.SysFileInfo;
import com.zenith.bbykz.api.register.train.RegisterTrainFileService;
import com.zenith.bbykz.dao.register.train.RegisterTrainFileMapper;
import com.zenith.bbykz.model.converter.register.train.RegisterTrainFileConverter;
import com.zenith.bbykz.model.dto.register.train.RegisterTrainFileDTO;
import com.zenith.bbykz.model.dto.register.train.RegisterTrainFileListDTO;
import com.zenith.bbykz.model.entity.register.train.RegisterTrainFile;
import com.zenith.bbykz.model.vo.register.train.RegisterTrainFileVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <p>
 * 登记信息-培训管理-课件管理 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @date 2024-03-05 15:44:53
 */
@Service
public class RegisterTrainFileServiceImpl extends ServiceImpl<RegisterTrainFileMapper, RegisterTrainFile> implements RegisterTrainFileService {

    @Autowired
    private RegisterTrainFileConverter registerTrainFileConverter;
    @Autowired
    private RegisterTrainFileMapper registerTrainFileMapper;
    @Autowired
    private SysFileInfoService sysFileInfoService;

    @Override
    public Result<RegisterTrainFile> save(RegisterTrainFileDTO dto) {
        RegisterTrainFile entity = registerTrainFileConverter.dto2Entity(dto);
        boolean flag = this.save(entity);
        String id = entity.getId();
        sysFileInfoService.saveIdListByBizId(dto.getFileIdList(), id);
        return Result.ok(entity);
    }

    @Override
    public Result<RegisterTrainFileVO> findById(String id) {
        RegisterTrainFile entity = this.getById(id);
        RegisterTrainFileVO vo = registerTrainFileConverter.entity2Vo(entity);
        // vo.setBizId(entity.getId());
        List<SysFileInfo> sysFileInfoList = sysFileInfoService.findByBizId(entity.getId());
        vo.setFileInfoList(sysFileInfoList);
        return Result.ok(vo);
    }

    @Override
    public Result<Boolean> update(RegisterTrainFileDTO dto) {
        boolean flag = this.updateById(registerTrainFileConverter.dto2Entity(dto));
        return flag ? Result.ok() : Result.fail();
    }

    @Override
    public Result<Boolean> delete(String id) {
        boolean flag = this.removeById(id);
        return flag ? Result.ok() : Result.fail();
    }

    @Override
    public Page<RegisterTrainFile> list(RegisterTrainFileListDTO dto) {
        LambdaQueryWrapper<RegisterTrainFile> queryWrapper = new LambdaQueryWrapper<>(RegisterTrainFile.class);
        queryWrapper.like(StrUtil.isNotBlank(dto.getKeyword()), RegisterTrainFile::getName, dto.getKeyword());
        queryWrapper.in(CollUtil.isNotEmpty(dto.getCourseTypeIdList()), RegisterTrainFile::getTypeId, dto.getCourseTypeIdList());
        queryWrapper.orderByDesc(RegisterTrainFile::getCreateTime);
        final Page<RegisterTrainFile> page = registerTrainFileMapper.selectPage(new Page<>(dto.getPageNum(), dto.getPageSize()), queryWrapper);
        return page;
    }
}
