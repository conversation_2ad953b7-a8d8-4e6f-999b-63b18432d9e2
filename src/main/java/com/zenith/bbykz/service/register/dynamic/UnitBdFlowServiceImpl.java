package com.zenith.bbykz.service.register.dynamic;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.efficient.common.constant.DbConstant;
import com.zenith.bbykz.api.register.dynamic.UnitBdFlowService;
import com.zenith.bbykz.common.enums.NotifyState;
import com.zenith.bbykz.dao.register.dynamic.UnitBdFlowMapper;
import com.zenith.bbykz.model.converter.register.dynamic.UnitBdFlowConverter;
import com.zenith.bbykz.model.entity.register.dynamic.UnitBdFlow;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.List;

import static com.efficient.common.constant.CommonConstant.FALSE_INT;

/**
 * <p>
 * 登记信息-变动流程记录 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @date 2024-04-18 10:01:34
 */
@Service
public class UnitBdFlowServiceImpl extends ServiceImpl<UnitBdFlowMapper, UnitBdFlow> implements UnitBdFlowService {

    @Autowired
    private UnitBdFlowConverter unitBdFlowConverter;
    @Autowired
    private UnitBdFlowMapper unitBdFlowMapper;

    @Override
    public boolean saveByBizId(String bizId, NotifyState notifyState) {
        UnitBdFlow flow = new UnitBdFlow();
        flow.setCode(notifyState.getCode());
        flow.setDescribe(notifyState.getName());
        flow.setBizId(bizId);
        return this.save(flow);
    }

    @Override
    public List<UnitBdFlow> findByBizId(String bizId) {
      List<UnitBdFlow> list=  unitBdFlowMapper.findByBizId(bizId);
        return list;
    }

    @Override
    public UnitBdFlow findByBizIdAndState(String bizId, String state) {
        LambdaQueryWrapper<UnitBdFlow> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(UnitBdFlow::getBizId, bizId);
        queryWrapper.eq(UnitBdFlow::getCode, state);
        queryWrapper.eq(UnitBdFlow::getIsDelete, FALSE_INT);
        queryWrapper.last(DbConstant.LIMIT_ONE);
        return this.getOne(queryWrapper);
    }

    @Override
    public void deleteAll() {
        unitBdFlowMapper.deleteAll();
    }
}
