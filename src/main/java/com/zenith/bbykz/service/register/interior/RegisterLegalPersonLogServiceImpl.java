package com.zenith.bbykz.service.register.interior;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.efficient.common.result.Result;
import com.efficient.common.util.JackSonUtil;
import com.zenith.bbykz.api.register.interior.RegisterLegalPersonLogService;
import com.zenith.bbykz.dao.register.interior.RegisterLegalPersonLogMapper;
import com.zenith.bbykz.model.converter.register.interior.RegisterLegalPersonLogConverter;
import com.zenith.bbykz.model.dto.register.interior.RegisterLegalPersonLogDTO;
import com.zenith.bbykz.model.dto.register.interior.RegisterLegalPersonLogListDTO;
import com.zenith.bbykz.model.entity.register.interior.RegisterLegalPersonLib;
import com.zenith.bbykz.model.entity.register.interior.RegisterLegalPersonLog;
import com.zenith.bbykz.model.vo.register.interior.RegisterLegalPersonLogVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <p>
 * 登记信息-内部事务-法人库日志 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @date 2024-03-12 17:24:37
 */
@Service
public class RegisterLegalPersonLogServiceImpl extends ServiceImpl<RegisterLegalPersonLogMapper, RegisterLegalPersonLog> implements RegisterLegalPersonLogService {

    @Autowired
    private RegisterLegalPersonLogConverter registerLegalPersonLogConverter;
    @Autowired
    private RegisterLegalPersonLogMapper registerLegalPersonLogMapper;

    @Override
    public Result<RegisterLegalPersonLog> save(RegisterLegalPersonLogDTO dto) {
        RegisterLegalPersonLog entity = registerLegalPersonLogConverter.dto2Entity(dto);
        boolean flag = this.save(entity);
        return Result.ok(entity);
    }

    @Override
    public Result<RegisterLegalPersonLogVO> findById(String id) {
        RegisterLegalPersonLog entity = this.getById(id);
        RegisterLegalPersonLogVO vo = registerLegalPersonLogConverter.entity2Vo(entity);
        return Result.ok(vo);
    }

    @Override
    public Result<Boolean> update(RegisterLegalPersonLogDTO dto) {
        boolean flag = this.updateById(registerLegalPersonLogConverter.dto2Entity(dto));
        return flag ? Result.ok() : Result.fail();
    }

    @Override
    public Result<Boolean> delete(String id) {
        boolean flag = this.removeById(id);
        return flag ? Result.ok() : Result.fail();
    }

    @Override
    public Page<RegisterLegalPersonLogVO> list(RegisterLegalPersonLogListDTO dto) {
        LambdaQueryWrapper<RegisterLegalPersonLog> queryWrapper = new LambdaQueryWrapper<>(RegisterLegalPersonLog.class);
        queryWrapper.like(StrUtil.isNotBlank(dto.getName()), RegisterLegalPersonLog::getName, dto.getName());
        queryWrapper.like(StrUtil.isNotBlank(dto.getIdCard()), RegisterLegalPersonLog::getIdCard, dto.getIdCard());
        queryWrapper.eq(RegisterLegalPersonLog::getIsSuccess,0);
        queryWrapper.orderByDesc(RegisterLegalPersonLog::getCreateTime);
        final Page<RegisterLegalPersonLog> page = registerLegalPersonLogMapper.selectPage(new Page<>(dto.getPageNum(), dto.getPageSize()), queryWrapper);
        List<RegisterLegalPersonLog> records = page.getRecords();
        List<RegisterLegalPersonLogVO> voList = CollUtil.newArrayList();
        Page<RegisterLegalPersonLogVO> newPage = new Page<>(page.getCurrent(), page.getSize(), page.getTotal());
        newPage.setRecords(voList);
        if (CollUtil.isEmpty(records)) {
            return new Page<>(page.getCurrent(), page.getSize(), page.getTotal());
        }
        records.stream().forEach(et -> {
            RegisterLegalPersonLogVO registerLegalPersonLogVO = registerLegalPersonLogConverter.entity2Vo(et);
            String description = et.getDescription();
            if (StrUtil.isNotBlank(description)) {
                registerLegalPersonLogVO.setLibList(JackSonUtil.toObjectList(description, RegisterLegalPersonLib.class));
            }
            voList.add(registerLegalPersonLogVO);

        });
        return newPage;
    }

    @Override
    public void saveByVerify(String name, String idCard, List<RegisterLegalPersonLib> list) {
        RegisterLegalPersonLog log1 = new RegisterLegalPersonLog();
        log1.setName(name);
        log1.setIdCard(idCard);
        log1.setIsSuccess(list.isEmpty() ? 1 : 0);
        log1.setDescription(JackSonUtil.toJson(list));
        this.save(log1);
    }
}
