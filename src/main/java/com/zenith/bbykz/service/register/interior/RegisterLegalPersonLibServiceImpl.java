package com.zenith.bbykz.service.register.interior;

import cn.hutool.core.util.StrUtil;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.ExcelReader;
import com.alibaba.excel.read.metadata.ReadSheet;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.efficient.cache.util.ProgressUtil;
import com.efficient.common.auth.RequestHolder;
import com.efficient.common.result.Result;
import com.efficient.common.util.ExceptionUtil;
import com.efficient.common.util.ThreadUtil;
import com.efficient.file.api.FileService;
import com.efficient.file.api.SysFileInfoService;
import com.efficient.file.properties.FileProperties;
import com.zenith.bbykz.api.register.interior.RegisterLegalPersonLibService;
import com.zenith.bbykz.api.register.interior.RegisterLegalPersonLogService;
import com.zenith.bbykz.common.result.BbResultEnum;
import com.zenith.bbykz.dao.register.interior.RegisterLegalPersonLibMapper;
import com.zenith.bbykz.model.converter.register.interior.RegisterLegalPersonLibConverter;
import com.zenith.bbykz.model.dto.register.interior.RegisterLegalPersonLibDTO;
import com.zenith.bbykz.model.dto.register.interior.RegisterLegalPersonLibListDTO;
import com.zenith.bbykz.model.easy.excel.model.RegisterLegalPersonLibExcel;
import com.zenith.bbykz.model.entity.register.interior.RegisterLegalPersonLib;
import com.zenith.bbykz.model.entity.register.interior.RegisterLegalPersonLog;
import com.zenith.bbykz.model.listener.RegisterLegalPersonLibExcelListener;
import com.zenith.bbykz.model.vo.register.interior.RegisterLegalPersonLibVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.io.File;
import java.nio.file.Files;
import java.util.List;
import java.util.Objects;

/**
 * <p>
 * 登记信息-内部事务-法人库 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @date 2024-03-12 14:55:18
 */
@Service
public class RegisterLegalPersonLibServiceImpl extends ServiceImpl<RegisterLegalPersonLibMapper, RegisterLegalPersonLib> implements RegisterLegalPersonLibService {

    @Autowired
    private RegisterLegalPersonLibConverter registerLegalPersonLibConverter;
    @Autowired
    private RegisterLegalPersonLibMapper registerLegalPersonLibMapper;
    @Autowired
    private ProgressUtil progressUtil;
    @Autowired
    private FileProperties fileProperties;
    @Autowired
    private FileService fileService;
    @Autowired
    private SysFileInfoService sysFileInfoService;
    @Autowired
    private RegisterLegalPersonLogService registerLegalPersonLogService;

    @Override
    public Result<RegisterLegalPersonLib> save(RegisterLegalPersonLibDTO dto) {
        RegisterLegalPersonLib entity = registerLegalPersonLibConverter.dto2Entity(dto);
        boolean flag = this.save(entity);
        return Result.ok(entity);
    }

    @Override
    public Result<RegisterLegalPersonLibVO> findById(String id) {
        RegisterLegalPersonLib entity = this.getById(id);
        RegisterLegalPersonLibVO vo = registerLegalPersonLibConverter.entity2Vo(entity);
        return Result.ok(vo);
    }

    @Override
    public Result<Boolean> update(RegisterLegalPersonLibDTO dto) {
        boolean flag = this.updateById(registerLegalPersonLibConverter.dto2Entity(dto));
        return flag ? Result.ok() : Result.fail();
    }

    @Override
    public Result<Boolean> delete(String id) {
        boolean flag = this.removeById(id);
        return flag ? Result.ok() : Result.fail();
    }

    @Override
    public Page<RegisterLegalPersonLib> list(RegisterLegalPersonLibListDTO dto) {
        LambdaQueryWrapper<RegisterLegalPersonLib> queryWrapper = new LambdaQueryWrapper<>(RegisterLegalPersonLib.class);
        queryWrapper.like(StrUtil.isNotBlank(dto.getName()), RegisterLegalPersonLib::getName, dto.getName());
        queryWrapper.like(StrUtil.isNotBlank(dto.getIdCard()), RegisterLegalPersonLib::getIdCard, dto.getIdCard());
        queryWrapper.orderByDesc(RegisterLegalPersonLib::getId);
        final Page<RegisterLegalPersonLib> page = registerLegalPersonLibMapper.selectPage(new Page<>(dto.getPageNum(), dto.getPageSize()), queryWrapper);
        return page;
    }

    @Override
    public String importExcel(String fileId) {
        String currKey = ProgressUtil.getCurrKey("LegalPersonServiceImpl.importFile", RequestHolder.getCurrUser().getToken());
        File file = fileService.getById(fileId);
        ThreadUtil.EXECUTOR_SERVICE.execute(() -> {
            ExcelReader excelReader = null;
            progressUtil.running("正在解析文件", 10, currKey);
            try {
                excelReader = EasyExcel.read(Files.newInputStream(file.toPath())).build();
                ReadSheet readSheet = EasyExcel
                        .readSheet()
                        .headRowNumber(1)
                        .head(RegisterLegalPersonLibExcel.class)
                        .registerReadListener(new RegisterLegalPersonLibExcelListener(this, progressUtil, currKey))
                        .build();
                excelReader.read(readSheet);
            } catch (Exception e) {
                log.error("导入文件异常", e);
                progressUtil.fail(BbResultEnum.DEVELOP_UPLOAD_EXCEL_ERROR.getMsg(), currKey, ExceptionUtil.getStackTrace(e));
                return;
            } finally {
                if (excelReader != null) {
                    excelReader.finish();
                }
                if (file.exists()) {
                    try {
                        fileService.delete(fileId);
                    } catch (Exception e) {
                        log.error("删除文件异常", e);
                    }
                }
            }
            progressUtil.success("导入成功", currKey);
        });
        return currKey;
    }

    @Override
    public RegisterLegalPersonLib findLastVersion() {
        return registerLegalPersonLibMapper.findLastVersion();
    }

    @Override
    public void deleteByVersion(Integer lastVersion) {
        registerLegalPersonLibMapper.deleteByVersion(lastVersion);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Result rollback() {
        RegisterLegalPersonLib lastVersionEntity = this.findLastVersion();
        if (Objects.isNull(lastVersionEntity)) {
            return Result.build(BbResultEnum.REGISTER_ROLL_BACK_VERSION_NO);
        }
        Integer version = lastVersionEntity.getVersion();
        if (Objects.isNull(version) || version == 1) {
            return Result.build(BbResultEnum.REGISTER_ROLL_BACK_VERSION_NO);
        }
        Integer lastVersion = version - 1;
        // 还原上一个版本
        registerLegalPersonLibMapper.rollback(lastVersion);
        registerLegalPersonLibMapper.physicallyDeleteByVersion(version);
        return Result.ok();
    }

    @Override
    public List<RegisterLegalPersonLib> verify(String name, String idCard) {
        LambdaQueryWrapper<RegisterLegalPersonLib> queryWrapper = new LambdaQueryWrapper<>(RegisterLegalPersonLib.class);
        queryWrapper.eq(RegisterLegalPersonLib::getName, name);
        queryWrapper.eq(RegisterLegalPersonLib::getIdCard, idCard);
        queryWrapper.orderByDesc(RegisterLegalPersonLib::getId);
        List<RegisterLegalPersonLib> list = this.list(queryWrapper);
        registerLegalPersonLogService.saveByVerify(name, idCard, list);
        return list;
    }
}
