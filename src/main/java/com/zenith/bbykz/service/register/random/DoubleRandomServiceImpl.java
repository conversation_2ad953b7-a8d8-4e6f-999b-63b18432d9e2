package com.zenith.bbykz.service.register.random;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.efficient.common.result.Result;
import com.efficient.common.result.ResultEnum;
import com.efficient.configs.util.PageUtil;
import com.zenith.bbykz.api.OrgInfoService;
import com.zenith.bbykz.api.register.random.DoubleRandomService;
import com.zenith.bbykz.dao.register.random.DoubleRandomMapper;
import com.zenith.bbykz.model.converter.register.random.DoubleRandomConverter;
import com.zenith.bbykz.model.dto.register.random.DoubleRandomDTO;
import com.zenith.bbykz.model.dto.register.random.DoubleRandomListDTO;
import com.zenith.bbykz.model.entity.OrgInfo;
import com.zenith.bbykz.model.entity.register.random.DoubleRandom;
import com.zenith.bbykz.model.vo.OrgInfoVO;
import com.zenith.bbykz.model.vo.register.random.DoubleRandomVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

/**
 * <p>
 * 登记信息-内部事务-双随机检查 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @date 2024-05-06 15:48:28
 */
@Service
public class DoubleRandomServiceImpl extends ServiceImpl<DoubleRandomMapper, DoubleRandom> implements DoubleRandomService {

    @Autowired
    private DoubleRandomConverter doubleRandomConverter;
    @Autowired
    private DoubleRandomMapper doubleRandomMapper;
    @Autowired
    private OrgInfoService orgInfoService;

    @Override
    public Result<DoubleRandom> save(DoubleRandomDTO dto) {

        DoubleRandom entity = doubleRandomConverter.dto2Entity(dto);
        OrgInfo orgInfo = orgInfoService.findByCreditCode(dto.getCreditCode());
        if(Objects.isNull(orgInfo)){
            entity.setRecordUnitLevelCode(orgInfoService.findLevelCodeById(dto.getOrgLevelCode()));
        }else{
            entity.setRecordUnitId(orgInfo.getId());
            entity.setRecordUnitName(orgInfo.getName());
            entity.setRecordUnitLevelCode(orgInfo.getLevelCode());
        }

        boolean flag = this.save(entity);
        return Result.ok(entity);
    }

    @Override
    public Result<DoubleRandomVO> findById(String id) {
        DoubleRandom entity = this.getById(id);
        if (Objects.isNull(entity)) {
            return Result.build(ResultEnum.DATA_NOT_EXIST);
        }
        DoubleRandomVO vo = doubleRandomConverter.entity2Vo(entity);
        return Result.ok(vo);
    }

    @Override
    public Result<Boolean> update(DoubleRandomDTO dto) {
        DoubleRandom entity = doubleRandomConverter.dto2Entity(dto);
        boolean flag = this.updateById(entity);
        return flag ? Result.ok() : Result.fail();
    }

    @Override
    public Result<Boolean> delete(String id) {
        boolean flag = this.removeById(id);
        return flag ? Result.ok() : Result.fail();
    }

    @Override
    public Page<DoubleRandomVO> list(DoubleRandomListDTO dto) {
        LambdaQueryWrapper<DoubleRandom> queryWrapper = new LambdaQueryWrapper<>(DoubleRandom.class);
        String unitName = dto.getUnitName();
        String creditCode = dto.getCreditCode();
        String orgLevelCode = dto.getOrgLevelCode();
        if (StrUtil.isNotBlank(orgLevelCode)) {
            queryWrapper.likeRight(DoubleRandom::getRecordUnitLevelCode, orgLevelCode);
        }
        if (StrUtil.isNotBlank(creditCode)) {
            queryWrapper.like(DoubleRandom::getCreditCode, creditCode);
        }
        if (StrUtil.isNotBlank(unitName)) {
            queryWrapper.like(DoubleRandom::getUnitName, unitName);
        }
        queryWrapper.orderByDesc(DoubleRandom::getCreateTime);
        final Page<DoubleRandom> page = doubleRandomMapper.selectPage(new Page<>(dto.getPageNum(), dto.getPageSize()), queryWrapper);
        List<DoubleRandomVO> voList = new ArrayList<>();
        List<DoubleRandom> records = page.getRecords();
        if (CollUtil.isEmpty(records)) {
            return PageUtil.change(page, voList);
        }
        records.forEach(et -> {
            DoubleRandomVO vo = doubleRandomConverter.entity2Vo(et);
            voList.add(vo);
        });
        return PageUtil.change(page, voList);
    }
}
