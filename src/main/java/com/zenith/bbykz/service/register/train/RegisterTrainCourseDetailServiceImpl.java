package com.zenith.bbykz.service.register.train;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.efficient.common.result.Result;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.zenith.bbykz.api.register.train.RegisterTrainCourseDetailService;
import com.zenith.bbykz.model.converter.register.train.RegisterTrainCourseDetailConverter;
import com.zenith.bbykz.dao.register.train.RegisterTrainCourseDetailMapper;
import com.zenith.bbykz.model.dto.register.train.RegisterTrainCourseDetailDTO;
import com.zenith.bbykz.model.dto.register.train.RegisterTrainCourseDetailListDTO;
import com.zenith.bbykz.model.entity.register.train.RegisterTrainCourseDetail;
import com.zenith.bbykz.model.vo.register.train.RegisterTrainCourseDetailVO;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Objects;

/**
 * <p>
 * 登记信息-培训管理-课程管理关联课件 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @date 2024-03-05 15:44:53
 */
@Service
public class RegisterTrainCourseDetailServiceImpl extends ServiceImpl<RegisterTrainCourseDetailMapper, RegisterTrainCourseDetail> implements RegisterTrainCourseDetailService {

    @Autowired
    private RegisterTrainCourseDetailConverter registerTrainCourseDetailConverter;
    @Autowired
    private RegisterTrainCourseDetailMapper registerTrainCourseDetailMapper;

    @Override
    public Result<RegisterTrainCourseDetail> save(RegisterTrainCourseDetailDTO dto) {
        RegisterTrainCourseDetail entity = registerTrainCourseDetailConverter.dto2Entity(dto);
        boolean flag = this.save(entity);
        return Result.ok(entity);
    }

    @Override
    public Result<RegisterTrainCourseDetailVO> findById(String id) {
        RegisterTrainCourseDetail entity = this.getById(id);
        RegisterTrainCourseDetailVO vo = registerTrainCourseDetailConverter.entity2Vo(entity);
        return Result.ok(vo);
    }

    @Override
    public Result<Boolean> update(RegisterTrainCourseDetailDTO dto) {
        boolean flag = this.updateById(registerTrainCourseDetailConverter.dto2Entity(dto));
        return flag ? Result.ok() : Result.fail();
    }

    @Override
    public Result<Boolean> delete(String id) {
        boolean flag = this.removeById(id);
        return flag ? Result.ok() : Result.fail();
    }

    @Override
    public Page<RegisterTrainCourseDetail> list(RegisterTrainCourseDetailListDTO dto) {
        LambdaQueryWrapper<RegisterTrainCourseDetail> queryWrapper = new LambdaQueryWrapper<>(RegisterTrainCourseDetail.class);
        final Page<RegisterTrainCourseDetail> page = registerTrainCourseDetailMapper.selectPage(new Page<>(dto.getPageNum(), dto.getPageSize()), queryWrapper);
        return page;
    }

    @Override
    public List<RegisterTrainCourseDetail> findByCourseId(String courseId) {
        LambdaQueryWrapper<RegisterTrainCourseDetail> queryWrapper = new LambdaQueryWrapper<>(RegisterTrainCourseDetail.class);
        queryWrapper.eq(RegisterTrainCourseDetail::getCourseId, courseId);
        queryWrapper.orderByDesc(RegisterTrainCourseDetail::getCreateTime).orderByAsc(RegisterTrainCourseDetail::getCourseId);
        return this.list(queryWrapper);
    }
}
