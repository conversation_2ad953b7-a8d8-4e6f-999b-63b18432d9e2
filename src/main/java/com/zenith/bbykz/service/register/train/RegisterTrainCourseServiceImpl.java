package com.zenith.bbykz.service.register.train;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.efficient.common.result.Result;
import com.efficient.file.api.SysFileInfoService;
import com.efficient.file.model.entity.SysFileInfo;
import com.zenith.bbykz.api.SysRoleService;
import com.zenith.bbykz.api.register.train.RegisterTrainCourseDetailService;
import com.zenith.bbykz.api.register.train.RegisterTrainCourseService;
import com.zenith.bbykz.api.register.train.RegisterTrainCourseUserService;
import com.zenith.bbykz.api.register.train.RegisterTrainTypeService;
import com.zenith.bbykz.dao.register.train.RegisterTrainCourseDetailMapper;
import com.zenith.bbykz.dao.register.train.RegisterTrainCourseMapper;
import com.zenith.bbykz.model.base.SelectModel;
import com.zenith.bbykz.model.converter.register.train.RegisterTrainCourseConverter;
import com.zenith.bbykz.model.converter.register.train.RegisterTrainCourseDetailConverter;
import com.zenith.bbykz.model.dto.register.train.RegisterTrainCourseDTO;
import com.zenith.bbykz.model.dto.register.train.RegisterTrainCourseListDTO;
import com.zenith.bbykz.model.dto.register.train.RegisterTrainFileDTO;
import com.zenith.bbykz.model.entity.SysRole;
import com.zenith.bbykz.model.entity.register.train.RegisterTrainCourse;
import com.zenith.bbykz.model.entity.register.train.RegisterTrainCourseDetail;
import com.zenith.bbykz.model.vo.register.train.RegisterTrainCourseVO;
import com.zenith.bbykz.model.vo.register.train.RegisterTrainFileVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <p>
 * 登记信息-培训管理-课程管理 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @date 2024-03-05 15:44:53
 */
@Service
public class RegisterTrainCourseServiceImpl extends ServiceImpl<RegisterTrainCourseMapper, RegisterTrainCourse> implements RegisterTrainCourseService {

    @Autowired
    private RegisterTrainCourseConverter registerTrainCourseConverter;
    @Autowired
    private RegisterTrainCourseMapper registerTrainCourseMapper;
    @Autowired
    private RegisterTrainCourseDetailService registerTrainCourseDetailService;
    @Autowired
    private RegisterTrainCourseDetailMapper registerTrainCourseDetailMapper;
    @Autowired
    private RegisterTrainCourseDetailConverter registerTrainCourseDetailConverter;
    @Autowired
    private SysRoleService sysRoleService;
    @Autowired
    private RegisterTrainTypeService registerTrainTypeService;
    @Autowired
    private SysFileInfoService sysFileInfoService;
    @Autowired
    private RegisterTrainCourseUserService registerTrainCourseUserService;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Result<RegisterTrainCourse> save(RegisterTrainCourseDTO dto) {
        RegisterTrainCourse entity = registerTrainCourseConverter.dto2Entity(dto);
        boolean flag = this.save(entity);
        List<RegisterTrainFileDTO> trainFileList = dto.getDetailList();
        this.saveOrUpdateDetail(entity.getId(), trainFileList);
        String scopeId = dto.getScopeId();
        String[] split = scopeId.split(",");
        return Result.ok(entity);
    }

    @Override
    public Result<RegisterTrainCourseVO> findById(String id) {
        RegisterTrainCourse entity = this.getById(id);
        RegisterTrainCourseVO vo = registerTrainCourseConverter.entity2Vo(entity);
        Optional.ofNullable(registerTrainTypeService.getById(vo.getTypeId())).ifPresent(et -> {
            vo.setTypeName(et.getName());
        });

        List<RegisterTrainCourseDetail> detailList = registerTrainCourseDetailService.findByCourseId(id);
        List<String> collected = detailList.stream().map(RegisterTrainCourseDetail::getTrainFileId).collect(Collectors.toList());
        List<SysFileInfo> sysFileInfoList = sysFileInfoService.findByBizIdList(collected);
        Map<String, List<SysFileInfo>> fileGroup = sysFileInfoList.stream().collect(Collectors.groupingBy(SysFileInfo::getBizId));
        // Map<String, SysFileInfo> collect = sysFileInfoList.stream().collect(Collectors.toMap(SysFileInfo::getBizId, et -> et, (k1, k2) -> k1));
        List<RegisterTrainFileVO> detailVOS = new ArrayList<>();
        detailList.forEach(et -> {
            RegisterTrainFileVO fileVO = new RegisterTrainFileVO();
            fileVO.setId(et.getTrainFileId());
            fileVO.setTrainFileId(et.getTrainFileId());
            fileVO.setCourseDetailId(et.getId());
            fileVO.setName(et.getDetailName());
            List<SysFileInfo> fileInfoList = fileGroup.get(et.getTrainFileId());
            fileVO.setFileInfoList(fileInfoList);
            detailVOS.add(fileVO);
        });
        vo.setDetailList(detailVOS);
        String scopeId = vo.getScopeId();
        if (StrUtil.isNotBlank(scopeId)) {
            String[] split = scopeId.split(",");
            List<SysRole> sysRoles = sysRoleService.listByIds(Arrays.asList(split));
            List<SelectModel> selectModels = new ArrayList<>();
            sysRoles.forEach(et -> {
                SelectModel selectModel = new SelectModel();
                selectModel.setId(et.getId());
                selectModel.setName(et.getName());
                selectModels.add(selectModel);
            });
            vo.setScopeList(selectModels);
        }

        return Result.ok(vo);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Result<Boolean> update(RegisterTrainCourseDTO dto) {
        boolean flag = this.updateById(registerTrainCourseConverter.dto2Entity(dto));
        this.saveOrUpdateDetail(dto.getId(), dto.getDetailList());
        return flag ? Result.ok() : Result.fail();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Result<Boolean> delete(String id) {
        boolean flag = this.removeById(id);
        registerTrainCourseDetailMapper.deleteByCourseId(id);
        return flag ? Result.ok() : Result.fail();
    }

    @Override
    public Page<RegisterTrainCourse> list(RegisterTrainCourseListDTO dto) {
        LambdaQueryWrapper<RegisterTrainCourse> queryWrapper = new LambdaQueryWrapper<>(RegisterTrainCourse.class);
        queryWrapper.like(StrUtil.isNotBlank(dto.getKeyword()), RegisterTrainCourse::getName, dto.getKeyword());
        queryWrapper.in(CollUtil.isNotEmpty(dto.getCourseTypeIdList()), RegisterTrainCourse::getTypeId, dto.getCourseTypeIdList());
        queryWrapper.orderByDesc(RegisterTrainCourse::getCreateTime);
        final Page<RegisterTrainCourse> page = registerTrainCourseMapper.selectPage(new Page<>(dto.getPageNum(), dto.getPageSize()), queryWrapper);
        return page;
    }

    private void saveOrUpdateDetail(String bizId, List<RegisterTrainFileDTO> trainFileList) {
        registerTrainCourseDetailMapper.deleteByCourseId(bizId);
        if (CollUtil.isNotEmpty(trainFileList)) {
            List<RegisterTrainCourseDetail> detailList = new ArrayList<>();
            trainFileList.forEach(et -> {
                RegisterTrainCourseDetail detail = new RegisterTrainCourseDetail();
                detail.setCourseId(bizId);
                detail.setTrainFileId(et.getId());
                detail.setDetailName(et.getName());
                detailList.add(detail);
            });
            registerTrainCourseDetailService.saveBatch(detailList);
        }
    }
}
