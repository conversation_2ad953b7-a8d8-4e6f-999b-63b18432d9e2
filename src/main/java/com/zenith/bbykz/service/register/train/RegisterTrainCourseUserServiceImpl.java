package com.zenith.bbykz.service.register.train;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.efficient.common.auth.RequestHolder;
import com.efficient.common.constant.DbConstant;
import com.efficient.common.result.Result;
import com.efficient.file.api.SysFileInfoService;
import com.efficient.file.model.entity.SysFileInfo;
import com.zenith.bbykz.api.SysUserService;
import com.zenith.bbykz.api.register.train.RegisterTrainCourseDetailService;
import com.zenith.bbykz.api.register.train.RegisterTrainCourseService;
import com.zenith.bbykz.api.register.train.RegisterTrainCourseUserService;
import com.zenith.bbykz.api.register.train.RegisterTrainUserInfoService;
import com.zenith.bbykz.dao.register.train.RegisterTrainCourseUserMapper;
import com.zenith.bbykz.model.base.BbUserAuthInfo;
import com.zenith.bbykz.model.base.UserInfo;
import com.zenith.bbykz.model.converter.register.train.RegisterTrainCourseUserConverter;
import com.zenith.bbykz.model.dto.register.train.RegisterTrainCourseUserDTO;
import com.zenith.bbykz.model.dto.register.train.RegisterTrainCourseUserListDTO;
import com.zenith.bbykz.model.entity.SysUser;
import com.zenith.bbykz.model.entity.register.train.RegisterTrainCourseDetail;
import com.zenith.bbykz.model.entity.register.train.RegisterTrainCourseUser;
import com.zenith.bbykz.model.entity.register.train.RegisterTrainUserInfo;
import com.zenith.bbykz.model.vo.register.train.RegisterTrainCourseUserVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <p>
 * 登记信息-培训管理-用户课程 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @date 2024-03-12 10:11:37
 */
@Service
public class RegisterTrainCourseUserServiceImpl extends ServiceImpl<RegisterTrainCourseUserMapper, RegisterTrainCourseUser> implements RegisterTrainCourseUserService {

    @Autowired
    private RegisterTrainCourseUserConverter registerTrainCourseUserConverter;
    @Autowired
    private RegisterTrainCourseUserMapper registerTrainCourseUserMapper;
    @Autowired
    private SysUserService sysUserService;
    @Autowired
    private RegisterTrainCourseService registerTrainCourseService;
    @Autowired
    private RegisterTrainCourseDetailService registerTrainCourseDetailService;
    @Autowired
    private RegisterTrainUserInfoService registerTrainUserInfoService;
    @Autowired
    private SysFileInfoService sysFileInfoService;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Result<RegisterTrainCourseUser> save(RegisterTrainCourseUserDTO dto) {
        String id = dto.getId();
        RegisterTrainCourseUser trainCourseUser = this.findByUserIdAndCourseId(dto.getUserId(), dto.getCourseId());
        boolean flag = true;
        if (Objects.isNull(trainCourseUser)) {
            // 新增
            trainCourseUser = registerTrainCourseUserConverter.dto2Entity(dto);
            trainCourseUser.setId(null);
            flag = this.saveOrUpdate(trainCourseUser);
            this.addTrainUserInfo(trainCourseUser);
        }
        return Result.ok(trainCourseUser);
    }

    @Override
    public Result<RegisterTrainCourseUserVO> findById(String id) {
        RegisterTrainCourseUser entity = this.getById(id);
        RegisterTrainCourseUserVO vo = registerTrainCourseUserConverter.entity2Vo(entity);
        return Result.ok(vo);
    }

    @Override
    public Result<Boolean> update(RegisterTrainCourseUserDTO dto) {
        boolean flag = this.updateById(registerTrainCourseUserConverter.dto2Entity(dto));
        return flag ? Result.ok() : Result.fail();
    }

    @Override
    public Result<Boolean> delete(String id) {
        boolean flag = this.removeById(id);
        return flag ? Result.ok() : Result.fail();
    }

    @Override
    public Page<RegisterTrainCourseUserVO> list(RegisterTrainCourseUserListDTO dto) {
        //  UserInfo currUser = (UserInfo) RequestHolder.getCurrUser();
        // BbUserAuthInfo userAuthInfo = currUser.getCurrLoginSystem().get(RequestHolder.getCurrSystemId());
        // String roleId = userAuthInfo.getRoleList().get(0);
        Page<RegisterTrainCourseUserVO> page = new Page<>(dto.getPageNum(), dto.getPageSize());
        // dto.setRoleId(roleId);
        Page<RegisterTrainCourseUserVO> pageResult = registerTrainCourseUserMapper.listByRole(dto, page);
        List<RegisterTrainCourseUserVO> records = pageResult.getRecords();
        if (CollUtil.isEmpty(records)) {
            return pageResult;
        }
        List<String> collected = records.stream().map(RegisterTrainCourseUserVO::getPublisherAccount).collect(Collectors.toList());
        List<SysUser> userList = sysUserService.getByAccountList(collected);
        Map<String, String> nameMap = userList.stream().collect(Collectors.toMap(SysUser::getAccount, SysUser::getName, (k1, k2) -> k1));
        records.forEach(et -> {
            et.setPublisher(nameMap.get(et.getPublisherAccount()));
            if (Objects.isNull(et.getIsFinish())) {
                et.setIsFinish(0);
            }
            if (Objects.isNull(et.getIsAttention())) {
                et.setIsAttention(0);
            }
            if (Objects.isNull(et.getProgress())) {
                et.setProgress(BigDecimal.ZERO);
            }
        });
        return pageResult;
    }

    @Override
    public void updateByUserIdAndCourseId(String userId, String courseId, BigDecimal average) {
        RegisterTrainCourseUser entity = this.findByUserIdAndCourseId(userId, courseId);
        if (Objects.nonNull(entity)) {
            entity.setProgress(average);
            int comparisonResult = average.compareTo(new BigDecimal("1"));
            if (comparisonResult >= 0) {
                entity.setIsFinish(1);
            }
        }
        this.updateById(entity);
    }

    @Override
    public RegisterTrainCourseUser findByUserIdAndCourseId(String userId, String courseId) {
        LambdaQueryWrapper<RegisterTrainCourseUser> queryWrapper = new LambdaQueryWrapper<>(RegisterTrainCourseUser.class);
        queryWrapper.eq(RegisterTrainCourseUser::getUserId, userId);
        queryWrapper.eq(RegisterTrainCourseUser::getCourseId, courseId);
        queryWrapper.last(DbConstant.LIMIT_ONE);
        return this.getOne(queryWrapper);
    }

    @Override
    public void addTrainUserInfo(RegisterTrainCourseUser entity) {
        String courseId = entity.getCourseId();
        String userId = entity.getUserId();
        List<RegisterTrainCourseDetail> details = registerTrainCourseDetailService.findByCourseId(courseId);
        List<RegisterTrainUserInfo> userInfoList = new ArrayList<>();
        List<String> trainFileList = details.stream().map(RegisterTrainCourseDetail::getTrainFileId).collect(Collectors.toList());
        List<SysFileInfo> sysFileInfoList = sysFileInfoService.findByBizIdList(trainFileList);
        Map<String, List<SysFileInfo>> collect = sysFileInfoList.stream().collect(Collectors.groupingBy(SysFileInfo::getBizId));
        details.forEach(et -> {
            List<SysFileInfo> fileInfoList = collect.get(et.getTrainFileId());
            if (CollUtil.isNotEmpty(fileInfoList)) {
                for (SysFileInfo sysFileInfo : fileInfoList) {
                    RegisterTrainUserInfo userInfo = new RegisterTrainUserInfo();
                    userInfo.setCourseId(courseId);
                    userInfo.setUserId(userId);
                    userInfo.setCourseDetailId(et.getId());
                    userInfo.setTrainFileId(et.getTrainFileId());
                    userInfo.setFileId(sysFileInfo.getId());
                    userInfo.setIsFinish(0);
                    userInfo.setProgress(new BigDecimal(0));
                    userInfoList.add(userInfo);
                }
            }
        });
        registerTrainUserInfoService.saveBatch(userInfoList);
    }
}
