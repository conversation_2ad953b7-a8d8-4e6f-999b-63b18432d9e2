package com.zenith.bbykz.service.register.train;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.efficient.common.auth.RequestHolder;
import com.efficient.common.constant.CommonConstant;
import com.efficient.common.result.Result;
import com.efficient.configs.util.PageUtil;
import com.zenith.bbykz.api.register.train.RegisterTrainCourseFeedbackService;
import com.zenith.bbykz.api.register.train.RegisterTrainCourseService;
import com.zenith.bbykz.dao.register.train.RegisterTrainCourseFeedbackMapper;
import com.zenith.bbykz.model.converter.register.train.RegisterTrainCourseFeedbackConverter;
import com.zenith.bbykz.model.dto.register.train.RegisterTrainCourseFeedbackDTO;
import com.zenith.bbykz.model.dto.register.train.RegisterTrainCourseFeedbackListDTO;
import com.zenith.bbykz.model.entity.register.train.RegisterTrainCourse;
import com.zenith.bbykz.model.entity.register.train.RegisterTrainCourseFeedback;
import com.zenith.bbykz.model.vo.register.train.RegisterTrainCourseFeedbackVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

/**
 * <p>
 * 登记信息-培训管理-反馈意见 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @date 2024-03-05 15:44:53
 */
@Service
public class RegisterTrainCourseFeedbackServiceImpl extends ServiceImpl<RegisterTrainCourseFeedbackMapper, RegisterTrainCourseFeedback> implements RegisterTrainCourseFeedbackService {

    @Autowired
    private RegisterTrainCourseFeedbackConverter registerTrainCourseFeedbackConverter;
    @Autowired
    private RegisterTrainCourseFeedbackMapper registerTrainCourseFeedbackMapper;
    @Autowired
    private RegisterTrainCourseService registerTrainCourseService;

    @Override
    public Result<RegisterTrainCourseFeedback> save(RegisterTrainCourseFeedbackDTO dto) {
        RegisterTrainCourseFeedback entity = registerTrainCourseFeedbackConverter.dto2Entity(dto);
        String courseId = dto.getCourseId();
        if (StrUtil.isNotBlank(courseId)) {
            RegisterTrainCourse trainCourse = registerTrainCourseService.getById(courseId);
            if (Objects.nonNull(trainCourse)) {
                entity.setCourseName(trainCourse.getName());
            }
        }
        boolean flag = this.save(entity);
        return Result.ok(entity);
    }

    @Override
    public Result<RegisterTrainCourseFeedbackVO> findById(String id) {
        RegisterTrainCourseFeedback entity = this.getById(id);
        RegisterTrainCourseFeedbackVO vo = registerTrainCourseFeedbackConverter.entity2Vo(entity);
        return Result.ok(vo);
    }

    @Override
    public Result<Boolean> update(RegisterTrainCourseFeedbackDTO dto) {
        String courseId = dto.getCourseId();
        if (StrUtil.isNotBlank(courseId)) {
            RegisterTrainCourse trainCourse = registerTrainCourseService.getById(courseId);
            if (Objects.nonNull(trainCourse)) {
                dto.setCourseName(trainCourse.getName());
            }
        }
        boolean flag = this.updateById(registerTrainCourseFeedbackConverter.dto2Entity(dto));
        return flag ? Result.ok() : Result.fail();
    }

    @Override
    public Result<Boolean> delete(String id) {
        boolean flag = this.removeById(id);
        return flag ? Result.ok() : Result.fail();
    }

    @Override
    public Page<RegisterTrainCourseFeedbackVO> list(RegisterTrainCourseFeedbackListDTO dto) {
        LambdaQueryWrapper<RegisterTrainCourseFeedback> queryWrapper = new LambdaQueryWrapper<>(RegisterTrainCourseFeedback.class);
        if (Objects.equals(dto.getIsOwn(), CommonConstant.TRUE_INT)) {
            queryWrapper.eq(RegisterTrainCourseFeedback::getCreateUser, RequestHolder.getCurrUser().getUserId());
        }
        String courseName = dto.getCourseName();
        if (StrUtil.isNotBlank(courseName)) {
            queryWrapper.like(RegisterTrainCourseFeedback::getCourseName, courseName);
        }
        queryWrapper.like(StrUtil.isNotBlank(dto.getKeyword()), RegisterTrainCourseFeedback::getDescribe, dto.getKeyword());
        queryWrapper.orderByDesc(RegisterTrainCourseFeedback::getCreateTime);
        final Page<RegisterTrainCourseFeedback> page = registerTrainCourseFeedbackMapper.selectPage(new Page<>(dto.getPageNum(), dto.getPageSize()), queryWrapper);
        List<RegisterTrainCourseFeedback> records = page.getRecords();
        if (CollUtil.isEmpty(records)) {
            return PageUtil.change(page, null);
        }
        List<RegisterTrainCourseFeedbackVO> result = new ArrayList<>();
        records.forEach(et -> result.add(registerTrainCourseFeedbackConverter.entity2Vo(et)));
        return PageUtil.change(page, result);
    }
}
