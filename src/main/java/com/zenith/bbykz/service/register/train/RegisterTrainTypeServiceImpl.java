package com.zenith.bbykz.service.register.train;

import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.efficient.common.constant.CommonConstant;
import com.efficient.common.entity.TreeNode;
import com.efficient.common.result.Result;
import com.efficient.common.util.TreeUtil;
import com.zenith.bbykz.api.register.train.RegisterTrainTypeService;
import com.zenith.bbykz.dao.register.train.RegisterTrainTypeMapper;
import com.zenith.bbykz.model.converter.register.train.RegisterTrainTypeConverter;
import com.zenith.bbykz.model.dto.register.train.RegisterTrainTypeDTO;
import com.zenith.bbykz.model.dto.register.train.RegisterTrainTypeListDTO;
import com.zenith.bbykz.model.entity.register.train.RegisterTrainType;
import com.zenith.bbykz.model.vo.register.train.RegisterTrainTypeVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

/**
 * <p>
 * 登记信息-培训管理-课程分类 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @date 2024-03-05 15:05:10
 */
@Service
public class RegisterTrainTypeServiceImpl extends ServiceImpl<RegisterTrainTypeMapper, RegisterTrainType> implements RegisterTrainTypeService {

    @Autowired
    private RegisterTrainTypeConverter registerTrainTypeConverter;
    @Autowired
    private RegisterTrainTypeMapper registerTrainTypeMapper;

    @Override
    public Result<RegisterTrainType> save(RegisterTrainTypeDTO dto) {
        RegisterTrainType entity = registerTrainTypeConverter.dto2Entity(dto);
        boolean flag = this.save(entity);
        return Result.ok(entity);
    }

    @Override
    public Result<RegisterTrainTypeVO> findById(String id) {
        RegisterTrainType entity = this.getById(id);
        RegisterTrainTypeVO vo = registerTrainTypeConverter.entity2Vo(entity);
        return Result.ok(vo);
    }

    @Override
    public Result<Boolean> update(RegisterTrainTypeDTO dto) {
        boolean flag = this.updateById(registerTrainTypeConverter.dto2Entity(dto));
        return flag ? Result.ok() : Result.fail();
    }

    @Override
    public Result<Boolean> delete(String id) {
        boolean flag = this.removeById(id);
        return flag ? Result.ok() : Result.fail();
    }

    @Override
    public Page<RegisterTrainType> list(RegisterTrainTypeListDTO dto) {
        LambdaQueryWrapper<RegisterTrainType> queryWrapper = new LambdaQueryWrapper<>(RegisterTrainType.class);
        queryWrapper.like(StrUtil.isNotBlank(dto.getKeyword()), RegisterTrainType::getName, dto.getKeyword());
        queryWrapper.orderByDesc(RegisterTrainType::getCreateTime);
        final Page<RegisterTrainType> page = registerTrainTypeMapper.selectPage(new Page<>(dto.getPageNum(), dto.getPageSize()), queryWrapper);
        return page;
    }

    @Override
    public List<TreeNode> select(RegisterTrainTypeDTO dto) {
        LambdaQueryWrapper<RegisterTrainType> queryWrapper = new LambdaQueryWrapper<>(RegisterTrainType.class);
        if (Objects.nonNull(dto.getEnabled())) {
            queryWrapper.eq(RegisterTrainType::getEnabled, dto.getEnabled());
        }
        queryWrapper.orderByDesc(RegisterTrainType::getCreateTime);
        List<RegisterTrainType> list = this.list(queryWrapper);
        List<TreeNode> treeNodeList = new ArrayList<>();
        for (RegisterTrainType trainType : list) {
            TreeNode node = TreeNode.builder()
                    .code(trainType.getId())
                    .id(trainType.getId())
                    .parentId(trainType.getParentId())
                    .name(trainType.getName())
                    .isRoot(Objects.isNull(trainType.getParentId()))
                    .build();
            treeNodeList.add(node);
        }
        List<TreeNode> listTree = TreeUtil.createListTree(treeNodeList);
        return listTree;
    }

    @Override
    public Result<Boolean> enabled(String id, Integer enabled) {
        registerTrainTypeMapper.enabled(id, enabled);
        return Result.ok();
    }
}
