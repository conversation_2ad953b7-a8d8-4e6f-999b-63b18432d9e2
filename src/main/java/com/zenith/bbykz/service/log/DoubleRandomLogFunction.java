package com.zenith.bbykz.service.log;

import com.efficient.logs.api.LogFunction;
import com.zenith.bbykz.api.register.random.DoubleRandomService;
import com.zenith.bbykz.model.entity.register.random.DoubleRandom;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @since 2024/7/3 17:43
 */
@Component
public class DoubleRandomLogFunction implements LogFunction {
    @Autowired
    private DoubleRandomService doubleRandomService;

    @Override
    public String apply(Object o) {
        DoubleRandom doubleRandom = doubleRandomService.getById(String.valueOf(o));
        return doubleRandom.getUnitName();
    }
}
