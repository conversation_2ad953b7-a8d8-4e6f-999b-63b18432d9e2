package com.zenith.bbykz.service.efficient;

import com.efficient.common.api.StartedEventServer;
import com.efficient.common.util.HanLPUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.io.IOException;

@Slf4j
@Service
public class StartedEventServerImpl implements StartedEventServer {

    @Override
    public void init() {
        try {
            HanLPUtil.init(null, null, null, null);
            log.info("init HanLP success!");
        } catch (IOException e) {
            log.error("init HanLP fail!", e);
        }
    }
}
