package com.zenith.bbykz.service.efficient;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.efficient.common.auth.RequestHolder;
import com.efficient.common.constant.CommonConstant;
import com.efficient.common.constant.DbConstant;
import com.efficient.common.result.Result;
import com.efficient.common.util.IdUtil;
import com.efficient.configs.util.PageUtil;
import com.efficient.system.api.DictCodeService;
import com.efficient.ykz.api.YkzApiService;
import com.efficient.ykz.model.dto.todo.YkzTodoInfo;
import com.efficient.ykz.model.vo.YkzTodoInfoVO;
import com.zenith.bbykz.api.SysUserService;
import com.zenith.bbykz.api.efficient.SysNotifyService;
import com.zenith.bbykz.api.register.dynamic.UnitBdFlowService;
import com.zenith.bbykz.api.register.dynamic.UnitBdService;
import com.zenith.bbykz.common.constant.DictConstant;
import com.zenith.bbykz.common.enums.NotifyState;
import com.zenith.bbykz.common.enums.efficient.NotifyRecipientSystemEnum;
import com.zenith.bbykz.common.enums.efficient.NotifyStateEnum;
import com.zenith.bbykz.common.enums.efficient.NotifyTypeEnum;
import com.zenith.bbykz.dao.efficient.SysNotifyMapper;
import com.zenith.bbykz.model.base.UserInfo;
import com.zenith.bbykz.model.converter.efficient.SysNotifyConverter;
import com.zenith.bbykz.model.dto.efficient.SysNotifyDTO;
import com.zenith.bbykz.model.dto.efficient.SysNotifyListDTO;
import com.zenith.bbykz.model.entity.OrgInfo;
import com.zenith.bbykz.model.entity.SysUser;
import com.zenith.bbykz.model.entity.efficient.SysNotify;
import com.zenith.bbykz.model.entity.register.dynamic.UnitBd;
import com.zenith.bbykz.model.vo.efficient.SysNotifyVO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;

/**
 * <p>
 * 系统消息通知 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @date 2024-03-18 15:07:00
 */
@Service
@Slf4j
public class SysNotifyServiceImpl extends ServiceImpl<SysNotifyMapper, SysNotify> implements SysNotifyService {

    @Autowired
    private SysNotifyConverter sysNotifyConverter;
    @Autowired
    private SysNotifyMapper sysNotifyMapper;
    @Autowired
    private YkzApiService ykzApiService;

    @Autowired
    private DictCodeService dictCodeService;
    @Autowired
    private SysUserService sysUserService;
    @Autowired
    private UnitBdFlowService unitBdFlowService;
    @Autowired
    private UnitBdService unitBdService;

    @Override
    public SysNotify saveNotify(SysNotifyDTO dto) {
        SysNotify notify = new SysNotify();
        notify.setBizId(dto.getBizId());
        notify.setBatchNum(dto.getBatchNum());
        notify.setSystemId(RequestHolder.getCurrSystemId());
        notify.setMenuId(dto.getMenuId());
        notify.setNotifyType(dto.getNotifyType());
        notify.setBizType(dto.getBizType());
        notify.setRecipientSystemType(dto.getRecipientSystemType());
        UserInfo currUser = (UserInfo) RequestHolder.getCurrUser();
        notify.setCreateUserId(currUser.getUserId());
        notify.setCreateZwddId(currUser.getZwddId());
        notify.setTitle(dto.getTitle());
        notify.setContent(dto.getContent());
        notify.setRemark(dto.getRemark());
        notify.setPcUrl(dto.getPcUrl());
        notify.setAppUrl(dto.getAppUrl());
        notify.setRecipientUserId(dto.getRecipientUserId());

        if (Objects.isNull(dto.getRecipientZwddId())) {
            SysUser sysUser = sysUserService.getById(notify.getRecipientUserId());
            notify.setRecipientZwddId(sysUser.getZwddId());
        } else {
            notify.setRecipientZwddId(dto.getRecipientZwddId());
        }
        notify.setState(NotifyStateEnum.DFS.getCode());
        notify.setCreateTime(new Date());
        notify.setIsDelete(CommonConstant.FALSE_INT);
        String userPostId = currUser.getUserPostId();
        OrgInfo orgInfo = sysUserService.findOrgInfoByUserAndSystemIdAndPostId(notify.getCreateUserId(), RequestHolder.getCurrSystemId(), userPostId);

        Optional.ofNullable(orgInfo).ifPresent(et -> {
            notify.setCreateUnitId(et.getId());
            notify.setCreateUnitName(et.getShortName());
        });
        OrgInfo orgInfoRecipient = sysUserService.findOrgInfoByUserAndSystemId(notify.getRecipientUserId(), RequestHolder.getCurrSystemId());
        Optional.ofNullable(orgInfoRecipient).ifPresent(et -> {
            notify.setRecipientUnitId(et.getId());
            notify.setRecipientUnitName(et.getShortName());
        });
        this.save(notify);
        return notify;
    }

    @Override
    public SysNotify getById(String notifyId) {
        LambdaQueryWrapper<SysNotify> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.eq(SysNotify::getId, notifyId);
        lambdaQueryWrapper.eq(SysNotify::getIsDelete, CommonConstant.FALSE_INT);
        lambdaQueryWrapper.last(DbConstant.LIMIT_ONE);
        return sysNotifyMapper.selectOne(lambdaQueryWrapper);
    }

    @Override
    public boolean changeState(String state, String notifyId) {
        SysNotify sysNotify = this.getById(notifyId);
        if (Objects.isNull(sysNotify)) {
            return false;
        }
        if (StrUtil.equals(state, NotifyStateEnum.YCK.getCode())) {
            sysNotify.setReadTime(new Date());
        } else if (StrUtil.equals(state, NotifyStateEnum.YCL.getCode())) {
            sysNotify.setHandleTime(new Date());
        }
        sysNotify.setState(state);
        sysNotify.setUpdateTime(new Date());
        return this.updateById(sysNotify);
    }

    @Override
    public boolean changeState(String state, String recipientMsgId, String notifyId) {
        SysNotify sysNotify = this.getById(notifyId);
        if (Objects.isNull(sysNotify)) {
            return false;
        }
        sysNotify.setState(state);
        sysNotify.setRecipientMsgId(recipientMsgId);
        sysNotify.setUpdateTime(new Date());
        return this.updateById(sysNotify);
    }

    @Override
    public boolean deleteById(String notifyId) {
        SysNotify sysNotify = this.getById(notifyId);
        if (Objects.isNull(sysNotify)) {
            return false;
        }
        sysNotify.setIsDelete(CommonConstant.TRUE_INT);
        sysNotify.setUpdateTime(new Date());

        return this.updateById(sysNotify);
    }

    @Override
    public boolean deleteByBatchNum(String batchNum) {
        LambdaUpdateWrapper<SysNotify> lambdaUpdateWrapper = new LambdaUpdateWrapper<>();
        lambdaUpdateWrapper.set(SysNotify::getIsDelete, CommonConstant.TRUE_INT);
        lambdaUpdateWrapper.set(SysNotify::getUpdateTime, new Date());
        lambdaUpdateWrapper.eq(SysNotify::getBatchNum, batchNum);
        return this.update(lambdaUpdateWrapper);
    }

    @Override
    public Result sendTodo(SysNotifyDTO dto) {
        String pcUrl = dto.getPcUrl();
        String appUrl = dto.getAppUrl();
        String batchNum = dto.getBatchNum();
        if (StrUtil.isBlank(batchNum)) {
            batchNum = IdUtil.generateBatchNumber();
        }
        dto.setNotifyType(NotifyTypeEnum.DB.getCode());
        dto.setBatchNum(batchNum);
        dto.setRecipientSystemType(NotifyRecipientSystemEnum.YKZ.getCode());
        SysNotify sysNotify = this.saveNotify(dto);
        sysNotify.setPcUrl(pcUrl);
        sysNotify.setAppUrl(appUrl);
        YkzTodoInfo todoInfo = new YkzTodoInfo();
        todoInfo.setSubject(dto.getTitle());
        todoInfo.setCreatorId(sysNotify.getCreateZwddId());
        todoInfo.setBizTaskId(sysNotify.getId());
        todoInfo.setMobileUrl(appUrl);
        todoInfo.setUrl(pcUrl);
        todoInfo.setAssigneeId(sysNotify.getRecipientZwddId());
        Result<YkzTodoInfoVO> todo = ykzApiService.createTodo(todoInfo);
        if (!Objects.equals(todo.getCode(), Result.ok().getCode())) {
            return todo;
        }
        YkzTodoInfoVO data = todo.getData();
        String taskUuid = data.getTaskUuid();
        sysNotify.setUpdateTime(new Date());
        sysNotify.setState(NotifyStateEnum.YFS.getCode());
        sysNotify.setPcUrl(data.getUrl());
        sysNotify.setAppUrl(data.getMobileUrl());
        sysNotify.setState(NotifyStateEnum.YFS.getCode());
        sysNotify.setRecipientMsgId(taskUuid);
        this.updateById(sysNotify);
        return todo;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Result finishTodo(String userId, String notifyId) {
        String zwddId = sysUserService.findZwddIdById(userId);
        if (Objects.isNull(zwddId)) {
            return Result.fail();
        }
        SysNotify sysNotify = this.getById(notifyId);
        if (Objects.isNull(sysNotify) || Objects.isNull(sysNotify.getRecipientMsgId())) {
            return Result.fail();
        }
        if (StrUtil.equals(sysNotify.getState(), NotifyStateEnum.YCK.getCode())) {
            return Result.ok();
        }
        Result<String> result = ykzApiService.finishTodo(zwddId, sysNotify.getRecipientMsgId(), false);
        if (Objects.equals(result.getCode(), Result.ok().getCode())) {
            this.changeState(NotifyStateEnum.YCK.getCode(), notifyId);
            if (StrUtil.equals(sysNotify.getMenuId(), "BDXX")) {
                String bizId = sysNotify.getBizId();
                UnitBd unitBd = unitBdService.getById(bizId);
                unitBd.setState(NotifyState.YCK.getCode());
                unitBdService.updateById(unitBd);
                unitBdFlowService.saveByBizId(bizId, NotifyState.YCK);
            }
        }
        return result;
    }

    @Override
    public Page<SysNotifyVO> list(SysNotifyListDTO dto) {
        LambdaQueryWrapper<SysNotify> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.eq(SysNotify::getIsDelete, CommonConstant.FALSE_INT);
        Integer pageType = dto.getPageType();
        String unitName = dto.getUnitName();
        if (Objects.equals(pageType, CommonConstant.TRUE_INT)) {
            lambdaQueryWrapper.eq(SysNotify::getCreateUserId, RequestHolder.getCurrUser().getUserId());

            if (StrUtil.isNotBlank(unitName)) {
                lambdaQueryWrapper.like(SysNotify::getRecipientUnitName, unitName);
            }
        } else {
            lambdaQueryWrapper.eq(SysNotify::getRecipientUserId, RequestHolder.getCurrUser().getUserId());
            if (StrUtil.isNotBlank(unitName)) {
                lambdaQueryWrapper.like(SysNotify::getCreateUnitName, unitName);
            }
        }

        Date startDate = dto.getStartDate();
        Date endDate = dto.getEndDate();
        if (Objects.nonNull(startDate)) {
            lambdaQueryWrapper.ge(SysNotify::getCreateTime, DateUtil.beginOfDay(startDate));
        }
        if (Objects.nonNull(endDate)) {
            lambdaQueryWrapper.ge(SysNotify::getCreateTime, DateUtil.endOfDay(endDate));
        }
        Integer isRead = dto.getIsRead();
        if (Objects.equals(isRead, 1)) {
            // 已读
            lambdaQueryWrapper.in(SysNotify::getState, Arrays.asList("3", "4"));
        } else if (Objects.equals(isRead, 2)) {
            // 未读
            lambdaQueryWrapper.in(SysNotify::getState, Arrays.asList("1", "2"));
        }
        lambdaQueryWrapper.orderByDesc(SysNotify::getCreateTime);
        Page<SysNotify> page = new Page<>(dto.getPageNum(), dto.getPageSize());
        Page<SysNotify> notifyPage = this.page(page, lambdaQueryWrapper);
        List<SysNotify> records = notifyPage.getRecords();
        if (CollUtil.isEmpty(records)) {
            return PageUtil.change(notifyPage, null);
        }
        Map<String, String> mapByType = dictCodeService.findMapByType(DictConstant.NOTIFY_STATE);

        List<SysNotifyVO> voList = new ArrayList<>();
        records.forEach(sysNotify -> {
            SysNotifyVO vo = sysNotifyConverter.entity2Vo(sysNotify);
            vo.setStateName(mapByType.get(vo.getState()));
            voList.add(vo);
        });
        return PageUtil.change(notifyPage, voList);
    }

    @Override
    public SysNotify getByBizId(String bizId) {
        LambdaQueryWrapper<SysNotify> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.eq(SysNotify::getBizId, bizId);
        lambdaQueryWrapper.orderByDesc(SysNotify::getUpdateTime);
        lambdaQueryWrapper.last(DbConstant.LIMIT_ONE);
        return this.getOne(lambdaQueryWrapper);
    }

    @Override
    public Result<SysNotifyVO> find(String id) {
        SysNotify byId = this.getById(id);
        SysNotifyVO vo = sysNotifyConverter.entity2Vo(byId);
        Map<String, String> mapByType = dictCodeService.findMapByType(DictConstant.NOTIFY_STATE);
        vo.setStateName(mapByType.get(vo.getState()));
        return Result.ok(vo);
    }

    @Override
    public List<SysNotify> findListByBizId(String bizId) {
        LambdaQueryWrapper<SysNotify> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.eq(SysNotify::getBizId, bizId);
        lambdaQueryWrapper.orderByDesc(SysNotify::getUpdateTime);
        return this.list(lambdaQueryWrapper);
    }
}
