package com.zenith.bbykz.service.efficient;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.efficient.common.constant.CommonConstant;
import com.efficient.common.result.Result;
import com.efficient.system.api.SysConfigService;
import com.zenith.bbykz.common.constant.SystemConstant;
import com.zenith.bbykz.common.constant.UnitTypeEnum;
import com.efficient.ykz.api.YkzUserCenterHandleService;
import com.efficient.ykz.constant.YkzConstant;
import com.efficient.ykz.model.vo.YkzOrg;
import com.efficient.ykz.model.vo.YkzUser;
import com.efficient.ykz.model.vo.YkzUserPost;
import com.efficient.ykz.service.YkzCommonServer;
import com.zenith.bbykz.api.*;
import com.zenith.bbykz.common.enums.SystemIdEnum;
import com.zenith.bbykz.model.entity.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.*;

/**
 * <AUTHOR>
 * @since 2024/3/22 11:06
 */
@Slf4j
public class YkzUserCenterHandleCustomService implements YkzUserCenterHandleService {
    @Autowired
    private OrgInfoService orgInfoService;
    @Autowired
    private SysUserService userService;
    @Autowired
    private SysUserSystemService userSystemService;
    @Autowired
    private YkzCommonServer ykzCommonServer;
    @Autowired
    private SysUserRoleService userRoleService;
    @Autowired
    private SysConfigService configService;
    @Autowired
    private SysUserPostService sysUserPostService;

    @Override
    public Result<YkzOrg> handleOrgByCode(YkzOrg data) {
        Long id = data.getId();
        // 先查找父级
        OrgInfo sysUnit = orgInfoService.getById(String.valueOf(id));
        if (Objects.nonNull(sysUnit)) {
            log.info("单位已存在,未入库：名称: {},id: {}", data.getName(), data.getId());
        } else {
            sysUnit = new OrgInfo();
            this.setOrgInfo(sysUnit, data);
            orgInfoService.save(sysUnit);
        }
        log.info("处理入库逻辑结束");
        return Result.ok(data);
    }

    private void setOrgInfo(OrgInfo sysUnit, YkzOrg data) {
        sysUnit.setId(String.valueOf(data.getId()));
        sysUnit.setParentId(String.valueOf(data.getParentId()));
        sysUnit.setName(data.getName());
        sysUnit.setShortName(data.getGovShortName());
        String levelCode = orgInfoService.createLevelCode(String.valueOf(data.getParentId()));
        sysUnit.setLevelCode(levelCode);
        sysUnit.setUnitType(YkzUserCenterHandleService.getUnitType(data.getOrgType()));
        sysUnit.setSort(data.getDisplayOrder());
//        sysUnit.setAddress(data.getGovAddress());
//        sysUnit.setGeocode(data.getGovDivisionCode());
        sysUnit.setOrgCode(data.getOrganizationCode());
        sysUnit.setParentOrgCode(data.getParentOrganizationCode());
//        sysUnit.setCreditCode(data.getCreditCode());
//        sysUnit.setAreaLevel(data.getAreaLevel());
//        sysUnit.setPrincipal(data.getPrincipal());
        String belong = orgInfoService.getBelongById(sysUnit.getParentId());
        if (StrUtil.isBlank(belong)) {
            belong = sysUnit.getName();
        } else {
            belong = belong + "-" + sysUnit.getName();
        }
        sysUnit.setBelong(belong);
//        sysUnit.setRemark(data.getRemark());
//        sysUnit.setIsEnable(data.getIsEnable());
        sysUnit.setCreateTime(new Date(data.getCreateTime()));
        sysUnit.setUpdateTime(new Date(data.getUpdateTime()));
        sysUnit.setIsDelete(data.getIsDeleted());
//        sysUnit.setPullTime(new Date());
    }

    @Override
    public Result<List<YkzOrg>> handleOrgByCodeList(List<YkzOrg> data) {
        for (YkzOrg ykzOrg : data) {
            this.handleOrgByCode(ykzOrg);
        }
        log.info("处理入库逻辑结束");
        return Result.ok(data);
    }

    @Override
    public Result<List<YkzOrg>> handleOrgByParentCode(String orgCode, boolean includeTop, boolean flattenTree, List<YkzOrg> data) {
        if (StrUtil.equalsAny(orgCode, YkzConstant.YKZ_ORG_TOP_CODE_DEV, YkzConstant.YKZ_ORG_TOP_CODE)) {
            // 虚拟顶级节点
            YkzOrg ykzOrg = data.get(0);
            List<YkzOrg> children = ykzOrg.getChildren();
            for (YkzOrg child : children) {
                this.setChild(child);
            }
        } else {
            for (YkzOrg ykzOrg : data) {
                this.setChild(ykzOrg);
            }
        }
        log.info("处理入库逻辑结束");
        return Result.ok(data);
    }

    private void setChild(YkzOrg ykzOrg) {
        this.handleOrgByCode(ykzOrg);
        List<YkzOrg> children = ykzOrg.getChildren();
        if (CollUtil.isNotEmpty(children)) {
            for (YkzOrg child : children) {
                this.setChild(child);
            }
        }
    }

    @Override
    public Result<YkzUser> handleUserByMobile(YkzUser data) {
        SysUser sysUser = userService.getById(data.getId());
        if (Objects.nonNull(sysUser)) {
            log.info("人员已存在,未入库：名称: {},id: {}", data.getName(), data.getId());
        } else {
            sysUser = new SysUser();
            this.setSysUser(sysUser, data);
            userService.save(sysUser);
        }
        log.info("处理入库逻辑结束");
        return Result.ok(data);
    }

    private void setSysUser(SysUser sysUser, YkzUser data) {
        sysUser.setId(String.valueOf(data.getId()));
        sysUser.setName(data.getName());
        sysUser.setAccount(data.getUsername());
        sysUser.setPassword(ykzCommonServer.encrypt(SystemConstant.DEFAULT_PASSWORD));
        sysUser.setZwddId(data.getAccountId());
        sysUser.setPhone(data.getMobile());
        sysUser.setCreateTime(new Date());
        sysUser.setIsDelete(CommonConstant.FALSE_INT);
    }

    @Override
    public Result<List<YkzUser>> handleUserByMobileList(List<YkzUser> data) {
        for (YkzUser ykzUser : data) {
            this.handleUserByMobile(ykzUser);
        }
        log.info("处理入库逻辑结束");
        return Result.ok(data);
    }

    @Override
    public Result<YkzUser> handleUserByZwddId(YkzUser data) {
        return this.handleUserByMobile(data);
    }

    @Override
    public Result<List<YkzUser>> handleUserByZwddIdList(List<YkzUser> data) {
        return this.handleUserByMobileList(data);
    }

    @Override
    public Result<List<YkzUserPost>> handleUserPostByZwddId(List<YkzUserPost> data) {
        List<SysUserPost> sysUserPostList = new ArrayList<>();

        SysUser sysUser = userService.getByZwddId(data.get(0).getAccountId());
        List<String> commonList = SystemIdEnum.commonList();
        Map<String, String> defaultRole = new HashMap<>();
        for (String string : commonList) {
            String service = configService.findByCode(string + "_DEFAULT_ROLE", String.class);
            if (StrUtil.isNotEmpty(service)) {
                defaultRole.put(string, service);
            }
        }

        for (YkzUserPost ykzUserPost : data) {
            String organizationCode = ykzUserPost.getOrganizationCode();
            OrgInfo sysUnit = orgInfoService.getByOrgCode(organizationCode);
            if (Objects.isNull(sysUnit)) {
                log.error("人员职务所在单位未入库：orgCode: {}", organizationCode);
                continue;
            }
            String zwddId = ykzUserPost.getAccountId();
            String id = sysUnit.getId() + "_" + zwddId;
            if (Objects.isNull(sysUser)) {
                log.error("人员信息未入库：zwddId: {}", zwddId);
                continue;
            }
            SysUserPost sysUserPost = sysUserPostService.getById(id);
            if (Objects.nonNull(sysUserPost)) {
                log.info("用户职务已存在,未入库：名称: {},id: {}", organizationCode, zwddId);
            } else {
                sysUserPost = new SysUserPost();
                sysUserPost.setId(id);
                sysUserPost.setUserId(sysUser.getId());
                sysUserPost.setDeptId(sysUnit.getId());
                sysUserPost.setDeptLevelCode(sysUnit.getLevelCode());
                if (StrUtil.equals(sysUnit.getUnitType(), UnitTypeEnum.NBJG.getCode())) {
                    OrgInfo unitInfo = orgInfoService.getUnitByDeptId(sysUnit.getId());
                    if (Objects.nonNull(unitInfo)) {
                        sysUserPost.setUnitId(unitInfo.getId());
                        sysUserPost.setUnitLevelCode(unitInfo.getLevelCode());
                    }
                } else {
                    sysUserPost.setUnitId(sysUserPost.getDeptId());
                    sysUserPost.setUnitLevelCode(sysUserPost.getDeptLevelCode());
                }

                Integer postType = ykzUserPost.getPostType();
                sysUserPost.setMainJob(Objects.equals(postType, 1) ? 1 : 0);
                sysUserPost.setPostName(ykzUserPost.getPosJob());
                sysUserPost.setPostName(ykzUserPost.getPosJob());
                sysUserPost.setCreateTime(new Date());
                sysUserPost.setIsDelete(CommonConstant.FALSE_INT);
                sysUserPost.setPullTime(new Date());
                sysUserPostList.add(sysUserPost);
            }
        }
        if (CollUtil.isNotEmpty(sysUserPostList)) {
            sysUserPostService.saveOrUpdateBatch(sysUserPostList);
        }

        log.info("处理入库逻辑结束");
        return Result.ok(data);
    }
}
