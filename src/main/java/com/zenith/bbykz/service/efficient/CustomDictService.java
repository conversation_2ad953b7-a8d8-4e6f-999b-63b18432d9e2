package com.zenith.bbykz.service.efficient;

import cn.hutool.core.collection.CollUtil;
import com.efficient.cache.api.CacheUtil;
import com.efficient.cache.constant.CacheConstant;
import com.zenith.bbykz.api.GeocodeService;
import com.zenith.bbykz.api.bz.common.SystemcodeService;
import com.zenith.bbykz.model.entity.Geocode;
import com.zenith.bbykz.model.entity.bz.common.Systemcode;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

import static com.efficient.cache.constant.CacheConstant.CACHE_DICT;
import static com.zenith.bbykz.common.constant.DictConstant.GEOCODE;
import static com.zenith.bbykz.common.constant.DictConstant.SYSTEMCODE;

/**
 * <AUTHOR>
 * @since 2024/9/6 14:21
 */
@Slf4j
@Service
public class CustomDictService {
    @Autowired
    private GeocodeService geocodeService;
    @Autowired
    private CacheUtil cacheUtil;
    @Autowired
    private SystemcodeService systemcodeService;

    public void loadGeocodes() {
        List<Geocode> geocodeList = geocodeService.findAll();
        cacheUtil.put(CACHE_DICT, GEOCODE, geocodeList);
    }

    public void loadSystemCodes() {
        List<Systemcode> systemcodeList = systemcodeService.getAll();
        cacheUtil.put(CACHE_DICT, SYSTEMCODE, systemcodeList);
    }

    public List<Geocode> getGeocodes() {
        List<Geocode> geocodeList = cacheUtil.get(CacheConstant.CACHE_DICT, GEOCODE);
        if (CollUtil.isEmpty(geocodeList)) {
            geocodeList = geocodeService.findAll();
            cacheUtil.put(CACHE_DICT, GEOCODE, geocodeList);
        }
        return geocodeList;
    }

    public List<Systemcode> getSystemCodes() {
        List<Systemcode> systemcodeList = cacheUtil.get(CACHE_DICT, SYSTEMCODE);
        if (CollUtil.isEmpty(systemcodeList)) {
            systemcodeList = systemcodeService.getAll();
            cacheUtil.put(CACHE_DICT, SYSTEMCODE, systemcodeList);
        }
        return systemcodeList;
    }
}
