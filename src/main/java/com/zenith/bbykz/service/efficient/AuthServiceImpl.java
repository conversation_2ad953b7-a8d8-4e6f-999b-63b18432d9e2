package com.zenith.bbykz.service.efficient;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.efficient.auth.api.AuthService;
import com.efficient.auth.model.dto.LoginInfo;
import com.efficient.auth.model.entity.UserAuthInfo;
import com.efficient.common.auth.UserTicket;
import com.efficient.common.constant.CommonConstant;
import com.efficient.common.result.Result;
import com.zenith.bbykz.api.OrgInfoService;
import com.zenith.bbykz.api.SysUserPostService;
import com.zenith.bbykz.api.SysUserService;
import com.zenith.bbykz.api.SysUserSystemService;
import com.zenith.bbykz.common.result.BbResultEnum;
import com.zenith.bbykz.model.base.UserInfo;
import com.zenith.bbykz.model.entity.OrgInfo;
import com.zenith.bbykz.model.entity.SysUser;
import com.zenith.bbykz.model.entity.SysUserPost;
import com.zenith.bbykz.model.entity.SysUserSystem;
import com.zenith.bbykz.model.vo.LoginSystem;
import com.zenith.bbykz.model.vo.LoginUnit;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2023/9/13 13:41
 */
@Service
public class AuthServiceImpl implements AuthService {
    @Autowired
    private SysUserService sysUserService;
    @Autowired
    private SysUserSystemService sysUserSystemService;
    @Autowired
    private SysUserPostService sysUserPostService;
    @Autowired
    private OrgInfoService orgInfoService;

    @Override
    public UserAuthInfo getUserByAccount(String s) {
        SysUser sysUser = sysUserService.getByAccount(s);
        return getUserAuthInfo(sysUser);
    }

    @Override
    public UserAuthInfo getUserByZwddId(String zwddId) {
        SysUser sysUser = sysUserService.getByZwddId(zwddId);
        return getUserAuthInfo(sysUser);
    }

    @Override
    public UserAuthInfo getUserByUserId(String s) {
        return null;
    }



    @Override
    public boolean unLockUser(String userId) {
        LambdaUpdateWrapper<SysUser> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.set(SysUser::getIsLock, CommonConstant.FALSE_INT);
        updateWrapper.set(SysUser::getUnlockTime, null);
        updateWrapper.eq(SysUser::getId, userId);
        return sysUserService.update(updateWrapper);
    }

    @Override
    public boolean lockUser(String userId, Date unLockTime) {
        LambdaUpdateWrapper<SysUser> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.set(SysUser::getIsLock, CommonConstant.TRUE_INT);
        updateWrapper.set(SysUser::getUnlockTime, unLockTime);
        updateWrapper.eq(SysUser::getId, userId);
        return sysUserService.update(updateWrapper);
    }

    @Override
    public Result<UserTicket> loadUserTicket(UserAuthInfo userAuthInfo) {
        UserInfo userInfo = new UserInfo();
        String userId = userAuthInfo.getUserId();
        SysUser sysUser = sysUserService.getById(userId);
        userInfo.setSysUser(sysUser);
        List<SysUserPost> userPostList = sysUserPostService.findByUserId(userId);
        if (CollUtil.isEmpty(userPostList)) {
            return Result.build(BbResultEnum.USER_SYSTEM_NULL);
        }
        String manageCode = sysUser.getManageCode();
        String levelCodeById = orgInfoService.findLevelCodeById(manageCode);
        userInfo.setLevelCode(levelCodeById);
        List<String> orgIdList = userPostList.stream().map(SysUserPost::getDeptId).collect(Collectors.toList());
        List<OrgInfo> orgInfoList = orgInfoService.findByIdList(orgIdList);
        Map<String, OrgInfo> orgIdMap = orgInfoList.stream().collect(Collectors.toMap(OrgInfo::getId, et -> et, (k1, k2) -> k1));

        SysUserPost sysUserPost = userPostList.get(0);
        List<SysUserSystem> sysUserSystemList = sysUserSystemService.findByUserPostId(sysUserPost.getId());
        if (CollUtil.isEmpty(sysUserSystemList)) {
            return Result.build(BbResultEnum.USER_SYSTEM_NULL);
        }
        List<LoginSystem> systemList = new ArrayList<>();
        for (SysUserSystem sysUserSystem : sysUserSystemList) {
            systemList.add(UserInfo.setLoginSystem(sysUserSystem,sysUser));
        }

        userInfo.setSystemList(systemList);

        userInfo.setUserPostId(sysUserPost.getId());
        userInfo.setUserUnitId(sysUserPost.getId());
        List<LoginUnit> loginUnitList = new ArrayList<>();
        for (SysUserPost userPost : userPostList) {
            LoginUnit forUnit = new LoginUnit();
            forUnit.setUserPostId(userPost.getId());
            forUnit.setDeptId(userPost.getDeptId());
            forUnit.setName(orgIdMap.get(userPost.getDeptId()).getName());
            loginUnitList.add(forUnit);
        }
        userInfo.setUnitList(loginUnitList);
        return Result.ok(userInfo);
    }

    private UserAuthInfo getUserAuthInfo(SysUser sysUser) {
        if (Objects.isNull(sysUser)) {
            return null;
        }
        UserAuthInfo userAuthInfo = new UserAuthInfo();
        userAuthInfo.setAccount(sysUser.getAccount());
        userAuthInfo.setPassword(sysUser.getPassword());
        userAuthInfo.setUsername(sysUser.getName());
        userAuthInfo.setUserId(sysUser.getId());
        userAuthInfo.setZwddId(sysUser.getZwddId());
        userAuthInfo.setLock(Objects.equals(CommonConstant.TRUE_INT, sysUser.getIsLock()));
        userAuthInfo.setUnLockTime(sysUser.getUnlockTime());
        userAuthInfo.setRealUserInfo(sysUser);
        return userAuthInfo;
    }
}
