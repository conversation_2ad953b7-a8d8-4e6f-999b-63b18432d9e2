package com.zenith.bbykz.service.staffingManagement;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.zenith.bbykz.api.staffingManagement.StaffingManagementRegistrationService;
import com.zenith.bbykz.dao.StaffingManagementRegistrationMapper;
import com.zenith.bbykz.model.converter.StaffingManagementRegistrationConverter;
import com.zenith.bbykz.model.dto.StaffingManagementRegistrationDTO;
import com.zenith.bbykz.model.entity.StaffingManagementRegistration;
import com.zenith.bbykz.model.vo.StaffingManagementRegistrationVO;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.stream.Collectors;

/**
 * <p>
 * 用编管理登记表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @date 2023-09-28 13:53:11
 */
@Service
public class StaffingManagementRegistrationServiceImpl extends ServiceImpl<StaffingManagementRegistrationMapper, StaffingManagementRegistration> implements StaffingManagementRegistrationService {

    @Autowired
    private StaffingManagementRegistrationConverter staffingManagementRegistrationConverter;
    @Autowired
    private StaffingManagementRegistrationMapper staffingManagementRegistrationMapper;

    @Override
    public Boolean addUpdate(List<StaffingManagementRegistrationDTO> registrations, String staffingManagementId) {
        List<StaffingManagementRegistration> list = this.list(new LambdaQueryWrapper<StaffingManagementRegistration>()
                .eq(StaffingManagementRegistration::getStaffingManagementId, staffingManagementId));
        if (CollectionUtils.isNotEmpty(list)) {
            this.removeBatchByIds(list);
        }
        List<StaffingManagementRegistration> collect = registrations
                .stream()
                .map(item -> staffingManagementRegistrationConverter.dto2Entity(item)).collect(Collectors.toList());
        collect.forEach(registration -> registration.setStaffingManagementId(staffingManagementId));
        return this.saveBatch(collect);
    }

    @Override
    public List<StaffingManagementRegistrationVO> getListByStaffingManagementId(String staffingManagementId) {
        List<StaffingManagementRegistration> list = this.list(new LambdaQueryWrapper<StaffingManagementRegistration>()
                .eq(StaffingManagementRegistration::getStaffingManagementId, staffingManagementId));
        return list.stream().map(item -> staffingManagementRegistrationConverter.entity2Vo(item)).collect(Collectors.toList());
    }

}
