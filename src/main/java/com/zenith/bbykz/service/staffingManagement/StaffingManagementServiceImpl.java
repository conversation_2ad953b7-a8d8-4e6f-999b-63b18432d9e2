package com.zenith.bbykz.service.staffingManagement;

import cn.afterturn.easypoi.excel.ExcelExportUtil;
import cn.afterturn.easypoi.excel.entity.ExportParams;
import cn.afterturn.easypoi.word.WordExportUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.io.FileUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.efficient.common.auth.RequestHolder;
import com.efficient.common.constant.CommonConstant;
import com.efficient.common.result.Result;
import com.efficient.common.util.JackSonUtil;
import com.efficient.file.api.FileService;
import com.efficient.file.model.vo.FileVO;
import com.efficient.system.api.DictCodeService;
import com.efficient.system.model.converter.DictCodeConverter;
import com.efficient.system.model.entity.DictCode;
import com.zenith.bbykz.api.OrgInfoService;
import com.zenith.bbykz.api.staffingManagement.StaffingManagementPersonsService;
import com.zenith.bbykz.api.staffingManagement.StaffingManagementRegistrationService;
import com.zenith.bbykz.api.staffingManagement.StaffingManagementService;
import com.zenith.bbykz.common.constant.BbCommonConstant;
import com.zenith.bbykz.common.enums.StaffingManagementStatusEnum;
import com.zenith.bbykz.common.result.BbResultEnum;
import com.zenith.bbykz.common.utils.IncreasingSequenceUtil;
import com.zenith.bbykz.dao.StaffingManagementMapper;
import com.zenith.bbykz.model.base.UserInfo;
import com.zenith.bbykz.model.converter.StaffingManagementConverter;
import com.zenith.bbykz.model.converter.StaffingManagementPersonsConverter;
import com.zenith.bbykz.model.dto.*;
import com.zenith.bbykz.model.entity.*;
import com.zenith.bbykz.model.vo.*;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.util.IOUtils;
import org.apache.poi.xwpf.usermodel.XWPFDocument;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.mock.web.MockMultipartFile;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ResourceUtils;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletResponse;
import java.io.File;
import java.io.FileInputStream;
import java.io.FileOutputStream;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <p>
 * 用编管理 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @date 2023-09-13
 */
@Service
public class StaffingManagementServiceImpl extends ServiceImpl<StaffingManagementMapper, StaffingManagement> implements StaffingManagementService {

    /**
     * 日期格式
     */
    private final String DATE_FORMAT = "yyyyMMdd";
    /**
     * 回复函模板地址
     */
    private final String REPLIES_TEMPLATE_URL = "templates/repliesWord.docx";
    /**
     * 生成回复函存储地址
     */
    private final String BUILD_REPLIES_URL = "static/repliesWords/";
    @Autowired
    private StaffingManagementConverter staffingManagementConverter;
    @Autowired
    private StaffingManagementMapper staffingManagementMapper;
    @Autowired
    private StaffingManagementPersonsService staffingManagementPersonsService;
    @Autowired
    private StaffingManagementPersonsConverter staffingManagementPersonsConverter;
    @Autowired
    private DictCodeService dictCodeService;
    @Autowired
    private DictCodeConverter dictCodeConverter;
    @Autowired
    private FileService fileService;
    @Autowired
    private OrgInfoService orgInfoService;
    @Autowired
    private StaffingManagementRegistrationService staffingManagementRegistrationService;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Result addUpdate(StaffingManagementDTO dto) {
        OrgInfo orgInfo = orgInfoService.getOne(new LambdaQueryWrapper<OrgInfo>().eq(OrgInfo::getLevelCode, dto.getOrgCode())
                .eq(OrgInfo::getIsDelete, CommonConstant.FALSE_INT));
        List<StaffingManagementFilesDTO> files = dto.getFilesList();
        String id = dto.getId();
        StaffingManagement entity = staffingManagementConverter.dto2Entity(dto);
        boolean a;
        if (StringUtils.isBlank(id)) {
            //新增操作
            //判断区域是否区化
            if (orgInfo.getOrgQh().equals(BbCommonConstant.TRUE_INT)) {
                return Result.build(BbResultEnum.ORGQH_CAN_NOT_ADD);
            }
            entity.setApplicatTime(new Date());
            entity.setApplicatPerson(RequestHolder.getCurrUser().getAccount());
            //获取附件转成json串存储
            entity.setFiles(JackSonUtil.toJson(files));
            //获取审批编办机构
            entity.setBbOrg(getBbOrgCode(orgInfo));
            a = this.save(entity);
        } else {
            //修改操作
            StaffingManagement byId = this.getById(id);
            if (Objects.isNull(byId)) {
                return Result.build(BbResultEnum.DATA_NOT_EXIST);
            }
            buildUpdateStaffingManagement(files, entity, byId);
            a = this.updateById(byId);
        }
        //新增用编管理人员信息
        boolean b = staffingManagementPersonsService.addUpdate(dto.getPersons(), entity.getId());
        return a && b ? Result.ok() : Result.fail();
    }

    /**
     * @description: 获取审批编办
     * @author: yanggm
     * @date: 2023/9/20 10:10
     * @param: orgCode 机构
     */
    private String getBbOrgCode(OrgInfo org) {
        LambdaQueryWrapper<OrgInfo> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(OrgInfo::getIsBbOrg, BbCommonConstant.TRUE_INT);
        //判断机构是否是由市编办审批
        if (org.getIsSjbbAudit().equals(BbCommonConstant.TRUE_INT)) {
            queryWrapper.likeRight(OrgInfo::getLevelCode, BbCommonConstant.CQ_SJBM);
        } else {
            //判断是否是区级以下单位且不是区由市编办审批
            if (org.getLevelCode().length() > 9) {
                queryWrapper.likeRight(OrgInfo::getLevelCode, org.getLevelCode().substring(0, 9))
                        .eq(OrgInfo::getIsDelete, CommonConstant.FALSE_INT);
            }
        }
        OrgInfo orgInfo = orgInfoService.getOne(queryWrapper);
        return orgInfo.getLevelCode();
    }

    private void buildUpdateStaffingManagement(List<StaffingManagementFilesDTO> files, StaffingManagement entity, StaffingManagement byId) {
        //获取附件转成json串存储
        byId.setFiles(JackSonUtil.toJson(files));
        byId.setTitle(entity.getTitle());
        byId.setApplicantUnit(entity.getApplicantUnit());
        byId.setPreparationType(entity.getPreparationType());
        byId.setEmployeesNumber(entity.getEmployeesNumber());
        byId.setEnrollmentTime(entity.getEnrollmentTime());
        byId.setContacts(entity.getContacts());
        byId.setTelephone(entity.getTelephone());
        byId.setStatus(entity.getStatus());
    }

    @Override
    public Result findById(String id) {
        StaffingManagement entity = this.getById(id);
        if (Objects.isNull(entity)) {
            return Result.build(BbResultEnum.DATA_NOT_EXIST);
        }
        StaffingManagementVO staffingManagementVO = getStaffingManagementInfo(entity);
        //根据用编管理id获取附件的用户集合
        List<StaffingManagementPersons> list = staffingManagementPersonsService.list(new LambdaQueryWrapper<StaffingManagementPersons>()
                .eq(StaffingManagementPersons::getStaffingManagementId, entity.getId())
                .orderByAsc(StaffingManagementPersons::getId));
        List<StaffingManagementPersonsVO> persons = list.stream().map(item -> staffingManagementPersonsConverter.entity2Vo(item)).collect(Collectors.toList());
        staffingManagementVO.setPersons(persons);
        //获取用编登记列表
        List<StaffingManagementRegistrationVO> registrations = staffingManagementRegistrationService.getListByStaffingManagementId(staffingManagementVO.getId());
        staffingManagementVO.setRegistrationList(registrations);
        return Result.ok(staffingManagementVO);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean delete(String id) {
        boolean a = this.removeById(id);
        //删除用户信息
        List<StaffingManagementPersons> list = staffingManagementPersonsService.list(new LambdaQueryWrapper<StaffingManagementPersons>()
                .eq(StaffingManagementPersons::getStaffingManagementId, id));
        boolean b = true;
        if (!CollectionUtils.isEmpty(list)) {
            b = staffingManagementPersonsService.removeBatchByIds(list);
        }
        return a && b;
    }

    @Override
    public Result list(StaffingManagementListDTO dto) {
        //入编时间
        if (null != dto.getStart() && null != dto.getEnd()) {
            if (dto.getStart().after(dto.getEnd())) {
                return Result.build(BbResultEnum.START_GT_END);
            }
        }
        LambdaQueryWrapper<StaffingManagement> queryWrapper = new LambdaQueryWrapper<>();
        //机构和包含下级
        if (dto.getSubordinate().equals(BbCommonConstant.TRUE_INT)) {
            queryWrapper.likeRight(StaffingManagement::getOrgCode, dto.getOrgCode());
        } else {
            queryWrapper.eq(StaffingManagement::getOrgCode, dto.getOrgCode());
        }
        buildQueryWrapper(dto, queryWrapper);
        Page<StaffingManagement> page = staffingManagementMapper.selectPage(new Page<>(dto.getPageNum(), dto.getPageSize()), queryWrapper);
        List<StaffingManagement> records = page.getRecords();
        for (StaffingManagement record : records) {
            String preparationType = record.getPreparationType();
            if (!StringUtils.isBlank(preparationType)) {
                DictCode dictCode = getPreparationType(record);
                record.setPreparationType(dictCode.getCodeName());
            }
            //使用数量与剩余数量
            getUsedAndRemainingNumber(record);
        }
        return Result.ok(page);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Result approve(String id, String status, String option) {
        //用编审核，添加意见,不同意时必填意见，同意不必填
        if (status.equals(StaffingManagementStatusEnum.DISAGREE.getCode())) {
            if (StringUtils.isBlank(option)) {
                return Result.build(BbResultEnum.ADVICE_IS_NULL);
            }
        }
        StaffingManagement staffingManagement = this.getById(id);
        if (Objects.isNull(staffingManagement)) {
            return Result.build(BbResultEnum.DATA_NOT_EXIST);
        }
        //判断当前用户是否有审批权限
        String bbOrg = staffingManagement.getBbOrg();
        UserInfo currUser = (UserInfo) RequestHolder.getCurrUser();
//        SysUser user = currUser.getUser();
        SysUser user = null;
        if (!user.getOrgLevelCode().equals(bbOrg)) {
            return Result.build(BbResultEnum.THE_USER_NO_PERMISSION_APPROVE);
        }
        if (status.equals(StaffingManagementStatusEnum.AGREE.getCode())) {
            //生成核编单号
            String date = DateUtil.format(new Date(), DATE_FORMAT);
            String num = IncreasingSequenceUtil.getNum(date);
            staffingManagement.setApprovalNumber(num);
            //生成word,并上传到服务器
            String name = System.currentTimeMillis() + ".doc";
            buildReplies(staffingManagement, name, user);
            //上传到文件服务器
            uploadReplies(staffingManagement, name);
        }
        staffingManagement.setStatus(status);
        staffingManagement.setOption(option);
        staffingManagement.setApprovedPerson(user.getAccount());
        boolean b = this.updateById(staffingManagement);
        return b ? Result.ok() : Result.fail();
    }

    @Override
    public Result bbList(StaffingManagementListDTO dto) {
        //入编时间
        if (null != dto.getStart() && null != dto.getEnd()) {
            if (dto.getStart().after(dto.getEnd())) {
                return Result.build(BbResultEnum.START_GT_END);
            }
        }

        LambdaQueryWrapper<StaffingManagement> queryWrapper = new LambdaQueryWrapper<>();
        UserInfo currUser = (UserInfo) RequestHolder.getCurrUser();
        String orgCode = null;
//        String orgCode = currUser.getCurrUnit().getManageLevelCode();
        //判断用户机构是否是编办机构
//        OrgInfo currUserOrg = orgInfoService.getOne(new LambdaQueryWrapper<OrgInfo>().eq(OrgInfo::getIsBbOrg, CommonConstant.TRUE_INT)
//                .eq(OrgInfo::getLevelCode, orgCode));
//        if (Objects.isNull(currUserOrg)){
//            return Result.build(ResultEnum.THE_USER_NOT_IS_BB_USER);
//        }

        //当前用户机构是市编办用户且是否查询全部是true时、查询全部
        OrgInfo orgInfo = orgInfoService.getOne(new LambdaQueryWrapper<OrgInfo>().eq(OrgInfo::getIsBbOrg, BbCommonConstant.TRUE_INT)
                .likeRight(OrgInfo::getLevelCode, BbCommonConstant.CQ_SJBM).eq(OrgInfo::getIsDelete, "0"));
        List<OrgInfo> orgqx = orgInfoService.list(new LambdaQueryWrapper<OrgInfo>().eq(OrgInfo::getIsBbOrg, BbCommonConstant.TRUE_INT)
                .likeRight(OrgInfo::getLevelCode, BbCommonConstant.CQ_QX).eq(OrgInfo::getIsDelete, CommonConstant.TRUE_INT));
        List<String> collect = orgqx.stream().map(OrgInfo::getLevelCode).collect(Collectors.toList());
        if (!orgCode.equals(orgInfo.getLevelCode()) || dto.getQueryAll().equals(BbCommonConstant.FALSE_INT)) {
            //当前用户机构是区县编办机构,区县编办能查询当前机构能审批的用编申请  是否查询全部是false时查询当前机构能审批的用编申请
            queryWrapper.eq(StaffingManagement::getBbOrg, orgCode);
        }
        //是否包含下级  是否参数orgcode是区编办  是的话查询自己申请和自己审核的用编申请
        if (dto.getSubordinate().equals(BbCommonConstant.TRUE_INT) || collect.contains(dto.getOrgCode())) {
            queryWrapper.and(query -> query.likeRight(StaffingManagement::getOrgCode, dto.getOrgCode())
                    .or()
                    .likeRight(StaffingManagement::getBbOrg, dto.getOrgCode()));
        } else {
            queryWrapper.eq(StaffingManagement::getOrgCode, dto.getOrgCode());
        }

        //条件查询
        buildQueryWrapper(dto, queryWrapper);
        Page<StaffingManagement> page = staffingManagementMapper.selectPage(new Page<>(dto.getPageNum(), dto.getPageSize()), queryWrapper);
        List<StaffingManagement> records = page.getRecords();
        for (StaffingManagement record : records) {
            String preparationType = record.getPreparationType();
            if (!StringUtils.isBlank(preparationType)) {
                DictCode dictCode = getPreparationType(record);
                record.setPreparationType(dictCode.getCodeName());
            }
            //判断用户是否能够审核
            if (orgCode.equals(record.getBbOrg())) {
                record.setCanApproved(BbCommonConstant.TRUE);
            } else {
                record.setCanApproved(BbCommonConstant.FALSE);
            }
        }
        return Result.ok(page);
    }

    @Override
    public void exportBbList(StaffingManagementListDTO dto, HttpServletResponse response) throws Exception {
        dto.setPageNum(1);
        dto.setPageSize(99999);
        Result result = bbList(dto);
        exportList(response, result);
    }

    @Override
    public void exportList(StaffingManagementListDTO dto, HttpServletResponse response) throws Exception {
        dto.setPageNum(1);
        dto.setPageSize(99999);
        Result result = list(dto);
        exportList(response, result);
    }

    private void getUsedAndRemainingNumber(StaffingManagement record) {
        //查询已使用的总数量
        List<StaffingManagementRegistration> list = staffingManagementRegistrationService.list(new LambdaQueryWrapper<StaffingManagementRegistration>()
                .eq(StaffingManagementRegistration::getStaffingManagementId, record.getId()));
        int sum = list.stream().mapToInt(StaffingManagementRegistration::getUsedNumber).sum();
        record.setUsedNumber(sum);
        record.setRemainingNumber(record.getEmployeesNumber() - sum);

    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Result registration(RegistrationDTO dto) {
        //校验申请数量与登记总使用数量
        List<StaffingManagementRegistrationDTO> registrationList = dto.getRegistrationList();
        int sum = registrationList.stream().mapToInt(StaffingManagementRegistrationDTO::getUsedNumber).sum();
        StaffingManagement staffingManagement = this.getById(dto.getStaffingManagementId());
        if (sum > staffingManagement.getEmployeesNumber()) {
            return Result.build(BbResultEnum.USED_NUM_MUST_LESS_EMPLOYEES_NUMBER);
        }
        Boolean b = staffingManagementRegistrationService.addUpdate(dto.getRegistrationList(), dto.getStaffingManagementId());
        return b ? Result.ok() : Result.fail();
    }

    @Override
    public Result countList(RegistrationListDTO dto) {
        LambdaQueryWrapper<StaffingManagement> queryWrapper = new LambdaQueryWrapper<>();
        UserInfo currUser = (UserInfo) RequestHolder.getCurrUser();
//        String orgCode = currUser.getCurrUnit().getManageLevelCode();
        String orgCode = null;
        //当前用户机构是市编办用户 查询全部
        OrgInfo orgInfo = orgInfoService.getOne(new LambdaQueryWrapper<OrgInfo>().eq(OrgInfo::getIsBbOrg, BbCommonConstant.TRUE_INT)
                .likeRight(OrgInfo::getLevelCode, BbCommonConstant.CQ_SJBM).eq(OrgInfo::getIsDelete, 0));
        if(Objects.isNull(orgInfo)){
            return Result.ok();
        }
        List<OrgInfo> orgqx = orgInfoService.list(new LambdaQueryWrapper<OrgInfo>().eq(OrgInfo::getIsBbOrg, BbCommonConstant.TRUE_INT)
                .likeRight(OrgInfo::getLevelCode, BbCommonConstant.CQ_QX).eq(OrgInfo::getIsDelete, 0));
        List<String> collect = orgqx.stream().map(OrgInfo::getLevelCode).collect(Collectors.toList());
        if (!orgCode.equals(orgInfo.getLevelCode())) {
            //当前用户机构是区县编办机构,区县编办能查询当前机构能审批和自己申请的用编申请  普通用户查询自己申请的
            if (collect.contains(orgCode)) {
                queryWrapper.and(query -> query.eq(StaffingManagement::getBbOrg, orgCode)
                        .or()
                        .eq(StaffingManagement::getOrgCode, orgCode));
            } else {
                queryWrapper.eq(StaffingManagement::getOrgCode, orgCode);
            }
        }
        //是否包含下级 是的话查询自己申请和自己审核的用编申请
        if (dto.getSubordinate().equals(BbCommonConstant.TRUE_INT)) {
            queryWrapper.likeRight(StaffingManagement::getOrgCode, dto.getOrgCode());
        } else {
            queryWrapper.eq(StaffingManagement::getOrgCode, dto.getOrgCode());
        }
        queryWrapper.orderByDesc(StaffingManagement::getEmployeesNumber);
        Page<StaffingManagement> page = staffingManagementMapper.selectPage(new Page<>(dto.getPageNum(), dto.getPageSize()), queryWrapper);
        List<StaffingManagement> records = page.getRecords();
        //使用数量与剩余数量
        records.forEach(this::getUsedAndRemainingNumber);
        return Result.ok(page);
    }

    @Override
    public void exportCountList(RegistrationListDTO dto, HttpServletResponse response) throws Exception {
        dto.setPageNum(1);
        dto.setPageSize(99999);
        Result result = countList(dto);
        exportCountList(response, result);
    }

    private void exportCountList(HttpServletResponse response, Result result) throws Exception {
        Page<StaffingManagement> resultData = (Page<StaffingManagement>) result.getData();
        List<StaffingManagement> dataList = resultData.getRecords();
        List<StaffingManagementRegistrationVoExcel> list = new ArrayList<>();
        int index = 1;
        for (StaffingManagement data : dataList) {
            StaffingManagementRegistrationVoExcel excel = new StaffingManagementRegistrationVoExcel();
            BeanUtils.copyProperties(data, excel);
            excel.setIndex(index);
            list.add(excel);
            index++;
        }
        Workbook workbook = ExcelExportUtil.exportExcel(new ExportParams(null, ""), StaffingManagementRegistrationVoExcel.class, list);
        exportFile(response, workbook);
    }

    private void exportList(HttpServletResponse response, Result result) throws Exception {
        Page<StaffingManagement> resultData = (Page<StaffingManagement>) result.getData();
        List<StaffingManagement> dataList = resultData.getRecords();
        List<StaffingManagementVoExcel> list = new ArrayList<>();
        int index = 1;
        for (StaffingManagement data : dataList) {
            StaffingManagementVoExcel excel = new StaffingManagementVoExcel();
            BeanUtils.copyProperties(data, excel);
            excel.setApplicatTime(DateUtil.format(data.getApplicatTime(), "yyyy-MM-dd HH:mm:ss"));
            excel.setEnrollmentTime(DateUtil.format(data.getEnrollmentTime(), "yyyy-MM-dd"));
            excel.setIndex(index);
            list.add(excel);
            index++;
        }
        Workbook workbook = ExcelExportUtil.exportExcel(new ExportParams(null, ""), StaffingManagementVoExcel.class, list);
        exportFile(response, workbook);
    }

    private void buildQueryWrapper(StaffingManagementListDTO dto, LambdaQueryWrapper<StaffingManagement> queryWrapper) {
        //状态
        String status = dto.getStatus();
        if (!StringUtils.isBlank(status)) {
            List<String> statusList = Arrays.asList(status.split(","));
            queryWrapper.in(StaffingManagement::getStatus, statusList);
        }
        //标题
        if (!StringUtils.isBlank(dto.getTitle())) {
            queryWrapper.eq(StaffingManagement::getTitle, dto.getTitle());
        }
        //申请单位
        if (!StringUtils.isBlank(dto.getApplicantUnit())) {
            queryWrapper.eq(StaffingManagement::getApplicantUnit, dto.getApplicantUnit());
        }
        //编制类型
        if (!StringUtils.isBlank(dto.getPreparationType())) {
            queryWrapper.eq(StaffingManagement::getPreparationType, dto.getPreparationType());
        }
        //入编开始时间
        if (null != dto.getStart()) {
            queryWrapper.ge(StaffingManagement::getEnrollmentTime, dto.getStart());
        }
        //入编结束时间
        if (null != dto.getEnd()) {
            queryWrapper.le(StaffingManagement::getEnrollmentTime, dto.getEnd());
        }
        queryWrapper.orderByDesc(StaffingManagement::getApplicatTime);
    }

    private void exportFile(HttpServletResponse response, Workbook workbook) throws Exception {
        response.reset();
        String name = DateUtil.format(new Date(), "yyyyMMddHHmmssSSS") + ".xls";
        response.setContentType("application/vnd.ms-excel;chartset=utf-8");
        response.setHeader("Content-Disposition", "attachment;filename=" + name);
        ServletOutputStream out = response.getOutputStream();
        workbook.write(out);
        out.flush();
        out.close();
    }

    /**
     * 生成回复函
     *
     * @param staffingManagement 用编管理信息
     * @param name               回复函名称
     * @param user               当前审批用户
     */
    private void buildReplies(StaffingManagement staffingManagement, String name, SysUser user) {
        try {
            String basePath = ResourceUtils.getURL(ResourceUtils.CLASSPATH_URL_PREFIX).getPath();
            String ftlPath = basePath + REPLIES_TEMPLATE_URL;
            FileInputStream fin = new FileInputStream(ftlPath);
            String string = JackSonUtil.toJson(staffingManagement);
            Map<String, Object> resultMap = JackSonUtil.toMap(string);
            //获取审批单位
            String orgLevelCode = user.getOrgLevelCode();
            OrgInfo orgInfo = orgInfoService.getOne(new LambdaQueryWrapper<OrgInfo>().eq(OrgInfo::getLevelCode, orgLevelCode)
                    .eq(OrgInfo::getIsDelete, CommonConstant.FALSE_INT));
            resultMap.put("approvalUnit", orgInfo.getName());
            XWPFDocument document = WordExportUtil.exportWord07(ftlPath, resultMap);
            String fileUrl = BUILD_REPLIES_URL;
            File baseFile = new File(basePath + fileUrl);
            if (!baseFile.exists()) {
                baseFile.mkdirs();
            }
            // 生成文件
            FileOutputStream fout = new FileOutputStream(FileUtil.file(basePath + fileUrl, name));
            staffingManagement.setRepliesAddress(basePath + fileUrl + name);
            document.write(fout);
            fout.close();
            fin.close();
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    /**
     * 上传回复函到文件服务器
     *
     * @param staffingManagement 用编管理信息
     * @param name               回复函名称
     */
    private void uploadReplies(StaffingManagement staffingManagement, String name) {
        try {
            String basePath = ResourceUtils.getURL(ResourceUtils.CLASSPATH_URL_PREFIX).getPath();
            FileInputStream fileInputStream = new FileInputStream(FileUtil.file(basePath + BUILD_REPLIES_URL, name));
            MultipartFile multipartFile = new MockMultipartFile("file", name, "text/plain", IOUtils.toByteArray(fileInputStream));
            Result result = fileService.upload(multipartFile, true, "replies", null, null);
            fileInputStream.close();
            if (result.getCode().equals(HttpStatus.OK.value())) {
                //上传服务器成功将文件名字，id保存到用编管理信息中
                FileVO data = (FileVO) result.getData();
                StaffingManagementFilesDTO filesDTO = new StaffingManagementFilesDTO();
                filesDTO.setFileName(data.getFileName());
                filesDTO.setFileId(data.getFileId());
                staffingManagement.setRepliesFile(JackSonUtil.toJson(filesDTO));
            }
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    private StaffingManagementVO getStaffingManagementInfo(StaffingManagement entity) {
        //附件json串转换为文件集合
        String files = entity.getFiles();
        //获取编制类型
        DictCode dictCode = getPreparationType(entity);
        List<StaffingManagementFilesVO> filesVO = JackSonUtil.toObjectList(files, StaffingManagementFilesVO.class);
        StaffingManagementVO staffingManagementVO = staffingManagementConverter.entity2Vo(entity);
        staffingManagementVO.setFilesList(filesVO);
        staffingManagementVO.setDictCode(dictCodeConverter.entity2Vo(dictCode));
        return staffingManagementVO;
    }

    /**
     * 获取用编管理的编制类型
     *
     * @param record 用编管理数据
     */
    private DictCode getPreparationType(StaffingManagement record) {
        //获取编制类型
        return dictCodeService.getOne(new LambdaQueryWrapper<DictCode>().eq(DictCode::getCodeType, "BZLX")
                .eq(DictCode::getCode, record.getPreparationType()));
    }
}