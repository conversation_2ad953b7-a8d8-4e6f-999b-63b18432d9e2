package com.zenith.bbykz.service.staffingManagement;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.zenith.bbykz.api.staffingManagement.StaffingManagementPersonsService;
import com.zenith.bbykz.dao.StaffingManagementPersonsMapper;
import com.zenith.bbykz.model.converter.StaffingManagementPersonsConverter;
import com.zenith.bbykz.model.dto.StaffingManagementPersonsDTO;
import com.zenith.bbykz.model.entity.StaffingManagementPersons;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.stream.Collectors;

/**
 * <p>
 * 用编管理人员信息 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @date 2023-09-13
 */
@Service
public class StaffingManagementPersonsServiceImpl extends ServiceImpl<StaffingManagementPersonsMapper, StaffingManagementPersons> implements StaffingManagementPersonsService {

    @Autowired
    private StaffingManagementPersonsConverter staffingManagementPersonsConverter;
    @Autowired
    private StaffingManagementPersonsMapper staffingManagementPersonsMapper;

    @Override
    public boolean addUpdate(List<StaffingManagementPersonsDTO> persons, String id) {
        List<StaffingManagementPersons> personsList = persons.
                stream()
                .map(item -> staffingManagementPersonsConverter.dto2Entity(item)).collect(Collectors.toList());
        //查询用编管理下绑定的所有用户
        List<StaffingManagementPersons> list = list(new LambdaQueryWrapper<StaffingManagementPersons>().
                eq(StaffingManagementPersons::getStaffingManagementId, id));
        personsList.forEach(person -> person.setStaffingManagementId(id));
        List<String> ids = list.stream().map(StaffingManagementPersons::getId).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(list)) {
            //更新操作，先删除，在插入数据
            this.removeBatchByIds(ids);
        }
        if (CollectionUtils.isNotEmpty(personsList)) {
            return this.saveBatch(personsList);
        }
        return true;
    }
}
