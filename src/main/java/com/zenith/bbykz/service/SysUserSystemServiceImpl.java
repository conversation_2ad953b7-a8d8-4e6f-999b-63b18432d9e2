package com.zenith.bbykz.service;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.efficient.common.auth.RequestHolder;
import com.efficient.common.constant.CommonConstant;
import com.efficient.common.constant.DbConstant;
import com.efficient.common.result.Result;
import com.efficient.common.result.ResultEnum;
import com.zenith.bbykz.api.SysUserSystemService;
import com.zenith.bbykz.dao.SysSystemMapper;
import com.zenith.bbykz.dao.SysUserSystemMapper;
import com.zenith.bbykz.model.base.UserInfo;
import com.zenith.bbykz.model.converter.SysUserSystemConverter;
import com.zenith.bbykz.model.dto.SysUserSystemDTO;
import com.zenith.bbykz.model.dto.UserSystemDTO;
import com.zenith.bbykz.model.entity.SysSystem;
import com.zenith.bbykz.model.entity.SysUserSystem;
import com.zenith.bbykz.model.vo.SysUserSystemVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <p>
 * 用户系统表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @date 2024-02-29 10:36:56
 */
@Service
public class SysUserSystemServiceImpl extends ServiceImpl<SysUserSystemMapper, SysUserSystem> implements SysUserSystemService {

    @Autowired
    private SysUserSystemConverter sysUserSystemConverter;
    @Autowired
    private SysUserSystemMapper sysUserSystemMapper;
    @Autowired
    private SysSystemMapper sysSystemMapper;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Result save(UserSystemDTO dto) {
        List<SysUserSystemDTO> systemDTOList = dto.getSystemIdList();
        if (CollUtil.isEmpty(systemDTOList)) {
            return Result.fail(ResultEnum.PARA_ERROR);
        }
        Collection<List<SysUserSystemDTO>> listCollection = systemDTOList.stream().collect(Collectors.groupingBy(SysUserSystemDTO::getSystemId)).values();
        int size = listCollection.stream().mapToInt(List::size).max().orElse(0);
        if (size > 1) {
            return Result.fail(ResultEnum.PARA_ERROR);
        }
        String userId = dto.getUserId();
        LambdaQueryWrapper<SysUserSystem> queryWrapper = new LambdaQueryWrapper<>(SysUserSystem.class);
        queryWrapper.eq(SysUserSystem::getUserId, userId);
        queryWrapper.orderByAsc(SysUserSystem::getSystemId);
        List<SysUserSystem> sysUserSystems = sysUserSystemMapper.selectList(queryWrapper);
        Map<String, SysUserSystem> oldMap = sysUserSystems.stream().collect(Collectors.toMap(SysUserSystem::getSystemId, et -> et, (k1, k2) -> k1));
        List<SysUserSystem> addList = new ArrayList<>();
        List<SysUserSystem> updateList = new ArrayList<>();

        List<String> systemIdList = new ArrayList<>();
        for (SysUserSystemDTO sysUserSystemDTO : systemDTOList) {
            String systemId = sysUserSystemDTO.getSystemId();
            systemIdList.add(systemId);
            SysUserSystem sysUserSystem = oldMap.get(systemId);
            if (Objects.nonNull(sysUserSystem)) {
                // 修改
                sysUserSystem.setIsLock(sysUserSystemDTO.getIsLock());
                sysUserSystem.setUnlockTime(sysUserSystemDTO.getUnlockTime());
                updateList.add(sysUserSystem);
            } else {
                // 新增
                sysUserSystem = sysUserSystemConverter.dto2Entity(sysUserSystemDTO);
                sysUserSystem.setUserId(userId);
                addList.add(sysUserSystem);
            }
        }

        List<String> deleteSystemIdList = oldMap.keySet().stream().filter(et -> !systemIdList.contains(et)).collect(Collectors.toList());
        boolean flag = this.deleteByUserAndSystemIdList(userId, deleteSystemIdList);
        if (CollUtil.isNotEmpty(addList)) {
            this.saveBatch(addList);
        }
        if (CollUtil.isNotEmpty(updateList)) {
            this.updateBatchById(updateList);
        }
        return Result.ok();
    }

    @Override
    public List<SysUserSystemVO> findByUserId(String userId) {
        LambdaQueryWrapper<SysUserSystem> queryWrapper = new LambdaQueryWrapper<>(SysUserSystem.class);
        queryWrapper.eq(SysUserSystem::getUserId, userId);
        queryWrapper.orderByAsc(SysUserSystem::getSystemId);
        List<SysUserSystem> sysUserSystemList = this.list(queryWrapper);
        List<SysUserSystemVO> list = new ArrayList<>();
        sysUserSystemList.forEach(et -> list.add(sysUserSystemConverter.entity2Vo(et)));
        return list;
    }

    @Override
    public List<SysUserSystem> findByUserPostId(String userPostId) {
        LambdaQueryWrapper<SysUserSystem> queryWrapper = new LambdaQueryWrapper<>(SysUserSystem.class);
        queryWrapper.eq(SysUserSystem::getUserPostId, userPostId);
        queryWrapper.orderByAsc(SysUserSystem::getSystemId);
        return this.list(queryWrapper);
    }

    @Override
    public boolean deleteByUserAndSystemIdList(String userId, List<String> systemIdList) {
        if (CollUtil.isEmpty(systemIdList)) {
            return true;
        }
        LambdaUpdateWrapper<SysUserSystem> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.set(SysUserSystem::getIsDelete, CommonConstant.TRUE_INT);
        updateWrapper.in(SysUserSystem::getSystemId, systemIdList);
        updateWrapper.eq(SysUserSystem::getUserId, userId);
        return this.update(updateWrapper);
    }

    @Override
    public Result findSystemList() {
        UserInfo userInfo = (UserInfo) RequestHolder.getCurrUser();
        List<String> systemIdList = userInfo.getSystemIdList();
        if (CollUtil.isEmpty(systemIdList)) {
            return Result.ok();
        }
        LambdaQueryWrapper<SysSystem> queryWrapper = new LambdaQueryWrapper<>(SysSystem.class);
        queryWrapper.in(SysSystem::getId, systemIdList);
        queryWrapper.orderByAsc(SysSystem::getSort);
        List<SysSystem> sysSystems = sysSystemMapper.selectList(queryWrapper);
        return Result.ok(sysSystems);
    }

    @Override
    public SysUserSystem findByUserIdAndSystemId(String userId, String systemId, String userPostId) {
        LambdaQueryWrapper<SysUserSystem> queryWrapper = new LambdaQueryWrapper<>(SysUserSystem.class);
        queryWrapper.eq(SysUserSystem::getUserId, userId);
        queryWrapper.eq(SysUserSystem::getSystemId, systemId);
        queryWrapper.eq(SysUserSystem::getUserPostId, userPostId);
        queryWrapper.last(DbConstant.LIMIT_ONE);
        return this.getOne(queryWrapper);
    }

    @Override
    public void updateUserOrgGroup(String currSystemId, String userId, String userOrgGroupId) {
        LambdaUpdateWrapper<SysUserSystem> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.set(SysUserSystem::getUserOrgGroupId, userOrgGroupId);
        updateWrapper.eq(SysUserSystem::getSystemId, currSystemId);
        updateWrapper.eq(SysUserSystem::getUserId, userId);
        this.update(updateWrapper);
    }

    @Override
    public SysUserSystem findByUserPostIdAndSystemId(String userPostId, String currSystemId) {
        LambdaQueryWrapper<SysUserSystem> queryWrapper = new LambdaQueryWrapper<>(SysUserSystem.class);
        queryWrapper.eq(SysUserSystem::getSystemId, currSystemId);
        queryWrapper.eq(SysUserSystem::getUserPostId, userPostId);
        return this.getOne(queryWrapper);
    }
}
