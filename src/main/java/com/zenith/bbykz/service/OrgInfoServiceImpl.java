package com.zenith.bbykz.service;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.efficient.cache.api.CacheUtil;
import com.efficient.common.auth.RequestHolder;
import com.efficient.common.constant.CommonConstant;
import com.efficient.common.constant.DbConstant;
import com.efficient.common.result.Result;
import com.efficient.configs.util.PageUtil;
import com.efficient.system.api.DictCodeService;
import com.zenith.bbykz.common.constant.SystemConstant;
import com.zenith.bbykz.api.*;
import com.zenith.bbykz.api.bz.common.SystemcodeService;
import com.zenith.bbykz.api.publicInstitutions.LegalPersonService;
import com.zenith.bbykz.api.publicInstitutions.UnifiedSocialCreditCodeService;
import com.zenith.bbykz.api.staffingManagement.StaffingManagementService;
import com.zenith.bbykz.common.constant.BbCommonConstant;
import com.zenith.bbykz.common.constant.DictConstant;
import com.zenith.bbykz.common.result.BbResultEnum;
import com.zenith.bbykz.common.utils.TreeUtil;
import com.zenith.bbykz.dao.GeocodeMapper;
import com.zenith.bbykz.dao.OrgInfoMapper;
import com.zenith.bbykz.model.base.TreeNode;
import com.zenith.bbykz.model.base.UserInfo;
import com.zenith.bbykz.model.converter.OrgInfoConverter;
import com.zenith.bbykz.model.dto.OrgInfoDTO;
import com.zenith.bbykz.model.dto.OrgInfoListDTO;
import com.zenith.bbykz.model.dto.OrgInfoTreeDTO;
import com.zenith.bbykz.model.entity.*;
import com.zenith.bbykz.model.entity.bz.common.Systemcode;
import com.zenith.bbykz.model.vo.OrgInfoVO;
import com.zenith.bbykz.model.vo.register.UnitRegisterInfo;
import com.zenith.bbykz.service.efficient.CustomDictService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.concurrent.CopyOnWriteArrayList;
import java.util.stream.Collectors;

import static com.zenith.bbykz.common.constant.BbCommonConstant.*;
import static com.zenith.bbykz.common.utils.TreeUtil.NXS_STR;

/**
 * <p>
 * org_info 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @date 2023-09-14 17:51:37
 */
@Service
@Slf4j
public class OrgInfoServiceImpl extends ServiceImpl<OrgInfoMapper, OrgInfo> implements OrgInfoService {

    @Autowired
    private OrgInfoConverter orgInfoConverter;
    @Autowired
    private OrgInfoMapper orgInfoMapper;
    @Autowired
    private StaffingManagementService staffingManagementService;
    @Autowired
    private SysUserSystemService sysUserSystemService;
    @Autowired
    private SysUserOrgGroupDetailService sysUserOrgGroupDetailService;
    @Autowired
    private SysUserPostService sysUserPostService;
    @Autowired
    private LegalPersonService legalPersonService;
    @Autowired
    private UnifiedSocialCreditCodeService unifiedSocialCreditCodeService;
    @Autowired
    private GeocodeMapper geocodeMapper;
    @Autowired
    private GeocodeService geocodeService;
    @Autowired
    private SystemcodeService systemcodeService;
    @Autowired
    private CacheUtil cacheUtil;
    @Autowired
    private DictCodeService dictCodeService;
    @Autowired
    private CustomDictService customDictService;

    @Override
    public Result save(OrgInfoDTO dto) {
        OrgInfo entity = orgInfoConverter.dto2Entity(dto);
        // 生成单位层级码
        String parentCode = dto.getParentCode();
        if (ObjectUtil.equal(dto.getIsBbOrg(), 1)) {
            OrgInfo bbOrgInfo = baseMapper.getOrgByParentCodeAndIsBbOrg(dto.getParentCode(), null);
            if (ObjectUtil.isNotNull(bbOrgInfo)) {
                return Result.build(BbResultEnum.THE_ORG_LEVEL_IS_EXIST_BB_ORG.getCode(), BbResultEnum.THE_ORG_LEVEL_IS_EXIST_BB_ORG.getMsg(), null);
            }
        }
        OrgInfo orgInfo = baseMapper.getOrgByParentCode(parentCode);
        String newLevelCode = "";
        if (ObjectUtil.isNull(orgInfo)) {
            newLevelCode = parentCode + "001";
        } else {
            newLevelCode = String.valueOf(Long.parseLong(orgInfo.getLevelCode()) + 1);
        }
        // 设置区划，是否能新增数据
        if (StrUtil.equals(dto.getParentCode(), BbCommonConstant.CQ_SJBM)) {
            entity.setOrgQh(BbCommonConstant.FALSE_INT);
        } else if (StrUtil.equals(dto.getParentCode(), BbCommonConstant.CQ_QX) && ObjectUtil.equal(dto.getIsSjbbAudit(), 1)) {
            entity.setOrgQh(BbCommonConstant.FALSE_INT);
        } else if (!StrUtil.equals(dto.getParentCode(), BbCommonConstant.CQ_QX) && StrUtil.startWithAny(dto.getParentCode(), BbCommonConstant.CQ_QX)) {
            entity.setOrgQh(BbCommonConstant.FALSE_INT);
        } else {
            entity.setOrgQh(BbCommonConstant.TRUE_INT);
        }

        entity.setLevelCode(newLevelCode);
        entity.setIsDelete(CommonConstant.FALSE_INT);
        boolean flag = this.save(entity);
        return Result.ok(entity);
    }

    @Override
    public OrgInfoVO findById(String id) {
        if (Objects.isNull(id)) {
            return new OrgInfoVO();
        }
        OrgInfo entity = this.getById(id);
        OrgInfoVO orgInfoVO = orgInfoConverter.entity2Vo(entity);
        // 查询上级机构名称
        LambdaQueryWrapper<OrgInfo> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(OrgInfo::getId, entity.getParentCode()).eq(OrgInfo::getIsDelete, CommonConstant.FALSE_INT);
        OrgInfo orgInfo = this.getOne(wrapper);
        if (Objects.nonNull(orgInfo)) {
            orgInfoVO.setParentName(orgInfo.getName());
        } else {
            orgInfoVO.setParentName("无");
        }
        Map<String, String> jgsyTypeMap = dictCodeService.findMapByType(DictConstant.JGSY_TYPE);
        orgInfoVO.setUnitTypeName(jgsyTypeMap.get(orgInfoVO.getTypeCode()));
        return orgInfoVO;
    }

    @Override
    public String findLevelCodeById(String id) {
        if (Objects.isNull(id)) {
            return null;
        } else if (StrUtil.endWithAny(id, "a", "b", "c", "d")) {
            return id;
        } else if (StrUtil.equals(id, CQ_CODE)) {
            return CQ_CODE + "-" + CQ_CODE;
        } else if (id.length() < 15) {
            if (id.length() == 6 && !StrUtil.equalsAny(id, SXQ_CODE, X_CODE)) {
                return this.qxCode(id);
            } else if (id.length() == 9 && !StrUtil.startWith(id, CQ_CODE)) {
                String[] split = id.split("-");
                return this.qxCode(split[0]) + "-" + split[1];
            } else {
                return CQ_CODE + "-" + id;
            }
        }
        LambdaQueryWrapper<OrgInfo> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.select(OrgInfo::getLevelCode);
        queryWrapper.eq(OrgInfo::getId, id);
        queryWrapper.last("limit 1");
        OrgInfo one = this.getOne(queryWrapper);
        if (Objects.nonNull(one)) {
            return one.getLevelCode();
        }
        return id;
    }

    @Override
    public String findDjxxLevel(String id) {
        if (StrUtil.isBlank(id)) {
            return null;
        }
        if (id.length() == 6) {
            if (Arrays.stream(BIANDONG_QX_GEOCODE_ARR).anyMatch(et -> StrUtil.equals(id, et))) {
                return BIANDONG_QX_GEOCODE.get(id);
            }
            return id;
        }
        return id;
    }

    @Override
    public Result update(OrgInfoDTO dto) {
        OrgInfo entity = orgInfoConverter.dto2Entity(dto);
        if (ObjectUtil.equal(dto.getIsBbOrg(), 1)) {
            OrgInfo bbOrgInfo = baseMapper.getOrgByParentCodeAndIsBbOrg(dto.getParentCode(), dto.getId());
            if (ObjectUtil.isNotNull(bbOrgInfo)) {
                return Result.build(BbResultEnum.THE_ORG_LEVEL_IS_EXIST_BB_ORG.getCode(), BbResultEnum.THE_ORG_LEVEL_IS_EXIST_BB_ORG.getMsg(), null);
            }
        }

        // 设置区划，是否能新增数据
        if (StrUtil.equals(dto.getParentCode(), BbCommonConstant.CQ_SJBM)) {
            entity.setOrgQh(BbCommonConstant.FALSE_INT);
        } else if (StrUtil.equals(dto.getParentCode(), BbCommonConstant.CQ_QX) && ObjectUtil.equal(dto.getIsSjbbAudit(), 1)) {
            entity.setOrgQh(BbCommonConstant.FALSE_INT);
        } else if (!StrUtil.equals(dto.getParentCode(), BbCommonConstant.CQ_QX) && StrUtil.startWithAny(dto.getParentCode(), BbCommonConstant.CQ_QX)) {
            entity.setOrgQh(BbCommonConstant.FALSE_INT);
        } else {
            entity.setOrgQh(BbCommonConstant.TRUE_INT);
        }
        return this.updateById(entity) ? Result.ok() : Result.fail();
    }

    @Override
    public Result delete(String id) {
        LambdaQueryWrapper<OrgInfo> wrapper1 = new LambdaQueryWrapper<>();
        wrapper1.eq(OrgInfo::getId, id);
        wrapper1.eq(OrgInfo::getIsDelete, CommonConstant.FALSE_INT);
        OrgInfo org = this.getOne(wrapper1);
        // 判断该机构下是否存在用编记录
        LambdaQueryWrapper<StaffingManagement> wrapper2 = new LambdaQueryWrapper<>();
        wrapper2.like(StaffingManagement::getOrgCode, org.getLevelCode());
        long count = staffingManagementService.count(wrapper2);
        if (count > 0) {
            return Result.fail(BbResultEnum.THE_ORG_IS_EXIST_UBJL.getCode(), BbResultEnum.THE_ORG_IS_EXIST_UBJL.getMsg(), null);
        }
        LambdaQueryWrapper<OrgInfo> wrapper3 = new LambdaQueryWrapper<>();
        wrapper3.ne(OrgInfo::getId, id);
        wrapper3.eq(OrgInfo::getIsDelete, CommonConstant.FALSE_INT);
        wrapper3.likeRight(OrgInfo::getLevelCode, org.getLevelCode());
        long count1 = this.count(wrapper3);
        if (count1 > 0) {
            return Result.fail(BbResultEnum.THE_ORG_IS_EXIST_SON_ORG.getCode(), BbResultEnum.THE_ORG_IS_EXIST_SON_ORG.getMsg(), null);
        }
        LambdaUpdateWrapper<OrgInfo> wrapper = new LambdaUpdateWrapper<>();
        wrapper.set(OrgInfo::getIsDelete, CommonConstant.TRUE_INT);
        wrapper.set(OrgInfo::getDeleteTime, new Date());
        wrapper.eq(OrgInfo::getId, id);
        wrapper.eq(OrgInfo::getIsDelete, CommonConstant.FALSE_INT);
        boolean update = this.update(wrapper);
        return update ? Result.ok() : Result.fail();
    }

    @Override
    public Page<OrgInfoVO> list(OrgInfoListDTO dto) {
        Page<OrgInfo> page = orgInfoMapper.getPage(new Page<>(dto.getPageNum(), dto.getPageSize()), dto);
        List<OrgInfo> records = page.getRecords();
        List<OrgInfoVO> voList = new ArrayList<>();
        if (CollUtil.isNotEmpty(records)) {
            List<String> parentIds = records.stream().map(OrgInfo::getParentCode).collect(Collectors.toList());
            List<OrgInfo> orgInfoList = this.findByIdList(parentIds);
            Map<String, String> orgNameMap = orgInfoList.stream().collect(Collectors.toMap(OrgInfo::getId, OrgInfo::getName, (k1, k2) -> k2));
            // Map<String, String> map = UnitTypeEnum.map();
            Map<String, String> jgsyTypeMap = dictCodeService.findMapByType(DictConstant.JGSY_TYPE);
            records.forEach(data -> {
                OrgInfoVO vo = orgInfoConverter.entity2Vo(data);
                vo.setParentName(orgNameMap.getOrDefault(data.getParentCode(), ""));
                vo.setUnitTypeName(jgsyTypeMap.get(vo.getTypeCode()));
                voList.add(vo);
            });

        }
        return PageUtil.change(page, voList);
    }

    @Override
    public TreeNode tree(OrgInfoTreeDTO dto) {
        String depCode = dto.getOrgCode();
        if (StrUtil.isBlank(depCode)) {
            UserInfo user = (UserInfo) RequestHolder.getCurrUser();
            depCode = user.getSysUser().getManageCode();
        }
        List<TreeNode> treeNodeList = this.creatTree(depCode, false, dto);

        long count = treeNodeList.stream().filter(et -> Objects.nonNull(et.getIsRoot()) && et.getIsRoot()).count();
        if (count != 1) {
            String finalDepCode = depCode;
            treeNodeList.forEach(et -> et.setIsRoot(StrUtil.equals(et.getCode(), finalDepCode)));
        }

        TreeNode tree = TreeUtil.createTree(treeNodeList);
        return tree;
    }

    private List<TreeNode> creatTree(String depCode, boolean isAll, OrgInfoTreeDTO dto) {
        boolean isBb = dto.isBb();
        boolean hasCg = dto.isHasCg();
        boolean isNs = dto.isNs();
        boolean excludeNs = dto.isExcludeNs();
        List<TreeNode> nodeList = new ArrayList<>();
        Boolean isVirtual = false;
        if (StrUtil.equals(CQ_CODE, depCode)) {
            // 重庆一级
            List<Geocode> geocodeList = geocodeMapper.getListByStartWithAndEndWith(CQ_CODE_START, CQ_CODE_END);
            nodeList = this.createNode(geocodeList);
            if (isBb) {
                this.addBbSystemNode(depCode, nodeList);
            } else {
                this.setSystemCodeNode(nodeList, depCode, "1", false);
            }
        } else {
            // 查询当前层级是否需要添加党委，人大一级
            if (depCode.length() == 6) {
                // 区县层级
                Geocode geocode = geocodeService.getById(depCode);
                if (StrUtil.endWith(depCode, CQ_CODE_END)) {
                    // 区县父级层级
                    String type = null;
                    if (StrUtil.equals(geocode.getType(), "8")) {
                        type = "4";
                    } else {
                        type = "5";
                    }
                    List<Geocode> geocodeList = geocodeMapper.getListByStartWith(type, geocode.getType());
                    nodeList = this.createNode(geocodeList);
                } else {
                    // 区县层级
                    List<Geocode> geocodeList = new ArrayList<>();
                    geocodeList.add(geocode);
                    nodeList = this.createNode(geocodeList);
                    if (isBb) {
                        this.addBbSystemNode(depCode, nodeList);
                    } else {
                        this.setSystemCodeNode(nodeList, depCode, null, false);
                    }
                }
            } else {
                // 区县下级层级
                LambdaQueryWrapper<OrgInfo> queryWrapper = new LambdaQueryWrapper<>();
                queryWrapper.select(OrgInfo::getJgsyCode, OrgInfo::getTypeCode, OrgInfo::getGeocode, OrgInfo::getName,
                        OrgInfo::getParentCode);
                // 判断账号是否区县账号
                // UserInfo currUser = (UserInfo)RequestHolder.getCurrUser();
                // SysUser user = currUser.getSysUser();
                // if (!hasCg) {
                //     if (Objects.equals(user.getIsBbUser(), 1) && !StrUtil.equals(user.getManageCode(), CQ_CODE)) {
                //         // 不显示垂管机构
                //         queryWrapper.ne(OrgInfo::getJgsyIfvertical, "1");
                //     }
                // }

                if (excludeNs) {
                    queryWrapper.notIn(OrgInfo::getTypeCode, "05", "07", "09", "11", "13");
                }

                // 地方行政机关
                final OrgInfo commonVO = this.getById(depCode);
                if (Objects.nonNull(commonVO) && commonVO.getJgsyCode().length() == 15 && StrUtil.equalsAny(commonVO.getTypeCode(), "04", "12")) {
                    nodeList.add(this.createTreeNode(commonVO, true, 0));
                    isVirtual = true;
                    // 行政机关需要查询 下属内设，下设，及事业单位
                    queryWrapper.eq(OrgInfo::getParentCode, depCode);
                    // setCurrCommonQuery(queryWrapper);
                    queryWrapper.ne(OrgInfo::getJgsyCode, TreeUtil.changeCode(depCode));
                    final List<OrgInfo> commonList = this.list(queryWrapper);

                    if (StrUtil.equals(commonVO.getTypeCode(), "04")) {
                        if (!excludeNs) {
                            final long count1 = commonList.stream().filter(common -> StrUtil.equals(common.getTypeCode(), NS)).count();
                            if (count1 > 0) {
                                TreeNode treeNode = TreeNode.builder().code(depCode + "a").parentCode(depCode).name("内设机构").type(NXS).isLeaf(false).order(new Long("1")).orderType(88).build();
                                nodeList.add(treeNode);
                            }
                        }

                        if (!isNs) {
                            final long count2 = commonList.stream().filter(common -> StrUtil.equals(common.getTypeCode(), XS)).count();
                            if (count2 > 0) {
                                TreeNode treeNode = TreeNode.builder().code(depCode + "b").parentCode(depCode).name("下设机构").type(NXS).isLeaf(false).order(new Long("2")).orderType(88).build();
                                nodeList.add(treeNode);
                            }
                        }
                    } else {
                        if (!excludeNs) {
                            // 事业内设
                            final long count1 = commonList.stream().filter(common -> StrUtil.equals(common.getTypeCode(), SY_NS)).count();
                            if (count1 > 0) {
                                TreeNode treeNode = TreeNode.builder().code(depCode + "d").parentCode(depCode).name("内设机构").type(NXS).isLeaf(false).order(new Long("1")).orderType(88).build();
                                nodeList.add(treeNode);
                            }
                        }

                    }
                    if (!isNs) {
                        final long count3 = commonList.stream().filter(common -> StrUtil.equals(common.getTypeCode(), SY)).count();
                        if (count3 > 0) {
                            TreeNode treeNode = TreeNode.builder().code(depCode + "c").parentCode(depCode).name("事业单位").type(NXS).isLeaf(false).order(new Long("3")).orderType(88).build();
                            nodeList.add(treeNode);
                        }
                    }
                } else {
                    if (!StrUtil.endWithAny(depCode, "a", "d")) {
                        // 不是查询内设机构则添加机构查询条件
                        // setCurrCommonQuery(queryWrapper);
                    }

                    List<OrgInfo> commonList;
                    // 其他事业
                    if (depCode.length() == 9) {
                        // 党委一级
                        String[] split = depCode.split("-");
                        List<Systemcode> all = customDictService.getSystemCodes();
                        Map<String, String> systemCodeMap = all.stream().collect(Collectors.toMap(et -> et.getSystemCode(), et -> et.getSystemCodeName(), (k1, k2) -> k1));
                        TreeNode treeNode = TreeNode.builder().code(depCode).parentCode(split[0]).name(systemCodeMap.get(split[1])).order(new Long("1")).isRoot(true).build();
                        nodeList.add(treeNode);
                        if (isBb) {
                            commonList = geocodeMapper.findBbList(split[0], split[1]);
                        } else {
                            queryWrapper.eq(OrgInfo::getGeocode, split[0]);
                            // queryWrapper.likeRight(JgsyCommon::getJgsyCode, depCode);
                            queryWrapper.eq(OrgInfo::getJgsySystemCode, split[1]);
                            queryWrapper.last(" and jgsy_code = parent_code order by type_code,sort");
                            commonList = this.list(queryWrapper);
                        }
                    } else {
                        this.changeIsVirtual(depCode, queryWrapper);
                        commonList = this.list(queryWrapper);
                    }
                    if (CollUtil.isEmpty(commonList)) {
                        OrgInfo orgInfo = this.getById(depCode);
                        if (Objects.nonNull(orgInfo)) {
                            commonList.add(this.getById(depCode));
                        }

                    } else {
                        if (isNs) {
                            commonList = commonList.stream().filter(common -> (StrUtil.equalsAny(common.getTypeCode(), NSJG_LIST.toArray(new String[]{}))) || StrUtil.equals(common.getJgsyCode(), depCode)).collect(Collectors.toList());
                        }
                    }
                    // queryWrapper.last(" order by LENGTH(jgsy_code),jgsy_code_order");
                    long count = commonList.stream().filter(et -> StrUtil.equals(et.getJgsyCode(), depCode)).count();
                    if (count == 0 && depCode.length() >= 15) {
                        OrgInfo jgsyCommon = this.getById(depCode);
                        if (Objects.nonNull(jgsyCommon)) {
                            commonList.add(jgsyCommon);
                        } else if (StrUtil.endWithAny(depCode, "a", "b", "c", "d")) {
                            TreeNode treeNode = TreeNode.builder().code(depCode).parentCode(depCode.substring(0, depCode.length() - 1))
                                    .isRoot(true).name("事业单位").type(NXS).isLeaf(false).order(new Long("3")).orderType(88).build();
                            if (StrUtil.endWith(depCode, "a")) {
                                treeNode.setName("内设机构");
                                treeNode.setType(NS);
                            } else if (StrUtil.endWith(depCode, "b")) {
                                treeNode.setName("下设机构");
                                treeNode.setType(XS);
                            } else if (StrUtil.endWith(depCode, "c")) {
                                treeNode.setName("内设机构");
                                treeNode.setType(SY_NS);
                            } else if (StrUtil.endWith(depCode, "d")) {
                                treeNode.setName("事业单位");
                                treeNode.setType(SY);
                            }
                            nodeList.add(treeNode);
                        }
                    }
                    this.createJgsyCommon(nodeList, commonList, depCode);
                }
            }
        }
        if (CollUtil.isEmpty(nodeList)) {
            return nodeList;
        }
        if (!isAll && !isVirtual) {
            return this.checkIsLeaf(nodeList, excludeNs);
        }
        return nodeList;
    }

    public List<TreeNode> createNode(List<Geocode> geocodeList) {
        List<TreeNode> nodeList = new ArrayList<>(16);
        // 中央 10 0000，重庆 50 0000
        boolean isRoot = false;
        for (Geocode geocode : geocodeList) {
            TreeNode treeNode = TreeNode.builder().code(geocode.getCode()).type(GEO_CODE_TYPE).name(geocode.getName() == null ? "" : geocode.getName().trim()).order(new Long(geocode.getSort())).build();
            if (!isRoot) {
                treeNode.setIsRoot(true);
                treeNode.setOrderType(0);
                isRoot = true;
            }
            final String code = geocode.getCode();
            if (StrUtil.endWith(code, ZY_CODE_END)) {
                // 中央--重庆一级
                if (StrUtil.startWith(code, "10")) {
                    treeNode.setParentCode(null);
                } else if (StrUtil.startWith(code, CQ_CODE_START)) {
                    treeNode.setParentCode(ZY_CODE);
                }
            } else if (StrUtil.endWith(code, CQ_CODE_END)) {
                // 重庆--区县一级
                treeNode.setParentCode(CQ_CODE);
            } else if (!StrUtil.endWith(code, "00")) {
                if (StrUtil.equals(geocode.getType(), "4")) {
                    // 区
                    treeNode.setParentCode(SXQ_CODE);
                } else {
                    // 县
                    treeNode.setParentCode(X_CODE);
                }
            } else {
                treeNode.setParentCode(code.substring(0, code.length() - 2) + CQ_CODE_END);
            }
            nodeList.add(treeNode);
        }
        return nodeList;
    }

    private void addBbSystemNode(String depCode, List<TreeNode> nodeList) {
        TreeNode treeNode1 = TreeNode.builder().code(depCode + CODE_JOINER + "01")
                .name("党委")
                .parentCode(depCode).type(SYSTEM_CODE_TYPE + "1")
                .systemCode("01").isLeaf(false)
                .orderType(TreeUtil.getOrderInt("1"))
                .order(new Long("1")).isRoot(false).build();
        TreeNode treeNode3 = TreeNode.builder().code(depCode + CODE_JOINER + "03")
                .name("政府")
                .parentCode(depCode).type(SYSTEM_CODE_TYPE + "1")
                .systemCode("03").isLeaf(false)
                .orderType(TreeUtil.getOrderInt("1"))
                .order(new Long("3")).isRoot(false).build();
        nodeList.add(treeNode1);
        nodeList.add(treeNode3);
    }

    public void setSystemCodeNode(List<TreeNode> nodes, String parentCode, String type, boolean isZy) {
        List<Systemcode> systemcodeList = customDictService.getSystemCodes();
        systemcodeList.stream().filter(systemcode -> {
            if (StrUtil.equals(systemcode.getSystemCode(), "11")) {
                return false;
            }
            // 排除监察机关
            if (StrUtil.isBlank(type)) {
                return true;
            }
            return StrUtil.equals(systemcode.getSystemCodeType(), type);
        }).forEach(systemcode -> {
            if (isZy && StrUtil.equals(systemcode.getSystemCode(), "11")) {
                return;
            }
            TreeNode treeNode = TreeNode.builder().code(parentCode + CODE_JOINER + systemcode.getSystemCode()).parentCode(parentCode).type(SYSTEM_CODE_TYPE + systemcode.getSystemCodeType()).systemCode(systemcode.getSystemCode()).isLeaf(false).orderType(TreeUtil.getOrderInt(systemcode.getSystemCodeType())).order(new Long(systemcode.getSystemCodeOrder())).isRoot(false).build();
            if (isZy) {
                treeNode.setName(systemcode.getSystemCodeStandby1());
            } else {
                treeNode.setName(systemcode.getSystemCodeName());
            }
            nodes.add(treeNode);
        });
    }

    public TreeNode createTreeNode(OrgInfo common, boolean isRoot, Integer sort) {
        final TreeNode build = TreeNode.builder().code(common.getJgsyCode())
                .parentCode(common.getParentCode())
                .name(common.getName())
                .type(common.getTypeCode())
                .order(common.getSort())
                .orderType(sort).isRoot(isRoot)
                .order(common.getSort())
                .geoCode(common.getGeocode())
                .build();
        if (StrUtil.equals(common.getJgsyCode(), common.getParentCode())) {
            build.setParentCode(common.getGeocode() + "-" + common.getJgsySystemCode());
        }
        return build;
    }

    // private void setCurrCommonQuery(LambdaQueryWrapper<JgsyCommon> queryWrapper) {
    //     final List<String> jgsyCodes = RequestHolder.getCurrUser().getJgsyCodes();
    //     if (CollUtil.isNotEmpty(jgsyCodes)) {
    //         queryWrapper.in(JgsyCommon::getJgsyCode, jgsyCodes);
    //     }
    // }
    private void changeIsVirtual(String depCode, LambdaQueryWrapper<OrgInfo> queryWrapper) {
        String oldDepCode = depCode;
        if (StrUtil.endWith(depCode, "a")) {
            // 内设机构
            oldDepCode = depCode.substring(0, depCode.length() - 1);
            queryWrapper.eq(OrgInfo::getTypeCode, NS);
            queryWrapper.ne(OrgInfo::getJgsyCode, oldDepCode);
        } else if (StrUtil.endWith(depCode, "b")) {
            // 下设机构
            oldDepCode = depCode.substring(0, depCode.length() - 1);
            queryWrapper.eq(OrgInfo::getTypeCode, XS);
            queryWrapper.ne(OrgInfo::getJgsyCode, oldDepCode);
        } else if (StrUtil.endWith(depCode, "c")) {
            // 事业单位
            oldDepCode = depCode.substring(0, depCode.length() - 1);
            queryWrapper.eq(OrgInfo::getTypeCode, SY);
            queryWrapper.ne(OrgInfo::getJgsyCode, oldDepCode);
        } else if (StrUtil.endWith(depCode, "d")) {
            // 事业单位-内设
            oldDepCode = depCode.substring(0, depCode.length() - 1);
            queryWrapper.eq(OrgInfo::getTypeCode, SY_NS);
            queryWrapper.ne(OrgInfo::getJgsyCode, oldDepCode);
        }
        queryWrapper.eq(OrgInfo::getParentCode, oldDepCode);
        queryWrapper.orderByAsc(OrgInfo::getTypeCode, OrgInfo::getSort);
    }

    public void createJgsyCommon(List<TreeNode> treeNodes, List<OrgInfo> commonList, String parentCode) {
        if (CollUtil.isNotEmpty(commonList)) {
            for (OrgInfo common : commonList) {
                TreeNode treeNode = this.createTreeNode(common, parentCode);
                treeNodes.add(treeNode);
            }
        }
    }

    private List<TreeNode> checkIsLeaf(List<TreeNode> nodeList, boolean excludeNs) {
        // 判断是否有下级
        List<OrgInfo> commonList = this.selectByParentCode(nodeList.stream().map(TreeNode::getCode).collect(Collectors.toList())).stream().filter(common -> !StrUtil.equals(common.getJgsyCode(), common.getParentCode())).collect(Collectors.toList());
        if (excludeNs) {
            commonList = commonList.stream().filter(common -> !StrUtil.equalsAny(common.getTypeCode(), "05", "07", "09", "11", "13")).collect(Collectors.toList());
        }
        final Set<String> set = commonList.stream().map(OrgInfo::getParentCode).collect(Collectors.toSet());
        for (TreeNode treeNode : nodeList) {
            if (StrUtil.equalsAny(treeNode.getType(), GEO_CODE_TYPE) || StrUtil.startWith(treeNode.getType(), SYSTEM_CODE_TYPE)) {
                treeNode.setIsLeaf(false);
            } else {
                treeNode.setIsLeaf(!set.contains(treeNode.getCode()));
            }
            if (Objects.isNull(treeNode.getOrderType())) {
                treeNode.setOrderType(5);
            }
        }
        return nodeList;
    }

    public TreeNode createTreeNode(OrgInfo common, String parentCode) {
        TreeNode treeNode = TreeNode.builder().code(common.getJgsyCode())
                .parentCode(common.getParentCode())
                .name(common.getName())
                .type(common.getTypeCode())
                .order(common.getSort())
                .geoCode(common.getGeocode())
                .build();

        if (StrUtil.equals(common.getParentCode(), common.getJgsyCode())) {
            treeNode.setIsRoot(true);
            final String[] split = common.getParentCode().split("-");
            treeNode.setParentCode(split[0] + "-" + split[1]);
            if (!StrUtil.startWith(common.getJgsyCode(), parentCode)) {
                treeNode.setParentCode(parentCode);
            }
        }
        if (StrUtil.endWithAny(parentCode, NXS_STR)) {
            treeNode.setParentCode(parentCode);
        }
        return treeNode;
    }

    @Override
    public List<OrgInfo> getListByParentCodes(List<String> parentCodes) {
        LambdaQueryWrapper<OrgInfo> wrapper = new LambdaQueryWrapper<>();
        wrapper.in(OrgInfo::getLevelCode, parentCodes);
        return this.list(wrapper);
    }

    @Override
    public List<OrgInfo> getListByCodes(List<String> orgCodeList) {
        LambdaQueryWrapper<OrgInfo> wrapper = new LambdaQueryWrapper<>();
        wrapper.in(OrgInfo::getLevelCode, orgCodeList);
        wrapper.eq(OrgInfo::getIsDelete, CommonConstant.FALSE_INT);
        return this.list(wrapper);
    }

    @Override
    public synchronized String createLevelCode(String parentId) {
        OrgInfo sysUnit = this.getById(parentId);
        if (Objects.isNull(sysUnit)) {
            OrgInfo sysUnitLast = orgInfoMapper.findLastUnitByLength(SystemConstant.UNIT_LEVEL_COUNT);
            if (Objects.isNull(sysUnitLast)) {
                return SystemConstant.UNIT_TOP_LEVEL;
            } else {
                String levelCode = sysUnitLast.getLevelCode();
                return getNextLevelCode(levelCode);
            }
        } else {
            OrgInfo sysUnitLast = orgInfoMapper.findLastUnitByParentId(parentId);
            if (Objects.isNull(sysUnitLast)) {
                return sysUnit.getLevelCode() + SystemConstant.UNIT_SUB_LEVEL;
            } else {
                String levelCode = sysUnitLast.getLevelCode();
                return getNextLevelCode(levelCode);
            }
        }
    }

    @Override
    public String getBelongById(String parentId) {
        return orgInfoMapper.getBelongById(parentId);
    }

    @Override
    public OrgInfo getByOrgCode(String organizationCode) {
        return orgInfoMapper.getByOrgCode(organizationCode);
    }

    @Override
    public OrgInfo getUnitByDeptId(String deptId) {
        return orgInfoMapper.getUnitByDeptId(deptId);
    }

    @Override
    public List<OrgInfo> findByIdList(List<String> orgIdList) {
        LambdaQueryWrapper<OrgInfo> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.in(OrgInfo::getId, orgIdList);
        queryWrapper.in(OrgInfo::getIsDelete, CommonConstant.FALSE_INT);
        return this.list(queryWrapper);
    }

    @Override
    public List<OrgInfo> treeSearch(OrgInfoListDTO dto) {
        LambdaQueryWrapper<OrgInfo> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.select(OrgInfo::getName, OrgInfo::getLevelCode, OrgInfo::getId);
        queryWrapper.like(OrgInfo::getName, dto.getKeyword());
        queryWrapper.likeRight(OrgInfo::getLevelCode, dto.getOrgCode());
        queryWrapper.eq(OrgInfo::getIsDelete, CommonConstant.FALSE_INT);
        queryWrapper.orderByAsc(OrgInfo::getId);
        if (Objects.nonNull(dto.getLimitCount())) {
            queryWrapper.last(" limit " + dto.getLimitCount());
        }
        return this.list(queryWrapper);
    }

    @Override
    public OrgInfo findTop() {
        return orgInfoMapper.findTop();
    }

    @Override
    public OrgInfo getByLevelCode(String levelCode) {
        LambdaQueryWrapper<OrgInfo> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(OrgInfo::getLevelCode, levelCode);
        queryWrapper.eq(OrgInfo::getIsDelete, CommonConstant.FALSE_INT);
        queryWrapper.last(DbConstant.LIMIT_ONE);
        return this.getOne(queryWrapper);
    }

    @Override
    public List<OrgInfo> findByLevelCodeList(List<String> levelCodeList) {
        LambdaQueryWrapper<OrgInfo> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.select(OrgInfo::getId, OrgInfo::getLevelCode);
        for (String levelCode : levelCodeList) {
            queryWrapper.or().likeRight(OrgInfo::getLevelCode, levelCode);
        }

        return this.list(queryWrapper);
    }

    @Override
    public Result updateNew(OrgInfoDTO dto) {
        String id = dto.getId();
        OrgInfo orgInfo = this.getById(id);
        orgInfo.setCreditCode(dto.getCreditCode());
        orgInfo.setUnitSource(dto.getUnitSource());
        this.updateById(orgInfo);
        return Result.ok();
    }

    @Override
    public Result<UnitRegisterInfo> getRegisterInfo(String id) {
        OrgInfo orgInfo = this.getById(id);
        String creditCode = orgInfo.getCreditCode();
        String unitSource = orgInfo.getTypeCode();
        UnitRegisterInfo unitRegisterInfo = new UnitRegisterInfo();
        // 单位来源，1-事业单位，2-机关登记信息
        if (Objects.isNull(creditCode)) {
            return Result.ok(unitRegisterInfo);
        }
        if (StrUtil.equals(unitSource, "12")) {
            unitRegisterInfo = legalPersonService.findByCreditCode(creditCode);
        } else {
            unitRegisterInfo = unifiedSocialCreditCodeService.findByCreditCode(creditCode);
        }
        return Result.ok(unitRegisterInfo);
    }

    @Override
    public OrgInfo findByCreditCode(String creditCode) {
        LambdaQueryWrapper<OrgInfo> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(OrgInfo::getCreditCode, creditCode);
        queryWrapper.last(DbConstant.LIMIT_ONE);
        return this.getOne(queryWrapper);
    }

    @Override
    public void deleteAll() {
        orgInfoMapper.deleteAll();
    }

    @Override
    public List<OrgInfo> selectByParentCode(List<String> collect) {
        LambdaQueryWrapper<OrgInfo> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.select(OrgInfo::getParentCode, OrgInfo::getJgsyCode, OrgInfo::getTypeCode);
        queryWrapper.in(OrgInfo::getParentCode, collect);
        final List<OrgInfo> commonList = orgInfoMapper.selectList(queryWrapper);
        return commonList == null ? new ArrayList<>() : commonList;
    }

    @Override
    public OrgInfo findByJgsyCode(String jgsyCode) {
        LambdaQueryWrapper<OrgInfo> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(OrgInfo::getJgsyCode, jgsyCode);
        queryWrapper.last(DbConstant.LIMIT_ONE);
        return this.getOne(queryWrapper);
    }

    @Override
    public OrgInfo findOneByLevelCode(String levelCode) {
        LambdaQueryWrapper<OrgInfo> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(OrgInfo::getLevelCode, levelCode);
        queryWrapper.last(DbConstant.LIMIT_ONE);
        return this.getOne(queryWrapper);
    }

    @Override
    public Set<String> treeLevel(String levelCode) {

        Set<String> codeList = new LinkedHashSet<>();
        OrgInfo orgInfo = this.findOneByLevelCode(levelCode);
        if (Objects.isNull(orgInfo)) {
            orgInfo = this.getById(levelCode);
        }
        String jgsyCode = orgInfo.getJgsyCode();
        String depCode = orgInfo.getGeocode();
        String[] splitStr = jgsyCode.split("-");
        splitStr[0] = depCode;
        codeList.add(CQ_CODE);
        if (!StrUtil.equals(orgInfo.getGeocode(), CQ_CODE)) {
            Geocode geocodeVO = geocodeService.getById(orgInfo.getGeocode());
            if (StrUtil.equals(geocodeVO.getType(), "5")) {
                codeList.add(X_CODE);
            } else if (StrUtil.equals(geocodeVO.getType(), "4")) {
                codeList.add(SXQ_CODE);
            }
            codeList.add(depCode);
        }
        codeList.add(depCode + "-" + orgInfo.getJgsySystemCode());
        List<String> sortList = new ArrayList<>();
        this.recursionParentCode(sortList, jgsyCode);
        for (int i = sortList.size() - 1; i >= 0; i--) {
            codeList.add(sortList.get(i));
        }
        return codeList;
    }

    @Override
    public List<String> findCreditCodeListById(String unitLevelCode) {
        OrgInfo orgInfo = this.getById(unitLevelCode);
        if (Objects.isNull(orgInfo)) {
            return null;
        }
        LambdaQueryWrapper<OrgInfo> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.select(OrgInfo::getCreditCode);
        queryWrapper.likeRight(OrgInfo::getLevelCode, orgInfo.getLevelCode());
        queryWrapper.isNotNull(OrgInfo::getCreditCode);
        return this.list(queryWrapper).stream().map(OrgInfo::getCreditCode).collect(Collectors.toList());
    }

    @Override
    public List<OrgInfo> findAllSy() {
        return orgInfoMapper.findAllSy();
    }

    @Override
    public Result findRegisterInfo(String id) {
        OrgInfo orgInfo = this.getById(id);
        if(Objects.isNull(orgInfo)){
            return Result.ok();
        }
        String creditCode = orgInfo.getCreditCode();
        String unitSource = orgInfo.getTypeCode();
        UnitRegisterInfo unitRegisterInfo = new UnitRegisterInfo();
        // 单位来源，1-事业单位，2-机关登记信息
        if (Objects.isNull(creditCode)) {
            return Result.ok(unitRegisterInfo);
        }
        if (StrUtil.equals(unitSource, "12")) {
            LegalPersonRegistration legalPersonRegistration = legalPersonService.findEntityByCreditCode(creditCode);
            return Result.ok(legalPersonRegistration);
        } else {
            UnifiedSocialCreditCode unifiedSocialCreditCode = unifiedSocialCreditCodeService.findEntityByCreditCode(creditCode);
            return Result.ok(unifiedSocialCreditCode);
        }
    }

    private String getNextLevelCode(String levelCode) {
        String substring = levelCode.substring(levelCode.length() - 3);
        int next = Integer.parseInt(substring) + 1;
        String format = String.format("%03d", next);
        return levelCode.substring(0, levelCode.length() - 3) + format;
    }

    public String qxCode(String id) {
        List<Geocode> geocodeList = customDictService.getGeocodes();
        Map<String, String> geoCodeMap = geocodeList.stream().collect(Collectors.toMap(Geocode::getCode, Geocode::getType, (k1, k2) -> k1));
        String type = geoCodeMap.get(id);
        if (StrUtil.equalsAny(type, "4", "5")) {
            String levelCode = id;
            switch (type) {
                case "4":
                    levelCode = CQ_CODE + "-" + SXQ_CODE + "-" + id;
                    break;
                case "5":
                    levelCode = CQ_CODE + "-" + X_CODE + "-" + id;
                    break;
            }
            return levelCode;
        }
        return id;
    }

    private void recursionParentCode(List<String> codeList, String jgsyCode) {
        OrgInfo commonVO = this.getById(jgsyCode);
        codeList.add(jgsyCode);
        if (Objects.isNull(commonVO)) {
            return;
        }
        if (!StrUtil.equals(commonVO.getJgsyCode(), commonVO.getParentCode())) {
            String jgsyParentCode = commonVO.getParentCode();
            if (jgsyParentCode.length() == 15 && !StrUtil.equalsAny(commonVO.getJgsySystemCode(), "0x", "0y", "0z", "09", "10")) {
                final String jgsyType = commonVO.getTypeCode();
                codeList.add(jgsyParentCode);
                if (StrUtil.equals(jgsyType, NS)) {
                    codeList.add(jgsyParentCode + "a");
                } else if (StrUtil.equals(jgsyType, XS)) {
                    codeList.add(jgsyParentCode + "b");
                } else if (StrUtil.equals(jgsyType, SY)) {
                    codeList.add(jgsyParentCode + "c");
                } else if (StrUtil.equals(jgsyType, SY_NS)) {
                    codeList.add(jgsyParentCode + "d");
                }
            }
            this.recursionParentCode(codeList, jgsyParentCode);
        }
    }

    public TreeNode treeOld(OrgInfoTreeDTO dto) {
        UserInfo userInfo = (UserInfo) RequestHolder.getCurrUser();
        String orgCode = dto.getOrgCode();
        SysUserSystem sysUserSystem = sysUserSystemService.findByUserIdAndSystemId(userInfo.getUserId(), RequestHolder.getCurrSystemId(), userInfo.getUserPostId());
        List<OrgInfo> orgInfoList = orgInfoMapper.selectTree(orgCode, sysUserSystem.getUserOrgGroupId());

        CopyOnWriteArrayList<TreeNode> nodeList = new CopyOnWriteArrayList<>();
        orgInfoList.parallelStream().forEach(org -> {
            final TreeNode treeNode = this.createTreeNodeOld(org, orgCode);
            nodeList.add(treeNode);
        });
        TreeNode tree = TreeUtil.createTree(nodeList);
        List<TreeNode> children = tree.getChildren();
        if (CollUtil.isNotEmpty(children)) {
            children.forEach(et -> et.setChildren(null));
        }
        return tree;
    }

    public TreeNode createTreeNodeOld(OrgInfo org, String currUserOrgCode) {
        // final TreeNode build = TreeNode.builder().code(org.getLevelCode())
        //         .id(org.getId())
        //         .parentId(org.getParentId())
        //         .name(org.getName())
        //         .order(org.getSort())
        //         .isRoot(StrUtil.isNotEmpty(currUserOrgCode) && StrUtil.equals(currUserOrgCode, org.getLevelCode()))
        //         .extInfo(BbTreeInfo.builder().unitType(org.getUnitType()).build())
        //         .build();
        // return build;
        return null;
    }
}
