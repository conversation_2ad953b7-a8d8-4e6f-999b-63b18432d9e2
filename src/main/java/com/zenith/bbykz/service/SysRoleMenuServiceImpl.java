package com.zenith.bbykz.service;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.zenith.bbykz.api.SysRoleMenuService;
import com.zenith.bbykz.dao.SysRoleMenuMapper;
import com.zenith.bbykz.model.converter.SysRoleMenuConverter;
import com.zenith.bbykz.model.dto.SysMenuDTO;
import com.zenith.bbykz.model.dto.SysRoleDTO;
import com.zenith.bbykz.model.dto.SysRoleMenuDTO;
import com.zenith.bbykz.model.dto.SysRoleMenuListDTO;
import com.zenith.bbykz.model.entity.SysRoleMenu;
import com.zenith.bbykz.model.vo.SysRoleMenuVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <p>
 * sys_role_menu 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @date 2023-09-14 17:14:26
 */
@Service
public class SysRoleMenuServiceImpl extends ServiceImpl<SysRoleMenuMapper, SysRoleMenu> implements SysRoleMenuService {

    @Autowired
    private SysRoleMenuConverter sysRoleMenuConverter;
    @Autowired
    private SysRoleMenuMapper sysRoleMenuMapper;

    @Override
    public SysRoleMenu save(SysRoleMenuDTO dto) {
        SysRoleMenu entity = sysRoleMenuConverter.dto2Entity(dto);
        boolean flag = this.save(entity);
        return entity;
    }

    @Override
    public SysRoleMenuVO findById(String id) {
        SysRoleMenu entity = this.getById(id);
        return sysRoleMenuConverter.entity2Vo(entity);
    }

    @Override
    public Boolean update(SysRoleMenuDTO dto) {
        return this.updateById(sysRoleMenuConverter.dto2Entity(dto));
    }

    @Override
    public Boolean delete(String id) {
        return this.removeById(id);
    }

    @Override
    public Page<SysRoleMenu> list(SysRoleMenuListDTO dto) {
        final Page<SysRoleMenu> page = sysRoleMenuMapper.selectPage(new Page<>(dto.getPageNum(), dto.getPageSize()), new QueryWrapper<>());
        return page;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean saveMenus(SysRoleDTO dto) {
        List<SysRoleMenu> roleMenuList = new ArrayList<>();
        for (SysMenuDTO sysMenuDTO : dto.getMenus()) {
            SysRoleMenu sysRoleMenu = new SysRoleMenu();
            sysRoleMenu.setRoleId(dto.getId());
            sysRoleMenu.setMenuId(sysMenuDTO.getId());
            sysRoleMenu.setMenuCode(sysMenuDTO.getCode());
            roleMenuList.add(sysRoleMenu);
        }
        return this.saveBatch(roleMenuList);
    }

    @Override
    public List<String> findByRoleId(String roleId) {
        LambdaQueryWrapper<SysRoleMenu> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(SysRoleMenu::getRoleId, roleId);
        List<SysRoleMenu> list = this.list(wrapper);
        List<String> menuCodes = new ArrayList<>();
        if (CollUtil.isNotEmpty(list)) {
            return list.stream().map(SysRoleMenu::getMenuCode).collect(Collectors.toList());
        }
        return new ArrayList<>();
    }

    @Override
    public List<SysRoleMenu> findByRoleList(List<String> roleList) {
        LambdaQueryWrapper<SysRoleMenu> wrapper = new LambdaQueryWrapper<>();
        wrapper.in(SysRoleMenu::getRoleId, roleList);
        return this.list(wrapper);
    }
}
