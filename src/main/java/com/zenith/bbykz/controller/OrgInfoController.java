package com.zenith.bbykz.controller;

import com.efficient.common.permission.Permission;
import com.efficient.common.result.Result;
import com.efficient.common.validate.Common1Group;
import com.efficient.logs.annotation.Log;
import com.efficient.logs.constant.LogEnum;
import com.zenith.bbykz.api.OrgInfoService;
import com.zenith.bbykz.model.dto.OrgInfoDTO;
import com.zenith.bbykz.model.dto.OrgInfoListDTO;
import com.zenith.bbykz.model.dto.OrgInfoTreeDTO;
import com.zenith.bbykz.model.entity.OrgInfo;
import com.zenith.bbykz.model.vo.OrgInfoVO;
import com.zenith.bbykz.model.vo.register.UnitRegisterInfo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.constraints.NotBlank;
import java.util.HashSet;
import java.util.LinkedList;
import java.util.List;
import java.util.Set;

import static com.zenith.bbykz.common.constant.BbCommonConstant.CQ_CODE;

/**
 * <p>
 * org_info controller 层
 * </p>
 *
 * <AUTHOR>
 * @date 2023-09-14 17:51:37
 */
@RestController
@RequestMapping("/orgInfo")
@Validated
@Api(tags = "org_info")
@Permission
public class OrgInfoController {

    @Autowired
    private OrgInfoService orgInfoService;

    /**
     * 新增
     */
    @Log(logOpt = LogEnum.SAVE, module = "机构管理")
    @PostMapping("/save")
    @ApiOperation(value = "保存", response = Result.class)
    public Result save(@Validated @RequestBody OrgInfoDTO dto) {
        return orgInfoService.save(dto);
    }

    /**
     * 详情
     */
    @Log(logOpt = LogEnum.QUERY, module = "机构管理")
    @GetMapping("/find")
    @ApiOperation(value = "详情", response = Result.class)
    @ApiImplicitParams({
            @ApiImplicitParam(name = "id", value = "数据唯一标识", required = true)
    })
    public Result find(@NotBlank(message = "id 不能为空") @RequestParam(name = "id") String id) {
        OrgInfoVO entity = orgInfoService.findById(id);
        return Result.ok(entity);
    }

    /**
     * 修改
     */
    @Log(logOpt = LogEnum.UPDATE, module = "机构管理")
    @PostMapping("/update")
    @ApiOperation(value = "修改", response = Result.class)
    public Result update(@Validated @RequestBody OrgInfoDTO dto) {
        return orgInfoService.update(dto);
    }

    /**
     * 修改
     */
    @Log(logOpt = LogEnum.UPDATE, module = "机构管理")
    @PostMapping("/updateNew")
    @ApiOperation(value = "修改", response = Result.class)
    public Result updateNew(@Validated @RequestBody OrgInfoDTO dto) {
        return orgInfoService.updateNew(dto);
    }

    /**
     * 删除
     */
    @Log(logOpt = LogEnum.DELETE, module = "机构管理")
    @GetMapping("/delete")
    @ApiOperation(value = "删除", response = Result.class)
    @ApiImplicitParams({
            @ApiImplicitParam(name = "id", value = "数据唯一标识", required = true)
    })
    public Result delete(@NotBlank(message = "id 不能为空") @RequestParam(name = "id") String id) {
        return orgInfoService.delete(id);
    }

    /**
     * 列表
     */
    // @Log(logOpt = LogEnum.PAGE, module = "机构管理")
    @PostMapping("/list")
    @ApiOperation(value = "列表", response = Result.class)
    public Result list(@Validated(value = Common1Group.class) @RequestBody OrgInfoListDTO dto) {
        dto.setOrgCode(orgInfoService.findLevelCodeById(dto.getOrgCode()));
        return Result.ok(orgInfoService.list(dto));
    }

    @Log(logOpt = LogEnum.PAGE, module = "机构树")
    @PostMapping("/treeSearch")
    @ApiOperation(value = "列表", response = Result.class)
    public Result treeSearch(@Validated @RequestBody OrgInfoListDTO dto) {
        dto.setOrgCode(orgInfoService.findLevelCodeById(dto.getOrgCode()));
        return Result.ok(orgInfoService.treeSearch(dto));
    }

    /**
     * 列表
     */
    @Log(logOpt = LogEnum.QUERY, module = "机构层级")
    @GetMapping("/treeLevel")
    @ApiOperation(value = "列表", response = Result.class)
    public Result treeLevel(@NotBlank(message = "levelCode 不能为空") @RequestParam(name = "levelCode") String levelCode) {
        return Result.ok(orgInfoService.treeLevel(levelCode));
    }

    /**
     * 机构树
     *
     * @param dto
     * @return
     */
//    @Log(logOpt = LogEnum.PAGE)
    @PostMapping("/tree")
    @ApiOperation(value = "机构树", response = Result.class)
    public Result tree(@Validated @RequestBody OrgInfoTreeDTO dto) {
        return Result.ok(orgInfoService.tree(dto));
    }

    /**
     * 获取登记信息
     */
    @Log(logOpt = LogEnum.QUERY, module = "机构管理")
    @GetMapping("/getRegisterInfo")
    @ApiOperation(value = "获取登记信息", response = Result.class)
    @ApiImplicitParams({
            @ApiImplicitParam(name = "id", value = "数据唯一标识", required = true)
    })
    public Result<UnitRegisterInfo> getRegisterInfo(@NotBlank(message = "id 不能为空") @RequestParam(name = "id") String id) {
        return orgInfoService.getRegisterInfo(id);
    }

    /**
     * 获取登记信息
     */
    @Log(logOpt = LogEnum.QUERY, module = "机构管理")
    @GetMapping("/findRegisterInfo")
    @ApiOperation(value = "获取登记信息", response = Result.class)
    @ApiImplicitParams({
            @ApiImplicitParam(name = "id", value = "数据唯一标识", required = true)
    })
    public Result findRegisterInfo(@NotBlank(message = "id 不能为空") @RequestParam(name = "id") String id) {
        return orgInfoService.findRegisterInfo(id);
    }

}
