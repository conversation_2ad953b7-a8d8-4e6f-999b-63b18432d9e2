package com.zenith.bbykz.controller.register.interior;

import com.efficient.common.permission.Permission;
import com.efficient.common.result.Result;
import com.efficient.logs.annotation.Log;
import com.efficient.logs.constant.LogEnum;
import com.zenith.bbykz.api.register.interior.RegisterLegalPersonLogService;
import com.zenith.bbykz.model.dto.register.interior.RegisterLegalPersonLogListDTO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <p>
 * 登记信息-内部事务-法人库日志 controller 层
 * </p>
 *
 * <AUTHOR>
 * @date 2024-03-12 17:24:37
 */
@RestController
@RequestMapping("/register/interior/registerLegalPersonLog")
@Validated
@Api(tags = "登记信息-内部事务-法人库日志")
@Permission
public class RegisterLegalPersonLogController {

    @Autowired
    private RegisterLegalPersonLogService registerLegalPersonLogService;

    // /**
    // * 新增
    // */
    // @Log(logOpt = LogEnum.SAVE, module = "登记信息-内部事务-法人库日志")
    // @PostMapping("/save")
    // @ApiOperation(value = "保存")
    // public Result<RegisterLegalPersonLog> save(@Validated @RequestBody RegisterLegalPersonLogDTO dto) {
    //     return registerLegalPersonLogService.save(dto);
    // }
    //
    // /**
    // * 详情
    // */
    // @Log(logOpt = LogEnum.QUERY, module = "登记信息-内部事务-法人库日志")
    // @GetMapping("/find")
    // @ApiOperation(value = "详情")
    // @ApiImplicitParams({
    //         @ApiImplicitParam(name = "id", value = "数据唯一标识", required = true)
    // })
    // public Result<RegisterLegalPersonLogVO> find(@NotBlank(message = "id 不能为空") @RequestParam(name="id") String id) {
    //     return registerLegalPersonLogService.findById(id);
    // }
    //
    // /**
    // * 修改
    // */
    // @Log(logOpt = LogEnum.UPDATE, module = "登记信息-内部事务-法人库日志")
    // @PostMapping("/update")
    // @ApiOperation(value = "修改")
    // public Result<Boolean> update(@Validated @RequestBody RegisterLegalPersonLogDTO dto) {
    //     return registerLegalPersonLogService.update(dto);
    // }
    //
    // /**
    // * 删除
    // */
    // @Log(logOpt = LogEnum.DELETE, module = "登记信息-内部事务-法人库日志")
    // @GetMapping("/delete")
    // @ApiOperation(value = "删除")
    // @ApiImplicitParams({
    //         @ApiImplicitParam(name = "id", value = "数据唯一标识", required = true)
    // })
    // public Result<Boolean> delete(@NotBlank(message = "id 不能为空") @RequestParam(name="id") String id) {
    //     return registerLegalPersonLogService.delete(id);
    // }

    /**
     * 列表
     */
    @Log(logOpt = LogEnum.PAGE, module = "登记信息-内部事务-法人库日志")
    @PostMapping("/list")
    @ApiOperation(value = "列表", response = Result.class)
    public Result list(@Validated @RequestBody RegisterLegalPersonLogListDTO dto) {
        return Result.ok(registerLegalPersonLogService.list(dto));
    }

}
