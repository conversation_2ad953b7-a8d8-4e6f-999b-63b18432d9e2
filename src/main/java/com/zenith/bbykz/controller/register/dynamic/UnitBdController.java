package com.zenith.bbykz.controller.register.dynamic;

import com.efficient.common.permission.Permission;
import com.efficient.common.result.Result;
import com.efficient.common.validate.Common1Group;
import com.efficient.common.validate.Common2Group;
import com.efficient.logs.annotation.Log;
import com.efficient.logs.constant.LogEnum;
import com.zenith.bbykz.api.OrgInfoService;
import com.zenith.bbykz.api.register.dynamic.UnitBdService;
import com.zenith.bbykz.model.dto.register.dynamic.UnitBdDTO;
import com.zenith.bbykz.model.dto.register.dynamic.UnitBdFlowDTO;
import com.zenith.bbykz.model.dto.register.dynamic.UnitBdListDTO;
import com.zenith.bbykz.model.entity.register.dynamic.UnitBd;
import com.zenith.bbykz.model.vo.register.dynamic.UnitBdVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.constraints.NotBlank;

/**
 * <p>
 * 登记信息-预警感知-信息变动 controller 层
 * </p>
 *
 * <AUTHOR>
 * @date 2024-04-09 11:50:41
 */
@RestController
@RequestMapping("/register/dynamic/unitBd")
@Validated
@Api(tags = "登记信息-预警感知-信息变动")
@Permission
public class UnitBdController {

    @Autowired
    private UnitBdService unitBdService;
    @Autowired
    private OrgInfoService orgInfoService;
    /**
     * 新增
     */
    @Log(logOpt = LogEnum.SAVE, module = "登记信息-预警感知-信息变动")
    @PostMapping("/save")
    @ApiOperation(value = "保存")
    public Result<UnitBd> save(@Validated @RequestBody UnitBdDTO dto) {
        return unitBdService.save(dto);
    }

    /**
     * 详情
     */
    @Log(logOpt = LogEnum.QUERY, module = "登记信息-预警感知-信息变动")
    @GetMapping("/find")
    @ApiOperation(value = "详情")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "id", value = "数据唯一标识", required = true)
    })
    public Result<UnitBdVO> find(@NotBlank(message = "id 不能为空") @RequestParam(name = "id") String id) {
        return unitBdService.findById(id);
    }

    /**
     * 修改
     */
    @Log(logOpt = LogEnum.UPDATE, module = "登记信息-预警感知-信息变动")
    @PostMapping("/update")
    @ApiOperation(value = "修改")
    public Result<Boolean> update(@Validated @RequestBody UnitBdDTO dto) {
        return unitBdService.update(dto);
    }

    /**
     * 删除
     */
    @Log(logOpt = LogEnum.DELETE, module = "登记信息-预警感知-信息变动")
    @GetMapping("/delete")
    @ApiOperation(value = "删除")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "id", value = "数据唯一标识", required = true)
    })
    public Result<Boolean> delete(@NotBlank(message = "id 不能为空") @RequestParam(name = "id") String id) {
        return unitBdService.delete(id);
    }

    /**
     * 列表
     */
    @Log(logOpt = LogEnum.PAGE, module = "登记信息-预警感知-信息变动")
    @PostMapping("/list")
    @ApiOperation(value = "列表", response = Result.class)
    public Result list(@Validated(Common1Group.class) @RequestBody UnitBdListDTO dto) {
        dto.setOrgLevelCode(orgInfoService.findLevelCodeById(dto.getOrgLevelCode()));
        return Result.ok(unitBdService.list(dto));
    }

    /**
     * 预警信息
     */
    @Log(logOpt = LogEnum.PAGE, module = "登记信息-预警感知-信息变动")
    @PostMapping("/warnList")
    @ApiOperation(value = "预警信息", response = Result.class)
    public Result warnList(@Validated({Common1Group.class, Common2Group.class}) @RequestBody UnitBdListDTO dto) {
        dto.setOrgLevelCode(orgInfoService.findLevelCodeById(dto.getOrgLevelCode()));
        return Result.ok(unitBdService.warnList(dto));
    }

    /**
     * 发送消息
     */
    @Log(logOpt = LogEnum.SEND, module = "登记信息-预警感知-信息变动")
    @PostMapping("/sendMsg")
    @ApiOperation(value = "发送消息", response = Result.class)
    public Result sendMsg(@Validated @RequestBody UnitBdDTO dto) {
        return unitBdService.sendMsg(dto);
    }

    /**
     * 改变状态
     */
    @Log(logOpt = LogEnum.SEND, module = "登记信息-预警感知-信息变动")
    @PostMapping("/changeState")
    @ApiOperation(value = "发送消息", response = Result.class)
    public Result changeState(@Validated @RequestBody UnitBdFlowDTO dto) {
        return unitBdService.changeState(dto);
    }


    /**
     * 获取进度
     */
    @Log(logOpt = LogEnum.SEND, module = "登记信息-预警感知-信息变动")
    @GetMapping("/findFlow")
    @ApiOperation(value = "获取进度", response = Result.class)
    public Result findFlow(@NotBlank(message = "id 不能为空") @RequestParam(name = "id") String id) {
        return unitBdService.findFlow(id);
    }

}
