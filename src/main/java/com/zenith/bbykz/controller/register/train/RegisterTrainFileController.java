package com.zenith.bbykz.controller.register.train;

import com.efficient.common.permission.Permission;
import com.efficient.common.result.Result;
import com.efficient.file.annotation.AutoSaveFileInfo;
import com.efficient.logs.annotation.Log;
import com.efficient.logs.constant.LogEnum;
import com.zenith.bbykz.api.register.train.RegisterTrainFileService;
import com.zenith.bbykz.model.dto.register.train.RegisterTrainFileDTO;
import com.zenith.bbykz.model.dto.register.train.RegisterTrainFileListDTO;
import com.zenith.bbykz.model.entity.register.train.RegisterTrainFile;
import com.zenith.bbykz.model.vo.register.train.RegisterTrainFileVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.constraints.NotBlank;

/**
 * <p>
 * 登记信息-培训管理-课件管理 controller 层
 * </p>
 *
 * <AUTHOR>
 * @date 2024-03-05 15:44:53
 */
@RestController
@RequestMapping("/register/train/registerTrainFile")
@Validated
@Api(tags = "登记信息-培训管理-课件管理")
@Permission
public class RegisterTrainFileController {

    @Autowired
    private RegisterTrainFileService registerTrainFileService;

    /**
     * 新增
     */
    @Log(logOpt = LogEnum.SAVE, module = "登记信息-培训管理-课件管理")
    @PostMapping("/save")
    @ApiOperation(value = "保存")
    public Result<RegisterTrainFile> save(@Validated @RequestBody RegisterTrainFileDTO dto) {
        return registerTrainFileService.save(dto);
    }

    /**
     * 详情
     */
    @Log(logOpt = LogEnum.QUERY, module = "登记信息-培训管理-课件管理")
    @GetMapping("/find")
    @ApiOperation(value = "详情")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "id", value = "数据唯一标识", required = true)
    })
    public Result<RegisterTrainFileVO> find(@NotBlank(message = "id 不能为空") @RequestParam(name = "id") String id) {
        return registerTrainFileService.findById(id);
    }

    /**
     * 修改
     */
    @Log(logOpt = LogEnum.UPDATE, module = "登记信息-培训管理-课件管理")
    @PostMapping("/update")
    @ApiOperation(value = "修改")
    @AutoSaveFileInfo
    public Result<Boolean> update(@Validated @RequestBody RegisterTrainFileDTO dto) {
        return registerTrainFileService.update(dto);
    }

    /**
     * 删除
     */
    @Log(logOpt = LogEnum.DELETE, module = "登记信息-培训管理-课件管理")
    @GetMapping("/delete")
    @ApiOperation(value = "删除")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "id", value = "数据唯一标识", required = true)
    })
    public Result<Boolean> delete(@NotBlank(message = "id 不能为空") @RequestParam(name = "id") String id) {
        return registerTrainFileService.delete(id);
    }

    /**
     * 列表
     */
    @Log(logOpt = LogEnum.PAGE, module = "登记信息-培训管理-课件管理")
    @PostMapping("/list")
    @ApiOperation(value = "列表", response = Result.class)
    public Result list(@Validated @RequestBody RegisterTrainFileListDTO dto) {
        return Result.ok(registerTrainFileService.list(dto));
    }

}
