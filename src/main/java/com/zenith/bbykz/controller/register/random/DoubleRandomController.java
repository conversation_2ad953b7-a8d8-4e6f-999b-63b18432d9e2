package com.zenith.bbykz.controller.register.random;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.efficient.common.permission.Permission;
import com.efficient.common.result.Result;
import com.efficient.logs.annotation.Log;
import com.efficient.logs.constant.LogEnum;
import com.zenith.bbykz.api.OrgInfoService;
import com.zenith.bbykz.api.register.random.DoubleRandomService;
import com.zenith.bbykz.model.dto.register.random.DoubleRandomDTO;
import com.zenith.bbykz.model.dto.register.random.DoubleRandomListDTO;
import com.zenith.bbykz.model.entity.register.random.DoubleRandom;
import com.zenith.bbykz.model.vo.register.random.DoubleRandomVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.constraints.NotBlank;

/**
 * <p>
 * 登记信息-内部事务-双随机检查 controller 层
 * </p>
 *
 * <AUTHOR>
 * @date 2024-05-06 15:48:28
 */
@RestController
@RequestMapping("/register/random/doubleRandom")
@Validated
@Api(tags = "登记信息-内部事务-双随机检查")
@Permission
public class DoubleRandomController {

    @Autowired
    private DoubleRandomService doubleRandomService;
    @Autowired
    private OrgInfoService orgInfoService;

    /**
     * 新增
     */
    @Log(logOpt = LogEnum.SAVE, module = "登记信息-内部事务-双随机检查")
    @PostMapping("/save")
    @ApiOperation(value = "保存", response = DoubleRandom.class)
    public Result<DoubleRandom> save(@Validated @RequestBody DoubleRandomDTO dto) {
        return doubleRandomService.save(dto);
    }

    /**
     * 详情
     */
    @Log(logOpt = LogEnum.QUERY, module = "登记信息-内部事务-双随机检查", desc = "查看 [DoubleRandomLogFunction{#id}] 双随机检查详情")
    @GetMapping("/find")
    @ApiOperation(value = "详情", response = DoubleRandomVO.class)
    @ApiImplicitParams({
            @ApiImplicitParam(name = "id", value = "数据唯一标识", required = true)
    })
    public Result<DoubleRandomVO> find(@NotBlank(message = "id 不能为空") @RequestParam(name = "id") String id) {
        return doubleRandomService.findById(id);
    }

    /**
     * 修改
     */
    @Log(logOpt = LogEnum.UPDATE, module = "登记信息-内部事务-双随机检查")
    @PostMapping("/update")
    @ApiOperation(value = "修改", response = Boolean.class)
    public Result<Boolean> update(@Validated @RequestBody DoubleRandomDTO dto) {
        return doubleRandomService.update(dto);
    }

    /**
     * 删除
     */
    @Log(logOpt = LogEnum.DELETE, module = "登记信息-内部事务-双随机检查")
    @GetMapping("/delete")
    @ApiOperation(value = "删除", response = Boolean.class)
    @ApiImplicitParams({
            @ApiImplicitParam(name = "id", value = "数据唯一标识", required = true)
    })
    public Result<Boolean> delete(@NotBlank(message = "id 不能为空") @RequestParam(name = "id") String id) {
        return doubleRandomService.delete(id);
    }

    /**
     * 列表
     */
    @Log(logOpt = LogEnum.PAGE, module = "登记信息-内部事务-双随机检查")
    @PostMapping("/list")
    @ApiOperation(value = "列表", response = DoubleRandomVO.class)
    public Result<Page<DoubleRandomVO>> list(@Validated @RequestBody DoubleRandomListDTO dto) {
        dto.setOrgLevelCode(orgInfoService.findLevelCodeById(dto.getOrgLevelCode()));
        Page<DoubleRandomVO> page = doubleRandomService.list(dto);
        return Result.ok(page);
    }

}
