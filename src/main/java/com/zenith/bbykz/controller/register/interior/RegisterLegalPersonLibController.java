package com.zenith.bbykz.controller.register.interior;

import com.efficient.common.permission.Permission;
import com.efficient.common.result.Result;
import com.efficient.logs.annotation.Log;
import com.efficient.logs.constant.LogEnum;
import com.zenith.bbykz.api.register.interior.RegisterLegalPersonLibService;
import com.zenith.bbykz.model.dto.register.interior.RegisterLegalPersonLibListDTO;
import com.zenith.bbykz.model.entity.register.interior.RegisterLegalPersonLib;
import com.zenith.bbykz.model.vo.register.interior.RegisterLegalPersonLibVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * <p>
 * 登记信息-内部事务-法人库 controller 层
 * </p>
 *
 * <AUTHOR>
 * @date 2024-03-12 14:55:18
 */
@RestController
@RequestMapping("/register/interior/registerLegalPersonLib")
@Validated
@Api(tags = "登记信息-内部事务-法人库")
@Permission
public class RegisterLegalPersonLibController {

    @Autowired
    private RegisterLegalPersonLibService registerLegalPersonLibService;

    // /**
    //  * 新增
    //  */
    // @Log(logOpt = LogEnum.SAVE, module = "登记信息-内部事务-法人库")
    // @PostMapping("/save")
    // @ApiOperation(value = "保存")
    // public Result<RegisterLegalPersonLib> save(@Validated @RequestBody RegisterLegalPersonLibDTO dto) {
    //     return registerLegalPersonLibService.save(dto);
    // }

    /**
     * 详情
     */
    @Log(logOpt = LogEnum.QUERY, module = "登记信息-内部事务-法人库")
    @GetMapping("/find")
    @ApiOperation(value = "详情")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "id", value = "数据唯一标识", required = true)
    })
    public Result<RegisterLegalPersonLibVO> find(@NotBlank(message = "id 不能为空") @RequestParam(name = "id") String id) {
        return registerLegalPersonLibService.findById(id);
    }

    @Log(logOpt = LogEnum.CUSTOM, customOpt = "验证双法人", module = "登记信息-内部事务-法人库")
    @GetMapping("/verify")
    @ApiOperation(value = "验证")
    // @ApiImplicitParams({
    //         @ApiImplicitParam(name = "id", value = "数据唯一标识", required = true)
    // })
    public Result<List<RegisterLegalPersonLib>> verify(@NotBlank(message = "name 不能为空") @RequestParam(name = "name") String name,
                                                       @NotBlank(message = "idCard 不能为空") @RequestParam(name = "idCard") String idCard) {
        return Result.ok(registerLegalPersonLibService.verify(name, idCard));
    }

    // /**
    //  * 修改
    //  */
    // @Log(logOpt = LogEnum.UPDATE, module = "登记信息-内部事务-法人库")
    // @PostMapping("/update")
    // @ApiOperation(value = "修改")
    // public Result<Boolean> update(@Validated @RequestBody RegisterLegalPersonLibDTO dto) {
    //     return registerLegalPersonLibService.update(dto);
    // }

    // /**
    //  * 删除
    //  */
    // @Log(logOpt = LogEnum.DELETE, module = "登记信息-内部事务-法人库")
    // @GetMapping("/delete")
    // @ApiOperation(value = "删除")
    // @ApiImplicitParams({
    //         @ApiImplicitParam(name = "id", value = "数据唯一标识", required = true)
    // })
    // public Result<Boolean> delete(@NotBlank(message = "id 不能为空") @RequestParam(name = "id") String id) {
    //     return registerLegalPersonLibService.delete(id);
    // }

    /**
     * 列表
     */
    @Log(logOpt = LogEnum.PAGE, module = "登记信息-内部事务-法人库")
    @PostMapping("/list")
    @ApiOperation(value = "列表", response = Result.class)
    public Result list(@Validated @RequestBody RegisterLegalPersonLibListDTO dto) {
        return Result.ok(registerLegalPersonLibService.list(dto));
    }

    /**
     * 导入
     */
    @Log(logOpt = LogEnum.IMPORT, module = "登记信息-内部事务-法人库")
    @GetMapping("/import")
    @ApiOperation(value = "导入", response = Result.class)
    public Result importExcel(@NotNull(message = "fileId 不能为空") String fileId) {
        return Result.ok(registerLegalPersonLibService.importExcel(fileId));
    }

    @GetMapping("/rollback")
    @ApiOperation(value = "回退上一个版本", response = Result.class)
    @Log(logOpt = LogEnum.ROLLBACK, module = "登记信息-内部事务-法人库")
    public Result rollback() {
        return registerLegalPersonLibService.rollback();
    }
}
