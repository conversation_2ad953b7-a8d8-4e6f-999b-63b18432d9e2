package com.zenith.bbykz.controller.register.train;

import com.efficient.common.permission.Permission;
import com.efficient.common.result.Result;
import com.efficient.logs.annotation.Log;
import com.efficient.logs.constant.LogEnum;
import com.zenith.bbykz.api.register.train.RegisterTrainCourseFeedbackService;
import com.zenith.bbykz.model.dto.register.train.RegisterTrainCourseFeedbackDTO;
import com.zenith.bbykz.model.dto.register.train.RegisterTrainCourseFeedbackListDTO;
import com.zenith.bbykz.model.entity.register.train.RegisterTrainCourseFeedback;
import com.zenith.bbykz.model.vo.register.train.RegisterTrainCourseFeedbackVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.constraints.NotBlank;

/**
 * <p>
 * 登记信息-培训管理-反馈意见 controller 层
 * </p>
 *
 * <AUTHOR>
 * @date 2024-03-05 15:44:53
 */
@RestController
@RequestMapping("/register/train/registerTrainCourseFeedback")
@Validated
@Api(tags = "登记信息-培训管理-反馈意见")
@Permission
public class RegisterTrainCourseFeedbackController {

    @Autowired
    private RegisterTrainCourseFeedbackService registerTrainCourseFeedbackService;

    /**
     * 新增
     */
    @Log(logOpt = LogEnum.SAVE, module = "登记信息-培训管理-反馈意见")
    @PostMapping("/save")
    @ApiOperation(value = "保存")
    public Result<RegisterTrainCourseFeedback> save(@Validated @RequestBody RegisterTrainCourseFeedbackDTO dto) {
        return registerTrainCourseFeedbackService.save(dto);
    }

    /**
     * 详情
     */
    @Log(logOpt = LogEnum.QUERY, module = "登记信息-培训管理-反馈意见")
    @GetMapping("/find")
    @ApiOperation(value = "详情")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "id", value = "数据唯一标识", required = true)
    })
    public Result<RegisterTrainCourseFeedbackVO> find(@NotBlank(message = "id 不能为空") @RequestParam(name = "id") String id) {
        return registerTrainCourseFeedbackService.findById(id);
    }

    /**
     * 修改
     */
    @Log(logOpt = LogEnum.UPDATE, module = "登记信息-培训管理-反馈意见")
    @PostMapping("/update")
    @ApiOperation(value = "修改")
    public Result<Boolean> update(@Validated @RequestBody RegisterTrainCourseFeedbackDTO dto) {
        return registerTrainCourseFeedbackService.update(dto);
    }

    /**
     * 删除
     */
    @Log(logOpt = LogEnum.DELETE, module = "登记信息-培训管理-反馈意见")
    @GetMapping("/delete")
    @ApiOperation(value = "删除")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "id", value = "数据唯一标识", required = true)
    })
    public Result<Boolean> delete(@NotBlank(message = "id 不能为空") @RequestParam(name = "id") String id) {
        return registerTrainCourseFeedbackService.delete(id);
    }

    /**
     * 列表
     */
    @Log(logOpt = LogEnum.PAGE, module = "登记信息-培训管理-反馈意见")
    @PostMapping("/list")
    @ApiOperation(value = "列表", response = Result.class)
    public Result list(@Validated @RequestBody RegisterTrainCourseFeedbackListDTO dto) {
        return Result.ok(registerTrainCourseFeedbackService.list(dto));
    }

}
