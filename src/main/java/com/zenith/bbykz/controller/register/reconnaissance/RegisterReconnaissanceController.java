package com.zenith.bbykz.controller.register.reconnaissance;

import com.efficient.common.permission.Permission;
import com.efficient.common.result.Result;
import com.efficient.logs.annotation.Log;
import com.efficient.logs.constant.LogEnum;
import com.zenith.bbykz.api.OrgInfoService;
import com.zenith.bbykz.api.register.reconnaissance.RegisterReconnaissanceService;
import com.zenith.bbykz.model.dto.register.reconnaissance.RegisterReconnaissanceDTO;
import com.zenith.bbykz.model.dto.register.reconnaissance.RegisterReconnaissanceListDTO;
import com.zenith.bbykz.model.entity.register.reconnaissance.RegisterReconnaissance;
import com.zenith.bbykz.model.vo.register.reconnaissance.RegisterReconnaissanceVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.constraints.NotBlank;

/**
 * <p>
 * 登记信息-内部事务-实地勘察 controller 层
 * </p>
 *
 * <AUTHOR>
 * @date 2024-03-28 11:28:37
 */
@RestController
@RequestMapping("/register/reconnaissance/registerReconnaissance")
@Validated
@Api(tags = "登记信息-内部事务-实地勘察")
@Permission
public class RegisterReconnaissanceController {

    @Autowired
    private RegisterReconnaissanceService registerReconnaissanceService;
    @Autowired
    private OrgInfoService orgInfoService;
    /**
     * 新增
     */
    @Log(logOpt = LogEnum.SAVE, module = "登记信息-内部事务-实地勘察")
    @PostMapping("/save")
    @ApiOperation(value = "保存")
    public Result<RegisterReconnaissance> save(@Validated @RequestBody RegisterReconnaissanceDTO dto) {
        return registerReconnaissanceService.save(dto);
    }

    /**
     * 详情
     */
    @Log(logOpt = LogEnum.QUERY, module = "登记信息-内部事务-实地勘察")
    @GetMapping("/find")
    @ApiOperation(value = "详情")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "id", value = "数据唯一标识", required = true)
    })
    public Result<RegisterReconnaissanceVO> find(@NotBlank(message = "id 不能为空") @RequestParam(name = "id") String id) {
        return registerReconnaissanceService.findById(id);
    }

    /**
     * 修改
     */
    @Log(logOpt = LogEnum.UPDATE, module = "登记信息-内部事务-实地勘察")
    @PostMapping("/update")
    @ApiOperation(value = "修改")
    public Result<Boolean> update(@Validated @RequestBody RegisterReconnaissanceDTO dto) {
        return registerReconnaissanceService.update(dto);
    }

    /**
     * 删除
     */
    @Log(logOpt = LogEnum.DELETE, module = "登记信息-内部事务-实地勘察")
    @GetMapping("/delete")
    @ApiOperation(value = "删除")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "id", value = "数据唯一标识", required = true)
    })
    public Result<Boolean> delete(@NotBlank(message = "id 不能为空") @RequestParam(name = "id") String id) {
        return registerReconnaissanceService.delete(id);
    }

    /**
     * 列表
     */
    @Log(logOpt = LogEnum.PAGE, module = "登记信息-内部事务-实地勘察")
    @PostMapping("/list")
    @ApiOperation(value = "列表", response = Result.class)
    public Result list(@Validated @RequestBody RegisterReconnaissanceListDTO dto) {
        dto.setOrgLevelCode(orgInfoService.findLevelCodeById(dto.getOrgLevelCode()));
        return Result.ok(registerReconnaissanceService.list(dto));
    }

}
