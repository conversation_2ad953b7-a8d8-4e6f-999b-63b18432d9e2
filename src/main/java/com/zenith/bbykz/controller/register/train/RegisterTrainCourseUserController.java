package com.zenith.bbykz.controller.register.train;

import com.efficient.common.permission.Permission;
import com.efficient.common.result.Result;
import com.efficient.logs.annotation.Log;
import com.efficient.logs.constant.LogEnum;
import com.zenith.bbykz.api.register.train.RegisterTrainCourseUserService;
import com.zenith.bbykz.api.register.train.RegisterTrainUserInfoService;
import com.zenith.bbykz.model.dto.register.train.RegisterTrainCourseUserDTO;
import com.zenith.bbykz.model.dto.register.train.RegisterTrainCourseUserListDTO;
import com.zenith.bbykz.model.entity.register.train.RegisterTrainCourseUser;
import com.zenith.bbykz.model.entity.register.train.RegisterTrainUserInfo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.constraints.NotBlank;
import java.util.List;

/**
 * <p>
 * 登记信息-培训管理-用户课程 controller 层
 * </p>
 *
 * <AUTHOR>
 * @date 2024-03-12 10:11:37
 */
@RestController
@RequestMapping("/register/train/registerTrainCourseUser")
@Validated
@Api(tags = "登记信息-培训管理-用户课程")
@Permission
public class RegisterTrainCourseUserController {

    @Autowired
    private RegisterTrainCourseUserService registerTrainCourseUserService;
    @Autowired
    private RegisterTrainUserInfoService registerTrainUserInfoService;

    /**
     * 新增
     */
    @Log(logOpt = LogEnum.SAVE, module = "登记信息-培训管理-用户课程")
    @PostMapping("/save")
    @ApiOperation(value = "保存")
    public Result<RegisterTrainCourseUser> save(@Validated @RequestBody RegisterTrainCourseUserDTO dto) {
        return registerTrainCourseUserService.save(dto);
    }

    // /**
    //  * 详情
    //  */
    // @Log(logOpt = LogEnum.QUERY, module = "登记信息-培训管理-用户课程")
    // @GetMapping("/find")
    // @ApiOperation(value = "详情")
    // @ApiImplicitParams({
    //         @ApiImplicitParam(name = "id", value = "数据唯一标识", required = true)
    // })
    // public Result<RegisterTrainCourseUserVO> find(@NotBlank(message = "id 不能为空") @RequestParam(name = "id") String id) {
    //     return registerTrainCourseUserService.findById(id);
    // }

    @Log(logOpt = LogEnum.QUERY, module = "登记信息-培训管理-用户课程")
    @GetMapping("/findCourseUserInfo")
    @ApiOperation(value = "查找课程进度")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "userId", value = "用户id", required = true),
            @ApiImplicitParam(name = "courseId", value = "课程ID", required = true)
    })
    public Result<List<RegisterTrainUserInfo>> findCourseUserInfo(
            @NotBlank(message = "userId 不能为空") @RequestParam(name = "userId") String userId,
            @NotBlank(message = "courseId 不能为空") @RequestParam(name = "courseId") String courseId
    ) {
        List<RegisterTrainUserInfo> courseUserInfo = registerTrainUserInfoService.findCourseUserInfo(userId, courseId);
        return Result.ok(courseUserInfo);
    }

    /**
     * 修改
     */
    @Log(logOpt = LogEnum.UPDATE, module = "登记信息-培训管理-用户课程")
    @PostMapping("/update")
    @ApiOperation(value = "修改")
    public Result<Boolean> update(@Validated @RequestBody RegisterTrainCourseUserDTO dto) {
        return registerTrainCourseUserService.update(dto);
    }

    // /**
    //  * 删除
    //  */
    // @Log(logOpt = LogEnum.DELETE, module = "登记信息-培训管理-用户课程")
    // @GetMapping("/delete")
    // @ApiOperation(value = "删除")
    // @ApiImplicitParams({
    //         @ApiImplicitParam(name = "id", value = "数据唯一标识", required = true)
    // })
    // public Result<Boolean> delete(@NotBlank(message = "id 不能为空") @RequestParam(name = "id") String id) {
    //     return registerTrainCourseUserService.delete(id);
    // }

    /**
     * 列表
     */
    @Log(logOpt = LogEnum.PAGE, module = "登记信息-培训管理-用户课程")
    @PostMapping("/list")
    @ApiOperation(value = "列表", response = Result.class)
    public Result list(@Validated @RequestBody RegisterTrainCourseUserListDTO dto) {
        return Result.ok(registerTrainCourseUserService.list(dto));
    }

}
