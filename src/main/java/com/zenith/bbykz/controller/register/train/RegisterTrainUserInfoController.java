package com.zenith.bbykz.controller.register.train;

import com.efficient.common.permission.Permission;
import com.efficient.common.result.Result;
import com.efficient.logs.annotation.Log;
import com.efficient.logs.constant.LogEnum;
import com.zenith.bbykz.api.register.train.RegisterTrainUserInfoService;
import com.zenith.bbykz.model.dto.register.train.RegisterTrainUserInfoDTO;
import com.zenith.bbykz.model.dto.register.train.RegisterTrainUserInfoListDTO;
import com.zenith.bbykz.model.entity.register.train.RegisterTrainUserInfo;
import com.zenith.bbykz.model.vo.register.train.RegisterTrainUserInfoVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.constraints.NotBlank;

/**
 * <p>
 * 登记信息-培训管理-学习进度 controller 层
 * </p>
 *
 * <AUTHOR>
 * @date 2024-03-06 14:39:04
 */
@RestController
@RequestMapping("/register/train/registerTrainUserInfo")
@Validated
@Api(tags = "登记信息-培训管理-学习进度")
@Permission
public class RegisterTrainUserInfoController {

    @Autowired
    private RegisterTrainUserInfoService registerTrainUserInfoService;

    /**
     * 新增
     */
    @Log(logOpt = LogEnum.SAVE, module = "登记信息-培训管理-学习进度")
    @PostMapping("/save")
    @ApiOperation(value = "保存，点击课程时调用")
    public Result<RegisterTrainUserInfo> save(@Validated @RequestBody RegisterTrainUserInfoDTO dto) {
        return registerTrainUserInfoService.save(dto);
    }

    /**
     * 详情
     */
    @Log(logOpt = LogEnum.QUERY, module = "登记信息-培训管理-学习进度")
    @GetMapping("/find")
    @ApiOperation(value = "详情")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "id", value = "数据唯一标识", required = true)
    })
    public Result<RegisterTrainUserInfoVO> find(@NotBlank(message = "id 不能为空") @RequestParam(name = "id") String id) {
        return registerTrainUserInfoService.findById(id);
    }

    /**
     * 修改
     */
    @Log(logOpt = LogEnum.UPDATE, module = "登记信息-培训管理-学习进度")
    @PostMapping("/update")
    @ApiOperation(value = "修改，播放视频时调用")
    public Result<Boolean> update(@Validated @RequestBody RegisterTrainUserInfoDTO dto) {
        return registerTrainUserInfoService.update(dto);
    }

    /**
     * 删除
     */
    @Log(logOpt = LogEnum.DELETE, module = "登记信息-培训管理-学习进度")
    @GetMapping("/delete")
    @ApiOperation(value = "删除")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "id", value = "数据唯一标识", required = true)
    })
    public Result<Boolean> delete(@NotBlank(message = "id 不能为空") @RequestParam(name = "id") String id) {
        return registerTrainUserInfoService.delete(id);
    }

    /**
     * 列表
     */
    @Log(logOpt = LogEnum.PAGE, module = "登记信息-培训管理-学习进度")
    @PostMapping("/list")
    @ApiOperation(value = "列表", response = Result.class)
    public Result list(@Validated @RequestBody RegisterTrainUserInfoListDTO dto) {
        return Result.ok(registerTrainUserInfoService.list(dto));
    }

}
