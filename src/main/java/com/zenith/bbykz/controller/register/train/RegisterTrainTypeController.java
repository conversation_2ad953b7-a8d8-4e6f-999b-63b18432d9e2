package com.zenith.bbykz.controller.register.train;

import com.efficient.common.permission.Permission;
import com.efficient.common.result.Result;
import com.efficient.logs.annotation.Log;
import com.efficient.logs.constant.LogEnum;
import com.zenith.bbykz.api.register.train.RegisterTrainTypeService;
import com.zenith.bbykz.model.dto.register.train.RegisterTrainTypeDTO;
import com.zenith.bbykz.model.dto.register.train.RegisterTrainTypeListDTO;
import com.zenith.bbykz.model.entity.register.train.RegisterTrainType;
import com.zenith.bbykz.model.vo.register.train.RegisterTrainTypeVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * <p>
 * 登记信息-培训管理-课程分类 controller 层
 * </p>
 *
 * <AUTHOR>
 * @date 2024-03-05 15:05:10
 */
@RestController
@RequestMapping("/register/train/registerTrainType")
@Validated
@Api(tags = "登记信息-培训管理-课程分类")
@Permission
public class RegisterTrainTypeController {

    @Autowired
    private RegisterTrainTypeService registerTrainTypeService;

    /**
     * 新增
     */
    @Log(logOpt = LogEnum.SAVE, module = "登记信息-培训管理-课程分类")
    @PostMapping("/save")
    @ApiOperation(value = "保存")
    public Result<RegisterTrainType> save(@Validated @RequestBody RegisterTrainTypeDTO dto) {
        return registerTrainTypeService.save(dto);
    }

    /**
     * 详情
     */
    @Log(logOpt = LogEnum.QUERY, module = "登记信息-培训管理-课程分类")
    @GetMapping("/find")
    @ApiOperation(value = "详情")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "id", value = "数据唯一标识", required = true)
    })
    public Result<RegisterTrainTypeVO> find(@NotBlank(message = "id 不能为空") @RequestParam(name = "id") String id) {
        return registerTrainTypeService.findById(id);
    }

    /**
     * 修改
     */
    @Log(logOpt = LogEnum.UPDATE, module = "登记信息-培训管理-课程分类")
    @PostMapping("/update")
    @ApiOperation(value = "修改")
    public Result<Boolean> update(@Validated @RequestBody RegisterTrainTypeDTO dto) {
        return registerTrainTypeService.update(dto);
    }

    /**
     * 类别启停
     */
    @Log(logOpt = LogEnum.UPDATE, module = "登记信息-培训管理-课程分类")
    @GetMapping("/enabled")
    @ApiOperation(value = "类别启停")
    // @ApiImplicitParams({
    //         @ApiImplicitParam(name = "id", value = "数据唯一标识", required = true),
    //         @ApiImplicitParam(name = "enabled", value = "1-启用，0-停用", required = true)
    // })
    public Result<Boolean> enabled(@NotBlank(message = "id 不能为空") @RequestParam(name = "id") String id,
                                   @NotNull(message = "enabled 不能为空") @RequestParam(name = "enabled") Integer enabled) {
        return registerTrainTypeService.enabled(id, enabled);
    }

    /**
     * 删除
     */
    @Log(logOpt = LogEnum.DELETE, module = "登记信息-培训管理-课程分类")
    @GetMapping("/delete")
    @ApiOperation(value = "删除")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "id", value = "数据唯一标识", required = true)
    })
    public Result<Boolean> delete(@NotBlank(message = "id 不能为空") @RequestParam(name = "id") String id) {
        return registerTrainTypeService.delete(id);
    }

    /**
     * 列表
     */
    @Log(logOpt = LogEnum.PAGE, module = "登记信息-培训管理-课程分类")
    @PostMapping("/list")
    @ApiOperation(value = "列表", response = Result.class)
    public Result list(@Validated @RequestBody RegisterTrainTypeListDTO dto) {
        return Result.ok(registerTrainTypeService.list(dto));
    }

    /**
     * 选择树
     */
    @Log(logOpt = LogEnum.QUERY, module = "登记信息-培训管理-课程分类", desc = "查询课程分类树")
    @PostMapping("/select")
    @ApiOperation(value = "选择树", response = Result.class)
    public Result select(@Validated @RequestBody RegisterTrainTypeDTO dto) {
        return Result.ok(registerTrainTypeService.select(dto));
    }

}
