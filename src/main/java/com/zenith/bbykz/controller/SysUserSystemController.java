package com.zenith.bbykz.controller;

import com.efficient.common.permission.Permission;
import com.efficient.common.result.Result;
import com.efficient.logs.annotation.Log;
import com.efficient.logs.constant.LogEnum;
import com.zenith.bbykz.api.SysUserSystemService;
import com.zenith.bbykz.model.dto.UserSystemDTO;
import com.zenith.bbykz.model.vo.SysUserSystemVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.constraints.NotBlank;
import java.util.List;

/**
 * <p>
 * 用户系统表 controller 层
 * </p>
 *
 * <AUTHOR>
 * @date 2024-02-29 10:36:56
 */
@RestController
@RequestMapping("/sysUserSystem")
@Validated
@Api(tags = "用户系统表")
@Permission
public class SysUserSystemController {

    @Autowired
    private SysUserSystemService sysUserSystemService;

    /**
     * 新增
     */
    @Log(logOpt = LogEnum.SAVE)
    @PostMapping("/save")
    @ApiOperation(value = "保存", response = Result.class)
    public Result save(@Validated @RequestBody UserSystemDTO dto) {
        return sysUserSystemService.save(dto);
    }

    /**
     * 查找系统列表
     */
    @Log(logOpt = LogEnum.SAVE)
    @PostMapping("/findSystemList")
    @ApiOperation(value = "查找系统列表", response = Result.class)
    public Result findSystemList() {
        return sysUserSystemService.findSystemList();
    }

    /**
     * 详情
     */
    @Log(logOpt = LogEnum.QUERY)
    @GetMapping("/find")
    @ApiOperation(value = "详情", response = Result.class)
    @ApiImplicitParams({
            @ApiImplicitParam(name = "userId", value = "用户主键数据唯一标识", required = true)
    })
    public Result findByUserId(@NotBlank(message = "userId 不能为空") @RequestParam(name = "userId") String userId) {
        List<SysUserSystemVO> voList = sysUserSystemService.findByUserId(userId);
        return Result.ok(voList);
    }
}
