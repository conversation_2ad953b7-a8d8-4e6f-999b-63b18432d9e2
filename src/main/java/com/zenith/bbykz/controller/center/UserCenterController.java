package com.zenith.bbykz.controller.center;

import com.efficient.common.permission.Permission;
import com.efficient.common.result.Result;
import com.efficient.logs.annotation.Log;
import com.efficient.logs.constant.LogEnum;
import com.zenith.bbykz.api.center.UserCenterService;
import com.zenith.bbykz.model.dto.OrgInfoDTO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

/**
 * <AUTHOR>
 * @since 2024/8/26 10:39
 */
@RestController
@RequestMapping("/userCenter")
@Validated
// @Api(tags = "org_info")
// @Permission
@Slf4j
public class UserCenterController {

    @Autowired
    private UserCenterService userCenterService;

    /**
     * 拉取SMZ机构
     */
    @Log(logOpt = LogEnum.PULL, module = "SMZ机构")
    @GetMapping("/syncSmz")
    public Result syncSmz() {
        log.info("执行同步任务 syncSmz ... 开始");
        try {
            userCenterService.syncSmzJg();
        } catch (Exception e) {
            log.error("同步机构树异常",e);
        }
        log.info("同步smz机构数据完成");
        userCenterService.orgBelong();
        log.info("设置机构层级关系完成");
        userCenterService.syncSmzUser();
        log.info("同步smz用户数据完成");
        log.info("执行同步任务 syncSmz ... 完成");
        return Result.ok();
    }

    /**
     * 拉取SMZ机构
     */
    @Log(logOpt = LogEnum.PULL, module = "SMZ机构")
    @GetMapping("/syncSmzJg")
    public Result syncSmzJg() {
        return userCenterService.syncSmzJg();
    }

    /**
     * 处理机构所属关系
     */
    @Log(logOpt = LogEnum.PULL, module = "SMZ机构")
    @GetMapping("/orgBelong")
    public Result orgBelong() {
        return userCenterService.orgBelong();
    }
    /**
     * 拉取SMZ机构
     */
    @Log(logOpt = LogEnum.PULL, module = "SMZ用户")
    @GetMapping("/syncSmzUser")
    public Result syncSmzUser() {
        return userCenterService.syncSmzUser();
    }

}
