package com.zenith.bbykz.controller.center;

import com.efficient.common.result.Result;
import com.efficient.logs.annotation.Log;
import com.efficient.logs.constant.LogEnum;
import com.zenith.bbykz.api.publicInstitutions.LegalPersonService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @since 2024/9/9 15:02
 */
@RestController
@RequestMapping("/flush")
@Validated
@Slf4j
public class FlushController {
    @Resource
    private LegalPersonService legalPersonService;
    /**
     * 刷新变动信息
     */
    @Log(logOpt = LogEnum.PAGE, module = "刷新变动信息")
    @GetMapping("/verifyZyInfo")
    public Result verifyZyInfo() {
        return legalPersonService.verifyZyInfo();
    }
}
