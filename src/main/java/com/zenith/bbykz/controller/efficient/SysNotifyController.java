package com.zenith.bbykz.controller.efficient;

import com.efficient.common.permission.Permission;
import com.efficient.common.result.Result;
import com.efficient.common.validate.Common1Group;
import com.efficient.logs.annotation.Log;
import com.efficient.logs.constant.LogEnum;
import com.zenith.bbykz.api.efficient.SysNotifyService;
import com.zenith.bbykz.model.dto.efficient.SysNotifyDTO;
import com.zenith.bbykz.model.dto.efficient.SysNotifyListDTO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.constraints.NotBlank;

/**
 * <p>
 * 系统消息通知 controller 层
 * </p>
 *
 * <AUTHOR>
 * @date 2024-03-18 15:07:00
 */
@RestController
@RequestMapping("/efficient/notify")
@Validated
@Api(tags = "系统消息通知")
@Permission
public class SysNotifyController {

    @Autowired
    private SysNotifyService sysNotifyService;

    /**
     * 查找消息
     */
    @Log(logOpt = LogEnum.SAVE, module = "系统消息通知")
    @GetMapping("/find")
    @ApiOperation(value = "发送待办消息", response = Result.class)
    public Result find(@NotBlank(message = "id 不能为空") @RequestParam("id") String id) {
        return sysNotifyService.find(id);
    }

    /**
     * 发送待办消息
     */
    @Log(logOpt = LogEnum.SAVE, module = "系统消息通知")
    @PostMapping("/sendTodo")
    @ApiOperation(value = "发送待办消息", response = Result.class)
    public Result sendTodo(@Validated @RequestBody SysNotifyDTO dto) {
        return sysNotifyService.sendTodo(dto);
    }

    /**
     * 完成待办消息
     */
    @Log(logOpt = LogEnum.SAVE, module = "系统消息通知")
    @GetMapping("/finishTodo")
    @ApiOperation(value = "完成待办消息", response = Result.class)
    public Result finishTodo(@RequestParam("userId") String userId,
                             @RequestParam("notifyId") String notifyId) {
        return sysNotifyService.finishTodo(userId, notifyId);
    }

    /**
     * 列表
     */
    @Log(logOpt = LogEnum.PAGE, module = "系统消息通知")
    @PostMapping("/list")
    @ApiOperation(value = "列表", response = Result.class)
    public Result list(@Validated(value = Common1Group.class) @RequestBody SysNotifyListDTO dto) {
        return Result.ok(sysNotifyService.list(dto));
    }
}
