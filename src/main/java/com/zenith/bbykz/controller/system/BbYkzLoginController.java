package com.zenith.bbykz.controller.system;

import com.efficient.common.auth.RequestHolder;
import com.efficient.common.permission.Permission;
import com.efficient.common.result.Result;
import com.efficient.logs.annotation.Log;
import com.efficient.logs.constant.LogEnum;
import com.zenith.bbykz.api.SysUserService;
import com.zenith.bbykz.model.base.UserInfo;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.constraints.NotBlank;

/**
 * <AUTHOR>
 * @date 2023/9/15 11:10
 */
@RestController
@RequestMapping("/system")
public class BbYkzLoginController {

    @Autowired
    private SysUserService sysUserService;

//    @PostMapping("/login")
//    @ApiOperation(value = "登录", response = Result.class)
//    @Log(logOpt = LogEnum.LOGIN)
//    public Result login(@Validated @RequestBody LoginDTO dto) {
//        return sysUserService.login(dto);
//    }

    @GetMapping("/getPermission")
    @ApiOperation(value = "获取系统权限", response = Result.class)
    @Log(logOpt = LogEnum.QUERY, desc = "获取系统权限")
    @Permission
    public Result getPermission(@NotBlank(message = "systemId 不能为空") @RequestParam(name = "systemId") String systemId) {
        UserInfo currUser = (UserInfo) RequestHolder.getCurrUser();
        return sysUserService.getPermission(systemId, currUser.getUserPostId());
    }

    @GetMapping("/changeUnit")
    @ApiOperation(value = "切换单位", response = Result.class)
    @Log(logOpt = LogEnum.QUERY, desc = "切换单位")
    @Permission
    public Result changeUnit(@NotBlank(message = "userPostId 不能为空") @RequestParam(name = "userPostId") String userPostId) {
        return sysUserService.changeUnit(userPostId);
    }
}
