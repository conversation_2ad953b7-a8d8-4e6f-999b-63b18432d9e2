package com.zenith.bbykz.controller.bz.qx;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.efficient.common.result.Result;
import com.efficient.common.permission.Permission;
import com.efficient.logs.annotation.Log;
import com.efficient.logs.constant.LogEnum;
import com.zenith.bbykz.api.bz.qx.BzQxBzsqDetailService;
import com.zenith.bbykz.model.dto.bz.qx.BzQxBzsqDetailDTO;
import com.zenith.bbykz.model.dto.bz.qx.BzQxBzsqDetailListDTO;
import com.zenith.bbykz.model.entity.bz.qx.BzQxBzsqDetail;
import com.zenith.bbykz.model.vo.bz.qx.BzQxBzsqDetailVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.constraints.NotBlank;

/**
* <p>
* 核编管理-用编管理-区县用编申请计划明细 controller 层
* </p>
*
* <AUTHOR>
* @date 2024-05-06 17:05:40
*/
@RestController
@RequestMapping("/bz/qx/bzQxBzsqDetail")
@Validated
@Api(tags = "核编管理-用编管理-区县用编申请计划明细")
@Permission
public class BzQxBzsqDetailController {

    @Autowired
    private BzQxBzsqDetailService bzQxBzsqDetailService;



    /**
    * 新增
    */
    @Log(logOpt = LogEnum.SAVE, module = "核编管理-用编管理-区县用编申请计划明细")
    @PostMapping("/save")
    @ApiOperation(value = "保存", response = BzQxBzsqDetail.class)
    public Result<BzQxBzsqDetail> save(@Validated @RequestBody BzQxBzsqDetailDTO dto) {
        return bzQxBzsqDetailService.save(dto);
    }

    /**
    * 详情
    */
    @Log(logOpt = LogEnum.QUERY, module = "核编管理-用编管理-区县用编申请计划明细")
    @GetMapping("/find")
    @ApiOperation(value = "详情", response = BzQxBzsqDetailVO.class)
    @ApiImplicitParams({
            @ApiImplicitParam(name = "id", value = "数据唯一标识", required = true)
    })
    public Result<BzQxBzsqDetailVO> find(@NotBlank(message = "id 不能为空") @RequestParam(name="id") String id) {
        return bzQxBzsqDetailService.findById(id);
    }

    /**
    * 修改
    */
    @Log(logOpt = LogEnum.UPDATE, module = "核编管理-用编管理-区县用编申请计划明细")
    @PostMapping("/update")
    @ApiOperation(value = "修改", response = Boolean.class)
    public Result<Boolean> update(@Validated @RequestBody BzQxBzsqDetailDTO dto) {
        return bzQxBzsqDetailService.update(dto);
    }

    /**
    * 删除
    */
    @Log(logOpt = LogEnum.DELETE, module = "核编管理-用编管理-区县用编申请计划明细")
    @GetMapping("/delete")
    @ApiOperation(value = "删除", response = Boolean.class)
    @ApiImplicitParams({
            @ApiImplicitParam(name = "id", value = "数据唯一标识", required = true)
    })
    public Result<Boolean> delete(@NotBlank(message = "id 不能为空") @RequestParam(name="id") String id) {
        return bzQxBzsqDetailService.delete(id);
    }

    /**
    * 列表
    */
    @Log(logOpt = LogEnum.PAGE, module = "核编管理-用编管理-区县用编申请计划明细")
    @PostMapping("/list")
    @ApiOperation(value = "列表", response = BzQxBzsqDetailVO.class)
    public Result<Page<BzQxBzsqDetailVO>> list(@Validated @RequestBody BzQxBzsqDetailListDTO dto) {
        Page<BzQxBzsqDetailVO> page =  bzQxBzsqDetailService.list(dto);
        return Result.ok(page);
    }

}
