package com.zenith.bbykz.controller.bz.common;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.efficient.common.permission.Permission;
import com.efficient.common.result.Result;
import com.efficient.logs.annotation.Log;
import com.efficient.logs.constant.LogEnum;
import com.zenith.bbykz.api.bz.common.AduitOpinionService;
import com.zenith.bbykz.model.dto.bz.common.AduitOpinionDTO;
import com.zenith.bbykz.model.dto.bz.common.AduitOpinionListDTO;
import com.zenith.bbykz.model.entity.bz.common.AduitOpinion;
import com.zenith.bbykz.model.vo.bz.common.AduitOpinionVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.constraints.NotBlank;

/**
 * <p>
 * 核编管理-常用审批意见 controller 层
 * </p>
 *
 * <AUTHOR>
 * @date 2024-05-17 11:10:37
 */
@RestController
@RequestMapping("/bz/common/aduitOpinion")
@Validated
@Api(tags = "核编管理-常用审批意见")
@Permission
public class AduitOpinionController {

    @Autowired
    private AduitOpinionService aduitOpinionService;

    /**
     * 新增
     */
    @Log(logOpt = LogEnum.SAVE, module = "核编管理-常用审批意见")
    @PostMapping("/save")
    @ApiOperation(value = "保存", response = AduitOpinion.class)
    public Result<AduitOpinion> save(@Validated @RequestBody AduitOpinionDTO dto) {
        return aduitOpinionService.save(dto);
    }

    /**
     * 详情
     */
    @Log(logOpt = LogEnum.QUERY, module = "核编管理-常用审批意见")
    @GetMapping("/find")
    @ApiOperation(value = "详情", response = AduitOpinionVO.class)
    @ApiImplicitParams({
            @ApiImplicitParam(name = "id", value = "数据唯一标识", required = true)
    })
    public Result<AduitOpinionVO> find(@NotBlank(message = "id 不能为空") @RequestParam(name = "id") String id) {
        return aduitOpinionService.findById(id);
    }

    /**
     * 修改
     */
    @Log(logOpt = LogEnum.UPDATE, module = "核编管理-常用审批意见")
    @PostMapping("/update")
    @ApiOperation(value = "修改", response = Boolean.class)
    public Result<Boolean> update(@Validated @RequestBody AduitOpinionDTO dto) {
        return aduitOpinionService.update(dto);
    }

    /**
     * 删除
     */
    @Log(logOpt = LogEnum.DELETE, module = "核编管理-常用审批意见")
    @GetMapping("/delete")
    @ApiOperation(value = "删除", response = Boolean.class)
    @ApiImplicitParams({
            @ApiImplicitParam(name = "id", value = "数据唯一标识", required = true)
    })
    public Result<Boolean> delete(@NotBlank(message = "id 不能为空") @RequestParam(name = "id") String id) {
        return aduitOpinionService.delete(id);
    }

    /**
     * 列表
     */
    @Log(logOpt = LogEnum.PAGE, module = "核编管理-常用审批意见")
    @PostMapping("/list")
    @ApiOperation(value = "列表", response = AduitOpinionVO.class)
    public Result<Page<AduitOpinionVO>> list(@Validated @RequestBody AduitOpinionListDTO dto) {
        Page<AduitOpinionVO> page = aduitOpinionService.list(dto);
        return Result.ok(page);
    }

}
