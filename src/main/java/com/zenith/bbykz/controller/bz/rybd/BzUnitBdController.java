package com.zenith.bbykz.controller.bz.rybd;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.efficient.common.permission.Permission;
import com.efficient.common.result.Result;
import com.efficient.common.validate.AddGroup;
import com.efficient.common.validate.Common1Group;
import com.efficient.common.validate.Common2Group;
import com.efficient.logs.annotation.Log;
import com.efficient.logs.constant.LogEnum;
import com.zenith.bbykz.api.OrgInfoService;
import com.zenith.bbykz.api.bz.rybd.BzUnitBdService;
import com.zenith.bbykz.model.dto.bz.rybd.BzUnitBdDTO;
import com.zenith.bbykz.model.dto.bz.rybd.BzUnitBdExportDTO;
import com.zenith.bbykz.model.dto.bz.rybd.BzUnitBdListDTO;
import com.zenith.bbykz.model.entity.bz.rybd.BzUnitBd;
import com.zenith.bbykz.model.vo.bz.rybd.BzUnitBdVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.constraints.NotBlank;

/**
 * <p>
 * 核编管理-动态信息维护 controller 层
 * </p>
 *
 * <AUTHOR>
 * @date 2024-04-22 11:21:43
 */
@RestController
@RequestMapping("/bz/rybd/bzUnitBd")
@Validated
@Api(tags = "核编管理-动态信息维护")
@Permission
public class BzUnitBdController {

    @Autowired
    private BzUnitBdService bzUnitBdService;
    @Autowired
    private OrgInfoService orgInfoService;
    /**
     * 新增
     */
    @Log(logOpt = LogEnum.SAVE, module = "核编管理-动态信息维护")
    @PostMapping("/save")
    @ApiOperation(value = "保存", response = BzUnitBd.class)
    public Result<BzUnitBd> save(@Validated(AddGroup.class) @RequestBody BzUnitBdDTO dto) {
        return bzUnitBdService.save(dto);
    }

    /**
     * 详情
     */
    @Log(logOpt = LogEnum.QUERY, module = "核编管理-动态信息维护")
    @GetMapping("/find")
    @ApiOperation(value = "详情", response = BzUnitBdVO.class)
    // @ApiImplicitParams({
    //         @ApiImplicitParam(name = "id", value = "数据唯一标识", required = true),
    //         @ApiImplicitParam(name = "pageType", value = "页面类型，1-上编，2-人员调整，3-下编", required = true)
    // })
    public Result<BzUnitBdVO> find(@NotBlank(message = "id 不能为空") @RequestParam(name = "id") String id,
                                   @RequestParam(name = "pageType", required = false) String pageType) {
        return bzUnitBdService.findById(id, pageType);
    }

    /**
     * 修改
     */
    @Log(logOpt = LogEnum.UPDATE, module = "核编管理-动态信息维护")
    @PostMapping("/update")
    @ApiOperation(value = "修改", response = Boolean.class)
    public Result<Boolean> update(@Validated(AddGroup.class) @RequestBody BzUnitBdDTO dto) {
        return bzUnitBdService.update(dto);
    }

    /**
     * 删除
     */
    @Log(logOpt = LogEnum.DELETE, module = "核编管理-动态信息维护")
    @GetMapping("/delete")
    @ApiOperation(value = "删除", response = Boolean.class)
    @ApiImplicitParams({
            @ApiImplicitParam(name = "id", value = "数据唯一标识", required = true)
    })
    public Result<Boolean> delete(@NotBlank(message = "id 不能为空") @RequestParam(name = "id") String id) {
        return bzUnitBdService.delete(id);
    }

    /**
     * 列表
     */
    @Log(logOpt = LogEnum.PAGE, module = "核编管理-动态信息维护")
    @PostMapping("/list")
    @ApiOperation(value = "列表", response = BzUnitBdVO.class)
    public Result<Page<BzUnitBdVO>> list(@Validated(Common1Group.class) @RequestBody BzUnitBdListDTO dto) {
        dto.setUnitLevelCode(orgInfoService.findLevelCodeById(dto.getUnitLevelCode()));
        Page<BzUnitBdVO> page = bzUnitBdService.list(dto);
        return Result.ok(page);
    }

    /**
     * 变动列表
     */
    @Log(logOpt = LogEnum.PAGE, module = "核编管理-动态信息维护")
    @PostMapping("/bdList")
    @ApiOperation(value = "变动列表", response = BzUnitBdVO.class)
    public Result<Page<BzUnitBdVO>> bdList(@Validated({Common1Group.class, Common2Group.class}) @RequestBody BzUnitBdListDTO dto) {
        dto.setUnitLevelCode(orgInfoService.findLevelCodeById(dto.getUnitLevelCode()));
        Page<BzUnitBdVO> page = bzUnitBdService.bdList(dto);
        return Result.ok(page);
    }

    /**
     * 导出编制管理
     */
    @Log(logOpt = LogEnum.PAGE, module = "核编管理-动态信息维护")
    @PostMapping("/exportRybd")
    @ApiOperation(value = "导出编制管理人员数据", response = BzUnitBdVO.class)
    public Result<String> exportRybd(@Validated(Common1Group.class) @RequestBody BzUnitBdExportDTO dto) throws Exception {
        return bzUnitBdService.exportRybd(dto);
    }

}
