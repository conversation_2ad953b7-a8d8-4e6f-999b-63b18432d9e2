// package com.zenith.bbykz.controller.bz.bzsq;
//
// import com.efficient.common.result.Result;
// import com.efficient.common.permission.Permission;
// import com.efficient.logs.annotation.Log;
// import com.efficient.logs.constant.LogEnum;
// import com.zenith.bbykz.api.bz.bzsq.BzBzsqFlowService;
// import com.zenith.bbykz.model.dto.bz.bzsq.BzBzsqFlowDTO;
// import com.zenith.bbykz.model.dto.bz.bzsq.BzBzsqFlowListDTO;
// import com.zenith.bbykz.model.entity.bz.bzsq.BzBzsqFlow;
// import com.zenith.bbykz.model.vo.bz.bzsq.BzBzsqFlowVO;
// import io.swagger.annotations.Api;
// import io.swagger.annotations.ApiImplicitParam;
// import io.swagger.annotations.ApiImplicitParams;
// import io.swagger.annotations.ApiOperation;
// import org.springframework.beans.factory.annotation.Autowired;
// import org.springframework.validation.annotation.Validated;
// import org.springframework.web.bind.annotation.*;
//
// import javax.validation.constraints.NotBlank;
//
// /**
// * <p>
// * 核编管理-用编审核 controller 层
// * </p>
// *
// * <AUTHOR>
// * @date 2024-04-02 11:51:22
// */
// @RestController
// @RequestMapping("/bz/bzsq/bzBzsqFlow")
// @Validated
// @Api(tags = "核编管理-用编审核")
// @Permission
// public class BzBzsqFlowController {
//
//     @Autowired
//     private BzBzsqFlowService bzBzsqFlowService;
//
//
// }
