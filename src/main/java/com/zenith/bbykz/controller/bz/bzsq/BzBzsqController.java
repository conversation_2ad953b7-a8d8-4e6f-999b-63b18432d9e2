package com.zenith.bbykz.controller.bz.bzsq;

import com.efficient.common.permission.Permission;
import com.efficient.common.result.Result;
import com.efficient.common.validate.Common3Group;
import com.efficient.logs.annotation.Log;
import com.efficient.logs.constant.LogEnum;
import com.zenith.bbykz.api.OrgInfoService;
import com.zenith.bbykz.api.bz.bzsq.BzBzsqFlowService;
import com.zenith.bbykz.api.bz.bzsq.BzBzsqService;
import com.zenith.bbykz.model.dto.bz.bzsq.BzBzsqDTO;
import com.zenith.bbykz.model.dto.bz.bzsq.BzBzsqListDTO;
import com.zenith.bbykz.model.entity.bz.bzsq.BzBzsq;
import com.zenith.bbykz.model.vo.bz.bzsq.BzBzsqFlowVO;
import com.zenith.bbykz.model.vo.bz.bzsq.BzBzsqVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.constraints.NotBlank;
import java.util.List;

/**
 * <p>
 * 核编管理-用编申请 controller 层
 * </p>
 *
 * <AUTHOR>
 * @date 2024-03-29 16:05:19
 */
@RestController
@RequestMapping("/bz/bzsq/bzBzsq")
@Validated
@Api(tags = "核编管理-用编申请")
@Permission
public class BzBzsqController {

    @Autowired
    private BzBzsqService bzBzsqService;
    @Autowired
    private BzBzsqFlowService bzBzsqFlowService;
    @Autowired
    private OrgInfoService orgInfoService;

    /**
     * 新增
     */
    @Log(logOpt = LogEnum.SAVE, module = "核编管理-用编申请")
    @PostMapping("/save")
    @ApiOperation(value = "保存")
    public Result<BzBzsq> save(@Validated @RequestBody BzBzsqDTO dto) {
        return bzBzsqService.save(dto);
    }

    /**
     * 详情
     */
    @Log(logOpt = LogEnum.QUERY, module = "核编管理-用编申请")
    @GetMapping("/find")
    @ApiOperation(value = "详情")
    // @ApiImplicitParams({
    //         @ApiImplicitParam(name = "id", value = "数据唯一标识", required = true)
    // })
    public Result<BzBzsqVO> find(@NotBlank(message = "id 不能为空") @RequestParam(name = "id") String id,
                                 @RequestParam(name = "isBack", required = false) Integer isBack) {
        return bzBzsqService.findById(id, isBack);
    }

    /**
     * 修改状态
     */
    @Log(logOpt = LogEnum.UPDATE, module = "核编管理-用编申请")
    @GetMapping("/changeStatus")
    @ApiOperation(value = "修改状态")
    // @ApiImplicitParams({
    //         @ApiImplicitParam(name = "id", value = "数据唯一标识", required = true)
    // })
    public Result changeStatus(@NotBlank(message = "id 不能为空") @RequestParam(name = "id") String id,
                               @NotBlank(message = "status 不能为空") @RequestParam(name = "status") String status) {
        return bzBzsqService.changeStatus(id, status);
    }

    /**
     * 审批
     */
    @Log(logOpt = LogEnum.UPDATE, module = "核编管理-用编申请")
    @PostMapping("/approve")
    @ApiOperation(value = "审批")
    // @ApiImplicitParams({
    //         @ApiImplicitParam(name = "id", value = "数据唯一标识", required = true)
    // })
    public Result approve(@Validated(value = Common3Group.class) @RequestBody BzBzsqDTO dto) {
        return bzBzsqService.approve(dto);
    }

    /**
     * 获取审批流程
     */
    @Log(logOpt = LogEnum.QUERY, module = "核编管理-用编申请")
    @GetMapping("/getFlow")
    @ApiOperation(value = "获取审批流程")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "id", value = "数据唯一标识", required = true)
    })
    public Result<List<BzBzsqFlowVO>> getFlow(@NotBlank(message = "id 不能为空") @RequestParam(name = "id") String id) {
        return Result.ok(bzBzsqFlowService.getFlow(id));
    }

    /**
     * 修改
     */
    @Log(logOpt = LogEnum.UPDATE, module = "核编管理-用编申请")
    @PostMapping("/update")
    @ApiOperation(value = "修改")
    public Result<Boolean> update(@Validated @RequestBody BzBzsqDTO dto) {
        return bzBzsqService.update(dto);
    }

    /**
     * 删除
     */
    @Log(logOpt = LogEnum.DELETE, module = "核编管理-用编申请")
    @GetMapping("/delete")
    @ApiOperation(value = "删除")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "id", value = "数据唯一标识", required = true)
    })
    public Result<Boolean> delete(@NotBlank(message = "id 不能为空") @RequestParam(name = "id") String id) {
        return bzBzsqService.delete(id);
    }

    /**
     * 列表
     */
    @Log(logOpt = LogEnum.PAGE, module = "核编管理-用编申请")
    @PostMapping("/list")
    @ApiOperation(value = "列表", response = Result.class)
    public Result list(@Validated @RequestBody BzBzsqListDTO dto) {
        dto.setUnitLevelCode(orgInfoService.findLevelCodeById(dto.getUnitLevelCode()));
        return Result.ok(bzBzsqService.list(dto));
    }

    @Log(logOpt = LogEnum.DOWNLOAD, module = "核编管理-用编申请")
    @GetMapping("/downReply")
    @ApiOperation(value = "下载批复函", response = Result.class)
    public Result downReply(@NotBlank(message = "id 不能为空") @RequestParam(name = "id") String id) {
        return Result.ok(bzBzsqService.downReply(id));
    }
    /**
     * 下载审核模版
     *
     * @param id
     * @return
     */
    @Log(logOpt = LogEnum.DOWNLOAD, module = "核编管理-用编申请")
    @GetMapping("/downAudit")
    @ApiOperation(value = "下载批复函", response = Result.class)
    public Result downAudit(@NotBlank(message = "id 不能为空") @RequestParam(name = "id") String id) throws Exception {
        return Result.ok(bzBzsqService.downAudit(id));
    }
}
