package com.zenith.bbykz.controller.bz.rybd;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.efficient.common.result.Result;
import com.efficient.common.permission.Permission;
import com.efficient.logs.annotation.Log;
import com.efficient.logs.constant.LogEnum;
import com.zenith.bbykz.api.bz.rybd.BzRyBdService;
import com.zenith.bbykz.model.dto.bz.rybd.BzRyBdDTO;
import com.zenith.bbykz.model.dto.bz.rybd.BzRyBdListDTO;
import com.zenith.bbykz.model.entity.bz.rybd.BzRyBd;
import com.zenith.bbykz.model.vo.bz.rybd.BzRyBdVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.constraints.NotBlank;

/**
* <p>
* 核编管理-动态信息维护-人员详情 controller 层
* </p>
*
* <AUTHOR>
* @date 2024-04-22 11:21:43
*/
@RestController
@RequestMapping("/bz/rybd/bzRyBd")
@Validated
@Api(tags = "核编管理-动态信息维护-人员详情")
@Permission
public class BzRyBdController {

    @Autowired
    private BzRyBdService bzRyBdService;



    /**
    * 新增
    */
    @Log(logOpt = LogEnum.SAVE, module = "核编管理-动态信息维护-人员详情")
    @PostMapping("/save")
    @ApiOperation(value = "保存", response = BzRyBd.class)
    public Result<BzRyBd> save(@Validated @RequestBody BzRyBdDTO dto) {
        return bzRyBdService.save(dto);
    }

    /**
    * 详情
    */
    @Log(logOpt = LogEnum.QUERY, module = "核编管理-动态信息维护-人员详情")
    @GetMapping("/find")
    @ApiOperation(value = "详情", response = BzRyBdVO.class)
    @ApiImplicitParams({
            @ApiImplicitParam(name = "id", value = "数据唯一标识", required = true)
    })
    public Result<BzRyBdVO> find(@NotBlank(message = "id 不能为空") @RequestParam(name="id") String id) {
        return bzRyBdService.findById(id);
    }

    /**
    * 修改
    */
    @Log(logOpt = LogEnum.UPDATE, module = "核编管理-动态信息维护-人员详情")
    @PostMapping("/update")
    @ApiOperation(value = "修改", response = Boolean.class)
    public Result<Boolean> update(@Validated @RequestBody BzRyBdDTO dto) {
        return bzRyBdService.update(dto);
    }

    /**
    * 删除
    */
    @Log(logOpt = LogEnum.DELETE, module = "核编管理-动态信息维护-人员详情")
    @GetMapping("/delete")
    @ApiOperation(value = "删除", response = Boolean.class)
    @ApiImplicitParams({
            @ApiImplicitParam(name = "id", value = "数据唯一标识", required = true)
    })
    public Result<Boolean> delete(@NotBlank(message = "id 不能为空") @RequestParam(name="id") String id) {
        return bzRyBdService.delete(id);
    }

    /**
    * 列表
    */
    @Log(logOpt = LogEnum.PAGE, module = "核编管理-动态信息维护-人员详情")
    @PostMapping("/list")
    @ApiOperation(value = "列表", response = BzRyBdVO.class)
    public Result<Page<BzRyBdVO>> list(@Validated @RequestBody BzRyBdListDTO dto) {
        Page<BzRyBdVO> page =  bzRyBdService.list(dto);
        return Result.ok(page);
    }

}
