package com.zenith.bbykz.controller.bz.bzsq;

import com.efficient.common.permission.Permission;
import com.efficient.common.result.Result;
import com.efficient.common.validate.Common3Group;
import com.efficient.logs.annotation.Log;
import com.efficient.logs.constant.LogEnum;
import com.zenith.bbykz.api.OrgInfoService;
import com.zenith.bbykz.api.bz.bzsq.BzBzsqMergeService;
import com.zenith.bbykz.model.dto.bz.bzsq.BzBzsqMergeDTO;
import com.zenith.bbykz.model.dto.bz.bzsq.BzBzsqMergeListDTO;
import com.zenith.bbykz.model.entity.bz.bzsq.BzBzsqMerge;
import com.zenith.bbykz.model.vo.bz.bzsq.BzBzsqMergeVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.constraints.NotBlank;

/**
 * <p>
 * 核编管理-用编审核-合并审核 controller 层
 * </p>
 *
 * <AUTHOR>
 * @date 2024-04-03 15:26:58
 */
@RestController
@RequestMapping("/bz/bzsq/bzBzsqMerge")
@Validated
@Api(tags = "核编管理-用编审核-合并审核")
@Permission
public class BzBzsqMergeController {

    @Autowired
    private BzBzsqMergeService bzBzsqMergeService;
    @Autowired
    private OrgInfoService orgInfoService;
    /**
     * 新增
     */
    @Log(logOpt = LogEnum.SAVE, module = "核编管理-用编审核-合并审核")
    @PostMapping("/save")
    @ApiOperation(value = "保存")
    public Result<BzBzsqMerge> save(@Validated @RequestBody BzBzsqMergeDTO dto) {
        return bzBzsqMergeService.save(dto);
    }

    /**
     * 详情
     */
    @Log(logOpt = LogEnum.QUERY, module = "核编管理-用编审核-合并审核")
    @GetMapping("/find")
    @ApiOperation(value = "详情")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "id", value = "数据唯一标识", required = true)
    })
    public Result<BzBzsqMergeVO> find(@NotBlank(message = "id 不能为空") @RequestParam(name = "id") String id) {
        return bzBzsqMergeService.findById(id);
    }

    @Log(logOpt = LogEnum.UPDATE, module = "核编管理-用编申请")
    @PostMapping("/approve")
    @ApiOperation(value = "审批")
    // @ApiImplicitParams({
    //         @ApiImplicitParam(name = "id", value = "数据唯一标识", required = true)
    // })
    public Result approve(@Validated(value = Common3Group.class) @RequestBody BzBzsqMergeDTO dto) {
        return bzBzsqMergeService.approve(dto);
    }

    /**
     * 删除
     */
    @Log(logOpt = LogEnum.DELETE, module = "核编管理-用编审核-合并审核")
    @GetMapping("/delete")
    @ApiOperation(value = "删除")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "id", value = "数据唯一标识", required = true)
    })
    public Result<Boolean> delete(@NotBlank(message = "id 不能为空") @RequestParam(name = "id") String id) {
        return bzBzsqMergeService.delete(id);
    }

    /**
     * 列表
     */
    @Log(logOpt = LogEnum.PAGE, module = "核编管理-用编审核-合并审核")
    @PostMapping("/list")
    @ApiOperation(value = "列表", response = Result.class)
    public Result list(@Validated @RequestBody BzBzsqMergeListDTO dto) {
        dto.setUnitLevelCode(orgInfoService.findLevelCodeById(dto.getUnitLevelCode()));

        return Result.ok(bzBzsqMergeService.list(dto));
    }

    /**
     * 下载审核模版
     *
     * @param id
     * @return
     */
    @Log(logOpt = LogEnum.DOWNLOAD, module = "核编管理-用编申请")
    @GetMapping("/downAudit")
    @ApiOperation(value = "下载批复函", response = Result.class)
    public Result downAudit(@NotBlank(message = "id 不能为空") @RequestParam(name = "id") String id) throws Exception {
        return Result.ok(bzBzsqMergeService.downAudit(id));
    }

}
