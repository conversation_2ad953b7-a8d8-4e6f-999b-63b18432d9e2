package com.zenith.bbykz.controller.bz.qx;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.efficient.common.permission.Permission;
import com.efficient.common.result.Result;
import com.efficient.logs.annotation.Log;
import com.efficient.logs.constant.LogEnum;
import com.zenith.bbykz.api.bz.qx.BzQxBzsqService;
import com.zenith.bbykz.model.dto.bz.qx.BzQxBzsqAuditDTO;
import com.zenith.bbykz.model.dto.bz.qx.BzQxBzsqDTO;
import com.zenith.bbykz.model.dto.bz.qx.BzQxBzsqListDTO;
import com.zenith.bbykz.model.entity.bz.qx.BzQxBzsq;
import com.zenith.bbykz.model.vo.bz.qx.BzQxBzsqVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.constraints.NotBlank;

/**
 * <p>
 * 核编管理-用编管理-区县用编申请计划 controller 层
 * </p>
 *
 * <AUTHOR>
 * @date 2024-05-06 17:05:40
 */
@RestController
@RequestMapping("/bz/qx/bzQxBzsq")
@Validated
@Api(tags = "核编管理-用编管理-区县用编申请计划")
@Permission
public class BzQxBzsqController {

    @Autowired
    private BzQxBzsqService bzQxBzsqService;

    /**
     * 新增
     */
    @Log(logOpt = LogEnum.SAVE, module = "核编管理-用编管理-区县用编申请计划")
    @PostMapping("/save")
    @ApiOperation(value = "保存", response = BzQxBzsq.class)
    public Result<BzQxBzsq> save(@Validated @RequestBody BzQxBzsqDTO dto) {
        return bzQxBzsqService.save(dto);
    }

    /**
     * 详情
     */
    @Log(logOpt = LogEnum.QUERY, module = "核编管理-用编管理-区县用编申请计划")
    @GetMapping("/find")
    @ApiOperation(value = "详情", response = BzQxBzsqVO.class)
    @ApiImplicitParams({
            @ApiImplicitParam(name = "id", value = "数据唯一标识", required = true)
    })
    public Result<BzQxBzsqVO> find(@NotBlank(message = "id 不能为空") @RequestParam(name = "id") String id) {
        return bzQxBzsqService.findById(id);
    }

    /**
     * 修改
     */
    @Log(logOpt = LogEnum.UPDATE, module = "核编管理-用编管理-区县用编申请计划")
    @PostMapping("/update")
    @ApiOperation(value = "修改", response = Boolean.class)
    public Result<Boolean> update(@Validated @RequestBody BzQxBzsqDTO dto) {
        return bzQxBzsqService.update(dto);
    }

    /**
     * 删除
     */
    @Log(logOpt = LogEnum.DELETE, module = "核编管理-用编管理-区县用编申请计划")
    @GetMapping("/delete")
    @ApiOperation(value = "删除", response = Boolean.class)
    @ApiImplicitParams({
            @ApiImplicitParam(name = "id", value = "数据唯一标识", required = true)
    })
    public Result<Boolean> delete(@NotBlank(message = "id 不能为空") @RequestParam(name = "id") String id) {
        return bzQxBzsqService.delete(id);
    }

    /**
     * 撤回
     */
    @Log(logOpt = LogEnum.DELETE, module = "核编管理-用编管理-区县用编申请计划")
    @GetMapping("/revoke")
    @ApiOperation(value = "撤回,只有未核准时才能测回", response = Boolean.class)
    @ApiImplicitParams({
            @ApiImplicitParam(name = "id", value = "数据唯一标识", required = true)
    })
    public Result<Boolean> revoke(@NotBlank(message = "id 不能为空") @RequestParam(name = "id") String id) {
        return bzQxBzsqService.revoke(id);
    }

    /**
     * 审核
     */
    @Log(logOpt = LogEnum.DELETE, module = "核编管理-用编管理-区县用编申请计划")
    @PostMapping("/audit")
    @ApiOperation(value = "审核", response = Boolean.class)
    public Result<Boolean> audit(@Validated @RequestBody BzQxBzsqAuditDTO dto) {
        return bzQxBzsqService.audit(dto);
    }

    /**
     * 列表
     */
    @Log(logOpt = LogEnum.PAGE, module = "核编管理-用编管理-区县用编申请计划")
    @PostMapping("/list")
    @ApiOperation(value = "列表", response = BzQxBzsqVO.class)
    public Result<Page<BzQxBzsqVO>> list(@Validated @RequestBody BzQxBzsqListDTO dto) {
        Page<BzQxBzsqVO> page = bzQxBzsqService.list(dto);
        return Result.ok(page);
    }

    @Log(logOpt = LogEnum.DOWNLOAD, module = "核编管理-用编申请")
    @GetMapping("/downReply")
    @ApiOperation(value = "下载批复函", response = Result.class)
    public Result downReply(@NotBlank(message = "id 不能为空") @RequestParam(name = "id") String id) {
        return Result.ok(bzQxBzsqService.downReply(id));
    }


}
