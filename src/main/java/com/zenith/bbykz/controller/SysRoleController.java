package com.zenith.bbykz.controller;

import com.efficient.common.permission.Permission;
import com.efficient.common.result.Result;
import com.efficient.logs.annotation.Log;
import com.efficient.logs.constant.LogEnum;
import com.zenith.bbykz.api.SysRoleService;
import com.zenith.bbykz.model.dto.SysRoleDTO;
import com.zenith.bbykz.model.dto.SysRoleListDTO;
import com.zenith.bbykz.model.vo.SysRoleVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.constraints.NotBlank;

/**
 * <p>
 * sys_role controller 层
 * </p>
 *
 * <AUTHOR>
 * @date 2023-09-14 17:14:26
 */
@RestController
@RequestMapping("/sysRole")
@Validated
@Api(tags = "角色表")
@Permission
public class SysRoleController {

    @Autowired
    private SysRoleService sysRoleService;

    /**
     * 新增
     */
    @Log(logOpt = LogEnum.SAVE, module = "角色管理")
    @PostMapping("/save")
    @ApiOperation(value = "保存", response = Result.class)
    public Result save(@Validated @RequestBody SysRoleDTO dto) {
        return sysRoleService.save(dto);
    }

    /**
     * 详情
     */
    @Log(logOpt = LogEnum.QUERY, module = "角色管理")
    @GetMapping("/find")
    @ApiOperation(value = "详情", response = Result.class)
    @ApiImplicitParams({
            @ApiImplicitParam(name = "id", value = "数据唯一标识", required = true)
    })
    public Result find(@NotBlank(message = "id 不能为空") @RequestParam(name = "id") String id) {
        SysRoleVO entity = sysRoleService.findById(id);
        return Result.ok(entity);
    }

    /**
     * 修改
     */
    @Log(logOpt = LogEnum.UPDATE, module = "角色管理")
    @PostMapping("/update")
    @ApiOperation(value = "修改", response = Result.class)
    public Result update(@Validated @RequestBody SysRoleDTO dto) {
        return sysRoleService.update(dto);
    }

    /**
     * 删除
     */
    @Log(logOpt = LogEnum.DELETE, module = "角色管理")
    @GetMapping("/delete")
    @ApiOperation(value = "删除", response = Result.class)
    @ApiImplicitParams({
            @ApiImplicitParam(name = "id", value = "数据唯一标识", required = true)
    })
    public Result delete(@NotBlank(message = "id 不能为空") @RequestParam(name = "id") String id) {
        return sysRoleService.deleteRole(id);
    }

    /**
     * 列表
     */
    @Log(logOpt = LogEnum.PAGE, module = "角色管理")
    @PostMapping("/list")
    @ApiOperation(value = "列表", response = Result.class)
    public Result list(@Validated @RequestBody SysRoleListDTO dto) {
        return Result.ok(sysRoleService.list(dto));
    }
}
