package com.zenith.bbykz.controller;

import cn.hutool.core.util.StrUtil;
import com.efficient.common.auth.RequestHolder;
import com.efficient.common.permission.Permission;
import com.efficient.common.result.Result;
import com.efficient.logs.annotation.Log;
import com.efficient.logs.constant.LogEnum;
import com.zenith.bbykz.api.OrgInfoService;
import com.zenith.bbykz.api.SysUserService;
import com.zenith.bbykz.common.result.BbResultEnum;
import com.zenith.bbykz.model.dto.SysUserDTO;
import com.zenith.bbykz.model.dto.SysUserListDTO;
import com.zenith.bbykz.model.vo.SysUserVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.constraints.NotBlank;

/**
 * <p>
 * sys_user controller 层
 * </p>
 *
 * <AUTHOR>
 * @date 2023-09-14 17:14:26
 */
@RestController
@RequestMapping("/sysUser")
@Validated
@Api(tags = "用户表")
@Permission
public class SysUserController {

    @Autowired
    private SysUserService sysUserService;
    @Autowired
    private OrgInfoService orgInfoService;
    /**
     * 新增
     */
    @Log(logOpt = LogEnum.SAVE, module = "用户管理")
    @PostMapping("/save")
    @ApiOperation(value = "保存", response = Result.class)
    public Result save(@Validated @RequestBody SysUserDTO dto) {
        return sysUserService.save(dto);
    }

    /**
     * 详情
     */
    @Log(logOpt = LogEnum.QUERY, module = "用户管理")
    @GetMapping("/find")
    @ApiOperation(value = "详情", response = Result.class)
    // @ApiImplicitParams({
    //         @ApiImplicitParam(name = "id", value = "数据唯一标识", required = true)
    // })
    public Result find(@NotBlank(message = "id 不能为空") @RequestParam(name = "id") String id,
                       @NotBlank(message = "userPostId 不能为空") @RequestParam(name = "userPostId") String userPostId) {
        SysUserVO entity = sysUserService.findById(id, userPostId);
        return Result.ok(entity);
    }

    /**
     * 修改
     */
    @Log(logOpt = LogEnum.UPDATE, module = "用户管理")
    @PostMapping("/update")
    @ApiOperation(value = "修改", response = Result.class)
    public Result update(@Validated @RequestBody SysUserDTO dto) {
        return sysUserService.update(dto);
    }

    /**
     * 修改
     */
    @Log(logOpt = LogEnum.UPDATE, module = "用户管理")
    @PostMapping("/sysUpdate")
    @ApiOperation(value = "修改", response = Result.class)
    public Result sysUpdate(@Validated @RequestBody SysUserDTO dto) {
        return sysUserService.sysUpdate(dto);
    }

    /**
     * 删除
     */
    @Log(logOpt = LogEnum.DELETE, module = "用户管理")
    @GetMapping("/delete")
    @ApiOperation(value = "删除", response = Result.class)
    @ApiImplicitParams({
            @ApiImplicitParam(name = "id", value = "数据唯一标识", required = true)
    })
    public Result delete(@NotBlank(message = "id 不能为空") @RequestParam(name = "id") String id) {
        String userId = RequestHolder.getCurrUser().getUserId();
        if (StrUtil.equals(userId, id)) {
            return Result.build(BbResultEnum.THE_USER_ACCOUNT_CANT_DEL);
        }
        boolean flag = sysUserService.delete(id);
        return flag ? Result.ok() : Result.fail();
    }

    /**
     * 列表
     */
    @Log(logOpt = LogEnum.PAGE, module = "用户管理")
    @PostMapping("/list")
    @ApiOperation(value = "列表", response = Result.class)
    public Result list(@Validated @RequestBody SysUserListDTO dto) {
        dto.setOrgCode(orgInfoService.findLevelCodeById(dto.getOrgCode()));
        return Result.ok(sysUserService.list(dto));
    }

    /**
     * 列表
     */
    @Log(logOpt = LogEnum.PAGE, module = "用户管理")
    @PostMapping("/sysList")
    @ApiOperation(value = "列表", response = Result.class)
    public Result sysList(@Validated @RequestBody SysUserListDTO dto) {
        dto.setOrgCode(orgInfoService.findLevelCodeById(dto.getOrgCode()));
        return Result.ok(sysUserService.sysList(dto));
    }

    /**
     * 修改政务钉
     */
    @Log(logOpt = LogEnum.UPDATE, module = "用户管理")
    @GetMapping("/updateZwd")
    @ApiOperation(value = "更新政务钉", response = Result.class)
    public Result updateZwd(@NotBlank(message = "政务钉id不能为空") String zwddId,
                            @NotBlank(message = "用户id不能为空") String accountId) {
        return sysUserService.updateZwd(zwddId, accountId);
    }

    /**
     * 修改政务钉
     */
    @Log(logOpt = LogEnum.UPDATE, module = "用户管理")
    @GetMapping("/updatePwd")
    @ApiOperation(value = "修改密码", response = Result.class)
    public Result updatePwd(@NotBlank(message = "主键不能为空") String id,
                            @NotBlank(message = "密码不能为空") String password) {
        return sysUserService.updatePwd(id, password);
    }
}
