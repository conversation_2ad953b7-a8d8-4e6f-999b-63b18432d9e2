package com.zenith.bbykz.controller;

import com.efficient.common.permission.Permission;
import com.efficient.common.result.Result;
import com.efficient.logs.annotation.Log;
import com.efficient.logs.constant.LogEnum;
import com.zenith.bbykz.api.SysMenuService;
import com.zenith.bbykz.model.dto.SysMenuDTO;
import com.zenith.bbykz.model.dto.SysMenuListDTO;
import com.zenith.bbykz.model.entity.SysMenu;
import com.zenith.bbykz.model.vo.SysMenuVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.constraints.NotBlank;

/**
 * <p>
 * 菜单表 controller 层
 * </p>
 *
 * <AUTHOR>
 * @date 2023-09-14 17:14:26
 */
@RestController
@RequestMapping("/sysMenu")
@Validated
@Api(tags = "菜单表")
@Permission
public class SysMenuController {

    @Autowired
    private SysMenuService sysMenuService;

    /**
     * 新增
     */
    @Log(logOpt = LogEnum.SAVE)
    @PostMapping("/save")
    @ApiOperation(value = "保存", response = Result.class)
    public Result save(@Validated @RequestBody SysMenuDTO dto) {
        SysMenu entity = sysMenuService.save(dto);
        return Result.ok(entity);
    }

    /**
     * 详情
     */
    @Log(logOpt = LogEnum.QUERY)
    @GetMapping("/find")
    @ApiOperation(value = "详情", response = Result.class)
    @ApiImplicitParams({
            @ApiImplicitParam(name = "id", value = "数据唯一标识", required = true)
    })
    public Result find(@NotBlank(message = "id 不能为空") @RequestParam(name = "id") String id) {
        SysMenuVO entity = sysMenuService.findById(id);
        return Result.ok(entity);
    }

    /**
     * 修改
     */
    @Log(logOpt = LogEnum.UPDATE)
    @PostMapping("/update")
    @ApiOperation(value = "修改", response = Result.class)
    public Result update(@Validated @RequestBody SysMenuDTO dto) {
        boolean flag = sysMenuService.update(dto);
        return flag ? Result.ok() : Result.fail();
    }

    /**
     * 删除
     */
    @Log(logOpt = LogEnum.DELETE)
    @GetMapping("/delete")
    @ApiOperation(value = "删除", response = Result.class)
    @ApiImplicitParams({
            @ApiImplicitParam(name = "id", value = "数据唯一标识", required = true)
    })
    public Result delete(@NotBlank(message = "id 不能为空") @RequestParam(name = "id") String id) {
        boolean flag = sysMenuService.delete(id);
        return flag ? Result.ok() : Result.fail();
    }

    /**
     * 列表
     */
    // @Log(logOpt = LogEnum.PAGE)
    @PostMapping("/list")
    @ApiOperation(value = "列表", response = Result.class)
    public Result list(@Validated @RequestBody SysMenuListDTO dto) {
        return Result.ok(sysMenuService.list(dto));
    }
}
