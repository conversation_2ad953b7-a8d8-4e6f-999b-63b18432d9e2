package com.zenith.bbykz.controller.sd;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.efficient.common.permission.Permission;
import com.efficient.common.result.Result;
import com.efficient.common.validate.QueryGroup;
import com.efficient.logs.annotation.Log;
import com.efficient.logs.constant.LogEnum;
import com.zenith.bbykz.api.OrgInfoService;
import com.zenith.bbykz.api.sd.SdOrgRelationService;
import com.zenith.bbykz.model.dto.sd.SdOrgRelationListDTO;
import com.zenith.bbykz.model.vo.sd.SdAllVO;
import com.zenith.bbykz.model.vo.sd.SdOrgRelationVO;
import com.zenith.bbykz.model.vo.sd.SdSearchVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.constraints.NotBlank;

/**
 * <p>
 * 三定-三定机构关联关系 controller 层
 * </p>
 *
 * <AUTHOR>
 * @date 2024-04-11 16:17:20
 */
@RestController
@RequestMapping("/sd/sdOrgRelation")
@Validated
@Api(tags = "三定-三定机构关联关系")
@Permission
public class SdOrgRelationController {

    @Autowired
    private SdOrgRelationService sdOrgRelationService;
    @Autowired
    private OrgInfoService orgInfoService;
    /**
     * 列表
     */
    @Log(logOpt = LogEnum.PAGE, module = "三定-三定机构关联关系")
    @PostMapping("/list")
    @ApiOperation(value = "列表", response = Result.class)
    public Result<Page<SdOrgRelationVO>> list(@Validated(QueryGroup.class) @RequestBody SdOrgRelationListDTO dto) {
        dto.setUnitLevelCode(orgInfoService.findLevelCodeById(dto.getUnitLevelCode()));
        return Result.ok(sdOrgRelationService.list(dto));
    }

    /**
     * 详情
     */
    @Log(logOpt = LogEnum.QUERY, module = "三定-业务事项")
    @GetMapping("/findAll")
    @ApiOperation(value = "详情")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "id", value = "数据唯一标识", required = true)
    })
    public Result<SdAllVO> findAll(@NotBlank(message = "id 不能为空") @RequestParam(name = "id") String id) {
        return sdOrgRelationService.findAll(id);
    }

    /**
     * 处室细化职责列表
     */
    @Log(logOpt = LogEnum.PAGE, module = "三定-处室细化职责列表")
    @PostMapping("/dutyList")
    @ApiOperation(value = "处室细化职责列表", response = SdSearchVO.class)
    public Result dutyList(@Validated(QueryGroup.class) @RequestBody SdOrgRelationListDTO dto) {
        return Result.ok(sdOrgRelationService.dutyList(dto));
    }
}
