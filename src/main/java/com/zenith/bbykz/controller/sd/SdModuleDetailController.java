package com.zenith.bbykz.controller.sd;

import com.efficient.common.permission.Permission;
import com.efficient.common.result.Result;
import com.efficient.common.validate.AddGroup;
import com.efficient.common.validate.Common1Group;
import com.efficient.common.validate.QueryGroup;
import com.efficient.logs.annotation.Log;
import com.efficient.logs.constant.LogEnum;
import com.zenith.bbykz.api.OrgInfoService;
import com.zenith.bbykz.api.sd.SdModuleDetailService;
import com.zenith.bbykz.model.dto.sd.SdModuleDetailDTO;
import com.zenith.bbykz.model.dto.sd.SdModuleDetailListDTO;
import com.zenith.bbykz.model.dto.sd.SdSaveModuleDetailDTO;
import com.zenith.bbykz.model.entity.sd.SdModuleDetail;
import com.zenith.bbykz.model.vo.sd.SdModuleDetailVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.constraints.NotBlank;

/**
 * <p>
 * 三定-三定明细 controller 层
 * </p>
 *
 * <AUTHOR>
 * @date 2024-04-11 16:17:20
 */
@RestController
@RequestMapping("/sd/sdModuleDetail")
@Validated
@Api(tags = "三定-三定明细")
@Permission
public class SdModuleDetailController {

    @Autowired
    private SdModuleDetailService sdModuleDetailService;
    @Autowired
    private OrgInfoService orgInfoService;

    /**
     * 新增
     */
    @Log(logOpt = LogEnum.SAVE, module = "三定-三定明细")
    @PostMapping("/save")
    @ApiOperation(value = "保存")
    public Result<SdModuleDetail> save(@Validated(AddGroup.class) @RequestBody SdSaveModuleDetailDTO dto) {
        return sdModuleDetailService.save(dto);
    }

    /**
     * 详情
     */
    @Log(logOpt = LogEnum.QUERY, module = "三定-三定明细")
    @GetMapping("/find")
    @ApiOperation(value = "详情")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "id", value = "数据唯一标识", required = true)
    })
    public Result<SdModuleDetailVO> find(@NotBlank(message = "id 不能为空") @RequestParam(name = "id") String id) {
        return sdModuleDetailService.findById(id);
    }

    /**
     * 修改
     */
    @Log(logOpt = LogEnum.UPDATE, module = "三定-三定明细")
    @PostMapping("/update")
    @ApiOperation(value = "修改")
    public Result<Boolean> update(@Validated @RequestBody SdModuleDetailDTO dto) {
        return sdModuleDetailService.update(dto);
    }

    /**
     * 删除
     */
    @Log(logOpt = LogEnum.DELETE, module = "三定-三定明细")
    @GetMapping("/delete")
    @ApiOperation(value = "删除")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "id", value = "数据唯一标识", required = true)
    })
    public Result<Boolean> delete(@NotBlank(message = "id 不能为空") @RequestParam(name = "id") String id) {
        return sdModuleDetailService.delete(id);
    }

    /**
     * 列表
     */
    @Log(logOpt = LogEnum.PAGE, module = "三定-三定明细")
    @PostMapping("/list")
    @ApiOperation(value = "列表", response = Result.class)
    public Result list(@Validated(QueryGroup.class) @RequestBody SdModuleDetailListDTO dto) {
        return Result.ok(sdModuleDetailService.list(dto));
    }

    /**
     * 三定明细维度查询
     */
    @Log(logOpt = LogEnum.PAGE, module = "三定-三定明细")
    @PostMapping("/searchList")
    @ApiOperation(value = "三定明细维度查询", response = Result.class)
    public Result searchList(@Validated(Common1Group.class) @RequestBody SdModuleDetailListDTO dto) {
        dto.setUnitLevelCode(orgInfoService.findLevelCodeById(dto.getUnitLevelCode()));
        return Result.ok(sdModuleDetailService.searchList(dto));
    }

}
