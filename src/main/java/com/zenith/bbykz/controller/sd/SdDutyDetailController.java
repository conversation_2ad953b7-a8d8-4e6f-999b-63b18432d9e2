package com.zenith.bbykz.controller.sd;

import com.efficient.common.permission.Permission;
import com.efficient.common.result.Result;
import com.efficient.common.validate.AddGroup;
import com.efficient.common.validate.Common1Group;
import com.efficient.common.validate.QueryGroup;
import com.efficient.logs.annotation.Log;
import com.efficient.logs.constant.LogEnum;
import com.zenith.bbykz.api.sd.SdDutyDetailService;
import com.zenith.bbykz.model.dto.sd.*;
import com.zenith.bbykz.model.entity.sd.SdDutyDetail;
import com.zenith.bbykz.model.vo.sd.SdDutyDetailVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.constraints.NotBlank;
import java.util.List;

/**
 * <p>
 * 三定-细化职责 controller 层
 * </p>
 *
 * <AUTHOR>
 * @date 2024-04-11 16:17:19
 */
@RestController
@RequestMapping("/sd/sdDutyDetail")
@Validated
@Api(tags = "三定-细化职责")
@Permission
public class SdDutyDetailController {

    @Autowired
    private SdDutyDetailService sdDutyDetailService;

    /**
     * 新增
     */
    @Log(logOpt = LogEnum.SAVE, module = "三定-细化职责")
    @PostMapping("/save")
    @ApiOperation(value = "保存")
    public Result<SdDutyDetail> save(@Validated(AddGroup.class) @RequestBody SdSaveDutyDetailDTO dto) {
        return sdDutyDetailService.save(dto);
    }

    /**
     * 详情
     */
    @Log(logOpt = LogEnum.QUERY, module = "三定-细化职责")
    @GetMapping("/find")
    @ApiOperation(value = "详情")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "id", value = "数据唯一标识", required = true)
    })
    public Result<SdDutyDetailVO> find(@NotBlank(message = "id 不能为空") @RequestParam(name = "id") String id) {
        return sdDutyDetailService.findById(id);
    }

    /**
     * 修改
     */
    @Log(logOpt = LogEnum.UPDATE, module = "三定-细化职责")
    @PostMapping("/update")
    @ApiOperation(value = "修改")
    public Result<Boolean> update(@Validated @RequestBody SdDutyDetailDTO dto) {
        return sdDutyDetailService.update(dto);
    }

    /**
     * 删除
     */
    @Log(logOpt = LogEnum.DELETE, module = "三定-细化职责")
    @GetMapping("/delete")
    @ApiOperation(value = "删除")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "id", value = "数据唯一标识", required = true)
    })
    public Result<Boolean> delete(@NotBlank(message = "id 不能为空") @RequestParam(name = "id") String id) {
        return sdDutyDetailService.delete(id);
    }

    /**
     * 列表
     */
    @Log(logOpt = LogEnum.PAGE, module = "三定-细化职责")
    @PostMapping("/list")
    @ApiOperation(value = "列表", response = Result.class)
    public Result list(@Validated(QueryGroup.class) @RequestBody SdDutyDetailListDTO dto) {
        return Result.ok(sdDutyDetailService.list(dto));
    }

    /**
     * 细化职责维度查询
     */
    @Log(logOpt = LogEnum.PAGE, module = "三定-细化职责")
    @PostMapping("/searchList")
    @ApiOperation(value = "细化职责维度查询", response = Result.class)
    public Result searchList(@Validated(Common1Group.class) @RequestBody SdDutyDetailListDTO dto) {
        return Result.ok(sdDutyDetailService.searchList(dto));
    }

}
