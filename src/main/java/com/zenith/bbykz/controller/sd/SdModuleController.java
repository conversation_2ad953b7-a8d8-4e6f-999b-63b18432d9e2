package com.zenith.bbykz.controller.sd;

import com.efficient.common.permission.Permission;
import com.efficient.common.result.Result;
import com.efficient.common.validate.AddGroup;
import com.efficient.common.validate.QueryGroup;
import com.efficient.logs.annotation.Log;
import com.efficient.logs.constant.LogEnum;
import com.zenith.bbykz.api.OrgInfoService;
import com.zenith.bbykz.api.sd.SdModuleService;
import com.zenith.bbykz.model.dto.sd.SdModuleCountDTO;
import com.zenith.bbykz.model.dto.sd.SdModuleDTO;
import com.zenith.bbykz.model.dto.sd.SdModuleListDTO;
import com.zenith.bbykz.model.entity.sd.SdModule;
import com.zenith.bbykz.model.vo.sd.SdModuleCountVO;
import com.zenith.bbykz.model.vo.sd.SdModuleVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.constraints.NotBlank;

/**
 * <p>
 * 三定-三定类型 controller 层
 * </p>
 *
 * <AUTHOR>
 * @date 2024-04-11 16:17:20
 */
@RestController
@RequestMapping("/sd/sdModule")
@Validated
@Api(tags = "三定-三定类型")
@Permission
public class SdModuleController {

    @Autowired
    private SdModuleService sdModuleService;
    @Autowired
    private OrgInfoService orgInfoService;
    /**
     * 新增
     */
    @Log(logOpt = LogEnum.SAVE, module = "三定-三定类型")
    @PostMapping("/save")
    @ApiOperation(value = "保存")
    public Result<SdModule> save(@Validated(value = AddGroup.class) @RequestBody SdModuleDTO dto) {
        return sdModuleService.save(dto);
    }

    /**
     * 详情
     */
    @Log(logOpt = LogEnum.QUERY, module = "三定-三定类型")
    @GetMapping("/find")
    @ApiOperation(value = "详情")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "id", value = "数据唯一标识", required = true)
    })
    public Result<SdModuleVO> find(@NotBlank(message = "id 不能为空") @RequestParam(name = "id") String id) {
        return sdModuleService.findById(id);
    }

    /**
     * 修改
     */
    @Log(logOpt = LogEnum.UPDATE, module = "三定-三定类型")
    @PostMapping("/update")
    @ApiOperation(value = "修改")
    public Result<Boolean> update(@Validated @RequestBody SdModuleDTO dto) {
        return sdModuleService.update(dto);
    }

    /**
     * 删除
     */
    @Log(logOpt = LogEnum.DELETE, module = "三定-三定类型")
    @GetMapping("/delete")
    @ApiOperation(value = "删除")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "id", value = "数据唯一标识", required = true)
    })
    public Result<Boolean> delete(@NotBlank(message = "id 不能为空") @RequestParam(name = "id") String id) {
        return sdModuleService.delete(id);
    }

    /**
     * 列表
     */
    @Log(logOpt = LogEnum.PAGE, module = "三定-三定类型")
    @PostMapping("/list")
    @ApiOperation(value = "列表", response = Result.class)
    public Result list(@Validated(QueryGroup.class) @RequestBody SdModuleListDTO dto) {
        return Result.ok(sdModuleService.list(dto));
    }

    /**
     * 职能职责分析
     */
    @Log(logOpt = LogEnum.QUERY, module = "三定-职能职责分析")
    @PostMapping("/analysis")
    @ApiOperation(value = "职能职责分析", response = SdModuleCountVO.class)
    public Result<SdModuleCountVO> analysis(@Validated @RequestBody SdModuleCountDTO dto) {
        dto.setUnitLevelCode(orgInfoService.findLevelCodeById(dto.getUnitLevelCode()));
        return Result.ok(sdModuleService.analysis(dto));
    }

    /**
     * 职能职责分析
     */
    // @Log(logOpt = LogEnum.QUERY, module = "三定-职能职责分析")
    // @PostMapping("/analysisList")
    // @ApiOperation(value = "职能职责分析明细", response = SdModuleCountVO.class)
    // public Result analysisList(@Validated @RequestBody SdModuleCountDTO dto) {
    //     return Result.ok(sdModuleService.analysisList(dto));
    // }

}
