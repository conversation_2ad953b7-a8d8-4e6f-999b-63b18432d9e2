package com.zenith.bbykz.controller.sd;

import com.efficient.common.permission.Permission;
import com.efficient.common.result.Result;
import com.efficient.common.validate.AddGroup;
import com.efficient.common.validate.Common1Group;
import com.efficient.common.validate.QueryGroup;
import com.efficient.logs.annotation.Log;
import com.efficient.logs.constant.LogEnum;
import com.zenith.bbykz.api.OrgInfoService;
import com.zenith.bbykz.api.sd.SdBusinessItemOneService;
import com.zenith.bbykz.api.sd.SdOneThingService;
import com.zenith.bbykz.model.dto.sd.SdBusinessItemOneDTO;
import com.zenith.bbykz.model.dto.sd.SdOneThingDTO;
import com.zenith.bbykz.model.dto.sd.SdOneThingListDTO;
import com.zenith.bbykz.model.entity.sd.SdOneThing;
import com.zenith.bbykz.model.vo.sd.SdBusinessItemOneVO;
import com.zenith.bbykz.model.vo.sd.SdOneThingVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.constraints.NotBlank;

/**
 * <p>
 * 三定-一件事 controller 层
 * </p>
 *
 * <AUTHOR>
 * @date 2024-04-11 16:17:20
 */
@RestController
@RequestMapping("/sd/sdOneThing")
@Validated
@Api(tags = "三定-一件事")
@Permission
public class SdOneThingController {

    @Autowired
    private SdOneThingService sdOneThingService;
    @Autowired
    private SdBusinessItemOneService sdBusinessItemOneService;
    @Autowired
    private OrgInfoService orgInfoService;
    /**
     * 新增
     */
    @Log(logOpt = LogEnum.SAVE, module = "三定-一件事")
    @PostMapping("/save")
    @ApiOperation(value = "保存")
    public Result<SdOneThing> save(@Validated(AddGroup.class) @RequestBody SdOneThingDTO dto) {
        return sdOneThingService.save(dto);
    }

    /**
     * 详情
     */
    @Log(logOpt = LogEnum.QUERY, module = "三定-一件事")
    @GetMapping("/find")
    @ApiOperation(value = "详情")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "id", value = "数据唯一标识", required = true)
    })
    public Result<SdOneThingVO> find(@NotBlank(message = "id 不能为空") @RequestParam(name = "id") String id) {
        return sdOneThingService.findById(id);
    }

    /**
     * 修改
     */
    @Log(logOpt = LogEnum.UPDATE, module = "三定-一件事")
    @PostMapping("/update")
    @ApiOperation(value = "修改")
    public Result<Boolean> update(@Validated @RequestBody SdOneThingDTO dto) {
        return sdOneThingService.update(dto);
    }

    /**
     * 删除
     */
    @Log(logOpt = LogEnum.DELETE, module = "三定-一件事")
    @GetMapping("/delete")
    @ApiOperation(value = "删除")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "id", value = "数据唯一标识", required = true)
    })
    public Result<Boolean> delete(@NotBlank(message = "id 不能为空") @RequestParam(name = "id") String id) {
        return sdOneThingService.delete(id);
    }

    /**
     * 列表
     */
    @Log(logOpt = LogEnum.PAGE, module = "三定-一件事")
    @PostMapping("/list")
    @ApiOperation(value = "列表", response = Result.class)
    public Result list(@Validated @RequestBody SdOneThingListDTO dto) {
        return Result.ok(sdOneThingService.list(dto));
    }

    /**
     * 列表
     */
    @Log(logOpt = LogEnum.PAGE, module = "三定-一件事")
    @PostMapping("/oneItemList")
    @ApiOperation(value = "列表", response = SdBusinessItemOneVO.class)
    public Result oneItemList(@Validated(QueryGroup.class) @RequestBody SdOneThingListDTO dto) {
        dto.setUnitLevelCode(orgInfoService.findLevelCodeById(dto.getUnitLevelCode()));
        return Result.ok(sdOneThingService.oneItemList(dto));
    }

    /**
     * 修改业务事项管理一件事
     */
    @Log(logOpt = LogEnum.UPDATE, module = "三定-一件事")
    @PostMapping("/updateItemOne")
    @ApiOperation(value = "修改业务事项管理一件事")
    public Result<Boolean> updateItemOne(@Validated(Common1Group.class) @RequestBody SdBusinessItemOneDTO dto) {
        return sdBusinessItemOneService.updateItemOne(dto);
    }

}
