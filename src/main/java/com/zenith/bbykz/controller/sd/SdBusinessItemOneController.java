// package com.zenith.bbykz.controller.sd;
//
// import com.efficient.common.result.Result;
// import com.efficient.common.permission.Permission;
// import com.efficient.logs.annotation.Log;
// import com.efficient.logs.constant.LogEnum;
// import com.zenith.bbykz.api.sd.SdBusinessItemOneService;
// import com.zenith.bbykz.model.dto.sd.SdBusinessItemOneDTO;
// import com.zenith.bbykz.model.dto.sd.SdBusinessItemOneListDTO;
// import com.zenith.bbykz.model.entity.sd.SdBusinessItemOne;
// import com.zenith.bbykz.model.vo.sd.SdBusinessItemOneVO;
// import io.swagger.annotations.Api;
// import io.swagger.annotations.ApiImplicitParam;
// import io.swagger.annotations.ApiImplicitParams;
// import io.swagger.annotations.ApiOperation;
// import org.springframework.beans.factory.annotation.Autowired;
// import org.springframework.validation.annotation.Validated;
// import org.springframework.web.bind.annotation.*;
//
// import javax.validation.constraints.NotBlank;
//
// /**
// * <p>
// * 三定-业务事项一件事关联表 controller 层
// * </p>
// *
// * <AUTHOR>
// * @date 2024-04-11 16:17:20
// */
// @RestController
// @RequestMapping("/sd/sdBusinessItemOne")
// @Validated
// @Api(tags = "三定-业务事项一件事关联表")
// @Permission
// public class SdBusinessItemOneController {
//
//     @Autowired
//     private SdBusinessItemOneService sdBusinessItemOneService;
//
//
//
//     /**
//     * 新增
//     */
//     @Log(logOpt = LogEnum.SAVE, module = "三定-业务事项一件事关联表")
//     @PostMapping("/save")
//     @ApiOperation(value = "保存")
//     public Result<SdBusinessItemOne> save(@Validated @RequestBody SdBusinessItemOneDTO dto) {
//         return sdBusinessItemOneService.save(dto);
//     }
//
//     /**
//     * 详情
//     */
//     @Log(logOpt = LogEnum.QUERY, module = "三定-业务事项一件事关联表")
//     @GetMapping("/find")
//     @ApiOperation(value = "详情")
//     @ApiImplicitParams({
//             @ApiImplicitParam(name = "id", value = "数据唯一标识", required = true)
//     })
//     public Result<SdBusinessItemOneVO> find(@NotBlank(message = "id 不能为空") @RequestParam(name="id") String id) {
//         return sdBusinessItemOneService.findById(id);
//     }
//
//     /**
//     * 修改
//     */
//     @Log(logOpt = LogEnum.UPDATE, module = "三定-业务事项一件事关联表")
//     @PostMapping("/update")
//     @ApiOperation(value = "修改")
//     public Result<Boolean> update(@Validated @RequestBody SdBusinessItemOneDTO dto) {
//         return sdBusinessItemOneService.update(dto);
//     }
//
//     /**
//     * 删除
//     */
//     @Log(logOpt = LogEnum.DELETE, module = "三定-业务事项一件事关联表")
//     @GetMapping("/delete")
//     @ApiOperation(value = "删除")
//     @ApiImplicitParams({
//             @ApiImplicitParam(name = "id", value = "数据唯一标识", required = true)
//     })
//     public Result<Boolean> delete(@NotBlank(message = "id 不能为空") @RequestParam(name="id") String id) {
//         return sdBusinessItemOneService.delete(id);
//     }
//
//     /**
//     * 列表
//     */
//     @Log(logOpt = LogEnum.PAGE, module = "三定-业务事项一件事关联表")
//     @PostMapping("/list")
//     @ApiOperation(value = "列表", response = Result.class)
//     public Result list(@Validated @RequestBody SdBusinessItemOneListDTO dto) {
//         return Result.ok(sdBusinessItemOneService.list(dto));
//     }
//
// }
