package com.zenith.bbykz.controller.sd;

import com.efficient.common.permission.Permission;
import com.efficient.common.result.Result;
import com.efficient.common.validate.AddGroup;
import com.efficient.common.validate.Common1Group;
import com.efficient.common.validate.QueryGroup;
import com.efficient.logs.annotation.Log;
import com.efficient.logs.constant.LogEnum;
import com.zenith.bbykz.api.OrgInfoService;
import com.zenith.bbykz.api.sd.SdCoreBusinessService;
import com.zenith.bbykz.model.dto.sd.SdCoreBusinessDTO;
import com.zenith.bbykz.model.dto.sd.SdCoreBusinessListDTO;
import com.zenith.bbykz.model.dto.sd.SdSaveCoreBusinessDTO;
import com.zenith.bbykz.model.entity.sd.SdCoreBusiness;
import com.zenith.bbykz.model.vo.sd.SdCoreBusinessVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.constraints.NotBlank;

/**
 * <p>
 * 三定-核心业务 controller 层
 * </p>
 *
 * <AUTHOR>
 * @date 2024-04-11 16:17:20
 */
@RestController
@RequestMapping("/sd/sdCoreBusiness")
@Validated
@Api(tags = "三定-核心业务")
@Permission
public class SdCoreBusinessController {

    @Autowired
    private SdCoreBusinessService sdCoreBusinessService;

    @Autowired
    private OrgInfoService orgInfoService;
    /**
     * 新增
     */
    @Log(logOpt = LogEnum.SAVE, module = "三定-核心业务")
    @PostMapping("/save")
    @ApiOperation(value = "保存")
    public Result<SdCoreBusiness> save(@Validated(AddGroup.class) @RequestBody SdSaveCoreBusinessDTO dto) {
        return sdCoreBusinessService.save(dto);
    }

    /**
     * 详情
     */
    @Log(logOpt = LogEnum.QUERY, module = "三定-核心业务")
    @GetMapping("/find")
    @ApiOperation(value = "详情")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "id", value = "数据唯一标识", required = true)
    })
    public Result<SdCoreBusinessVO> find(@NotBlank(message = "id 不能为空") @RequestParam(name = "id") String id) {
        return sdCoreBusinessService.findById(id);
    }

    /**
     * 修改
     */
    @Log(logOpt = LogEnum.UPDATE, module = "三定-核心业务")
    @PostMapping("/update")
    @ApiOperation(value = "修改")
    public Result<Boolean> update(@Validated @RequestBody SdCoreBusinessDTO dto) {
        return sdCoreBusinessService.update(dto);
    }

    /**
     * 删除
     */
    @Log(logOpt = LogEnum.DELETE, module = "三定-核心业务")
    @GetMapping("/delete")
    @ApiOperation(value = "删除")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "id", value = "数据唯一标识", required = true)
    })
    public Result<Boolean> delete(@NotBlank(message = "id 不能为空") @RequestParam(name = "id") String id) {
        return sdCoreBusinessService.delete(id);
    }

    /**
     * 列表
     */
    @Log(logOpt = LogEnum.PAGE, module = "三定-核心业务")
    @PostMapping("/list")
    @ApiOperation(value = "列表", response = Result.class)
    public Result list(@Validated(QueryGroup.class) @RequestBody SdCoreBusinessListDTO dto) {
        return Result.ok(sdCoreBusinessService.list(dto));
    }

    /**
     * 核心业务
     */
    @Log(logOpt = LogEnum.PAGE, module = "三定-核心业务")
    @PostMapping("/coreList")
    @ApiOperation(value = "核心业务", response = Result.class)
    public Result coreList(@Validated(Common1Group.class) @RequestBody SdCoreBusinessListDTO dto) {
        dto.setUnitLevelCode(orgInfoService.findLevelCodeById(dto.getUnitLevelCode()));
        return Result.ok(sdCoreBusinessService.coreList(dto));
    }

}
