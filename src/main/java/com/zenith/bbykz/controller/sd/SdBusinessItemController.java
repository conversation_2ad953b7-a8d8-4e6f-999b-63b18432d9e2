package com.zenith.bbykz.controller.sd;

import com.efficient.common.permission.Permission;
import com.efficient.common.result.Result;
import com.efficient.common.validate.AddGroup;
import com.efficient.common.validate.QueryGroup;
import com.efficient.logs.annotation.Log;
import com.efficient.logs.constant.LogEnum;
import com.zenith.bbykz.api.sd.SdBusinessItemService;
import com.zenith.bbykz.model.dto.sd.SdBusinessItemDTO;
import com.zenith.bbykz.model.dto.sd.SdBusinessItemListDTO;
import com.zenith.bbykz.model.dto.sd.SdSaveBusinessItemDTO;
import com.zenith.bbykz.model.entity.sd.SdBusinessItem;
import com.zenith.bbykz.model.vo.sd.SdBusinessItemVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.constraints.NotBlank;

/**
 * <p>
 * 三定-业务事项 controller 层
 * </p>
 *
 * <AUTHOR>
 * @date 2024-04-11 16:17:20
 */
@RestController
@RequestMapping("/sd/sdBusinessItem")
@Validated
@Api(tags = "三定-业务事项")
@Permission
public class SdBusinessItemController {

    @Autowired
    private SdBusinessItemService sdBusinessItemService;

    /**
     * 新增
     */
    @Log(logOpt = LogEnum.SAVE, module = "三定-业务事项")
    @PostMapping("/save")
    @ApiOperation(value = "保存", response = SdBusinessItem.class)
    public Result<SdBusinessItem> save(@Validated(AddGroup.class) @RequestBody SdSaveBusinessItemDTO dto) {
        return sdBusinessItemService.save(dto);
    }

    /**
     * 详情
     */
    @Log(logOpt = LogEnum.QUERY, module = "三定-业务事项")
    @GetMapping("/find")
    @ApiOperation(value = "详情", response = SdBusinessItemVO.class)
    @ApiImplicitParams({
            @ApiImplicitParam(name = "id", value = "数据唯一标识", required = true)
    })
    public Result<SdBusinessItemVO> find(@NotBlank(message = "id 不能为空") @RequestParam(name = "id") String id) {
        return sdBusinessItemService.findById(id);
    }

    /**
     * 修改
     */
    @Log(logOpt = LogEnum.UPDATE, module = "三定-业务事项")
    @PostMapping("/update")
    @ApiOperation(value = "修改", response = Boolean.class)
    public Result<Boolean> update(@Validated @RequestBody SdBusinessItemDTO dto) {
        return sdBusinessItemService.update(dto);
    }

    /**
     * 删除
     */
    @Log(logOpt = LogEnum.DELETE, module = "三定-业务事项")
    @GetMapping("/delete")
    @ApiOperation(value = "删除", response = Boolean.class)
    @ApiImplicitParams({
            @ApiImplicitParam(name = "id", value = "数据唯一标识", required = true)
    })
    public Result<Boolean> delete(@NotBlank(message = "id 不能为空") @RequestParam(name = "id") String id) {
        return sdBusinessItemService.delete(id);
    }

    /**
     * 列表
     */
    @Log(logOpt = LogEnum.PAGE, module = "三定-业务事项")
    @PostMapping("/list")
    @ApiOperation(value = "列表", response = Result.class)
    public Result list(@Validated(QueryGroup.class) @RequestBody SdBusinessItemListDTO dto) {
        return Result.ok(sdBusinessItemService.list(dto));
    }

}
