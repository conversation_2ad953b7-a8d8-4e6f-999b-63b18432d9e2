package com.zenith.bbykz.controller;

import com.efficient.common.permission.Permission;
import com.efficient.common.result.Result;
import com.efficient.logs.annotation.Log;
import com.efficient.logs.constant.LogEnum;
import com.zenith.bbykz.api.SysUserOrgGroupService;
import com.zenith.bbykz.model.dto.SysUserOrgGroupDTO;
import com.zenith.bbykz.model.dto.SysUserOrgGroupListDTO;
import com.zenith.bbykz.model.entity.SysUserOrgGroup;
import com.zenith.bbykz.model.vo.SysUserOrgGroupVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.constraints.NotBlank;

/**
 * <p>
 * 用户机构组 controller 层
 * </p>
 *
 * <AUTHOR>
 * @date 2024-03-13 10:48:03
 */
@RestController
@RequestMapping("/sysUserOrgGroup")
@Validated
@Api(tags = "用户机构组")
@Permission
public class SysUserOrgGroupController {

    @Autowired
    private SysUserOrgGroupService sysUserOrgGroupService;

    /**
     * 新增
     */
    @Log(logOpt = LogEnum.SAVE, module = "用户机构组")
    @PostMapping("/save")
    @ApiOperation(value = "保存")
    public Result<SysUserOrgGroup> save(@Validated @RequestBody SysUserOrgGroupDTO dto) {
        return sysUserOrgGroupService.save(dto);
    }

    /**
     * 详情
     */
    @Log(logOpt = LogEnum.QUERY, module = "用户机构组")
    @GetMapping("/find")
    @ApiOperation(value = "详情")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "id", value = "数据唯一标识", required = true)
    })
    public Result<SysUserOrgGroupVO> find(@NotBlank(message = "id 不能为空") @RequestParam(name = "id") String id) {
        return sysUserOrgGroupService.findById(id);
    }

    /**
     * 修改
     */
    @Log(logOpt = LogEnum.UPDATE, module = "用户机构组")
    @PostMapping("/update")
    @ApiOperation(value = "修改")
    public Result<Boolean> update(@Validated @RequestBody SysUserOrgGroupDTO dto) {
        return sysUserOrgGroupService.update(dto);
    }

    /**
     * 删除
     */
    @Log(logOpt = LogEnum.DELETE, module = "用户机构组")
    @GetMapping("/delete")
    @ApiOperation(value = "删除")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "id", value = "数据唯一标识", required = true)
    })
    public Result<Boolean> delete(@NotBlank(message = "id 不能为空") @RequestParam(name = "id") String id) {
        return sysUserOrgGroupService.delete(id);
    }

    /**
     * 列表
     */
    @Log(logOpt = LogEnum.PAGE, module = "用户机构组")
    @PostMapping("/list")
    @ApiOperation(value = "列表", response = Result.class)
    public Result list(@Validated @RequestBody SysUserOrgGroupListDTO dto) {
        return Result.ok(sysUserOrgGroupService.list(dto));
    }

}
