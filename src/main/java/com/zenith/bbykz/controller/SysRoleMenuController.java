package com.zenith.bbykz.controller;

import com.efficient.common.permission.Permission;
import com.efficient.common.result.Result;
import com.efficient.logs.annotation.Log;
import com.efficient.logs.constant.LogEnum;
import com.zenith.bbykz.api.SysRoleMenuService;
import com.zenith.bbykz.model.dto.SysRoleMenuDTO;
import com.zenith.bbykz.model.dto.SysRoleMenuListDTO;
import com.zenith.bbykz.model.entity.SysRoleMenu;
import com.zenith.bbykz.model.vo.SysRoleMenuVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.constraints.NotBlank;

/**
 * <p>
 * sys_role_menu controller 层
 * </p>
 *
 * <AUTHOR>
 * @date 2023-09-14 17:14:26
 */
@RestController
@RequestMapping("/sysRoleMenu")
@Validated
@Api(tags = "sys_role_menu")
@Permission
public class SysRoleMenuController {

    @Autowired
    private SysRoleMenuService sysRoleMenuService;

    /**
     * 新增
     */
    @Log(logOpt = LogEnum.SAVE)
    @PostMapping("/save")
    @ApiOperation(value = "保存", response = Result.class)
    public Result save(@Validated @RequestBody SysRoleMenuDTO dto) {
        SysRoleMenu entity = sysRoleMenuService.save(dto);
        return Result.ok(entity);
    }

    /**
     * 详情
     */
    @Log(logOpt = LogEnum.QUERY)
    @GetMapping("/find")
    @ApiOperation(value = "详情", response = Result.class)
    @ApiImplicitParams({
            @ApiImplicitParam(name = "id", value = "数据唯一标识", required = true)
    })
    public Result find(@NotBlank(message = "id 不能为空") @RequestParam(name = "id") String id) {
        SysRoleMenuVO entity = sysRoleMenuService.findById(id);
        return Result.ok(entity);
    }

    /**
     * 修改
     */
    @Log(logOpt = LogEnum.UPDATE)
    @PostMapping("/update")
    @ApiOperation(value = "修改", response = Result.class)
    public Result update(@Validated @RequestBody SysRoleMenuDTO dto) {
        boolean flag = sysRoleMenuService.update(dto);
        return flag ? Result.ok() : Result.fail();
    }

    /**
     * 删除
     */
    @Log(logOpt = LogEnum.DELETE)
    @GetMapping("/delete")
    @ApiOperation(value = "删除", response = Result.class)
    @ApiImplicitParams({
            @ApiImplicitParam(name = "id", value = "数据唯一标识", required = true)
    })
    public Result delete(@NotBlank(message = "id 不能为空") @RequestParam(name = "id") String id) {
        boolean flag = sysRoleMenuService.delete(id);
        return flag ? Result.ok() : Result.fail();
    }

    /**
     * 列表
     */
    @Log(logOpt = LogEnum.PAGE)
    @PostMapping("/list")
    @ApiOperation(value = "列表", response = Result.class)
    public Result list(@Validated @RequestBody SysRoleMenuListDTO dto) {
        return Result.ok(sysRoleMenuService.list(dto));
    }
}
