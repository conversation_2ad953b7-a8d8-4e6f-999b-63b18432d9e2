package com.zenith.bbykz.controller.staffingManagement;

import com.efficient.common.permission.Permission;
import com.efficient.common.result.Result;
import com.efficient.logs.annotation.Log;
import com.efficient.logs.constant.LogEnum;
import com.zenith.bbykz.api.OrgInfoService;
import com.zenith.bbykz.api.staffingManagement.StaffingManagementService;
import com.zenith.bbykz.model.dto.RegistrationDTO;
import com.zenith.bbykz.model.dto.RegistrationListDTO;
import com.zenith.bbykz.model.dto.StaffingManagementDTO;
import com.zenith.bbykz.model.dto.StaffingManagementListDTO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.servlet.http.HttpServletResponse;
import javax.validation.constraints.NotBlank;

/**
 * <p>
 * 用编管理 controller 层
 * </p>
 *
 * <AUTHOR>
 * @date 2023-09-13
 */
@RestController
@RequestMapping("/staffingManagement")
@Validated
@Api(tags = "用编管理")
@Permission
public class StaffingManagementController {

    @Autowired
    private StaffingManagementService staffingManagementService;
    @Autowired
    private OrgInfoService orgInfoService;
    /**
     * 新增
     */
    @Log(logOpt = LogEnum.SAVE)
    @PostMapping("/addUpdate")
    @ApiOperation(value = "保存", response = Result.class)
    public Result save(@Validated @RequestBody StaffingManagementDTO dto) {
        return staffingManagementService.addUpdate(dto);
    }

    /**
     * 详情
     */
    @Log(logOpt = LogEnum.QUERY)
    @GetMapping("/find")
    @ApiOperation(value = "详情", response = Result.class)
    @ApiImplicitParams({
            @ApiImplicitParam(name = "id", value = "数据唯一标识", required = true)
    })
    public Result find(@NotBlank(message = "id 不能为空") @RequestParam(name = "id") String id) {
        return staffingManagementService.findById(id);
    }

    /**
     * 删除
     */
    @Log(logOpt = LogEnum.DELETE)
    @GetMapping("/delete")
    @ApiOperation(value = "删除", response = Result.class)
    @ApiImplicitParams({
            @ApiImplicitParam(name = "id", value = "数据唯一标识", required = true)
    })
    public Result delete(@NotBlank(message = "id 不能为空") @RequestParam(name = "id") String id) {
        boolean flag = staffingManagementService.delete(id);
        return flag ? Result.ok() : Result.fail();
    }

    /**
     * 普通用户查询列表
     */
    @Log(logOpt = LogEnum.PAGE)
    @PostMapping("/list")
    @ApiOperation(value = "列表", response = Result.class)
    public Result list(@Validated @RequestBody StaffingManagementListDTO dto) {
        return staffingManagementService.list(dto);
    }

    /**
     * 审批
     */
    @Log(logOpt = LogEnum.CHECK)
    @GetMapping("/approve")
    @ApiOperation(value = "审批", response = Result.class)
    public Result approve(@NotBlank(message = "id 不能为空") @RequestParam(name = "id") String id,
                          @NotBlank(message = "状态不能为空") @RequestParam(name = "status") String status,
                          @RequestParam(name = "option") String option) {
        return staffingManagementService.approve(id, status, option);
    }

    /**
     * 编办用户查询列表
     */
    @Log(logOpt = LogEnum.PAGE)
    @PostMapping("/bbList")
    @ApiOperation(value = "列表", response = Result.class)
    public Result bblist(@Validated @RequestBody StaffingManagementListDTO dto) {
        return staffingManagementService.bbList(dto);
    }

    /**
     * 导出申请列表
     */
    @Log(logOpt = LogEnum.EXPORT)
    @PostMapping("/exportList")
    @ApiOperation(value = "导出申请列表", response = Result.class)
    public void exportList(@Validated @RequestBody StaffingManagementListDTO dto) throws Exception {
        ServletRequestAttributes servletRequestAttributes = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
        HttpServletResponse response = servletRequestAttributes.getResponse();
        staffingManagementService.exportList(dto, response);
    }

    /**
     * 导出审核列表
     */
    @Log(logOpt = LogEnum.EXPORT)
    @PostMapping("/exportBbList")
    @ApiOperation(value = "导出审核列表", response = Result.class)
    public void exportBbList(@Validated @RequestBody StaffingManagementListDTO dto) throws Exception {
        ServletRequestAttributes servletRequestAttributes = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
        HttpServletResponse response = servletRequestAttributes.getResponse();
        staffingManagementService.exportBbList(dto, response);
    }

    /**
     * 用编管理登记
     */
    @Log(logOpt = LogEnum.SAVE)
    @PostMapping("/registration")
    @ApiOperation(value = "用编管理登记", response = Result.class)
    public Result registration(@Validated @RequestBody RegistrationDTO dto) {
        return staffingManagementService.registration(dto);
    }

    /**
     * 查询登记列表
     */
    @Log(logOpt = LogEnum.PAGE)
    @PostMapping("/countList")
    @ApiOperation(value = "用编统计列表", response = Result.class)
    public Result countList(@Validated @RequestBody RegistrationListDTO dto) {
        dto.setOrgCode(orgInfoService.findLevelCodeById(dto.getOrgCode()));
        return staffingManagementService.countList(dto);
    }

    /**
     * 导出统计列表
     */
    @Log(logOpt = LogEnum.EXPORT)
    @PostMapping("/exportCountList")
    @ApiOperation(value = "导出统计列表", response = Result.class)
    public void exportCountList(@Validated @RequestBody RegistrationListDTO dto) throws Exception {
        ServletRequestAttributes servletRequestAttributes = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
        HttpServletResponse response = servletRequestAttributes.getResponse();
        staffingManagementService.exportCountList(dto, response);
    }

}
