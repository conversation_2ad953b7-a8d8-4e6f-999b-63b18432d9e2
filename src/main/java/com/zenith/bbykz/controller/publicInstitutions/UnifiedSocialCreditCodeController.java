package com.zenith.bbykz.controller.publicInstitutions;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.efficient.common.permission.Permission;
import com.efficient.common.result.Result;
import com.efficient.common.validate.Common1Group;
import com.efficient.logs.annotation.Log;
import com.efficient.logs.constant.LogEnum;
import com.zenith.bbykz.api.OrgInfoService;
import com.zenith.bbykz.api.publicInstitutions.UnifiedSocialCreditCodeService;
import com.zenith.bbykz.model.dto.UnifiedSocialCreditCodeDTO;
import com.zenith.bbykz.model.entity.UnifiedSocialCreditCode;
import com.zenith.bbykz.model.vo.UnifiedSocialCreditCodeVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.IOException;

/**
 * 机关事业单位统一社会信用代码服务
 *
 * <AUTHOR>
 * @date 2023/9/12
 */
@RestController
@RequestMapping("/unifiedSocialCreditCode")
@Api(tags = "机关事业单位统一社会信用代码")
@Validated
@Permission
public class UnifiedSocialCreditCodeController {

    @Resource
    private UnifiedSocialCreditCodeService service;
    @Autowired
    private OrgInfoService orgInfoService;

    /**
     * 机关事业单位统一信用代码导入
     */
    @GetMapping("/import")
    @ApiOperation(value = "导入", response = Result.class)
    @Log(logOpt = LogEnum.IMPORT, module = "机关事业单位统一信用代码", desc = "机关事业单位统一信用代码信息")
    public Result importUnifiedSocialCreditCode(@NotNull(message = "fileId 不能为空") String fileId ) throws IOException {
        return service.importFile(fileId);
    }


    @GetMapping("/rollback")
    @ApiOperation(value = "回退上一个版本", response = Result.class)
    @Log(logOpt = LogEnum.ROLLBACK, module = "机关事业单位统一信用代码", desc = "机关事业单位统一信用代码信息")
    public Result rollback() {
        return service.rollback();
    }

    /**
     * 查询机关事业单位统一信用代码
     */
    @GetMapping("/query")
    @ApiOperation(value = "详情", response = Result.class)
    @Log(logOpt = LogEnum.QUERY, module = "机关事业单位统一信用代码", desc = "机关事业单位统一信用代码信息")
    public Result queryImportRecords(
            @NotNull(message = "pageNum 不能为空") @RequestParam(value = "pageNum") Integer pageNum,
            @NotNull(message = "pageSize 不能为空") @RequestParam(value = "pageSize") Integer pageSize,
            @NotBlank(message = "keyWord 不能为空") @RequestParam(value = "keyWord") String keyWord,
            @NotBlank(message = "key 不能为空") @RequestParam(value = "key") String key,
            @NotBlank(message = "captcha 不能为空") @RequestParam(value = "captcha") String captcha) {
        return service.queryImportRecords(pageNum,pageSize,keyWord, key, captcha);
    }

    /**
     * 列表
     */
    @Log(logOpt = LogEnum.PAGE)
    @PostMapping("/list")
    @ApiOperation(value = "列表", response = Result.class)
    public Result list(@Validated @RequestBody UnifiedSocialCreditCodeDTO dto) {
        dto.setUnitLevelCode(orgInfoService.findDjxxLevel(dto.getUnitLevelCode()));
        Page<UnifiedSocialCreditCode> page = service.list(dto);
        return Result.ok(page);
    }

    /**
     * 变动列表
     */
    @Log(logOpt = LogEnum.PAGE)
    @PostMapping("/bdList")
    @ApiOperation(value = "变动列表", response = Result.class)
    public Result bdList(@Validated(Common1Group.class) @RequestBody UnifiedSocialCreditCodeDTO dto) {
        Page<UnifiedSocialCreditCodeVO> page = service.bdList(dto);
        return Result.ok(page);
    }
}
