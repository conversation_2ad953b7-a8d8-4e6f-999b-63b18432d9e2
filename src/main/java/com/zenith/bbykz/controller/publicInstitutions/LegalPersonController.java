package com.zenith.bbykz.controller.publicInstitutions;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.efficient.common.permission.Permission;
import com.efficient.common.result.Result;
import com.efficient.common.validate.Common1Group;
import com.efficient.logs.annotation.Log;
import com.efficient.logs.constant.LogEnum;
import com.zenith.bbykz.api.OrgInfoService;
import com.zenith.bbykz.api.publicInstitutions.LegalPersonService;
import com.zenith.bbykz.model.dto.LegalPersonRegistrationListDTO;
import com.zenith.bbykz.model.entity.LegalPersonRegistration;
import com.zenith.bbykz.model.vo.LegalPersonRegistrationVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * 事业单位法人登记控制层
 *
 * <AUTHOR>
 * @date 2023/9/12
 */
@RestController
@Api(tags = "事业单位法人登记")
@RequestMapping("/legalPerson")
@Permission
public class LegalPersonController {

    @Resource
    private LegalPersonService service;
    @Autowired
    private OrgInfoService orgInfoService;

    /**
     * 事业单位法人登记导入
     */
    @GetMapping("/import")
    @ApiOperation(value = "导入", response = Result.class)
    @Log(logOpt = LogEnum.IMPORT, module = "事业单位法人登记", desc = "事业单位法人登记信息")
    public Result importPublicInstitutionLegalPerson(@NotNull(message = "fileId 不能为空") String fileId) throws Exception {
        return service.importFile(fileId);
    }

    @GetMapping("/rollback")
    @ApiOperation(value = "回退上一个版本", response = Result.class)
    @Log(logOpt = LogEnum.ROLLBACK, module = "事业单位法人登记", desc = "事业单位法人登记信息")
    public Result rollback() {
        return service.rollback();
    }

    /**
     * 查询事业单位法人登记
     */
    @GetMapping("/query")
    @ApiOperation(value = "详情", response = Result.class)
    @Log(logOpt = LogEnum.QUERY, module = "事业单位法人登记", desc = "事业单位法人登记信息")
    public Result queryImportRecords(
            @NotNull(message = "pageNum 不能为空") @RequestParam(value = "pageNum") Integer pageNum,
            @NotNull(message = "pageSize 不能为空") @RequestParam(value = "pageSize") Integer pageSize,
            @NotBlank(message = "keyWord 不能为空") @RequestParam(value = "keyWord") String keyWord,
                                     @NotBlank(message = "type 不能为空,1-正常，2-撤销") @RequestParam(value = "type") String type,
                                     @NotBlank(message = "key 不能为空") @RequestParam(value = "key") String key,
                                     @NotBlank(message = "captcha 不能为空") @RequestParam(value = "captcha") String captcha) {
        return service.queryImportRecords(pageNum,pageSize,keyWord, type,key, captcha);
    }

    /**
     * 列表
     */
    @Log(logOpt = LogEnum.PAGE, module = "事业单位法人登记")
    @PostMapping("/list")
    @ApiOperation(value = "列表", response = Result.class)
    public Result list(@Validated @RequestBody LegalPersonRegistrationListDTO dto) {
        dto.setUnitLevelCode(orgInfoService.findDjxxLevel(dto.getUnitLevelCode()));
        Page<LegalPersonRegistration> page = service.list(dto);
        return Result.ok(page);
    }

    /**
     * 变动列表
     */
    @Log(logOpt = LogEnum.PAGE, module = "事业单位法人登记")
    @PostMapping("/bdList")
    @ApiOperation(value = "变动列表", response = Result.class)
    public Result bdList(@Validated(Common1Group.class) @RequestBody LegalPersonRegistrationListDTO dto) {
        Page<LegalPersonRegistrationVO> page = service.bdList(dto);
        return Result.ok(page);
    }

    /**
     * 刷新变动信息
     */
    @Log(logOpt = LogEnum.PAGE, module = "刷新变动信息")
    @GetMapping("/verifyZyInfo")
    public Result verifyZyInfo() {
        return service.verifyZyInfo();
    }

}
