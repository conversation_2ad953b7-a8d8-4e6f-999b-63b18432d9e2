package com.zenith.bbykz.common.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Objects;

/**
 * 消息状态，1-已感知，2-已发送，3-已查看，4-已处理,5-已完成
 *
 * <AUTHOR>
 * @since 2024/4/18 10:12
 */
@AllArgsConstructor
@Getter
public enum NotifyState {
    YGZ("1", "已感知"),
    YFS("2", "已发送"),
    YCK("3", "已查看"),
    YCL("4", "已处理"),
    YWC("5", "已完成");
    private final String code;
    private final String name;

    public static String getNameByCode(String code) {
        for (NotifyState value : NotifyState.values()) {
            if (Objects.equals(code, value.getCode())) {
                return value.getName();
            }
        }
        return null;
    }
    public static NotifyState getByCode(String code) {
        for (NotifyState value : NotifyState.values()) {
            if (Objects.equals(code, value.getCode())) {
                return value;
            }
        }
        return null;
    }
}
