package com.zenith.bbykz.common.enums;

import java.util.Objects;

/**
 * 公共枚举
 *
 * @param <C>
 * @param <L>
 */
public interface BaseEnum<C, L> {
    C getCode();

    L getLabel();

    /**
     * 匹配code
     *
     * @param enums
     * @param code
     * @return
     */
    default Boolean isCode(BaseEnum<C, L> enums, C code) {
        return Objects.equals(enums.getCode(), code);
    }

    /**
     * 匹配label
     *
     * @param enums
     * @param label
     * @return
     */
    default Boolean isLabel(BaseEnum<C, L> enums, L label) {
        return Objects.equals(enums.getLabel(), label);
    }
}
