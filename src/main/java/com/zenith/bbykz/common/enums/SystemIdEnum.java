package com.zenith.bbykz.common.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2024/3/1 13:54
 */
@AllArgsConstructor
@Getter
public enum SystemIdEnum {
    JGSY("1", "机关事业单位登记"),
    YBGL("2", "用编核编管理系统"),
    XTSZ("3", "系统设置"),
    SDGL("4", "三定管理系统"),
    RWFP("5", "任务分配系统");
    private final String id;
    private final String name;

    public static List<String> commonList() {
        List<String> list = new ArrayList<>();
        list.add(JGSY.getId());
        list.add(YBGL.getId());
        return list;
    }
}
