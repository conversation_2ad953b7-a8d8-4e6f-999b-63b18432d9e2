package com.zenith.bbykz.common.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 用编管理状态
 */
@AllArgsConstructor
@Getter
public enum StaffingManagementStatusEnum implements BaseEnum<String, String> {
    /**
     * 已送审，未审批
     */
    UNAPPROVED("0", "已送审，未审核"),

    /**
     * 同意
     */
    AGREE("1", "同意"),

    /**
     * 不同意
     */
    DISAGREE("2", "不同意"),

    /**
     * 已保存,未审批
     */
    SAVED("3", "已保存,未送审");
    private final String code;
    private final String label;

}
