package com.zenith.bbykz.common.enums.efficient;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 通知消息类型
 *
 * <AUTHOR>
 * @since 2024/1/10 14:18
 */
@Getter
@AllArgsConstructor
public enum NotifyTypeEnum {
    PTXX("1", "普通消息"),
    DX("2", "短信"),
    <PERSON><PERSON>("3", "邮件"),
    DB("4", "待办"),
    GZTZ("5", "工作通知"),
    DINGXX("6", "ding消息"),
    GG("7", "公告"),
    QT("9", "其他");
    private final String code;
    private final String name;

}
