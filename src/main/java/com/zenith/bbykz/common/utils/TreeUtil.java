package com.zenith.bbykz.common.utils;

import cn.hutool.core.util.StrUtil;
import com.zenith.bbykz.model.base.TreeNode;

import java.math.BigInteger;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 树形工具类
 *
 * <AUTHOR>
 * @since 2022/4/29 11:29
 */
public class TreeUtil {

    private static final Long LAST_ORDER = new Long("9999");
    public static final String[] NXS_STR = {"a", "b", "c", "d"};
    /**
     * 获取书节点集合
     *
     * @param nodeList 节点集合
     * @return 树
     */
    public static List<TreeNode> createListTree(List<TreeNode> nodeList) {
        List<TreeNode> rootNodes = nodeList.stream().filter(node -> Objects.nonNull(node.getIsRoot()) && node.getIsRoot()).collect(Collectors.toList());
        List<TreeNode> resultList = new ArrayList<>(4);
        for (TreeNode rootNode : rootNodes) {
            if (Objects.isNull(rootNode.getOrder())) {
                rootNode.setOrder(LAST_ORDER);
            }
            createChildren(rootNode, nodeList);
            resultList.add(rootNode);
        }
        return resultList.stream().sorted(Comparator.comparing(TreeNode::getOrderType).thenComparing(TreeNode::getOrder)).collect(Collectors.toList());
    }

    /**
     * 递归构建 子节点
     *
     * @param parentNode 父节点
     * @param nodeList   节点集合
     * @return 父节点 含下级
     */
    public static TreeNode createChildren(TreeNode parentNode, List<TreeNode> nodeList) {
        List<TreeNode> childrenList = new ArrayList<>();
        for (TreeNode treeNode : nodeList) {
            if (Objects.isNull(treeNode.getOrder())) {
                treeNode.setOrder(LAST_ORDER);
            }
            if (StrUtil.equals(treeNode.getParentCode(), parentNode.getCode())) {
                childrenList.add(createChildren(treeNode, nodeList));
            }

        }
        final List<TreeNode> collect = childrenList.stream().sorted(Comparator.comparing(TreeNode::getOrderType).thenComparing(TreeNode::getType,Comparator.nullsLast(Comparator.naturalOrder())).thenComparing(TreeNode::getOrder)).collect(Collectors.toList());
        // parentNode.setIsLeaf(collect.size() == 0);
        parentNode.setChildren(collect);
        return parentNode;
    }

    /**
     * 获取书节点
     *
     * @param nodeList 节点集合
     * @return 树
     */
    public static TreeNode createTree(List<TreeNode> nodeList) {
        final List<TreeNode> listTree = createListTree(nodeList);
        return listTree.get(0);
    }

    public static BigInteger getOrderBigInteger(String order) {
        return new BigInteger(getOrderStr(order));
    }

    public static String getOrderStr(String order) {
        if (StrUtil.isBlank(order)) {
            return "9999";
        }
        final String[] str = order.trim().split(" ");
        // final String replaceAll = order.replaceAll(" ", "");
        return str[str.length - 1].replaceAll("[^0-9]", "");
    }

    public static BigInteger getOrderBigInteger(Integer order) {
        return new BigInteger(order.toString());
    }

    public static Integer getOrderInt(String order) {
        return Integer.parseInt(getOrderStr(order));
    }

    public static String changeCode(String code) {
        if (StrUtil.equalsAny(code, NXS_STR)) {
            return code.substring(0, code.length() - 1);
        }
        return code;
    }
}
