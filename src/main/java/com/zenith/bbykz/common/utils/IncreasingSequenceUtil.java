package com.zenith.bbykz.common.utils;

import cn.hutool.core.date.DateUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.zenith.bbykz.dao.RepliesSequenceMapper;
import com.zenith.bbykz.model.entity.RepliesSequence;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Date;
import java.util.Objects;

/**
 * 回复函递增序列工具类
 */
@Component
public class IncreasingSequenceUtil {

    private final static String DATE_FORMAT = "yyyyMMdd";
    private static RepliesSequenceMapper repliesSequenceMapper;

    public static synchronized String createNum(String geocode) {
        String code = DateUtil.format(new Date(), DATE_FORMAT);
        return getNum(geocode + "-" + code);
    }

    public static synchronized String getNum(String code) {
        RepliesSequence repliesSequence = repliesSequenceMapper.selectOne(new LambdaQueryWrapper<RepliesSequence>()
                .eq(RepliesSequence::getCurrDate, code)
                .orderByDesc(RepliesSequence::getId)
                .last("limit 1"));
        String newNum;
        if (Objects.isNull(repliesSequence)) {
            newNum = "001";
        } else {
            //获取最大的code
            String maxCode = repliesSequence.getCode();
            // String newNumStr = String.valueOf(Integer.parseInt(maxCode) + 1);
            Integer newNumOld = Integer.parseInt(maxCode) + 1;
            //前面补0
            newNum = String.format("%03d", newNumOld);
        }
        RepliesSequence entity = new RepliesSequence();
        entity.setCurrDate(code);
        entity.setCode(newNum.substring(2));
        repliesSequenceMapper.insert(entity);
        return code + newNum;
    }

    @Resource
    public void setRepliesSequenceMapper(RepliesSequenceMapper repliesSequenceMapper) {
        IncreasingSequenceUtil.repliesSequenceMapper = repliesSequenceMapper;
    }

}
