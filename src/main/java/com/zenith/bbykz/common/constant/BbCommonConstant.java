package com.zenith.bbykz.common.constant;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @since 2023/9/13 11:07
 */
public class BbCommonConstant {
    public static final String BASE_STR = "ABCDEFGHJKMNPQRSTUVWXYZabcdefghjkmnpqrstuvwxyz0123456789";
    public static final Integer TRUE_INT = 1;
    public static final Integer FALSE_INT = 0;
    public static final boolean TRUE = true;
    public static final boolean FALSE = false;
    /**
     * 重庆市市级部门机构层级码 500
     */
    public static final String CQ = "500";
    public static final String CQ_GEOCODE = "500000";
    /**
     * 重庆市市级部门机构层级码 500001
     */
    public static final String CQ_SJBM = "500001";
    /**
     * 重庆市市区县机构层级码 500002
     */
    public static final String CQ_QX = "500002";

    public static final String SYSTEM_MANAGE_ROLE = "系统管理员角色";


    /**
     * 中央区划代码
     */
    public static final String ZY_CODE = "100000";
    public static final String ZY_CODE_END = "0000";
    /**
     * code 连接符
     */
    public static final String CODE_JOINER = "-";
    public static final String GEO_CODE_TYPE = "geocode";
    public static final String SYSTEM_CODE_TYPE = "systemcode";
    public static final String NXS = "nxs";
    /**
     * 内设机构
     */
    public static final List<String> NSJG_LIST = new ArrayList<String>(){{
        add("05");
        add("07");
        add("09");
        add("11");
        add("13");
    }};
    /**
     * 重庆区划代码
     */
    public static final String CQ_CODE = "500000";
    public static final String CQ_CODE_START = "50";
    public static final String CQ_CODE_END = "00";
    /**
     * 市辖区
     */
    public static final String SXQ_CODE = "500100";
    /**
     * 县
     */
    public static final String X_CODE = "500200";
    /**
     * 有效
     */
    public static final String ENABLED = "1";
    /**
     * 无效
     */
    public static final String INVALID = "0";
    /**
     * 内设单位
     */
    public static final String NS = "05";
    /**
     * 事业内设
     */
    public static final String SY_NS = "13";
    /**
     * 下设单位
     */
    public static final String XS = "06";
    /**
     * 事业单位
     */
    public static final String SY = "12";
    /**
     * 锁定次数
     */
    public static final Integer LOCK_COUNT = 5;
    /**
     * 锁定时间
     */
    public static final Integer LOCK_TIME = 30;
    /**
     * 机构code分隔符
     */
    public static final String CODE_DELIMITER = "-";

    public static final Integer ONE = 1;

    public static final Integer ZERO = 0;

    public static final Integer HUNDRED = 100;

    public static final String STRING_ONE = "1";

    public static final String STRING_ZERO = "0";

    public static final String STRING_TWO = "2";

    public static final String STRING_THREE = "3";
    /**
     * 全选
     */
    public static final String SELECT_ALL = "select_all";
    /**
     * 换行
     */
    public static final String NEWLINE = "\r\n";

    /**
     * 分号
     */
    public static final String SEMICOLON = ";";
    public static final String GEOCODE_QU_PARENT = "8";
    public static final String GEOCODE_XIAN_PARENT = "9";
    public static final String GEOCODE_QU = "4";
    public static final String GEOCODE_XIAN = "5";
    public static final String GEOCODE_SHI = "2";
    /**
     * 变动区划
     *500119 江津区
     * 500120 合川区
     * 500121 永川区
     * 500122 南川区
     */
    public static final Map<String,String> BIANDONG_QX_GEOCODE = new HashMap<String,String>(){{
        put("500119","500381");
        put("500120","500382");
        put("500121","500383");
        put("500122","500384");
    }};

    public static final String[] BIANDONG_QX_GEOCODE_ARR=new String[]{"500119","500120","500121","500122"};
    /**
     * 央编办区划码
     */
    public static final String[] BIANDONG_QX_GEOCODE_ARR_REAL=new String[]{"500381","500382","500383","500384"};
    public static final Map<String,String> BIANDONG_QX_GEOCODE_REAL = new HashMap<String,String>(){{
        put("500381","500119");
        put("500382","500120");
        put("500383","500121");
        put("500384","500122");
    }};


}
