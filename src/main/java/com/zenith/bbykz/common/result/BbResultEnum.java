package com.zenith.bbykz.common.result;

import com.efficient.common.result.ResultConstant;

/**
 * <AUTHOR>
 * @date 2023/9/12
 */
public enum BbResultEnum implements ResultConstant {
    FAILED(-1, "操作失败！"),
    SUCCESS(200, "操作成功！"),

    /**
     * 业务相关
     */
    IMPORT_SUCCESS(1000, "导入成功！"),
    VERIFICATION_CODE_ERROR(1001, "验证码错误，请重新输入！"),
    QEURY_FAILED(1002, "输入不正确，请重新输入查询信息或刷新图片获取验证码进行查询！"),
    START_GT_END(1003, "结束时间不能小于开始时间"),
    ORGQH_CAN_NOT_ADD(1004, "区级机构不能新增！"),
    THE_USER_NOT_IS_BB_USER(1005, "该用户机构不是编办机构不能查询!"),
    DEVELOP_UPLOAD_EXCEL_ERROR(1006, "请使用正确的模板进行导入！"),
    ADVICE_IS_NULL(1007, "意见不能为空！"),
    USED_NUM_MUST_LESS_EMPLOYEES_NUMBER(1008, "总共使用量只能小于等于申请的用编数量！"),

    REGISTER_ROLL_BACK_VERSION(5000, "当前已经是最后一个版本，无法再回退到上一个版本！"),
    REGISTER_ROLL_BACK_VERSION_NO(5000, "当前暂无可回退版本！"),
    /**
     * 三定系统
     */
    SD_LOWER_DATA(8000, "含有下级数据无法删除！"),
    SD_USE_DATA(8001, "数据已被关联无法删除！"),

    /**
     * 系统相关
     */
    USER_SYSTEM_NULL(9001, "用户未授权，请联系管理员进行授权！"),
    USER_LOCK(9002, "用户已被锁定，请联系管理员进行解锁！"),
    NOT_LOGIN(9992, "用户未登录！"),
    NOT_IDEMPOTENCE(9993, "请勿重复提交！"),
    ERROR_METHOD(9994, "请求方式错误！"),
    ERROR_PATH(9995, "请求路径错误！"),
    NOT_PERMISSION(9996, "权限不足！"),
    PARA_ERROR(9997, "参数错误！"),
    DATA_NOT_EXIST(9998, "数据不存在！"),
    ERROR(9999, "系统繁忙！"),

    /**
     * sso
     */
    ACCESS_TOKEN_ERROR(9800, "未获取到 AccessToken！"),
    NOT_FIND_DD_USER_INFO(9801, "未获取到钉钉用户信息！"),
    NOT_FIND_JSAPI_TOKEN(9802, "未获取到JSAPI鉴权信息！"),
    USER_SAVE_ERROR(9803, "用户保存失败！"),

    /**
     * 权限相关
     */
    THE_USER_NOT_EXIST(8000, "该用户账号不存在"),
    THE_USER_PASSWORD_ERROR(8001, "该用户账号密码不正确"),
    THE_USER_ACCOUNT_IS_EXIST(8002, "该用户账号已存在"),
    THE_ZWD_ID_IS_EXIST(8003, "该政务钉ID已存在"),
    THE_USER_ACCOUNT_CANT_DEL(8004, "登录用户不能删除自己账号"),
    THE_ROLE_IS_USED(8010, "该角色被关联用户使用"),
    THE_ROLE_NAME_IS_REPEAT(8011, "该角色名称已存在"),
    THE_ORG_IS_EXIST_UBJL(8020, "该机构下存在用编记录，无法删除"),
    THE_ORG_IS_EXIST_SON_ORG(8021, "该机构下存在下级机构，无法删除"),
    THE_ORG_LEVEL_IS_EXIST_BB_ORG(8023, "同一层级下已存在编办机构"),
    THE_USER_NO_PERMISSION_APPROVE(8022, "该用户无权审批!"),

    ;

    private int code;
    private String msg;

    BbResultEnum(int code, String msg) {
        this.code = code;
        this.msg = msg;
    }

    @Override
    public int getCode() {
        return code;
    }

    @Override
    public void setCode(int code) {
        this.code = code;
    }

    @Override
    public String getMsg() {
        return msg;
    }

    @Override
    public void setMsg(String msg) {
        this.msg = msg;
    }
}
