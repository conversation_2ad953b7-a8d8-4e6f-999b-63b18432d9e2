package com.zenith.bbykz.dao;

import cn.hutool.core.date.DateTime;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.zenith.bbykz.model.dto.UnifiedSocialCreditCodeDTO;
import com.zenith.bbykz.model.entity.UnifiedSocialCreditCode;
import com.zenith.bbykz.model.vo.UnifiedSocialCreditCodeVO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 机关事业单位统一社会信用代码服务 Mapper 接口
 *
 * <AUTHOR>
 * @since 2023-09-12
 */
@Mapper
public interface UnifiedSocialCreditCodeMapper extends BaseMapper<UnifiedSocialCreditCode> {

    UnifiedSocialCreditCode findLastVersion();

    void deleteByVersion(@Param("lastVersion") Integer lastVersion);

    void rollback(@Param("lastVersion") Integer lastVersion);

    void physicallyDeleteByVersion(@Param("version") Integer version);

    Page<UnifiedSocialCreditCodeVO> bdList(Page<UnifiedSocialCreditCodeVO> page,@Param("dto") UnifiedSocialCreditCodeDTO dto);

    List<UnifiedSocialCreditCode> findCertificateExpiration(@Param("version") Integer version, @Param("startDate")DateTime startDate,@Param("endDate") DateTime endDate);
}
