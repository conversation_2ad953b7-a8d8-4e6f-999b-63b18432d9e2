package com.zenith.bbykz.dao.bz.bzsq;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.zenith.bbykz.model.entity.bz.bzsq.BzBzsqMerge;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * <p>
 * 核编管理-用编审核-合并审核 持久层
 * </p>
 *
 * <AUTHOR>
 * @date 2024-04-03 15:26:58
 */
@Mapper
public interface BzBzsqMergeMapper extends BaseMapper<BzBzsqMerge> {

    void removeListById(@Param("id") String id);
}
