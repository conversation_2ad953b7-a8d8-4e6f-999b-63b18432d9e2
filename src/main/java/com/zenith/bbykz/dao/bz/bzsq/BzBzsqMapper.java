package com.zenith.bbykz.dao.bz.bzsq;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.zenith.bbykz.model.entity.bz.bzsq.BzBzsq;
import com.zenith.bbykz.model.vo.bz.bzsq.BzBzsqDownVO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * <p>
 * 核编管理-用编申请 持久层
 * </p>
 *
 * <AUTHOR>
 * @date 2024-03-29 16:05:19
 */
@Mapper
public interface BzBzsqMapper extends BaseMapper<BzBzsq> {

    BzBzsqDownVO downReplyInfo(@Param("id") String id);
}
