package com.zenith.bbykz.dao.bz.qx;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.zenith.bbykz.model.entity.bz.qx.BzQxBzsqDetail;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
* <p>
* 核编管理-用编管理-区县用编申请计划明细 持久层
* </p>
*
* <AUTHOR>
* @date 2024-05-06 17:05:40
*/
@Mapper
public interface BzQxBzsqDetailMapper extends BaseMapper<BzQxBzsqDetail> {

    void deleteByBizId(@Param("bizId") String bizId);
}
