package com.zenith.bbykz.dao.bz.qx;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.zenith.bbykz.model.entity.bz.qx.BzQxBzsq;
import com.zenith.bbykz.model.vo.bz.bzsq.BzBzsqDownVO;
import org.apache.ibatis.annotations.Mapper;

/**
* <p>
* 核编管理-用编管理-区县用编申请计划 持久层
* </p>
*
* <AUTHOR>
* @date 2024-05-06 17:05:40
*/
@Mapper
public interface BzQxBzsqMapper extends BaseMapper<BzQxBzsq> {

    BzBzsqDownVO downReplyInfo(String id);
}
