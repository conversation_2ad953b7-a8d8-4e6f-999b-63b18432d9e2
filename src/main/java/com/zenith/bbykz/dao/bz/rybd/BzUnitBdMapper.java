package com.zenith.bbykz.dao.bz.rybd;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.zenith.bbykz.model.dto.bz.rybd.BzUnitBdListDTO;
import com.zenith.bbykz.model.entity.bz.rybd.BzUnitBd;
import com.zenith.bbykz.model.vo.bz.rybd.BzUnitBdVO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
* <p>
* 核编管理-动态信息维护 持久层
* </p>
*
* <AUTHOR>
* @date 2024-04-22 11:21:43
*/
@Mapper
public interface BzUnitBdMapper extends BaseMapper<BzUnitBd> {

    Page<BzUnitBdVO> bdList(Page<BzUnitBdVO> voPage,@Param("dto") BzUnitBdListDTO dto);
}
