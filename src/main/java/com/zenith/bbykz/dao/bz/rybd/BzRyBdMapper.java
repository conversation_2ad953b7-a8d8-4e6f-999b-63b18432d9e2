package com.zenith.bbykz.dao.bz.rybd;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.zenith.bbykz.model.entity.bz.rybd.BzRyBd;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
* <p>
* 核编管理-动态信息维护-人员详情 持久层
* </p>
*
* <AUTHOR>
* @date 2024-04-22 11:21:43
*/
@Mapper
public interface BzRyBdMapper extends BaseMapper<BzRyBd> {

    void removeByBizId(@Param("bizId") String bizId);
}
