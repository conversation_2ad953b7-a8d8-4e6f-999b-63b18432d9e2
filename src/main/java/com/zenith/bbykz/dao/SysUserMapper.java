package com.zenith.bbykz.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.zenith.bbykz.model.dto.SysUserListDTO;
import com.zenith.bbykz.model.entity.OrgInfo;
import com.zenith.bbykz.model.entity.SysUser;
import com.zenith.bbykz.model.vo.SysUserVO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * sys_user 持久层
 * </p>
 *
 * <AUTHOR>
 * @date 2023-09-14 17:14:26
 */
@Mapper
public interface SysUserMapper extends BaseMapper<SysUser> {

    Page<SysUserVO> getPage(@Param("page") Page<Object> page, @Param("dto") SysUserListDTO dto);

    Page<SysUserVO> sysList(@Param("page") Page<Object> page, @Param("dto") SysUserListDTO dto);

    List<SysUser> findUserByDeptIdAndSystemId(@Param("unitId") String unitId, @Param("systemId") String currSystemId);

    OrgInfo findOrgInfoByUserAndSystemId(@Param("userId") String userId, @Param("systemId") String systemId);

    OrgInfo findOrgInfoByUserAndSystemIdAndPostId(@Param("userId") String userId, @Param("systemId") String systemId, @Param("userPostId") String userPostId);

    OrgInfo findBbUnitByUserId(@Param("userId") String userId,@Param("systemId")String systemId);

    void deleteUserAll();

    void deleteUserPostAll();

    void deleteUserRoleAll();

    void deleteUserSystemAll();
}
