package com.zenith.bbykz.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.zenith.bbykz.model.entity.SysUserRole;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * <p>
 * sys_user_role 持久层
 * </p>
 *
 * <AUTHOR>
 * @date 2023-09-14 17:14:26
 */
@Mapper
public interface SysUserRoleMapper extends BaseMapper<SysUserRole> {

    List<SysUserRole> getByIsDefault(Integer isDefault);

    List<SysUserRole> getList(String id);
}
