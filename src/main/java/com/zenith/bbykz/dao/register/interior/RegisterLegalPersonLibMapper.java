package com.zenith.bbykz.dao.register.interior;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.zenith.bbykz.model.entity.register.interior.RegisterLegalPersonLib;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * <p>
 * 登记信息-内部事务-法人库 持久层
 * </p>
 *
 * <AUTHOR>
 * @date 2024-03-12 14:55:18
 */
@Mapper
public interface RegisterLegalPersonLibMapper extends BaseMapper<RegisterLegalPersonLib> {

    RegisterLegalPersonLib findLastVersion();

    void deleteByVersion(@Param("lastVersion") Integer lastVersion);

    void rollback(@Param("lastVersion") Integer lastVersion);

    void physicallyDeleteByVersion(@Param("version") Integer version);
}
