package com.zenith.bbykz.dao.register.train;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.zenith.bbykz.model.entity.register.train.RegisterTrainType;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * <p>
 * 登记信息-培训管理-课程分类 持久层
 * </p>
 *
 * <AUTHOR>
 * @date 2024-03-05 15:05:10
 */
@Mapper
public interface RegisterTrainTypeMapper extends BaseMapper<RegisterTrainType> {

    void enabled(@Param("id") String id, @Param("enabled") Integer enabled);
}
