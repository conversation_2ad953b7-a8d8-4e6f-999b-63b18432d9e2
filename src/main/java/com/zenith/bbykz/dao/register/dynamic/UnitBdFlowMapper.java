package com.zenith.bbykz.dao.register.dynamic;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.zenith.bbykz.model.entity.register.dynamic.UnitBdFlow;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
* <p>
* 登记信息-变动流程记录 持久层
* </p>
*
* <AUTHOR>
* @date 2024-04-18 10:01:34
*/
@Mapper
public interface UnitBdFlowMapper extends BaseMapper<UnitBdFlow> {

    List<UnitBdFlow> findByBizId(@Param("bizId") String bizId);

    void deleteAll();

}
