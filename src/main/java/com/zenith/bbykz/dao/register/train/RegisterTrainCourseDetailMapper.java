package com.zenith.bbykz.dao.register.train;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.zenith.bbykz.model.entity.register.train.RegisterTrainCourseDetail;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * <p>
 * 登记信息-培训管理-课程管理关联课件 持久层
 * </p>
 *
 * <AUTHOR>
 * @date 2024-03-05 15:44:53
 */
@Mapper
public interface RegisterTrainCourseDetailMapper extends BaseMapper<RegisterTrainCourseDetail> {

    void deleteByCourseId(@Param("bizId") String bizId);
}
