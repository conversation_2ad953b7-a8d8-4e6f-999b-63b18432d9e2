package com.zenith.bbykz.dao.register.train;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.zenith.bbykz.model.dto.register.train.RegisterTrainCourseUserListDTO;
import com.zenith.bbykz.model.entity.register.train.RegisterTrainCourseUser;
import com.zenith.bbykz.model.vo.register.train.RegisterTrainCourseUserVO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * <p>
 * 登记信息-培训管理-用户课程 持久层
 * </p>
 *
 * <AUTHOR>
 * @date 2024-03-12 10:11:37
 */
@Mapper
public interface RegisterTrainCourseUserMapper extends BaseMapper<RegisterTrainCourseUser> {

    Page<RegisterTrainCourseUserVO> listByRole(@Param("dto") RegisterTrainCourseUserListDTO dto, Page<RegisterTrainCourseUserVO> page);
}
