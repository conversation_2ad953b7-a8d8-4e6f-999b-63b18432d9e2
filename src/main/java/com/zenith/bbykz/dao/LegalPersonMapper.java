package com.zenith.bbykz.dao;

import cn.hutool.core.date.DateTime;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.zenith.bbykz.model.dto.LegalPersonRegistrationListDTO;
import com.zenith.bbykz.model.entity.LegalPersonRegistration;
import com.zenith.bbykz.model.vo.LegalPersonRegistrationVO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 机关事业单位法人登记 Mapper 接口
 *
 * <AUTHOR>
 * @since 2023-09-12
 */
@Mapper
public interface LegalPersonMapper extends BaseMapper<LegalPersonRegistration> {
    LegalPersonRegistration findLastVersion();

    void deleteByVersion(@Param("lastVersion") Integer lastVersion);

    void rollback(@Param("lastVersion") Integer lastVersion);

    void physicallyDeleteByVersion(@Param("version") Integer version);

    Page<LegalPersonRegistrationVO> bdList(@Param("page") Page<LegalPersonRegistrationVO> page,@Param("dto") LegalPersonRegistrationListDTO dto);

    List<LegalPersonRegistration> findCertificateExpiration(@Param("version") Integer version, @Param("startDate")DateTime startDate,@Param("endDate") DateTime endDate);

    List<LegalPersonRegistration> findAllByVersion(@Param("version") Integer version);
}
