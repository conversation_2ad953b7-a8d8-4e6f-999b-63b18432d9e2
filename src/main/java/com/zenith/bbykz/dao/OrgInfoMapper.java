package com.zenith.bbykz.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.zenith.bbykz.model.dto.OrgInfoListDTO;
import com.zenith.bbykz.model.entity.OrgInfo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * org_info 持久层
 * </p>
 *
 * <AUTHOR>
 * @date 2023-09-14 17:51:37
 */
@Mapper
public interface OrgInfoMapper extends BaseMapper<OrgInfo> {

    OrgInfo getOrgByParentCode(String parentCode);

    OrgInfo getOrgByParentCodeAndIsBbOrg(String parentCode, String id);

    Page<OrgInfo> getPage(@Param("page") Page<OrgInfo> page, @Param("dto") OrgInfoListDTO dto);

    OrgInfo findLastUnitByLength(@Param("unitLevelCount") Integer unitLevelCount);

    OrgInfo findLastUnitByParentId(@Param("parentId") String parentId);

    String getBelongById(@Param("id") String id);

    OrgInfo getByOrgCode(@Param("organizationCode") String organizationCode);

    OrgInfo getUnitByDeptId(@Param("deptId") String deptId);

    List<OrgInfo> selectTree(@Param("levelCode") String levelCode, @Param("userGroupOrgId") String userGroupOrgId);

    OrgInfo findTop();

    void deleteAll();

    void flushErrorLevelCode();

    List<OrgInfo> findAllSy();

}
