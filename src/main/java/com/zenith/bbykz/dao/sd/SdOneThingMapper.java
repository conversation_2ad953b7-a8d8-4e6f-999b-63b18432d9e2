package com.zenith.bbykz.dao.sd;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.zenith.bbykz.model.dto.sd.SdOneThingListDTO;
import com.zenith.bbykz.model.entity.sd.SdOneThing;
import com.zenith.bbykz.model.vo.sd.SdBusinessItemOneVO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * <p>
 * 三定-一件事 持久层
 * </p>
 *
 * <AUTHOR>
 * @date 2024-04-11 16:17:20
 */
@Mapper
public interface SdOneThingMapper extends BaseMapper<SdOneThing> {

    Page<SdBusinessItemOneVO> oneItemList(Page<SdBusinessItemOneVO> page,@Param("dto") SdOneThingListDTO dto);
}
