package com.zenith.bbykz.dao.sd;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.zenith.bbykz.model.dto.sd.SdOrgRelationListDTO;
import com.zenith.bbykz.model.entity.sd.SdOrgRelation;
import com.zenith.bbykz.model.vo.sd.SdSearchVO;
import com.zenith.bbykz.model.vo.sd.SdSqlVO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 三定-三定机构关联关系 持久层
 * </p>
 *
 * <AUTHOR>
 * @date 2024-04-11 16:17:20
 */
@Mapper
public interface SdOrgRelationMapper extends BaseMapper<SdOrgRelation> {

    List<SdSqlVO> findAll(@Param("unitId") String unitId);

    Page<SdSearchVO> dutyList(Page<SdSearchVO> page,@Param("dto") SdOrgRelationListDTO dto);
}
