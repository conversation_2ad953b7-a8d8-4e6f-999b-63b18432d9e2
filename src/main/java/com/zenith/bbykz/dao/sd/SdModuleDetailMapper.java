package com.zenith.bbykz.dao.sd;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.zenith.bbykz.model.dto.sd.SdModuleDetailListDTO;
import com.zenith.bbykz.model.entity.sd.SdModuleDetail;
import com.zenith.bbykz.model.vo.sd.SdModuleDetailVO;
import com.zenith.bbykz.model.vo.sd.SdSearchVO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * <p>
 * 三定-三定明细 持久层
 * </p>
 *
 * <AUTHOR>
 * @date 2024-04-11 16:17:20
 */
@Mapper
public interface SdModuleDetailMapper extends BaseMapper<SdModuleDetail> {

    int countByUnitId(@Param("unitId") String unitId);

    Page<SdSearchVO> searchList(Page<SdSearchVO> page, @Param("dto") SdModuleDetailListDTO dto);
}
