package com.zenith.bbykz.dao.sd;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.zenith.bbykz.model.dto.sd.SdModuleCountDTO;
import com.zenith.bbykz.model.entity.sd.SdModule;
import com.zenith.bbykz.model.vo.sd.SdModuleCountVO;
import com.zenith.bbykz.model.vo.sd.SdModuleTypeCountVO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 三定-三定类型 持久层
 * </p>
 *
 * <AUTHOR>
 * @date 2024-04-11 16:17:20
 */
@Mapper
public interface SdModuleMapper extends BaseMapper<SdModule> {

    int countByUnitId(@Param("unitId") String unitId);

    Integer analysisModuleCount(@Param("dto") SdModuleCountDTO dto);

    Integer analysisInvolveUnitCount(@Param("dto") SdModuleCountDTO dto);

    List<SdModuleTypeCountVO> analysisTypeCountList(@Param("dto") SdModuleCountDTO dto);

    Integer analysisDutyDetailCount(@Param("dto") SdModuleCountDTO dto);
}
