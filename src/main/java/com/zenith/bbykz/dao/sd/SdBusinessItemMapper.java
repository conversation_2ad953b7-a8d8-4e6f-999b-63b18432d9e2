package com.zenith.bbykz.dao.sd;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.zenith.bbykz.model.dto.sd.SdCoreBusinessListDTO;
import com.zenith.bbykz.model.entity.sd.SdBusinessItem;
import com.zenith.bbykz.model.vo.sd.SdCoreBusinessVO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * <p>
 * 三定-业务事项 持久层
 * </p>
 *
 * <AUTHOR>
 * @date 2024-04-11 16:17:20
 */
@Mapper
public interface SdBusinessItemMapper extends BaseMapper<SdBusinessItem> {

    int countByUnitId(@Param("unitId") String unitId);

    int countByCoreBusinessId(@Param("coreBusinessId") String coreBusinessId);

    Page<SdCoreBusinessVO> coreList(Page<SdCoreBusinessVO> page, @Param("dto")SdCoreBusinessListDTO dto);
}
