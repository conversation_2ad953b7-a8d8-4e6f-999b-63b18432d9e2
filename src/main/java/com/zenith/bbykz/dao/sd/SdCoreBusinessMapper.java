package com.zenith.bbykz.dao.sd;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.zenith.bbykz.model.entity.sd.SdCoreBusiness;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * <p>
 * 三定-核心业务 持久层
 * </p>
 *
 * <AUTHOR>
 * @date 2024-04-11 16:17:20
 */
@Mapper
public interface SdCoreBusinessMapper extends BaseMapper<SdCoreBusiness> {

    int countByUnitId(@Param("unitId") String unitId);
}
