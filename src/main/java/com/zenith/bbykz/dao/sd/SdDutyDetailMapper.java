package com.zenith.bbykz.dao.sd;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.zenith.bbykz.model.dto.sd.SdDutyDetailListDTO;
import com.zenith.bbykz.model.entity.sd.SdDutyDetail;
import com.zenith.bbykz.model.vo.sd.SdDutyDetailVO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * <p>
 * 三定-细化职责 持久层
 * </p>
 *
 * <AUTHOR>
 * @date 2024-04-11 16:17:19
 */
@Mapper
public interface SdDutyDetailMapper extends BaseMapper<SdDutyDetail> {

    int countByUnitId(@Param("unitId") String unitId);

    Page<SdDutyDetailVO> searchList(Page<SdDutyDetailVO> page,@Param("dto") SdDutyDetailListDTO dto);
}
