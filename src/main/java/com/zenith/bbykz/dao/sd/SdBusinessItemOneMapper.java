package com.zenith.bbykz.dao.sd;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.zenith.bbykz.model.dto.sd.SdOrgRelationListDTO;
import com.zenith.bbykz.model.entity.sd.SdBusinessItemOne;
import com.zenith.bbykz.model.vo.sd.SdOrgRelationVO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * <p>
 * 三定-业务事项一件事关联表 持久层
 * </p>
 *
 * <AUTHOR>
 * @date 2024-04-11 16:17:20
 */
@Mapper
public interface SdBusinessItemOneMapper extends BaseMapper<SdBusinessItemOne> {

    int countByUnitId(@Param("unitId") String unitId);

    void deleteByItemId(@Param("itemId") String itemId);

    Page<SdOrgRelationVO> list(@Param("page") Page<SdOrgRelationVO> page, @Param("dto") SdOrgRelationListDTO dto);
}
