package com.zenith.bbykz.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.zenith.bbykz.model.entity.Geocode;
import com.zenith.bbykz.model.entity.OrgInfo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 区划代码 持久层
 * </p>
 *
 * <AUTHOR>
 * @date 2024-03-13 09:52:37
 */
@Mapper
public interface GeocodeMapper extends BaseMapper<Geocode> {

    List<Geocode> getListByStartWithAndEndWith(@Param("startCode") String cqCodeStart,@Param("endCode")  String cqCodeEnd);

    List<Geocode> getListByStartWith(@Param("type") String type,@Param("geoType") String geoType);

    List<OrgInfo> findBbList(@Param("geoCode") String geoCode,@Param("jgsySystemCode") String jgsySystemCode);
}
