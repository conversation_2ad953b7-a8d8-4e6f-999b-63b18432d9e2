package com.zenith.bbykz.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.zenith.bbykz.model.entity.SysRole;
import com.zenith.bbykz.model.vo.SysMenuVO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * sys_role 持久层
 * </p>
 *
 * <AUTHOR>
 * @date 2023-09-14 17:14:26
 */
@Mapper
public interface SysRoleMapper extends BaseMapper<SysRole> {

    List<SysMenuVO> findMenuListByRoleId(@Param("roleId") String roleId, @Param("systemCode") String systemCode);
}
