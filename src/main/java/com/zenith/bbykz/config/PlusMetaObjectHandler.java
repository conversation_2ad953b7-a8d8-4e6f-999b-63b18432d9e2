//package com.zenith.bbykz.config;
//
//import com.baomidou.mybatisplus.core.handlers.MetaObjectHandler;
//import com.efficient.common.auth.RequestHolder;
//import com.efficient.common.auth.UserTicket;
//import com.zenith.bbykz.model.base.UserInfo;
//import lombok.extern.slf4j.Slf4j;
//import org.apache.ibatis.reflection.MetaObject;
//import org.springframework.stereotype.Component;
//
//import java.util.Date;
//import java.util.Objects;
//
///**
// * <AUTHOR>
// * @date 2023/9/18 17:00
// */
//@Component
//@Slf4j
//public class PlusMetaObjectHandler implements MetaObjectHandler {
//    public static String createUser() {
//        return RequestHolder.getCurrUser().getUserId();
//    }
//    public static UserInfo getUserInfo() {
//        return (UserInfo) RequestHolder.getCurrUser();
//    }
//
//    public static String getUserLevelCode() {
//        UserInfo currUser = (UserInfo) RequestHolder.getCurrUser();
//        if(Objects.isNull(currUser)){
//            return null;
//        }
//        return currUser.getLevelCode();
//    }
//
//
//    @Override
//    public void insertFill(MetaObject metaObject) {
//        this.strictInsertFill(metaObject, "createTime", Date.class, new Date());
//        UserTicket currUser = RequestHolder.getCurrUser();
//        if (Objects.nonNull(currUser)) {
//            this.strictInsertFill(metaObject, "createUser", String.class, currUser.getUserId());
//        }
//    }
//
//    @Override
//    public void updateFill(MetaObject metaObject) {
//        this.setFieldValByName("updateTime", new Date(), metaObject);
//        UserTicket currUser = RequestHolder.getCurrUser();
//        if (Objects.nonNull(currUser)) {
//            this.strictInsertFill(metaObject, "updateUser", String.class, currUser.getUserId());
//        }
//    }
//}
