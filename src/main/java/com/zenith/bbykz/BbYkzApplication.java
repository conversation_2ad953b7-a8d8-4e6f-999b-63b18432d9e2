package com.zenith.bbykz;

import com.efficient.configs.config.UniqueNameGenerator;
import org.mybatis.spring.annotation.MapperScan;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.ComponentScan;

/**
 * 启动类
 */
@SpringBootApplication
@ComponentScan(basePackages = {"com.efficient", "com.zenith.bbykz"}, nameGenerator = UniqueNameGenerator.class)
@MapperScan(basePackages = {"com.zenith.bbykz.dao"}, nameGenerator = UniqueNameGenerator.class)
public class BbYkzApplication {

    public static void main(String[] args) {
        SpringApplication.run(BbYkzApplication.class, args);
    }

}
