spring:
  main:
    allow-circular-references: true
    allow-bean-definition-overriding: true
  profiles:
    active: @env@
  application:
    name: bb_ykz
  servlet:
    multipart:
      # 上传单个文件限制
      max-file-size: 100MB
      # 总文件大小，允许存储的文件夹大小
      max-request-size: 10240MB

#jta:
#  log-dir: /home/<USER>/server/logs/jta/
#  atomikos:
#    properties:
#      default-jta-timeout: 3000000
#      max-actives: -1
#      max-timeout: 600000


  mvc:
    servlet:
      load-on-startup: 0
    #出现错误时, 直接抛出异常
    throw-exception-if-no-handler-found: true
    path-match:
      matching-strategy: ant_path_matcher
  web:
    resources:
      add-mappings: true
  datasource:
    type: com.zaxxer.hikari.HikariDataSource
    url: @datasource.url@
    username: @datasource.username@
    password: @datasource.password@
    driver-class-name: @datasource.driver@
    # 连接池
    hikari:
      #连接池名
      pool-name: DateHikariCP
      #最小空闲连接数
      minimum-idle: 10
      # 空闲连接存活最大时间，默认600000（10分钟）
      idle-timeout: 180000
      # 连接池最大连接数，默认是10
      maximum-pool-size: 200
      # 此属性控制从池返回的连接的默认自动提交行为,默认值：true
      auto-commit: true
      # 此属性控制池中连接的最长生命周期，值0表示无限生命周期，默认1800000即30分钟
      max-lifetime: 1800000
      # 数据库连接超时时间,默认30秒，即30000
      connection-timeout: 30000
#      connection-test-query: SELECT 1

mybatis-plus:
  mapper-locations: classpath*:mapper/**/*Mapper.xml
  global-config:
    db-config:
      id-type: ASSIGN_ID
      logic-delete-value: 1 # 逻辑已删除值(默认为 1)
      logic-not-delete-value: 0 # 逻辑未删除值(默认为 0)
  configuration:
    map-underscore-to-camel-case: true
    cache-enabled: false
    jdbc-type-for-null: 'null'
    # 这个配置会将执行的sql打印出来，在开发或测试的时候可以用
    #log-impl: org.apache.ibatis.logging.stdout.StdOutImpl

# 愉快政接入配置
uc:
  zwd: # -政务钉钉
    appKey: jgbzjg-zgcqswjgbzwyhbgs-4Ut46MroEqJG7y2jG571FWDYH9wGbBK4xOVRJneq
    appSecret: pt5zmn71EO79jClC5GbHJmvZ3ZAfK5E84p3uKmFW
    domainName: zd-openplatform.bigdatacq.com
  user-center: # -用户中心
    appId: ce3ccec62ff888a3b63c48192b1ae19f
    appSecret: ce3ccec62ff888a38d221ff998c8036e
    url: https://uc-openplatform.bigdatacq.com:4403 # 用户中心地址
    accessToken: ${uc.user-center.url}/ykz/access_token # 获取token
    getByAccountId: ${uc.user-center.url}/ykz/user/getByAccountId # 根据用户政钉ID查询用户信息
    listUserPostByAccountId: ${uc.user-center.url}/ykz/user/listUserPostByAccountId # 根据用户ID查询用户职位信息
    getByOrganizationCode: ${uc.user-center.url}/ykz/org/getByOrganizationCode # 根据组织机构code查询详细信息
    listByOrganizationCodes: ${uc.user-center.url}/ykz/org/listByOrganizationCodes # 根据组织机构code批量查询详细信息
  jwt: # 证书，uc给的公钥
    publicKeyCerPath: bb.jwt.sm2.cer