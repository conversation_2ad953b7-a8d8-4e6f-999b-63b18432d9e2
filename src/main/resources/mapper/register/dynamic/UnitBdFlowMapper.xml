<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.zenith.bbykz.dao.register.dynamic.UnitBdFlowMapper">
    <delete id="deleteAll">
        delete from register_unit_bd_flow
    </delete>


    <select id="findByBizId" resultType="com.zenith.bbykz.model.entity.register.dynamic.UnitBdFlow">
        select min(id) as id, min(code) as code, min(describe) as describe,min(create_time) as create_time
        from register_unit_bd_flow
        where register_unit_bd_flow.biz_id = #{bizId}
        group by code
        order by code
    </select>
</mapper>

