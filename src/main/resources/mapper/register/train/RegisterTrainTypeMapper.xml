<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.zenith.bbykz.dao.register.train.RegisterTrainTypeMapper">


    <update id="enabled">
        WITH RECURSIVE Subordinates AS (SELECT id, parent_id
                                        FROM register_train_type
                                        WHERE id = #{id}
                                        UNION
                                        SELECT rt.id, rt.parent_id
                                        FROM register_train_type rt
                                                 JOIN Subordinates s ON rt.parent_id = s.id)
        UPDATE register_train_type
        SET enabled = #{enabled}
        WHERE id IN (SELECT id FROM Subordinates);
    </update>
</mapper>

