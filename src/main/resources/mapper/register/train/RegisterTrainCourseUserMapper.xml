<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.zenith.bbykz.dao.register.train.RegisterTrainCourseUserMapper">


    <select id="listByRole" resultType="com.zenith.bbykz.model.vo.register.train.RegisterTrainCourseUserVO">
        select register_train_course.name as courseName,
        register_train_course.create_user as publisherAccount,
        register_train_course.id as courseId,
        register_train_course.deadline as deadline,
        register_train_course_user.*
        FROM register_train_course
        left join register_train_course_user on register_train_course.id = register_train_course_user.course_id
        and (register_train_course_user.user_id is null or
        register_train_course_user.user_id = #{dto.userId})
        WHERE register_train_course.is_delete = 0
        <if test="dto.roleId != null and dto.roleId != ''">
            and #{dto.roleId}::TEXT = ANY(string_to_array(scope_id, ','))
        </if>
        and register_train_course.is_delete = 0
        <if test="dto.isFinish != null ">
            and register_train_course_user.is_finish = #{dto.isFinish}
        </if>
        <if test="dto.isAttention != null ">
            and register_train_course_user.is_attention = #{dto.isAttention}
        </if>
        order by register_train_course.create_time desc
    </select>
</mapper>

