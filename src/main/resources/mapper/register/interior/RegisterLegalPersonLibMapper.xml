<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.zenith.bbykz.dao.register.interior.RegisterLegalPersonLibMapper">
    <update id="rollback">
        update register_legal_person_lib
        set is_delete   = 0,
            update_time = now()
        where version = #{lastVersion}
    </update>
    <delete id="deleteByVersion">
        update register_legal_person_lib
        set is_delete   = 1,
            update_time = now()
        where version = #{lastVersion}
    </delete>
    <delete id="physicallyDeleteByVersion">
        delete
        from register_legal_person_lib
        where version = #{version}
    </delete>


    <select id="findLastVersion"
            resultType="com.zenith.bbykz.model.entity.register.interior.RegisterLegalPersonLib">
        select *
        from register_legal_person_lib
        where is_delete = 0
        order by version desc limit 1
    </select>
</mapper>

