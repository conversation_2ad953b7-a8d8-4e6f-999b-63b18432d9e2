<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.zenith.bbykz.dao.bz.bzsq.BzBzsqMapper">


    <select id="downReplyInfo" resultType="com.zenith.bbykz.model.vo.bz.bzsq.BzBzsqDownVO">
        select min(bz_bzsq.id)               as id,
               min(bz_bzsq.approval_num)     as approval_num,
               min(bz_bzsq.org_id)           as org_id,
               min(bz_bzsq.apply_unit_name)  as apply_unit_name,
               min(bz_bzsq.use_bz_unit_name) as use_bz_unit_name,
               sum(bz_bzsq_detail.apply_num) as apply_num,
               max(bz_bzsq_flow.create_user) as auditUserId,
               max(bz_bzsq_flow.create_time) as auditTime

        from bz_bzsq
                 left join bz_bzsq_detail on bz_bzsq.id = bz_bzsq_detail.bzsq_id
                 left join bz_bzsq_flow on bz_bzsq.id = bz_bzsq_flow.bzsq_id
        where bz_bzsq.id = #{id}
          and bz_bzsq_detail.is_back = 0
          and bz_bzsq_detail.is_delete = 0
          and bz_bzsq_flow.is_finish = 1
        group by bz_bzsq_flow.create_time
        order by bz_bzsq_flow.create_time desc limit 1
    </select>
</mapper>

