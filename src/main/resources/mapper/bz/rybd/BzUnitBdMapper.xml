<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.zenith.bbykz.dao.bz.rybd.BzUnitBdMapper">


    <select id="bdList" resultType="com.zenith.bbykz.model.vo.bz.rybd.BzUnitBdVO">
        select
        bz_unit_bd.id as id,
        min(bz_unit_bd.title) as title,
        min(bz_unit_bd.unit_id) as unit_id,
        min(bz_unit_bd.unit_level_code) as unit_level_code,
        min(bz_unit_bd.unit_name) as unit_name,
        min(bz_unit_bd.credit_code) as credit_code,
        min(bz_unit_bd.bd_type) as bd_type,
        min(bz_unit_bd.remark) as remark,
        min(bz_unit_bd.change_time) as change_time
        from bz_unit_bd
        left join bz_ry_bd on bz_unit_bd.id= bz_ry_bd.unit_bd_id
        where bz_ry_bd.is_delete = 0
        <if test="dto.unitLevelCode != null and dto.unitLevelCode != '' ">
           and bz_unit_bd.unit_level_code like concat(#{dto.unitLevelCode},'%')
        </if>
        <if test="dto.pageType != null and dto.pageType == '1'.toString() ">
            and bz_ry_bd.bd_type = '01'
        </if>
        <if test="dto.pageType != null and dto.pageType == '2'.toString() ">
            and bz_ry_bd.bd_type = '0301'
        </if>
        <if test="dto.pageType != null and dto.pageType == '3'.toString() ">
            and bz_ry_bd.bd_type = '02'
        </if>
        <if test="dto.keyword != null and dto.keyword != '' ">
            and bz_unit_bd.title like concat('',#{dto.keyword},'')
        </if>
        group by bz_unit_bd.id
        order by min(bz_unit_bd.create_time) desc
    </select>
</mapper>

