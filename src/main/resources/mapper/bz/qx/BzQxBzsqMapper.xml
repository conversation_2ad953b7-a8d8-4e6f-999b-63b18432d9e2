<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.zenith.bbykz.dao.bz.qx.BzQxBzsqMapper">


    <select id="downReplyInfo" resultType="com.zenith.bbykz.model.vo.bz.bzsq.BzBzsqDownVO">
        select min(bz_qx_bzsq.id)               as id,
               min(bz_qx_bzsq.approval_num)     as approval_num,

               min(bz_qx_bzsq.apply_unit_name)  as apply_unit_name,
               min(bz_qx_bzsq.apply_unit_name)  as use_bz_unit_name,
               sum(bz_qx_bzsq_detail.apply_num) as apply_num

        from bz_qx_bzsq
                 left join bz_qx_bzsq_detail on bz_qx_bzsq.id = bz_qx_bzsq_detail.qx_bzsq_id

        where bz_qx_bzsq.id = #{id}
          and bz_qx_bzsq.is_delete = 0
          and bz_qx_bzsq_detail.is_delete = 0
          and bz_qx_bzsq.status = '5'
        group by bz_qx_bzsq.id

    </select>
</mapper>

