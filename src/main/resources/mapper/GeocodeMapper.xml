<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.zenith.bbykz.dao.GeocodeMapper">


    <select id="getListByStartWithAndEndWith" resultType="com.zenith.bbykz.model.entity.Geocode">
        select *
        from GEOCODE
        where code like  CONCAT(#{startCode}, '%', #{endCode})
        ORDER BY sort;
    </select>
    <select id="getListByStartWith" resultType="com.zenith.bbykz.model.entity.Geocode">
        select *
        from GEOCODE
        where type in(#{type},#{geoType})
        ORDER BY sort;
    </select>
    <select id="findBbList" resultType="com.zenith.bbykz.model.entity.OrgInfo">
        select * from org_info where is_bb_org = '1' and geoCode = #{geoCode} and jgsy_system_code = #{jgsySystemCode}
    </select>
</mapper>

