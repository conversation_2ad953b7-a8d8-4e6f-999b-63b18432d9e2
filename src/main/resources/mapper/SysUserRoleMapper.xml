<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.zenith.bbykz.dao.SysUserRoleMapper">


    <select id="getByIsDefault" resultType="com.zenith.bbykz.model.entity.SysUserRole">
        select sys_user_role.*
        from sys_user_role
                 inner join sys_role on sys_user_role.role_id = sys_role.id
        where sys_role.is_default = #{isDefault}
    </select>
    <select id="getList" resultType="com.zenith.bbykz.model.entity.SysUserRole">
        select sur.*
        from sys_user_role sur
                 inner join sys_user su on su.id = sur.user_id
        where su.is_delete = 0
          and sur.role_id = #{id}
    </select>
</mapper>

