<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.zenith.bbykz.dao.SysRoleMapper">


    <select id="findMenuListByRoleId" resultType="com.zenith.bbykz.model.vo.SysMenuVO">
        select sys_menu.*
        from sys_menu
                 inner join sys_role_menu
                            on sys_menu.id = sys_role_menu.menu_id
        where sys_role_menu.role_id = #{roleId}
        order by sys_menu.code
    </select>
</mapper>

