<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.zenith.bbykz.dao.SysUserMapper">
    <delete id="deleteUserAll">
        delete from sys_user where id != '1'
    </delete>
    <delete id="deleteUserPostAll">
        delete from sys_user_post where user_id != '1'
    </delete>
    <delete id="deleteUserRoleAll">
        delete from sys_user_role where user_id != '1'
    </delete>
    <delete id="deleteUserSystemAll">
        delete from sys_user_system where user_id != '1'
    </delete>


    <select id="getPage" resultType="com.zenith.bbykz.model.vo.SysUserVO">
        select sys_user.*,
        sys_user_post.id as userPostId,
        sys_user_system.id as userSystemId,
        org_info.name as orgName
        from sys_user
        left join sys_user_post on sys_user.id = sys_user_post.user_id
        left join sys_user_system on sys_user_system.user_post_id = sys_user_post."id" and sys_user_system.is_delete = 0
        left join org_info on sys_user_post.dept_id = org_info.id
        where sys_user.is_delete = 0 and is_builtin = 0
        <if test="dto.userId != null and dto.userId != ''">
            and sys_user.id != #{dto.userId}
        </if>
        <if test="dto.systemId != null and dto.systemId != ''">
            and sys_user_system.system_id = #{dto.systemId}
        </if>
        <if test="dto.subordinate != null and dto.subordinate == 1">
            AND org_info.level_code LIKE concat(#{dto.orgCode}, '%')
        </if>
        <if test="dto.subordinate != null and dto.subordinate == 0">
            and (org_info.level_code = #{dto.orgCode} )
        </if>
        <if test="dto.phone != null and dto.phone != ''">
            and sys_user.phone like concat(#{dto.phone}, '%')
        </if>
        <if test="dto.account != null and dto.account != ''">
            and sys_user.account like concat(#{dto.account}, '%')
        </if>
        <if test="dto.neUserIds != null and dto.neUserIds.size > 0">
            and sys_user.id not in
            <foreach collection="dto.neUserIds" item="id" separator="," open="(" close=")">
                #{id}
            </foreach>
        </if>
        order by sys_user.id,sys_user_post.id
    </select>
    <select id="sysList" resultType="com.zenith.bbykz.model.vo.SysUserVO">
        select sys_user.*,
        sys_user_post.id as userPostId,
        org_info.name as orgName
        from sys_user
        left join sys_user_post on sys_user.id = sys_user_post.user_id
        left join org_info on sys_user_post.dept_id = org_info.id
        where sys_user.is_delete = 0 and is_builtin = 0
        <if test="dto.subordinate != null and dto.subordinate == 1">
            AND org_info.level_code LIKE concat(#{dto.orgCode}, '%')
        </if>
        <if test="dto.subordinate != null and dto.subordinate == 0">
            and (org_info.level_code = #{dto.orgCode} )
        </if>
        <if test="dto.phone != null and dto.phone != ''">
            and sys_user.phone like concat(#{dto.phone}, '%')
        </if>
        <if test="dto.account != null and dto.account != ''">
            and sys_user.account like concat(#{dto.account}, '%')
        </if>
        <if test="dto.neUserIds != null and dto.neUserIds.size > 0">
            and sys_user.id not in
            <foreach collection="dto.neUserIds" item="id" separator="," open="(" close=")">
                #{id}
            </foreach>
        </if>
        order by sys_user.id,sys_user_post.id
    </select>
    <select id="findUserByDeptIdAndSystemId" resultType="com.zenith.bbykz.model.entity.SysUser">
        select sys_user.*
        from sys_user
                 left join sys_user_post sup on sys_user.id = sup.user_id
                 left join sys_user_system sus on sup.id = sus.user_post_id
        where sus.id is not null
          and sus.system_id = #{systemId}
          and sup.dept_id = #{unitId}
          and sys_user.is_delete = 0
        order by sys_user.id
    </select>
    <select id="findOrgInfoByUserAndSystemId" resultType="com.zenith.bbykz.model.entity.OrgInfo">
        select oi.*
        from sys_user su
                 left join sys_user_post sup on su.id = sup.user_id
                 left join sys_user_system sus on sup.id = sus.user_post_id
                 left join org_info oi on sup.dept_id = oi.id
        where su.is_delete = 0
          and oi.is_delete = 0
          and sus.system_id = #{systemId}
          and su.id = #{userId}
        order by su.id limit 1
    </select>
    <select id="findOrgInfoByUserAndSystemIdAndPostId" resultType="com.zenith.bbykz.model.entity.OrgInfo">
        select oi.*
        from sys_user su
                 left join sys_user_post sup on su.id = sup.user_id
                 left join sys_user_system sus on sup.id = sus.user_post_id
                 left join org_info oi on sup.dept_id = oi.id
        where su.is_delete = 0
          and oi.is_delete = 0
          and sus.system_id = #{systemId}
          and su.id = #{userId}
          and sup.id = #{userPostId}
        order by su.id limit 1
    </select>
    <select id="findBbUnitByUserId" resultType="com.zenith.bbykz.model.entity.OrgInfo">
        select org_info.*
        from sys_user_system
                 left join sys_user_post on sys_user_system.user_post_id = sys_user_post.id
                 left join org_info on org_info.id = sys_user_post.dept_id
        where sys_user_system.user_id = #{userId}
          and sys_user_system.system_id = #{systemId}
          and sys_user_system.is_bb_user = '1'
        order by sys_user_system.id limit 1
    </select>
</mapper>

