<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.zenith.bbykz.dao.UnifiedSocialCreditCodeMapper">

    <delete id="deleteByVersion">
        update unified_social_credit_code
        set is_delete   = 1,
            delete_time = now()
        where version = #{lastVersion}
    </delete>


    <select id="findLastVersion" resultType="com.zenith.bbykz.model.entity.UnifiedSocialCreditCode">
        select *
        from unified_social_credit_code
        where is_delete = 0
        order by version desc limit 1
    </select>
    <select id="bdList" resultType="com.zenith.bbykz.model.vo.UnifiedSocialCreditCodeVO">
        select unified_social_credit_code.* ,
        register_unit_bd.id as bdId
        from register_unit_bd
        left join unified_social_credit_code on register_unit_bd.credit_code = unified_social_credit_code.code
        where unified_social_credit_code.code is not null
        and register_unit_bd.unit_level_code like concat(#{dto.unitLevelCode},'%')
        and unified_social_credit_code.is_delete = 0 and register_unit_bd.state = '5'
        <if test="dto.unifiedSocialCreditCode != null and dto.unifiedSocialCreditCode != ''">
            and unified_social_credit_code.code like concat('%',#{dto.unifiedSocialCreditCode},'%')
        </if>
        <if test="dto.keyword != null and dto.keyword != ''">
            and unified_social_credit_code.name like concat('%',#{dto.keyword},'%')
        </if>
        order by register_unit_bd.create_time desc
    </select>
    <select id="findCertificateExpiration" resultType="com.zenith.bbykz.model.entity.UnifiedSocialCreditCode">
        select *
        from unified_social_credit_code
        where is_delete = 0
          and version = #{version}
          and certificate_expiration_time !=  ''
        and cast(certificate_expiration_time as date) &lt;= #{endDate}
          and cast (certificate_expiration_time as date) &gt;= #{startDate}
    </select>

    <update id="rollback">
        update unified_social_credit_code
        set is_delete   = 0,
            uptime_time = now()
        where version = #{lastVersion}
    </update>
    <delete id="physicallyDeleteByVersion">
        delete
        from unified_social_credit_code
        where version = #{version}
    </delete>
</mapper>

