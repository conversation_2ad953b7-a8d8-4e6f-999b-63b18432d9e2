<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.zenith.bbykz.dao.LegalPersonMapper">
    <update id="rollback">
        update legal_person_registration
        set is_delete   = 0,
            uptime_time = now()
        where version = #{lastVersion}
    </update>
    <delete id="deleteByVersion">
        update legal_person_registration
        set is_delete   = 1,
            delete_time = now()
        where version = #{lastVersion}
    </delete>
    <delete id="physicallyDeleteByVersion">
        delete
        from legal_person_registration
        where version = #{version}
    </delete>


    <select id="findLastVersion" resultType="com.zenith.bbykz.model.entity.LegalPersonRegistration">
        select version
        from legal_person_registration
        where is_delete = 0
        order by version desc limit 1
    </select>
    <select id="bdList" resultType="com.zenith.bbykz.model.vo.LegalPersonRegistrationVO">
        select legal_person_registration.* ,
        register_unit_bd.id as bdId
        from register_unit_bd
        left join legal_person_registration on register_unit_bd.credit_code =
        legal_person_registration.unified_social_credit_code
        where legal_person_registration.unified_social_credit_code is not null
        and register_unit_bd.unit_level_code like concat(#{dto.unitLevelCode},'%')
        and legal_person_registration.is_delete = 0 and register_unit_bd.state = '5'
        <if test="dto.unifiedSocialCreditCode != null and dto.unifiedSocialCreditCode != ''">
            and legal_person_registration.unified_social_credit_code like concat('%',#{dto.unifiedSocialCreditCode},'%')
        </if>
        <if test="dto.keyword != null and dto.keyword != ''">
            and legal_person_registration.name like concat('%',#{dto.keyword},'%')
        </if>
        order by register_unit_bd.create_time desc
    </select>
    <select id="findCertificateExpiration" resultType="com.zenith.bbykz.model.entity.LegalPersonRegistration">
        select *
        from legal_person_registration
        where is_delete = 0
          and version = #{version}
          and STATUS = '正常'
          and  cast(certificate_validity_end_time as date) &lt;= #{endDate}
          and  cast(certificate_validity_end_time as date) &gt;= #{startDate}
        order by id
    </select>
    <select id="findAllByVersion" resultType="com.zenith.bbykz.model.entity.LegalPersonRegistration">
        select * from legal_person_registration   where is_delete = 0 and version = #{version} and STATUS = '正常' order by id
    </select>
</mapper>

