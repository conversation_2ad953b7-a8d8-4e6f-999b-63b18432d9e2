<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.zenith.bbykz.dao.sd.SdCoreBusinessMapper">


    <select id="countByUnitId" resultType="java.lang.Integer">
        select count(*)
        from sd_module_detail
                 left join sd_core_business on sd_module_detail.id = sd_core_business.module_detail_id
        where sd_module_detail.is_delete = 0
          and sd_core_business.is_delete = 0
          and sd_module_detail.unit_id = #{unitId}
    </select>
</mapper>

