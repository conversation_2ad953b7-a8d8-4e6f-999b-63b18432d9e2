<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.zenith.bbykz.dao.sd.SdBusinessItemMapper">


    <select id="countByUnitId" resultType="java.lang.Integer">
        select count(*)
        from sd_module_detail
                 left join sd_business_item on sd_module_detail.id = sd_business_item.module_detail_id
        where sd_module_detail.is_delete = 0
          and sd_business_item.is_delete = 0

          and sd_module_detail.unit_id = #{unitId}
    </select>
    <select id="countByCoreBusinessId" resultType="java.lang.Integer">
        select count(*) from sd_business_item where core_business_id = #{coreBusinessId} and is_delete = 0
    </select>
    <select id="coreList" resultType="com.zenith.bbykz.model.vo.sd.SdCoreBusinessVO">
        select
        min(org_info.id) as unitId,
        min(org_info.name) as unitName,
        min(org_info.belong) as belong,
        min(sd_duty_detail.title) as dutyDetailName,
        min(sd_core_business.id) as id,
        min(sd_core_business.title) as title,
        count(sd_business_item.id) as businessItem
        from sd_core_business
        left join sd_module_detail on sd_core_business.module_detail_id = sd_module_detail.id
        left join sd_duty_detail on sd_duty_detail.id = sd_core_business.duty_detail_id
        left join sd_business_item on sd_core_business.id = sd_business_item.core_business_id
        left join org_info on sd_module_detail.unit_id = org_info.id
        where sd_core_business.is_delete =0 and sd_module_detail.unit_level_code like concat(#{dto.unitLevelCode},'%')
        <if test="dto.coreBusinessName != null and dto.coreBusinessName != ''">
            and sd_core_business.title like concat('%',#{dto.coreBusinessName},'%')
        </if>
        <if test="dto.dutyDetailName != null and dto.dutyDetailName != ''">
            and sd_duty_detail.title like concat('%',#{dto.dutyDetailName},'%')
        </if>
        group by sd_core_business.id
        order by
        length(min(org_info.level_code)),
        min(org_info.level_code),
        min(org_info.sort),
        min(sd_duty_detail.id),
        min(sd_core_business.sort),
        min(sd_core_business.create_time)
    </select>
</mapper>

