<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.zenith.bbykz.dao.sd.SdOrgRelationMapper">


    <select id="findAll" resultType="com.zenith.bbykz.model.vo.sd.SdSqlVO">
        select sd_module.id           as moduleId,
               sd_module.title        as moduleTile,
               sd_module.sort         as moduleSort,

               sd_module_detail.id    as moduleDetailId,
               sd_module_detail.title as moduleDetailTile,
               sd_module_detail.sort  as moduleDetailSort,
               sd_module_detail.record_num  as moduleDetailRecordNum,

               sd_duty_detail.id      as dutyDetailId,
               sd_duty_detail.title   as dutyDetailTile,
               sd_duty_detail.sort    as dutyDetailSort,
               sd_duty_detail.take_unit_name    as dutyDetailTakeUnitName,
               sd_duty_detail.record_num    as dutyDetailRecordNum,

               sd_core_business.id    as coreBusinessId,
               sd_core_business.title as coreBusinessTile,
               sd_core_business.sort  as coreBusinessSort,


               sd_business_item.id    as businessItemId,
               sd_business_item.title as businessItemTile,
               sd_business_item.sort  as businessItemSort,

               sd_one_thing.id        as oneId,
               sd_one_thing.title     as oneName,
               sd_one_thing.sort      as oneSort


        from sd_module
                 left join sd_module_detail
                           on sd_module.id = sd_module_detail.module_id and sd_module_detail.is_delete = 0
                 left join sd_duty_detail
                           on sd_module_detail.id = sd_duty_detail.module_detail_id and sd_duty_detail.is_delete = 0
                 left join sd_core_business
                           on sd_duty_detail.id = sd_core_business.duty_detail_id and sd_core_business.is_delete = 0
                 left join sd_business_item
                           on sd_core_business.id = sd_business_item.core_business_id and sd_business_item.is_delete = 0
                 left join sd_business_item_one
                           on sd_business_item_one.item_id = sd_business_item.id and sd_business_item_one.is_delete = 0
                 left join sd_one_thing on sd_one_thing.id = sd_business_item_one.one_id

        where sd_module_detail.unit_id = #{unitId}
    </select>
    <select id="dutyList" resultType="com.zenith.bbykz.model.vo.sd.SdSearchVO">
        select
            min(sd_module_detail.module_id)  as moduleId,
            min(sd_module_detail.id) as moduleDetailId,
            min(sd_module_detail.title) as moduleDetailTitle,
            min(sd_module_detail.record_num)  as moduleDetailRecordNum,
            min(sd_duty_detail.id) as dutyDetailId,
            min(sd_duty_detail.title) as dutyDetailTitle,
            min(sd_duty_detail.record_num)  as moduleDetailRecordNum,
            min(sd_duty_detail.take_unit_id)  as dutyDetailTakeUnitId,
            min(sd_duty_detail.take_unit_name)  as dutyDetailTakeUnitName,
            count(sd_core_business.id) as coreBusinessNum
        from sd_duty_detail
                 left join sd_module_detail on sd_duty_detail.module_detail_id  = sd_module_detail.id
                 left join sd_core_business on sd_core_business.duty_detail_id  = sd_duty_detail.id
        where sd_duty_detail.is_delete  = 0 and sd_module_detail.is_delete  = 0
          and sd_duty_detail.take_unit_level_code like concat(#{dto.unitLevelCode},'%')
        group by sd_duty_detail.id
        order by sd_duty_detail.take_unit_id ,sd_duty_detail.sort
    </select>
</mapper>

