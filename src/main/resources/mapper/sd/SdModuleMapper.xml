<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.zenith.bbykz.dao.sd.SdModuleMapper">


    <select id="countByUnitId" resultType="java.lang.Integer">
        select count(*)
        from (select count(*)
              from sd_module_detail
                       left join sd_module on sd_module_detail.module_id = sd_module.id
              where sd_module_detail.is_delete = 0
                and sd_module_detail.unit_id = #{unitId}
              group by sd_module.id) td
    </select>
    <select id="analysisModuleCount" resultType="java.lang.Integer">
        select count(*) from (select count(*) from sd_module_detail
        left join sd_module on sd_module.id = sd_module_detail.module_id
        left join sd_duty_detail on sd_module_detail.id = sd_duty_detail.module_detail_id  and sd_duty_detail.is_delete = 0
        where sd_module_detail.is_delete = 0 and sd_module.is_delete =0
        and sd_module_detail.unit_level_code like concat(#{dto.unitLevelCode},'%')
        <if test="dto.keyword != null and dto.keyword != ''">
            and (sd_module_detail.title like concat('%',#{dto.keyword}, '%')
            or sd_duty_detail.title like concat('%',#{dto.keyword}, '%')
            )
        </if>
        group by sd_module_detail.id) td
    </select>
    <select id="analysisInvolveUnitCount" resultType="java.lang.Integer">
        select count(*) from (select count(*) from sd_module_detail
        left join sd_module on sd_module.id = sd_module_detail.module_id
        left join sd_duty_detail on sd_module_detail.id = sd_duty_detail.module_detail_id  and sd_duty_detail.is_delete = 0
        where sd_module_detail.is_delete = 0 and sd_module.is_delete =0
        and sd_module_detail.unit_level_code like concat(#{dto.unitLevelCode},'%')
        <if test="dto.keyword != null and dto.keyword != ''">
            and (sd_module_detail.title like concat('%',#{dto.keyword}, '%')
            or sd_duty_detail.title like concat('%',#{dto.keyword}, '%')
            )
        </if>
        group by sd_module_detail.unit_id ) td
    </select>
    <select id="analysisTypeCountList" resultType="com.zenith.bbykz.model.vo.sd.SdModuleTypeCountVO">
        select sd_module_detail.module_id as id,min(sd_module.title) as name ,count(*) as count  from sd_module_detail
            left join sd_module on sd_module.id  = sd_module_detail.module_id
            left join sd_duty_detail   on sd_module_detail.id = sd_duty_detail.module_detail_id and sd_duty_detail.is_delete = 0
        where sd_module_detail.is_delete = 0 and sd_module.is_delete =0
          and sd_module_detail.unit_level_code like concat(#{dto.unitLevelCode},'%')
        <if test="dto.keyword != null and dto.keyword != ''">
            and (sd_module_detail.title like concat('%',#{dto.keyword}, '%')
            or sd_duty_detail.title like concat('%',#{dto.keyword}, '%')
            )
        </if>
        group by sd_module_detail.module_id
    </select>
    <select id="analysisDutyDetailCount" resultType="java.lang.Integer">
        select count(*) from (select count(*) from sd_module_detail
        left join sd_module on sd_module.id = sd_module_detail.module_id
        left join sd_duty_detail on sd_module_detail.id = sd_duty_detail.module_detail_id  and sd_duty_detail.is_delete = 0
        where sd_module_detail.is_delete = 0 and sd_module.is_delete =0
        and sd_module_detail.unit_level_code like concat(#{dto.unitLevelCode},'%')
        <if test="dto.keyword != null and dto.keyword != ''">
            and (sd_module_detail.title like concat('%',#{dto.keyword}, '%')
            or sd_duty_detail.title like concat('%',#{dto.keyword}, '%')
            )
        </if>
        group by sd_duty_detail.id ) td
    </select>

</mapper>

