<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.zenith.bbykz.dao.sd.SdDutyDetailMapper">


    <select id="countByUnitId" resultType="java.lang.Integer">
        select count(*)
        from sd_module_detail
                 left join sd_duty_detail on sd_module_detail.id = sd_duty_detail.module_detail_id
        where sd_module_detail.is_delete = 0
          and sd_duty_detail.is_delete = 0
          and sd_module_detail.unit_id = #{unitId}
    </select>
    <select id="searchList" resultType="com.zenith.bbykz.model.vo.sd.SdDutyDetailVO">
        select org_info.belong as unitBelong, sd_duty_detail.*
        from sd_duty_detail
                 left join sd_module_detail on sd_module_detail.id = sd_duty_detail.module_detail_id
                 left join org_info on sd_module_detail.unit_id = org_info.id
        where sd_duty_detail.is_delete = 0
          and sd_module_detail.is_delete = 0
          and sd_module_detail.unit_level_code like concat(#{dto.unitLevelCode},'%')
        <if test="dto.keyword != null and dto.keyword != ''">
            and sd_duty_detail.title  like concat('%',#{dto.keyword}, '%')
        </if>
        <if test="dto.moduleId != null and dto.moduleId != ''">
            and sd_module_detail.module_id = #{dto.moduleId}
        </if>
        order by length(org_info.level_code), org_info.level_code, org_info.sort, org_info.id
    </select>
</mapper>

