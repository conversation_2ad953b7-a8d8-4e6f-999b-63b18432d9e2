<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.zenith.bbykz.dao.sd.SdBusinessItemOneMapper">
    <delete id="deleteByItemId">
        update sd_business_item_one set is_delete = 0 ,update_time= now()  where item_id = #{itemId}
    </delete>


    <select id="countByUnitId" resultType="java.lang.Integer">
        select count(*)
        from (select one_id
              from sd_module_detail
                       left join sd_business_item on sd_module_detail.id = sd_business_item.module_detail_id
                       left join sd_business_item_one on sd_business_item.id = sd_business_item_one.item_id
              where sd_module_detail.is_delete = 0
                and sd_business_item.is_delete = 0
                and sd_business_item_one.is_delete = 0
                and sd_module_detail.unit_id = #{unitId}
              group by sd_business_item_one.one_id) td
    </select>
    <select id="list" resultType="com.zenith.bbykz.model.vo.sd.SdOrgRelationVO">
        select org_info.id as unitId,
        org_info.name as unitName,
        org_info.level_code as unitLevelCode,
        org_info.belong as unitBelong,
        sd_org_relation.module_count as moduleCount,
        sd_org_relation.module_detail_count as moduleDetailCount,
        sd_org_relation.duty_detail_count as dutyDetailCount,
        sd_org_relation.core_business_count as coreBusinessCount,
        sd_org_relation.business_item_count as businessItemCount,
        sd_org_relation.one_thing_count as oneThingCount
        from org_info left join sd_org_relation on org_info.id =sd_org_relation.unit_id
        where org_info.type_code is not null
        <if test="dto.unitLevelCode != null and dto.unitLevelCode != ''">
            and org_info.level_code like concat(#{dto.unitLevelCode},'%')
        </if>
        <if test="dto.name != null and dto.name != ''">
            and org_info.name like concat('%',#{dto.name},'%')
        </if>
        order by length(org_info.level_code),org_info.type_code,org_info.sort,org_info.id
    </select>
</mapper>

