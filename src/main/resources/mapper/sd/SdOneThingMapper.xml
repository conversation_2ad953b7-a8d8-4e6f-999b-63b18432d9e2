<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.zenith.bbykz.dao.sd.SdOneThingMapper">


    <select id="oneItemList" resultType="com.zenith.bbykz.model.vo.sd.SdBusinessItemOneVO">
        select
        sd_business_item.id as itemId,
        sd_business_item.title as itemName,
        sd_business_item.remark as itemRemark,
        sd_one_thing.id as oneId,
        sd_one_thing.title as oneName,
        sd_one_thing.one_type as oneType,
        sd_one_thing.describe as oneDescribe,
        sd_business_item_one.id as itemOneId

        from sd_business_item
        left join sd_core_business on sd_core_business.id = sd_business_item.core_business_id
        left join sd_business_item_one on sd_business_item.id = sd_business_item_one.item_id
        and sd_business_item_one.is_delete = 0
        left join sd_module_detail on sd_business_item.module_detail_id =sd_module_detail.id
        left join sd_one_thing on sd_one_thing.id =sd_business_item_one.one_id
        where sd_business_item.is_delete =0
        and sd_module_detail.unit_level_code like concat(#{dto.unitLevelCode},'%')
        <if test="dto.oneType != null and dto.oneType != ''">
            and sd_one_thing.one_type = #{dto.oneType}
        </if>
        <if test="dto.oneName != null and dto.oneName != ''">
            and sd_one_thing.title like concat('%',#{dto.oneName},'%')
        </if>
        <if test="dto.itemName != null and dto.itemName != ''">
            and sd_business_item.itemName like concat('%',#{dto.itemName},'%')
        </if>
        order  by sd_core_business.sort ,sd_core_business.id,sd_business_item.sort ,sd_business_item.id
    </select>
</mapper>

