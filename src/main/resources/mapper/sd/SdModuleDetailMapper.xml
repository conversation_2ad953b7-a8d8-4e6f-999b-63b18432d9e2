<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.zenith.bbykz.dao.sd.SdModuleDetailMapper">


    <select id="countByUnitId" resultType="java.lang.Integer">
        select count(*)
        from sd_module_detail
        where sd_module_detail.is_delete = 0
          and sd_module_detail.unit_id = #{unitId}
    </select>
    <select id="searchList" resultType="com.zenith.bbykz.model.vo.sd.SdSearchVO">
        select
        org_info.id as unitId,
        org_info.level_code  as unitLevelCode,
        org_info."name"  as unitName,
        org_info.belong as unitBelong,

        sd_module_detail.module_id  as moduleId,
        sd_module_detail.id as moduleDetailId,
        sd_module_detail.title as moduleDetailTitle,
        sd_module_detail.record_num  as moduleDetailRecordNum,
        sd_duty_detail.id as dutyDetailId,
        sd_duty_detail.title as dutyDetailTitle,
        sd_duty_detail.record_num  as dutyDetailRecordNum,
        sd_duty_detail.take_unit_id  as dutyDetailTakeUnitId,
        sd_duty_detail.take_unit_name  as dutyDetailTakeUnitName

        from sd_module_detail
        left join org_info on sd_module_detail.unit_id = org_info.id
        left join sd_duty_detail on sd_duty_detail.module_detail_id  = sd_module_detail.id
        where sd_module_detail.is_delete  = 0 and sd_duty_detail.is_delete  = 0
          and sd_module_detail.unit_level_code  like concat(#{dto.unitLevelCode},'%')
        <if test="dto.keyword != null and dto.keyword != ''">
            and (sd_module_detail.title  like concat('%',#{dto.keyword}, '%')
                or sd_duty_detail.title  like concat('%',#{dto.keyword}, '%')
                )
        </if>
        <if test="dto.moduleId != null and dto.moduleId != ''">
            and sd_module_detail.module_id = #{dto.moduleId}
        </if>
        order by length(org_info.level_code),org_info.level_code,org_info.sort,sd_duty_detail.take_unit_id ,sd_module_detail.sort ,sd_duty_detail.sort
    </select>
</mapper>

