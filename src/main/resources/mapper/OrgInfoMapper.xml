<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.zenith.bbykz.dao.OrgInfoMapper">
    <update id="flushErrorLevelCode">
        UPDATE org_info SET "level_code" = concat((SELECT "level_code" FROM org_info o1 WHERE  o1.id = org_info."parent_code" LIMIT 1),SUBSTR("level_code",LENGTH("level_code")-5,LENGTH("level_code")))
        WHERE id NOT LIKE concat( "parent_code",'%')
    </update>
    <delete id="deleteAll">
        truncate table org_info
    </delete>


    <select id="getOrgByParentCode" resultType="com.zenith.bbykz.model.entity.OrgInfo">
        select *
        from org_info
        where level_code like concat(#{parentCode}, '___')
          and is_delete = 0
        order by level_code desc limit 1
    </select>

    <select id="getOrgByParentCodeAndIsBbOrg" resultType="com.zenith.bbykz.model.entity.OrgInfo">
        select * from org_info
        where level_code like concat(#{parentCode}, '___')
        and is_delete = 0
        and is_bb_org = '1'
        <if test="id != null and id != ''">
            and id != #{id}
        </if>
        limit 1
    </select>

    <select id="getPage" resultType="com.zenith.bbykz.model.entity.OrgInfo">
        select id,level_code,type_code,name,short_name,parent_code,unit_type,credit_code,unit_source from org_info
        where  is_delete = 0
        <if test="dto.subordinate != null and dto.subordinate == 1">
            and  level_code like concat(#{dto.orgCode}, '%')
        </if>
        <if test="dto.subordinate != null and dto.subordinate == 0">
            and (level_code = #{dto.orgCode} or level_code like concat(#{dto.orgCode}, '___') )
        </if>
        <if test="dto.excludeNs">
            and type_code not in('05','07','09','11','13')
        </if>
        <if test="dto.keyword != null and dto.keyword != ''">
            and name like concat('%',#{dto.keyword}, '%')
        </if>
        order by length(level_code),level_code,sort,id
    </select>
    <select id="findLastUnitByLength" resultType="com.zenith.bbykz.model.entity.OrgInfo">
        select *
        from org_info
        where length(level_code) = #{unitLevelCount}
        order by level_code desc limit 1
    </select>
    <select id="findLastUnitByParentId" resultType="com.zenith.bbykz.model.entity.OrgInfo">
        select *
        from org_info
        where parent_id = #{parentId}
        order by level_code desc limit 1
    </select>
    <select id="getBelongById" resultType="java.lang.String">
        WITH RECURSIVE org_path AS (SELECT id, parent_id, name, CAST(name AS TEXT) AS path
                                    FROM org_info
                                    WHERE id = #{id}
                                    UNION ALL
                                    SELECT o.id, o.parent_id, o.name, CONCAT(o.name, '-', op.path)
                                    FROM org_info o
                                             JOIN org_path op ON o.id = op.parent_id)
        SELECT path
        FROM org_path
        WHERE id = (SELECT id
                    FROM org_path
                    ORDER BY length(path) DESC
            LIMIT 1
            );
    </select>
    <select id="getByOrgCode" resultType="com.zenith.bbykz.model.entity.OrgInfo">
        select *
        from org_info
        where org_code = #{organizationCode} limit 1
    </select>
    <select id="getUnitByDeptId" resultType="com.zenith.bbykz.model.entity.OrgInfo">
        WITH RECURSIVE UnitHierarchy AS (SELECT id, parent_id, unit_type, level_code, 1 as depth
                                         FROM org_info
                                         WHERE id = #{deptId}
                                         UNION
                                         SELECT su.id, su.parent_id, su.unit_type, su.level_code, uh.depth + 1
                                         FROM org_info su
                                                  JOIN UnitHierarchy uh ON su.id = uh.parent_id)
        SELECT id, parent_id, unit_type, level_code
        FROM UnitHierarchy
        WHERE unit_type = '2'
        ORDER BY depth ASC LIMIT 1;
    </select>
    <select id="selectTree" resultType="com.zenith.bbykz.model.entity.OrgInfo">
        select id,parent_id,name,sort,type_code,level_code,unit_type
        from org_info where
        <if test="userGroupOrgId != null and userGroupOrgId != ''">
            and id in(select org_id from sys_user_org_group_detail where user_org_group_id = #{userGroupOrgId})
        </if>
         level_code LIKE concat(#{levelCode},'%')
        and length(level_code) &lt;= (length(#{levelCode}) +6)
        and is_delete = 0
        order by id
    </select>
    <select id="findTop" resultType="com.zenith.bbykz.model.entity.OrgInfo">
        select *
        from org_info
        order by length(level_code) limit 1
    </select>
    <select id="findAllSy" resultType="com.zenith.bbykz.model.entity.OrgInfo">
        SELECT * FROM org_info WHERE type_code = '12' AND "is_delete"  = 0 order by id
    </select>
</mapper>

