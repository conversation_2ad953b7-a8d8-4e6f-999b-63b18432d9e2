server:
  servlet:
    context-path: /api
  port: 8088
  error:
    include-exception: true


spring:
  redis:
    database: 8
    host: ************
    port: 6379
    password: sjr1809
  jackson:
    default-property-inclusion: non_null

com:
  efficient:
    swagger:
      enable: false
      version: 1.0.1
      description: "BBYKZ"
      title: "BBYK<PERSON>"
    file:
      active: local
      tempPath: "/home/<USER>/server/file/temp/"
      local:
        localPath: "/home/<USER>/server/file/"
    task:
      enable: true
    cache:
      active: redis
      redis:
        enableRedisson: true

    logs:
      db: true
      name: bbykz
      level: info
      path: /home/<USER>/server/logs/
      sql:
        daoPackage: com.zenith.bbykz.dao
    rate:
      expireTime: 1
      global: true
      excludeApiList:
        - "/register/train/registerTrainCourseUser/save"

    auth:
      login:
        enable-salt: false
        salt-value: 1809
      authService: custom
      #        token-live: 30
      permissionCheckType: default
      systemIdField: Systemid
      userTicketClassName: com.zenith.bbykz.model.base.UserInfo
      whiteList:
        - "**swagger-resources**"
        - "/login**"
        - "/captcha**"
        #        - "/video/play**"
        ## 前端路由
        - "/search**"
        - "/flow**"
        - "/desktop**"
        - "/desktop**"
        - "/**/**.html"
        - "/**/**.js"
        - "/**/**.css"
        - "/**/**.ico"
        - "/**/**.png"
        - "/**/**.jpg"

    ykz:
      userCenter:
        db: false
        # bb
        #        appId: ce3ccec62ff888a3b63c48192b1ae19f
        #        appSecret: ce3ccec62ff888a38d221ff998c8036e
        # zx
        appId: 8b28f442173c6923885b0d2748b4c817
        appSecret: 8b28f442173c6923a11aa2db053b0eec
        handle: custom
        handleClassName: com.zenith.bbykz.service.efficient.YkzUserCenterHandleCustomService
      ykzApi:
        appkey: jgbzjg-zgcqswjgbzwyhbgs-4Ut46MroEqJG7y2jG571FWDYH9wGbBK4xOVRJneq
        appsecret: pt5zmn71EO79jClC5GbHJmvZ3ZAfK5E84p3uKmFW
        winOpen: true
        pcUrl: "http://192.168.0.76:8081"
        appUrl: "http://192.168.0.76:8081"
  smz:
    http-url: "http://127.0.0.1:8888/smz"