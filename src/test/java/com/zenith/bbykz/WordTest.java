package com.zenith.bbykz;

import cn.afterturn.easypoi.word.WordExportUtil;
import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.date.DateUtil;
import com.efficient.file.exception.FileException;
import com.zenith.bbykz.model.vo.bz.bzsq.BzBzsqMergeWordVO;
import org.apache.poi.xwpf.usermodel.XWPFDocument;
import org.junit.jupiter.api.Test;

import java.io.File;
import java.io.FileOutputStream;
import java.util.*;

/**
 * <AUTHOR>
 * @since 2024/5/11 14:43
 */
public class WordTest {

    @Test
    public void test01() {
        List<BzBzsqMergeWordVO> wordVOList = new ArrayList<>();
        List<Map<String, Object>> mapList = new ArrayList<>();

        for (int i = 0; i < 10; i++) {
            Map<String, Object> dataListMap = new HashMap<>();
            BzBzsqMergeWordVO vo = new BzBzsqMergeWordVO();
            vo.setSort(String.valueOf(i));
            vo.setApplyUnitName(String.valueOf(i));
            vo.setUseBzUnitName(String.valueOf(i));
            vo.setBzTypeName(String.valueOf(i));
            vo.setYbTypeName(String.valueOf(i));
            vo.setDsbNum(String.valueOf(i));
            vo.setZbNum(String.valueOf(i));
            vo.setYbNum(String.valueOf(i));
            vo.setApplyNum(String.valueOf(i));
            vo.setRemark(String.valueOf(i));
            wordVOList.add(vo);
            mapList.add(BeanUtil.beanToMap(vo));
        }
        Map<String, Object> dataMap = new HashMap<>();
        dataMap.put("createTime", DateUtil.formatDate(new Date()));
        // dataMap.put("list", dataListMap);
        dataMap.put("list", mapList);
        String tempName = "/bz/bz_merge_export.docx";
        String tempPath = "F:\\work\\project\\learn\\bb_ykz\\src\\main\\resources\\templates\\bz\\bz_merge_export.docx";

        File downFile = new File("C:\\Users\\<USER>\\Desktop\\config\\test.docx");
        try (FileOutputStream fout = new FileOutputStream(downFile)) {
            XWPFDocument document = WordExportUtil.exportWord07(tempPath, dataMap);
            // 生成文件
            document.write(fout);
        } catch (Exception e) {
            throw new FileException("编制使用核准审批单", e);
        }
    }
}
