package com.zenith.bbykz;

import cn.hutool.crypto.SecureUtil;
import com.zenith.bbykz.model.dto.register.train.RegisterTrainFileDTO;
import org.junit.jupiter.api.Test;

import java.lang.reflect.Field;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2024/2/20 10:50
 */
public class LoginTest {
    @Test
    public void test() throws NoSuchFieldException, IllegalAccessException {
        System.out.println(SecureUtil.md5("123456" + "1809"));
        // System.out.println(SecureUtil.md5("123456"));
        RegisterTrainFileDTO fileBizRelation = new RegisterTrainFileDTO();
        fileBizRelation.setBizId("1");
        List<String> list = new ArrayList<>();
        list.add("111");
        fileBizRelation.setFileIdList(list);

        Object bizId1 = this.getField(fileBizRelation, "bizId");
        Object bizId2 = this.getField(fileBizRelation, "fileIdList");

        System.out.println(bizId1 + "---" + bizId2);
    }

    private <T> T getField(Object object, String fieldName) throws NoSuchFieldException, IllegalAccessException {
        Class<?> current = object.getClass();
        while (current != Object.class) {
            try {
                Field field = current.getDeclaredField(fieldName);
                field.setAccessible(true);
                return (T) field.get(object);
            } catch (NoSuchFieldException e) {
                // 如果当前类中没有该字段，继续在父类中查找
                current = current.getSuperclass();
            }
        }
        throw new NoSuchFieldException("The field '" + fieldName + "' was not found in " + object.getClass() + " or its superclasses.");
    }

    public static void main(String[] args) {
        String nextLevelCode = getNextLevelCode("500001002003004005006007008");
    }

    public static String getNextLevelCode(String levelCode) {
        String substring = levelCode.substring(levelCode.length() - 3);

        int next = Integer.parseInt(substring) + 1;
        String format = String.format("%03d", next);
        return levelCode.substring(0, levelCode.length() - 3) + format;
    }

}
