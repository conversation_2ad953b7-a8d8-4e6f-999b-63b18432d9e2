package com.zenith.bbykz;

import cn.hutool.core.util.CharsetUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.crypto.asymmetric.KeyType;
import cn.hutool.crypto.asymmetric.RSA;

import java.nio.charset.StandardCharsets;
import java.util.HashMap;
import java.util.Map;
import java.util.Random;

/**
 * <AUTHOR>
 * @since 2024/3/18 16:18
 */
public class JwtTest {
    private static final String CHARACTERS = "abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789";
    private static final int SHORT_LENGTH = 36; // 设置短字符串的长度

    private static final Map<String, String> shortToLongMap = new HashMap<>();
    private static final Map<String, String> longToShortMap = new HashMap<>();

    public static String shortenString(String originalString) {
        if (longToShortMap.containsKey(originalString)) {
            return longToShortMap.get(originalString);
        }

        StringBuilder shortBuilder = new StringBuilder();
        Random random = new Random();
        for (int i = 0; i < SHORT_LENGTH; i++) {
            shortBuilder.append(CHARACTERS.charAt(random.nextInt(CHARACTERS.length())));
        }

        String shortString = shortBuilder.toString();
        shortToLongMap.put(shortString, originalString);
        longToShortMap.put(originalString, shortString);

        return shortString;
    }

    public static String expandString(String shortString) {
        if (shortToLongMap.containsKey(shortString)) {
            return shortToLongMap.get(shortString);
        }
        return null;
    }

    public static void main(String[] args) {
        String originalString = "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzUxMiJ9.eyJzdWIiOiJ7XCJhY2NvdW50XCI6XCJhZG1pblwiLFwicGFzc3dvcmRcIjpcIjNhYzE0YzAyZDYyYzhmOWVlOGNjNjZhMzU0ZDE4Y2I0XCIsXCJ1c2VybmFtZVwiOlwi57O757uf566h55CG5ZGYXCIsXCJ1c2VySWRcIjpcIjFcIixcInp3ZGRJZFwiOlwiYWRtaW5cIixcImxvY2tcIjp0cnVlLFwidXNlckluZm9cIjp7XCJpZFwiOlwiMVwiLFwiYWNjb3VudFwiOlwiYWRtaW5cIixcInBhc3N3b3JkXCI6XCIzYWMxNGMwMmQ2MmM4ZjllZThjYzY2YTM1NGQxOGNiNFwiLFwiendkZElkXCI6XCJhZG1pblwiLFwib3BlbklkXCI6XCJzdHJpbmdcIixcImlzRGVsZXRlXCI6MCxcImNyZWF0ZVRpbWVcIjoxNjk1MjkwNTAzOTE5LFwidXBkYXRlVGltZVwiOjE2OTQ2OTAxMDI5ODIsXCJkZWxldGVUaW1lXCI6MTY5NDY5MDY5MDk4OCxcImlkQ2FyZFwiOlwic3RyaW5nXCIsXCJwaG9uZVwiOlwic3RyaW5nXCIsXCJvcmdMZXZlbENvZGVcIjpcIjUwMDAwMTAwN1wiLFwiaXNCYlVzZXJcIjoxLFwibWFuYWdlQ29kZVwiOlwiNTAwXCIsXCJuYW1lXCI6XCLns7vnu5_nrqHnkIblkZhcIixcImlzTG9ja1wiOjF9fSJ9.e6TGj2DFhOZEEqTRD4Cb6qqYsec4SUn8pbmcPLWIbY5cKmwEmtSMfc5xiE1VSxJg4zy4EKM1DJFH6Z8hYyQijg";

        RSA rsa = new RSA();

//获得私钥
        rsa.getPrivateKey();
        rsa.getPrivateKeyBase64();
//获得公钥
        rsa.getPublicKey();
        rsa.getPublicKeyBase64();

        // 公钥加密，私钥解密
        byte[] encrypt = rsa.encrypt(StrUtil.bytes("我是一段测试aaaa", CharsetUtil.CHARSET_UTF_8), KeyType.PublicKey);
        System.out.println(new String(encrypt));
        byte[] decrypt = rsa.decrypt(encrypt, KeyType.PrivateKey);
        System.out.println(new String(decrypt));
        System.out.println(new String(rsa.encrypt(originalString.getBytes(StandardCharsets.UTF_8), KeyType.PublicKey)));
        ;
    }
}

