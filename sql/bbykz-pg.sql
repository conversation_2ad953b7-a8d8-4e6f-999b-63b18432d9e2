CREATE TABLE IF NOT EXISTS "YKZ_USER_POST"
(
 "ID" int8 NOT NULL,
 "OR<PERSON><PERSON><PERSON><PERSON>ION_CODE" VARCHAR(255) NULL,
 "POST_TYPE" VARCHAR(255) NULL,
 "POS_JOB" VARCHAR(255) NULL,
 "CREATE_TIME" timestamp(6) DEFAULT now() NULL,
 "CREATE_USER" VARCHAR(255) NULL,
 "UPDATE_TIME" timestamp(6) DEFAULT NULL,
 "UPDATE_USER" VARCHAR(255) NULL,
 "IS_DELETED" int2 DEFAULT 0 NULL,
  CONSTRAINT "YKZ_USER_POST_pkey" PRIMARY KEY("ID")
);

CREATE TABLE IF NOT EXISTS "dict_code"
(
 "id" int8 NOT NULL,
 "code_type" VARCHAR(100) NOT NULL,
 "code" VARCHAR(100) NOT NULL,
 "code_name" VARCHAR(100) NOT NULL,
 "code_short_name" VARCHAR(100) NULL,
 "sort" int8 NOT NULL,
 "is_enable" int2 NOT NULL,
 "parent_code" VARCHAR(100) NOT NULL,
 "code_level" int2 NULL,
 "is_leaf" int2 NOT NULL,
 "pinyin" VARCHAR(200) NULL,
 CONSTRAINT "dict_code_pkey" PRIMARY KEY("id","code_type","code","code_name")
);

CREATE TABLE IF NOT EXISTS "LEGAL_PERSON_REGISTRATION"
(
 "ID" VARCHAR(100) NOT NULL,
 "UNIFIED_SOCIAL_CREDIT_CODE" VARCHAR(100) NULL,
 "CERTIFICATE_NO" VARCHAR(100) NULL,
 "NAME" VARCHAR(100) NULL,
 "ESTABLISHMENT_TYPE" VARCHAR(100) NULL,
 "LEGAL_REPRESENTATIVE" VARCHAR(100) NULL,
 "RESIDENCE" VARCHAR(255) NULL,
 "CREATER" VARCHAR(32) NULL,
 "CREATE_TIME" timestamp(6) DEFAULT NULL,
 "UPTIME_TIME" timestamp(6) DEFAULT NULL,
 "DELETE_TIME" timestamp(6) DEFAULT NULL,
 "IS_DELETE" INT2 NULL,
 "ORGANIZER" VARCHAR(255) NULL,
 "FUNDING_SOURCE" VARCHAR(255) NULL,
 "INITIAL_FUNDS" VARCHAR(255) NULL,
 "PURPOSE" TEXT NULL,
 "STATUS" VARCHAR(100) NULL,
 "SECOND_NAME" VARCHAR(255) NULL,
 "THIRD_NAME" VARCHAR(255) NULL,
 "OTHER_NAMES" VARCHAR(255) NULL,
 "ORGANIZER_CATEGORY" VARCHAR(255) NULL,
 "FIXED_ASSETS" VARCHAR(32) NULL,
 "CURRENT_ASSETS" VARCHAR(32) NULL,
 "OTHER_FUNDS" VARCHAR(32) NULL,
 "DEPOSIT_BANK" VARCHAR(255) NULL,
 "BANK_ACCOUNT" VARCHAR(64) NULL,
 "APPROVAL_AUTHORITY" VARCHAR(100) NULL,
 "APPROVAL_NUMBER" VARCHAR(100) NULL,
 "PRACTICING_LICENSE_CERTIFICATE" VARCHAR(100) NULL,
 "INSTITUTIONAL_SPECIFICATIONS" VARCHAR(100) NULL,
 "STAFF_ESTABLISHING" VARCHAR(100) NULL,
 "EMPLOYEE_NUMBER" INT2 NULL,
 "ESTABLISH_TIME" VARCHAR(50) NULL,
 "LICENSOR" VARCHAR(100) NULL,
 "CERTIFICATE_RECIPIENT" VARCHAR(100) NULL,
 "CERTIFICATE_ACQUISITION_TIME" VARCHAR(50) NULL,
 "COPIES_NUMBER" INT2 NULL,
 "CERTIFICATE_VALIDITY_START_TIME" VARCHAR(50) NULL,
 "CERTIFICATE_VALIDITY_END_TIME" VARCHAR(50) NULL,
 "CONTACTS" VARCHAR(100) NULL,
 "TELEPHONE" VARCHAR(100) NULL,
 "POSTAL_CODE" VARCHAR(100) NULL,
 "INDUSTRY_CATEGORY" VARCHAR(255) NULL,
 "REGISTRATION_AUTHORITY_CODE" VARCHAR(32) NULL,
 "ANNUAL_REPORT" VARCHAR(100) NULL,
 "ANNUAL_REPORT_TIME" VARCHAR(50) NULL,
 "LEGAL_REPRESENTATIVE_PHONE" VARCHAR(64) NULL,
 "REGISTRATION_MANAGEMENT_AUTHORITY" VARCHAR(255) NULL,
 CONSTRAINT "LEGAL_PERSON_REGISTRATION_pkey" PRIMARY KEY("ID")
);

CREATE TABLE IF NOT EXISTS "org_info"
(
 "id" VARCHAR(50) NOT NULL,
 "level_code" VARCHAR(50) NOT NULL,
 "name" VARCHAR(50) NOT NULL,
 "short_name" VARCHAR(50) NULL,
 "parent_code" VARCHAR(50) NULL,
 "type_code" VARCHAR(50) NULL,
 "is_delete" VARCHAR(50) NOT NULL,
 "create_time" timestamp(6) DEFAULT NULL,
 "update_time" timestamp(6) DEFAULT NULL,
 "delete_time" timestamp(6) DEFAULT NULL,
 "sort" int8 NULL,
 "jgsy_code" VARCHAR(50) NULL,
 "jgsy_other_name" VARCHAR(50) NULL,
 "gfjc" VARCHAR(50) NULL,
 "xgjc" VARCHAR(50) NULL,
 "jggg" VARCHAR(50) NULL,
 "jgxz" VARCHAR(50) NULL,
 "jgsy_system_code" VARCHAR(50) NULL,
 "jglb" VARCHAR(50) NULL,
 "jgbm" VARCHAR(50) NULL,
 "doc_no" VARCHAR(50) NULL,
 "pzslsj" timestamp(6) DEFAULT NULL,
 "sfskfq" VARCHAR(50) NULL,
 "jgsy_bzsycj" VARCHAR(50) NULL,
 "unify_code" VARCHAR(50) NULL,
 "jgsy_ifzfdw" VARCHAR(50) NULL,
 "jgsy_ifts" VARCHAR(50) NULL,
 "is_bb_org" int2 NULL,
 "is_sjbb_audit" int2 NULL,
 "org_qh" int2 NULL,
 CONSTRAINT "org_info_pkey" PRIMARY KEY("id")  
);
CREATE TABLE IF NOT EXISTS "REPLIES_SEQUENCE"
(
 "ID" VARCHAR(32) NOT NULL,
 "CODE" VARCHAR(32) NULL,
 "CURR_DATE" VARCHAR(12) NULL,
 CONSTRAINT "REPLIES_SEQUENCE_pkey" PRIMARY KEY("ID")  
);

CREATE TABLE IF NOT EXISTS "STAFFING_MANAGEMENT"
(
 "ID" VARCHAR(32) NOT NULL,
 "TITLE" VARCHAR(255) NULL,
 "APPLICANT_UNIT" VARCHAR(64) NULL,
 "PREPARATION_TYPE" VARCHAR(32) NULL,
 "EMPLOYEES_NUMBER" INT4 NULL,
 "ENROLLMENT_TIME" timestamp(6) DEFAULT NULL,
 "CONTACTS" VARCHAR(12) NULL,
 "TELEPHONE" VARCHAR(15) NULL,
 "APPLICAT_TIME" timestamp(6) DEFAULT NULL,
 "STATUS" VARCHAR(12) NULL,
 "APPLICAT_PERSON" VARCHAR(12) NULL,
 "FILES" VARCHAR(255) NULL,
 "APPROVAL_NUMBER" VARCHAR(16) NULL,
 "REPLIES_ADDRESS" VARCHAR(100) NULL,
 "REPLIES_FILE" VARCHAR(100) NULL,
 "ORG_CODE" VARCHAR(12) NULL,
 "APPROVED_PERSON" VARCHAR(12) NULL,
 "BB_ORG" VARCHAR(16) NULL,
  CONSTRAINT "STAFFING_MANAGEMENT_pkey" PRIMARY KEY("ID")  
);
CREATE TABLE IF NOT EXISTS "STAFFING_MANAGEMENT_PERSONS"
(
 "ID" VARCHAR(32) NOT NULL,
 "STAFFING_MANAGEMENT_ID" VARCHAR(32) NULL,
 "NAME" VARCHAR(12) NULL,
 "SEX" VARCHAR(8) NULL,
 "AGE" INT2 NULL,
 "EDUCATION" VARCHAR(12) NULL,
 "WORK_UNIT_DUTIES" VARCHAR(100) NULL,
 "PROPOSED_JOB" VARCHAR(100) NULL,
 "COMMENTS" VARCHAR(255) NULL,
  CONSTRAINT "STAFFING_MANAGEMENT_PERSONS_pkey" PRIMARY KEY("ID")  
);
CREATE TABLE IF NOT EXISTS "sys_file_info"
(
 "id" VARCHAR(1000) NOT NULL,
 "biz_id" VARCHAR(1000) NULL,
 "store_type" VARCHAR(100) NULL,
 "file_name" TEXT NULL,
 "file_path" TEXT NULL,
 "file_content" TEXT NULL,
 "file_size" int8 NULL,
 "create_time" timestamp(6) DEFAULT NULL,
 "remark" TEXT NULL,
  CONSTRAINT "sys_file_info_pkey" PRIMARY KEY("ID") 
);

CREATE TABLE IF NOT EXISTS "sys_log"
(
 "id" VARCHAR(1000) NOT NULL,
 "module" VARCHAR(1000) NULL,
 "user_id" VARCHAR(1000) NULL,
 "user_name" VARCHAR(1000) NULL,
 "log_ip" VARCHAR(1000) NULL,
 "log_time" VARCHAR(1000) NULL,
 "request_url" TEXT NULL,
 "log_opt" VARCHAR(1000) NULL,
 "log_content" TEXT NULL,
 "params" TEXT NULL,
 "result_code" VARCHAR(50) NULL,
 "result" TEXT NULL,
 "exception" TEXT NULL,
 CONSTRAINT "sys_log_pkey"  PRIMARY KEY("id")  
);

CREATE TABLE IF NOT EXISTS "sys_menu"
(
 "id" VARCHAR(50) NOT NULL,
 "code" VARCHAR(50) NOT NULL,
 "parent_code" VARCHAR(50) NOT NULL,
 "name" VARCHAR(50) NOT NULL,
 "description" VARCHAR(100) NULL,
 "enable" int2 NOT NULL,
 "is_leaf" int2 NOT NULL,
 "create_time" timestamp(6) DEFAULT NULL,
 "system_code" VARCHAR(50) NOT NULL,
 CONSTRAINT "sys_menu_pkey"  PRIMARY KEY("id")  
);
CREATE TABLE IF NOT EXISTS "sys_role"
(
 "id" VARCHAR(64) NOT NULL,
 "name" VARCHAR(200) NULL,
 "is_default" int2 NULL,
 "system_code" VARCHAR(50) NULL,
 CONSTRAINT "sys_role_pkey"  PRIMARY KEY("id")  
);
CREATE TABLE IF NOT EXISTS "sys_role_menu"
(
 "id" VARCHAR(64) NOT NULL,
 "role_id" VARCHAR(200) NOT NULL,
 "menu_code" VARCHAR(200) NOT NULL,
  CONSTRAINT "sys_role_menu_pkey" PRIMARY KEY("id")  
);
CREATE TABLE IF NOT EXISTS "sys_system"
(
 "id" VARCHAR(50) NOT NULL,
 "name" VARCHAR(50) NOT NULL,
  CONSTRAINT "sys_system_pkey" PRIMARY KEY("id")  
);
CREATE TABLE IF NOT EXISTS "sys_user"
(
 "id" VARCHAR(64) NOT NULL,
 "account" VARCHAR(200) NOT NULL,
 "password" VARCHAR(200) NULL,
 "zwdd_id" VARCHAR(200) NULL,
 "open_id" VARCHAR(200) NULL,
 "is_delete" int2 NOT NULL,
 "create_time" timestamp(6) DEFAULT NULL,
 "update_time" timestamp(6) DEFAULT NULL,
 "delete_time" timestamp(6) DEFAULT NULL,
 "id_card" VARCHAR(200) NULL,
 "phone" VARCHAR(200) NULL,
 "org_id" VARCHAR(200) NULL,
 "org_level_code" VARCHAR(200) NULL,
 "is_bb_user" int2 NULL,
 "manage_code" VARCHAR(200) NULL,
 "name" VARCHAR(200) NULL,
  CONSTRAINT "sys_user_pkey" PRIMARY KEY("id")  
);
CREATE TABLE IF NOT EXISTS "sys_user_role"
(
 "id" VARCHAR(50) NULL,
 "user_id" VARCHAR(50) NULL,
 "role_id" VARCHAR(50) NULL,
 "system_code" VARCHAR(50) NULL,
  CONSTRAINT "sys_user_role_pkey" PRIMARY KEY("id")  
);

CREATE TABLE IF NOT EXISTS "UNIFIED_SOCIAL_CREDIT_CODE"
(
 "ID" VARCHAR(32) NOT NULL,
 "CODE" VARCHAR(100) NULL,
 "NUM" VARCHAR(100) NULL,
 "CREATER" VARCHAR(32) NULL,
 "CREATE_TIME" timestamp(6) DEFAULT NULL,
 "UPTIME_TIME" timestamp(6) DEFAULT NULL,
 "DELETE_TIME" timestamp(6) DEFAULT NULL,
 "IS_DELETE" INT2 NULL,
 "INSTITUTION_NAME" VARCHAR(100) NULL,
 "INSTITUTIONAL_STATUS" VARCHAR(100) NULL,
 "INSTITUTION_ADDRESS" VARCHAR(255) NULL,
 "HEAD" VARCHAR(100) NULL,
 "INSTITUTIONAL_SPECIFICATIONS" VARCHAR(100) NULL,
 "INSTITUTIONAL_TYPE" VARCHAR(100) NULL,
 "INSTITUTIONAL_NATURE" VARCHAR(100) NULL,
 "INSTITUTION_CATEGORY" VARCHAR(100) NULL,
 "APPROVING_AUTHORITY" VARCHAR(100) NULL,
 "APPROVAL_NUMBER" VARCHAR(64) NULL,
 "CERTIFICATE_NUMBER" INT4 NULL,
 "LICENSOR" VARCHAR(12) NULL,
 "ISSUE_TIME" VARCHAR(50) NULL,
 "CERTIFICATE_EXPIRATION_TIME" VARCHAR(50) NULL,
 "CERTIFICATE_RECIPIENT" VARCHAR(100) NULL,
 "CERTIFICATE_ACQUISITION_TIME" VARCHAR(50) NULL,
 "CONTACTS" VARCHAR(100) NULL,
 "TELEPHONE" VARCHAR(100) NULL,
 "POSTAL_CODE" VARCHAR(100) NULL,
  CONSTRAINT "UNIFIED_SOCIAL_CREDIT_CODE_pkey"  PRIMARY KEY("ID")  
);

CREATE TABLE IF NOT EXISTS "YKZ_ORGANIZATION"
(
 "ID" int8 NOT NULL,
 "NAME" VARCHAR(255) NULL,
 "ORG_TYPE" VARCHAR(255) NULL,
 "PARENT_ID" int8 NULL,
 "DISPLAY_ORDER" int8 NULL,
 "GOV_ADDRESS" VARCHAR(255) NULL,
 "GOV_DIVISION_CODE" VARCHAR(255) NULL,
 "GOV_BUSINESS_STRIP_CODES" VARCHAR(255) NULL,
 "GOV_INSTITUTION_LEVEL_CODE" VARCHAR(255) NULL,
 "GOV_SHORT_NAME" VARCHAR(255) NULL,
 "ORGANIZATION_CODE" VARCHAR(255) NULL,
 "PARENT_ORGANIZATION_CODE" VARCHAR(255) NULL,
 "PRINCIPAL" VARCHAR(255) NULL,
 "CREDIT_CODE" VARCHAR(255) NULL,
 "REMARK" VARCHAR(255) NULL,
 "AREA_LEVEL" VARCHAR(255) NULL,
 "CREATE_TIME" timestamp(6) DEFAULT NOW(),
 "CREATE_USER" VARCHAR(255) NULL,
 "UPDATE_TIME" timestamp(6) DEFAULT NULL,
 "UPDATE_USER" VARCHAR(255) NULL,
 "IS_DELETED" SMALLINT DEFAULT 0
 NULL,
  CONSTRAINT "YKZ_ORGANIZATION_pkey"  PRIMARY KEY("ID")  
);
CREATE TABLE IF NOT EXISTS "YKZ_USER"
(
 "ID" int8 NOT NULL,
 "NAME" VARCHAR(255) NULL,
 "USERNAME" VARCHAR(255) NULL,
 "ACCOUNT_ID" VARCHAR(255) NULL,
 "EMPLOYEE_CODE" VARCHAR(255) NULL,
 "CREATE_TIME" timestamp(6) DEFAULT now(),
 "CREATE_USER" VARCHAR(255) NULL,
 "UPDATE_TIME" timestamp(6) DEFAULT NULL,
 "UPDATE_USER" VARCHAR(255) NULL,
 "IS_DELETED" SMALLINT DEFAULT 0
 NULL,
   CONSTRAINT "YKZ_USER_pkey"  PRIMARY KEY("ID")  
);


