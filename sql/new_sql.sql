DROP TABLE IF EXISTS "sys_user_system";
CREATE TABLE IF NOT EXISTS "sys_user_system" (
                                                 "id" varchar(32) NOT NULL,
    "user_id" varchar(32)  NOT NULL,
    "system_id" varchar(32)  NOT NULL,
    "is_lock" int2  NOT NULL DEFAULT 0,
    "unlock_time"  timestamp,
    "create_time" timestamp ,
    "create_user" varchar(32) ,
    "update_time" timestamp ,
    "update_user" varchar(32) ,
    "is_delete" int2  NOT NULL DEFAULT 0,

    CONSTRAINT "sys_user_system_pkey" PRIMARY KEY ("id")
    )
;
COMMENT ON TABLE "sys_user_system" IS '用户系统表';
COMMENT ON COLUMN "sys_user_system"."id" IS '主键id';
COMMENT ON COLUMN "sys_user_system"."user_id" IS '用户id';
COMMENT ON COLUMN "sys_user_system"."system_id" IS '系统id';
COMMENT ON COLUMN "sys_user_system"."is_lock" IS '是否锁定';
COMMENT ON COLUMN "sys_user_system"."unlock_time" IS '解锁时间';
COMMENT ON COLUMN "sys_user_system"."create_time" IS '创建时间';
COMMENT ON COLUMN "sys_user_system"."create_user" IS '创建人';
COMMENT ON COLUMN "sys_user_system"."update_time" IS '修改时间';
COMMENT ON COLUMN "sys_user_system"."update_user" IS '修改人';
COMMENT ON COLUMN "sys_user_system"."is_delete" IS '是否删除';


ALTER TABLE  "sys_system"  ADD  sort int4;
comment on column sys_system.sort is '排序';
ALTER TABLE  "sys_system"  ADD  remark text ;
comment on column sys_system.remark is '备注';

alter table sys_role alter COLUMN is_default set DEFAULT 0;

ALTER TABLE  "sys_user_system"  ADD  is_default int2 ;
comment on column sys_user_system.is_default is '是否默认角色';

alter table sys_user_system alter COLUMN is_default set DEFAULT 0;

ALTER TABLE  "sys_role_menu"  ADD  menu_id varchar(32) ;
comment on column sys_role_menu.menu_id is '菜单id';
ALTER TABLE  "sys_role"  ADD  parent_id varchar(32) ;
comment on column sys_role.parent_id is '父级角色id';
ALTER TABLE  "sys_role"  ADD  create_user varchar(32) ;
comment on column sys_role.create_user is '创建人';
ALTER TABLE  "sys_role"  ADD  create_time TIMESTAMP ;
comment on column sys_role.create_time is '创建时间';

ALTER TABLE  "sys_user"  ADD  is_lock int2 ;
comment on column sys_user.is_lock is '是否锁定';
alter table sys_user alter COLUMN is_lock set DEFAULT 0;

ALTER TABLE  "sys_user"  ADD  unlock_time timestamp ;
comment on column sys_user.unlock_time is '解锁时间';

DROP TABLE IF EXISTS "register_train_type";
CREATE TABLE IF NOT EXISTS "register_train_type" (
    "id" varchar(32) NOT NULL,
    "name" varchar(32)  NOT NULL,
    "parent_id" varchar(32) ,
    "enabled" int2  NOT NULL DEFAULT 1,
    "sort"  int4,
    "create_time" timestamp ,
    "create_user" varchar(32) ,
    "update_time" timestamp ,
    "update_user" varchar(32) ,
    "is_delete" int2  NOT NULL DEFAULT 0,

    CONSTRAINT "register_train_type_pkey" PRIMARY KEY ("id")
    )
;
COMMENT ON TABLE "register_train_type" IS '登记信息-培训管理-课程分类';
COMMENT ON COLUMN "register_train_type"."id" IS '主键id';
COMMENT ON COLUMN "register_train_type"."name" IS '分类名称';
COMMENT ON COLUMN "register_train_type"."parent_id" IS '父级ID';
COMMENT ON COLUMN "register_train_type"."enabled" IS '是否启用';
COMMENT ON COLUMN "register_train_type"."sort" IS '序号';
COMMENT ON COLUMN "register_train_type"."create_time" IS '创建时间';
COMMENT ON COLUMN "register_train_type"."create_user" IS '创建人';
COMMENT ON COLUMN "register_train_type"."update_time" IS '修改时间';
COMMENT ON COLUMN "register_train_type"."update_user" IS '修改人';
COMMENT ON COLUMN "register_train_type"."is_delete" IS '是否删除';

DROP TABLE IF EXISTS "register_train_file";
CREATE TABLE IF NOT EXISTS "register_train_file" (
    "id" varchar(32) NOT NULL,
    "name" varchar(255) ,
    "type_id" varchar(32),
    "type_name" varchar(32),
    "mins" varchar(32),
    "describe"  text,
    "create_time" timestamp ,
    "create_user" varchar(32) ,
    "update_time" timestamp ,
    "update_user" varchar(32) ,
    "is_delete" int2  NOT NULL DEFAULT 0,

    CONSTRAINT "register_train_file_pkey" PRIMARY KEY ("id")
    )
;
COMMENT ON TABLE "register_train_file" IS '登记信息-培训管理-课件管理';
COMMENT ON COLUMN "register_train_file"."id" IS '主键id';
COMMENT ON COLUMN "register_train_file"."name" IS '名称';
COMMENT ON COLUMN "register_train_file"."type_id" IS '分类ID';
COMMENT ON COLUMN "register_train_file"."type_name" IS '分类名称';
COMMENT ON COLUMN "register_train_file"."mins" IS '时长';
COMMENT ON COLUMN "register_train_file"."describe" IS '描述';
COMMENT ON COLUMN "register_train_file"."create_time" IS '创建时间';
COMMENT ON COLUMN "register_train_file"."create_user" IS '创建人';
COMMENT ON COLUMN "register_train_file"."update_time" IS '修改时间';
COMMENT ON COLUMN "register_train_file"."update_user" IS '修改人';
COMMENT ON COLUMN "register_train_file"."is_delete" IS '是否删除';

DROP TABLE IF EXISTS "register_train_course";
CREATE TABLE IF NOT EXISTS "register_train_course" (
    "id" varchar(32) NOT NULL,
    "name" varchar(255) ,
    "scope_id" varchar(255) ,
    "type_id" varchar(32),
    "type_name" varchar(32),
    "describe"  text,
    "deadline" timestamp ,
    "create_time" timestamp ,
    "create_user" varchar(32) ,
    "update_time" timestamp ,
    "update_user" varchar(32) ,
    "is_delete" int2  NOT NULL DEFAULT 0,

    CONSTRAINT "register_train_course_pkey" PRIMARY KEY ("id")
    )
;
COMMENT ON TABLE "register_train_course" IS '登记信息-培训管理-课程管理';
COMMENT ON COLUMN "register_train_course"."id" IS '主键id';
COMMENT ON COLUMN "register_train_course"."name" IS '名称';
COMMENT ON COLUMN "register_train_course"."scope_id" IS '阅读范围控制';
COMMENT ON COLUMN "register_train_course"."type_id" IS '分类ID';
COMMENT ON COLUMN "register_train_course"."type_name" IS '分类名称';
COMMENT ON COLUMN "register_train_course"."describe" IS '描述';
COMMENT ON COLUMN "register_train_course"."deadline" IS '截至时间';
COMMENT ON COLUMN "register_train_course"."create_time" IS '创建时间';
COMMENT ON COLUMN "register_train_course"."create_user" IS '创建人';
COMMENT ON COLUMN "register_train_course"."update_time" IS '修改时间';
COMMENT ON COLUMN "register_train_course"."update_user" IS '修改人';
COMMENT ON COLUMN "register_train_course"."is_delete" IS '是否删除';

DROP TABLE IF EXISTS "register_train_course_detail";
CREATE TABLE IF NOT EXISTS "register_train_course_detail" (
    "id" varchar(32) NOT NULL,
    "course_id" varchar(32) ,
    "train_file_id" varchar(32) ,
    "detail_name" varchar(255),
    "mins" int4,
    "create_time" timestamp ,
    "create_user" varchar(32) ,
    "update_time" timestamp ,
    "update_user" varchar(32) ,
    "is_delete" int2  NOT NULL DEFAULT 0,

    CONSTRAINT "register_train_course_detail_pkey" PRIMARY KEY ("id")
    )
;
COMMENT ON TABLE "register_train_course_detail" IS '登记信息-培训管理-课程管理关联课件';
COMMENT ON COLUMN "register_train_course_detail"."id" IS '主键id';
COMMENT ON COLUMN "register_train_course_detail"."course_id" IS '课程主键';
COMMENT ON COLUMN "register_train_course_detail"."detail_name" IS '课件名称';
COMMENT ON COLUMN "register_train_course_detail"."mins" IS '时长，秒';
COMMENT ON COLUMN "register_train_course_detail"."create_time" IS '创建时间';
COMMENT ON COLUMN "register_train_course_detail"."create_user" IS '创建人';
COMMENT ON COLUMN "register_train_course_detail"."update_time" IS '修改时间';
COMMENT ON COLUMN "register_train_course_detail"."update_user" IS '修改人';
COMMENT ON COLUMN "register_train_course_detail"."is_delete" IS '是否删除';

DROP TABLE IF EXISTS "register_train_course_user";
CREATE TABLE IF NOT EXISTS "register_train_course_user" (
    "id" varchar(32) NOT NULL,
    "course_id" varchar(32) ,
    "user_id" varchar(32) ,
    "mins" int4 ,
    "view_mins" int4,
    "is_finish" int2,
    "is_attention" int2,
    "progress" numeric(10,2),
    "create_time" timestamp ,
    "create_user" varchar(32) ,
    "update_time" timestamp ,
    "update_user" varchar(32) ,
    "is_delete" int2  NOT NULL DEFAULT 0,

    CONSTRAINT "register_train_course_user_pkey" PRIMARY KEY ("id")
    )
;
COMMENT ON TABLE "register_train_course_user" IS '登记信息-培训管理-用户课程';
COMMENT ON COLUMN "register_train_course_user"."id" IS '主键id';
COMMENT ON COLUMN "register_train_course_user"."user_id" IS '用户id';
COMMENT ON COLUMN "register_train_course_user"."course_id" IS '课程主键';
COMMENT ON COLUMN "register_train_course_user"."mins" IS '课件时长';
COMMENT ON COLUMN "register_train_course_user"."progress" IS '进度';
COMMENT ON COLUMN "register_train_course_user"."view_mins" IS '学习时长';
COMMENT ON COLUMN "register_train_course_user"."is_finish" IS '是否完成';
COMMENT ON COLUMN "register_train_course_user"."is_attention" IS '是否关注';
COMMENT ON COLUMN "register_train_course_user"."create_time" IS '创建时间';
COMMENT ON COLUMN "register_train_course_user"."create_user" IS '创建人';
COMMENT ON COLUMN "register_train_course_user"."update_time" IS '修改时间';
COMMENT ON COLUMN "register_train_course_user"."update_user" IS '修改人';
COMMENT ON COLUMN "register_train_course_user"."is_delete" IS '是否删除';

DROP TABLE IF EXISTS "register_train_user_info";
CREATE TABLE IF NOT EXISTS "register_train_user_info" (
    "id" varchar(32) NOT NULL,
    "course_id" varchar(32) ,
    "user_id" varchar(32) ,
    "course_detail_id" varchar(32) ,
    "mins" int4 ,
    "view_mins" int4,
    "is_finish" int2,
    "is_attention" int2,
    "progress" numeric(10,2),
    "create_time" timestamp ,
    "create_user" varchar(32) ,
    "update_time" timestamp ,
    "update_user" varchar(32) ,
    "is_delete" int2  NOT NULL DEFAULT 0,

    CONSTRAINT "register_train_user_info_pkey" PRIMARY KEY ("id")
    )
;
COMMENT ON TABLE "register_train_user_info" IS '登记信息-培训管理-学习进度';
COMMENT ON COLUMN "register_train_user_info"."id" IS '主键id';
COMMENT ON COLUMN "register_train_user_info"."user_id" IS '用户id';
COMMENT ON COLUMN "register_train_user_info"."course_id" IS '课程主键';
COMMENT ON COLUMN "register_train_user_info"."course_detail_id" IS '课件ID';
COMMENT ON COLUMN "register_train_user_info"."mins" IS '课件时长';
COMMENT ON COLUMN "register_train_user_info"."progress" IS '进度';
COMMENT ON COLUMN "register_train_user_info"."view_mins" IS '学习时长';
COMMENT ON COLUMN "register_train_user_info"."is_finish" IS '是否完成';
COMMENT ON COLUMN "register_train_user_info"."is_attention" IS '是否关注';
COMMENT ON COLUMN "register_train_user_info"."create_time" IS '创建时间';
COMMENT ON COLUMN "register_train_user_info"."create_user" IS '创建人';
COMMENT ON COLUMN "register_train_user_info"."update_time" IS '修改时间';
COMMENT ON COLUMN "register_train_user_info"."update_user" IS '修改人';
COMMENT ON COLUMN "register_train_user_info"."is_delete" IS '是否删除';

DROP TABLE IF EXISTS "register_train_course_feedback";
CREATE TABLE IF NOT EXISTS "register_train_course_feedback" (
    "id" varchar(32) NOT NULL,
    "course_id" varchar(32) ,
    "describe" text ,
    "create_time" timestamp ,
    "create_user" varchar(32) ,
    "update_time" timestamp ,
    "update_user" varchar(32) ,
    "is_delete" int2  NOT NULL DEFAULT 0,

    CONSTRAINT "register_train_course_feedback_pkey" PRIMARY KEY ("id")
    )
;
COMMENT ON TABLE "register_train_course_feedback" IS '登记信息-培训管理-反馈意见';
COMMENT ON COLUMN "register_train_course_feedback"."id" IS '主键id';
COMMENT ON COLUMN "register_train_course_feedback"."course_id" IS '课程主键';
COMMENT ON COLUMN "register_train_course_feedback"."describe" IS '描述';
COMMENT ON COLUMN "register_train_course_feedback"."create_time" IS '创建时间';
COMMENT ON COLUMN "register_train_course_feedback"."create_user" IS '创建人';
COMMENT ON COLUMN "register_train_course_feedback"."update_time" IS '修改时间';
COMMENT ON COLUMN "register_train_course_feedback"."update_user" IS '修改人';
COMMENT ON COLUMN "register_train_course_feedback"."is_delete" IS '是否删除';


ALTER TABLE  "legal_person_registration"  ADD  version int4;
comment on column legal_person_registration.version is '版本号';
ALTER TABLE  "unified_social_credit_code"  ADD  version int4;
comment on column unified_social_credit_code.version is '版本号';

DROP TABLE IF EXISTS "register_legal_person_lib";
CREATE TABLE IF NOT EXISTS "register_legal_person_lib" (
    "id" varchar(32) NOT NULL,
    "name" varchar(32) ,
    "id_card" varchar(32) ,
    "unit_name" text ,
    "register_time" timestamp ,
    "start_time" timestamp ,
    "end_time" timestamp ,
    "remark" text,
    "version" int4 NOT NULL,
    "create_time" timestamp ,
    "create_user" varchar(32) ,
    "update_time" timestamp ,
    "update_user" varchar(32) ,
    "is_delete" int2  NOT NULL DEFAULT 0,

    CONSTRAINT "register_legal_person_lib_pkey" PRIMARY KEY ("id")
    )
;
COMMENT ON TABLE "register_legal_person_lib" IS '登记信息-内部事务-法人库';
COMMENT ON COLUMN "register_legal_person_lib"."id" IS '主键id';
COMMENT ON COLUMN "register_legal_person_lib"."name" IS '名称';
COMMENT ON COLUMN "register_legal_person_lib"."id_card" IS '身份证';
COMMENT ON COLUMN "register_legal_person_lib"."unit_name" IS '单位名称';
COMMENT ON COLUMN "register_legal_person_lib"."register_time" IS '注册时间';
COMMENT ON COLUMN "register_legal_person_lib"."start_time" IS '有效期开始时间';
COMMENT ON COLUMN "register_legal_person_lib"."end_time" IS '有效期结束时间';
COMMENT ON COLUMN "register_legal_person_lib"."remark" IS '备注';
COMMENT ON COLUMN "register_legal_person_lib"."version" IS '版本号';
COMMENT ON COLUMN "register_legal_person_lib"."create_time" IS '创建时间';
COMMENT ON COLUMN "register_legal_person_lib"."create_user" IS '创建人';
COMMENT ON COLUMN "register_legal_person_lib"."update_time" IS '修改时间';
COMMENT ON COLUMN "register_legal_person_lib"."update_user" IS '修改人';
COMMENT ON COLUMN "register_legal_person_lib"."is_delete" IS '是否删除';

DROP TABLE IF EXISTS "register_legal_person_log";
CREATE TABLE IF NOT EXISTS "register_legal_person_log" (
    "id" varchar(32) NOT NULL,
    "name" varchar(32) ,
    "id_card" varchar(32) ,
    "unit_name" text ,
    "is_success" int2 ,
    "description" text ,
    "create_time" timestamp ,
    "create_user" varchar(32) ,
    "update_time" timestamp ,
    "update_user" varchar(32) ,
    "is_delete" int2  NOT NULL DEFAULT 0,

    CONSTRAINT "register_legal_person_log_pkey" PRIMARY KEY ("id")
    )
;
COMMENT ON TABLE "register_legal_person_log" IS '登记信息-内部事务-法人库日志';
COMMENT ON COLUMN "register_legal_person_log"."id" IS '主键id';
COMMENT ON COLUMN "register_legal_person_log"."name" IS '名称';
COMMENT ON COLUMN "register_legal_person_log"."id_card" IS '身份证';
COMMENT ON COLUMN "register_legal_person_log"."unit_name" IS '单位名称';
COMMENT ON COLUMN "register_legal_person_log"."is_success" IS '是否通过';
COMMENT ON COLUMN "register_legal_person_log"."description" IS '描述';
COMMENT ON COLUMN "register_legal_person_log"."create_time" IS '创建时间';
COMMENT ON COLUMN "register_legal_person_log"."create_user" IS '创建人';
COMMENT ON COLUMN "register_legal_person_log"."update_time" IS '修改时间';
COMMENT ON COLUMN "register_legal_person_log"."update_user" IS '修改人';
COMMENT ON COLUMN "register_legal_person_log"."is_delete" IS '是否删除';

ALTER TABLE  "register_train_user_info"  ADD  train_file_id varchar(64);
comment on column register_train_user_info.train_file_id is '课件ID';
ALTER TABLE  "register_train_user_info"  ADD  file_id varchar(64);
comment on column register_train_user_info.file_id is '文件ID';


DROP TABLE IF EXISTS "geocode";
CREATE TABLE IF NOT EXISTS  geocode (
                                        code VARCHAR(10) NOT NULL,
    name VARCHAR(60) NOT NULL,
    type VARCHAR(10) NOT NULL,
    sort INTEGER NOT NULL,
    org_id VARCHAR(255) NULL,
    level_code VARCHAR(255) NULL,
    CONSTRAINT geocode_pkey PRIMARY KEY (CODE)
    );
COMMENT ON TABLE "geocode" IS '区划代码';
COMMENT ON COLUMN "geocode"."code" IS 'code';
COMMENT ON COLUMN "geocode"."name" IS '名称';
COMMENT ON COLUMN "geocode"."type" IS '类型，1-全国，2-省，3-市，4-区，5-区县';
COMMENT ON COLUMN "geocode"."sort" IS '排序';
COMMENT ON COLUMN "geocode"."org_id" IS '机构id';
COMMENT ON COLUMN "geocode"."level_code" IS '层级码';

ALTER TABLE  "sys_user_system"  ADD  org_id varchar(64);
comment on column sys_user_system.org_id is '所属机构ID';
ALTER TABLE  "sys_user_system"  ADD  org_level_code varchar(64);
comment on column sys_user_system.org_level_code is '所属机构层级码';
ALTER TABLE  "sys_user_system"  ADD  is_bb_user int2;
comment on column sys_user_system.is_bb_user is '是否编办用户';
ALTER TABLE  "sys_user_system"  ADD  manage_code varchar(64);
comment on column sys_user_system.manage_code is '管理层级';
ALTER TABLE  "sys_user_system"  ADD  geocode varchar(64);
comment on column sys_user_system.geocode is '区划层级';
ALTER TABLE  "sys_user_system"  ADD  user_org_group_id varchar(64);
comment on column sys_user_system.user_org_group_id is '用户机构组ID';
ALTER TABLE  "sys_user"  ADD  geocode varchar(64);
comment on column sys_user.geocode is '区划层级';

DROP TABLE IF EXISTS "sys_user_org_group";
CREATE TABLE IF NOT EXISTS  sys_user_org_group (
    "id" varchar(32) NOT NULL,
    "name" varchar(32) ,
    "system_id" varchar(32) ,
    "user_id" varchar(32) ,
    "remark" text ,
    "create_time" timestamp ,
    "create_user" varchar(32) ,
    "update_time" timestamp ,
    "update_user" varchar(32) ,
    "is_delete" int2  NOT NULL DEFAULT 0,
    CONSTRAINT sys_user_org_group_pkey PRIMARY KEY (id)
    );
COMMENT ON TABLE "sys_user_org_group" IS '用户机构组';
COMMENT ON COLUMN "sys_user_org_group"."name" IS '名称';
COMMENT ON COLUMN "sys_user_org_group"."system_id" IS '系统ID';
COMMENT ON COLUMN "sys_user_org_group"."user_id" IS '用户id';
COMMENT ON COLUMN "sys_user_org_group"."remark" IS '备注';

DROP TABLE IF EXISTS "sys_user_org_group_detail";
CREATE TABLE IF NOT EXISTS  sys_user_org_group_detail (
    "id" varchar(32) NOT NULL,
    "user_org_group_id" varchar(32) ,
    "org_id" varchar(32) ,
    "org_level_code" varchar(32) ,
    CONSTRAINT sys_user_org_group_detail_pkey PRIMARY KEY (id)
);
COMMENT ON TABLE "sys_user_org_group_detail" IS '用户机构组详情';
COMMENT ON COLUMN "sys_user_org_group_detail"."id" IS '主键';
COMMENT ON COLUMN "sys_user_org_group_detail"."user_org_group_id" IS '用户机构组';
COMMENT ON COLUMN "sys_user_org_group_detail"."org_id" IS '单位ID';
COMMENT ON COLUMN "sys_user_org_group_detail"."org_level_code" IS '层级码';

ALTER TABLE  "register_train_course_feedback"  ADD  course_name varchar(255);
comment on column register_train_course_feedback.course_name is '课程名称';

ALTER TABLE  "org_info"  ADD  parent_id varchar(255);
comment on column org_info.parent_id is '父级id';
ALTER TABLE  "org_info"  ADD  org_code varchar(255);
comment on column org_info.org_code is '渝快政机构编码';
ALTER TABLE  "org_info"  ADD  parent_org_code varchar(255);
comment on column org_info.parent_org_code is '渝快政机构编码';
ALTER TABLE  "org_info"  ADD  geocode varchar(255);
comment on column org_info.geocode is '区划代码';
ALTER TABLE  "org_info"  ADD  unit_type varchar(255);
comment on column org_info.unit_type is '渝快政单位类型';
ALTER TABLE  "org_info"  ADD  belong text;
comment on column org_info.belong is '所属层级';

ALTER TABLE  "sys_user_system"  ADD  main_job int2;
comment on column sys_user_system.main_job is '是否主职务';
ALTER TABLE  "sys_user_system"  ADD  dept_id varchar(255);
comment on column sys_user_system.dept_id is '部门ID';
ALTER TABLE  "sys_user_system"  ADD  dept_level_code varchar(255);
comment on column sys_user_system.dept_level_code is '部门层级码';
ALTER TABLE  "sys_user_system"  ADD  unit_id varchar(255);
comment on column sys_user_system.unit_id is '单位ID';
ALTER TABLE  "sys_user_system"  ADD  unit_level_code varchar(255);
comment on column sys_user_system.unit_level_code is '单位层级码';
ALTER TABLE  "sys_user_system"  ADD  post_name text;
comment on column sys_user_system.post_name is '职务';
ALTER TABLE  "sys_user_role"  ADD  user_system_id text;
comment on column sys_user_role.user_system_id is '用户系统id';


DROP TABLE IF EXISTS "sys_user_post";
CREATE TABLE sys_user_post (
                                         id VARCHAR(64) PRIMARY KEY,
                                         user_id VARCHAR(255),
                                         dept_id VARCHAR(255),
                                         dept_level_code VARCHAR(255),
                                         unit_id VARCHAR(255),
                                         unit_level_code VARCHAR(255),
                                         main_job int2,
                                         join_date timestamp,
                                         manage_id varchar(255),
                                         manage_code varchar(255),
                                         user_group_org_id varchar(255),
                                         sort BIGINT,
                                         post_name VARCHAR(255),
                                         create_time timestamp,
                                         update_time timestamp,
                                         is_delete int2  default 0,
                                         pull_time timestamp
);

COMMENT ON TABLE sys_user_post IS '用户职位信息';

COMMENT ON COLUMN sys_user_post.id IS '主键';
COMMENT ON COLUMN sys_user_post.user_id IS '用户id';
COMMENT ON COLUMN sys_user_post.main_job IS '是否主职务';
COMMENT ON COLUMN sys_user_post.dept_id IS '部门ID';
COMMENT ON COLUMN sys_user_post.dept_level_code IS '部门层级码';
COMMENT ON COLUMN sys_user_post.unit_id IS '单位ID';
COMMENT ON COLUMN sys_user_post.unit_level_code IS '单位层级码';
COMMENT ON COLUMN sys_user_post.post_name IS '职务';
COMMENT ON COLUMN sys_user_post.sort IS '排序';
COMMENT ON COLUMN sys_user_post.pull_time IS '拉取时间';
COMMENT ON COLUMN sys_user_post.manage_id IS '管理单位ID';
COMMENT ON COLUMN sys_user_post.manage_code IS '管理单位层级码';
COMMENT ON COLUMN sys_user_post.user_group_org_id IS '用户机构组';

ALTER TABLE  "sys_user_system"  ADD  user_post_id varchar(100);
comment on column sys_user_system.user_post_id is '用户职务ID';

ALTER TABLE  "sys_user"  ADD  is_builtin int2 default 0;
comment on column sys_user.is_builtin is '是否内置';


--DROP TABLE IF EXISTS "register_unit_info";
--CREATE TABLE IF NOT EXISTS "register_unit_info" (
--    "id" varchar(64) NOT NULL,
--    "unit_name" varchar(255) ,
--    "credit_code" varchar(100) ,
--    "legal_representative" varchar(100) ,
--    "source_of_funds" text ,
--    "start_up_capital" varchar(255) ,
--    "hold_organizer" varchar(255) ,
--    "business_scope" text ,
--    "start_time" timestamp ,
--    "end_time" timestamp ,
--    "create_time" timestamp ,
--    "create_user" varchar(32) ,
--    "update_time" timestamp ,
--    "update_user" varchar(32) ,
--    "is_delete" int2  NOT NULL DEFAULT 0,
--
--    CONSTRAINT "register_unit_info_pkey" PRIMARY KEY ("id")
--    )
--;
--COMMENT ON TABLE "register_unit_info" IS '登记信息-单位信息';
--COMMENT ON COLUMN "register_unit_info"."id" IS '主键id';
--COMMENT ON COLUMN "register_unit_info"."unit_name" IS '名称';
--COMMENT ON COLUMN "register_unit_info"."credit_code" IS '统一社会信用代码';
--COMMENT ON COLUMN "register_unit_info"."legal_representative" IS '法定代表人';
--COMMENT ON COLUMN "register_unit_info"."source_of_funds" IS '经费来源';
--COMMENT ON COLUMN "register_unit_info"."start_up_capital" IS '开办资金';
--COMMENT ON COLUMN "register_unit_info"."hold_organizer" IS '举办单位';
--COMMENT ON COLUMN "register_unit_info"."business_scope" IS '宗旨和业务范围';
--COMMENT ON COLUMN "register_unit_info"."start_time" IS '开始时间';
--COMMENT ON COLUMN "register_unit_info"."end_time" IS '结束时间';
--COMMENT ON COLUMN "register_unit_info"."create_time" IS '创建时间';
--COMMENT ON COLUMN "register_unit_info"."create_user" IS '创建人';
--COMMENT ON COLUMN "register_unit_info"."update_time" IS '修改时间';
--COMMENT ON COLUMN "register_unit_info"."update_user" IS '修改人';
--COMMENT ON COLUMN "register_unit_info"."is_delete" IS '是否删除';
DROP TABLE IF EXISTS "register_reconnaissance";
CREATE TABLE IF NOT EXISTS "register_reconnaissance" (
    "id" varchar(64) NOT NULL,
    "unit_name" varchar(255) ,
    "credit_code" varchar(100) ,
    "certificate_num" varchar(100) ,
    "legal_representative" varchar(100) ,
    "source_of_funds" text ,
    "start_up_capital" varchar(255) ,
    "hold_organizer" varchar(255) ,
    "business_scope" text ,
    "domicile" text ,
    "start_time" timestamp ,
    "end_time" timestamp ,
    "org_id" varchar(255) ,
    "org_level_code" varchar(255) ,
    "check_date" timestamp ,
    "check_reason" text ,
    "check_result" text ,
    "check_unit" varchar(100) ,
    "check_user" varchar(100),
    "check_user_phone" varchar(100),
    "check_record" text,
    "create_time" timestamp ,
    "create_user" varchar(32) ,
    "update_time" timestamp ,
    "update_user" varchar(32) ,
    "is_delete" int2  NOT NULL DEFAULT 0,

    CONSTRAINT "register_reconnaissance_pkey" PRIMARY KEY ("id")
    )
;
COMMENT ON TABLE "register_reconnaissance" IS '登记信息-内部事务-实地勘察';
COMMENT ON COLUMN "register_reconnaissance"."id" IS '主键id';
COMMENT ON COLUMN "register_reconnaissance"."unit_name" IS '名称';
COMMENT ON COLUMN "register_reconnaissance"."credit_code" IS '统一社会信用代码';
COMMENT ON COLUMN "register_reconnaissance"."certificate_num" IS '证书编号';
COMMENT ON COLUMN "register_reconnaissance"."domicile" IS '住所';
COMMENT ON COLUMN "register_reconnaissance"."legal_representative" IS '法定代表人';
COMMENT ON COLUMN "register_reconnaissance"."source_of_funds" IS '经费来源';
COMMENT ON COLUMN "register_reconnaissance"."start_up_capital" IS '开办资金';
COMMENT ON COLUMN "register_reconnaissance"."hold_organizer" IS '举办单位';
COMMENT ON COLUMN "register_reconnaissance"."business_scope" IS '宗旨和业务范围';
COMMENT ON COLUMN "register_reconnaissance"."start_time" IS '开始时间';
COMMENT ON COLUMN "register_reconnaissance"."end_time" IS '结束时间';
COMMENT ON COLUMN "register_reconnaissance"."check_date" IS '勘察时间';
COMMENT ON COLUMN "register_reconnaissance"."check_reason" IS '勘察原因';
COMMENT ON COLUMN "register_reconnaissance"."check_result" IS '勘察结果 JGSY_SDKC_STATUS';
COMMENT ON COLUMN "register_reconnaissance"."check_unit" IS '勘察单位';
COMMENT ON COLUMN "register_reconnaissance"."check_user" IS '勘察人';
COMMENT ON COLUMN "register_reconnaissance"."check_user_phone" IS '勘察人电话';
COMMENT ON COLUMN "register_reconnaissance"."check_record" IS '勘察记录';
COMMENT ON COLUMN "register_reconnaissance"."create_time" IS '创建时间';
COMMENT ON COLUMN "register_reconnaissance"."create_user" IS '创建人';
COMMENT ON COLUMN "register_reconnaissance"."update_time" IS '修改时间';
COMMENT ON COLUMN "register_reconnaissance"."update_user" IS '修改人';
COMMENT ON COLUMN "register_reconnaissance"."is_delete" IS '是否删除';

ALTER TABLE  "sys_user_org_group"  ADD  request_params text;
comment on column sys_user_org_group.request_params is '请求参数';


DROP TABLE IF EXISTS "bz_bzsq";
CREATE TABLE IF NOT EXISTS "bz_bzsq" (
    "id" varchar(64) NOT NULL,
    "org_id" varchar(255) ,
    "org_level_code" varchar(255) ,
    "title" varchar(200) ,
    "apply_unit_name" varchar(200) ,
    "bz_type" varchar(100) ,
    "apply_time" timestamp,
    "concat_name" varchar(255) ,
    "concat_phone" varchar(255) ,
    "status" varchar(10) ,
    "deadline" timestamp ,
    "approval_num" varchar(100) ,
    "create_time" timestamp ,
    "create_user" varchar(32) ,
    "update_time" timestamp ,
    "update_user" varchar(32) ,
    "is_delete" int2  NOT NULL DEFAULT 0,

    CONSTRAINT "bz_bzsq_pkey" PRIMARY KEY ("id")
    )
;
COMMENT ON TABLE "bz_bzsq" IS '核编管理-用编申请';
COMMENT ON COLUMN "bz_bzsq"."id" IS '主键id';
COMMENT ON COLUMN "bz_bzsq"."org_id" IS '提交单位id';
COMMENT ON COLUMN "bz_bzsq"."org_level_code" IS '提交单位层级码';
COMMENT ON COLUMN "bz_bzsq"."title" IS '标题';
COMMENT ON COLUMN "bz_bzsq"."apply_unit_name" IS '申请单位名称';
COMMENT ON COLUMN "bz_bzsq"."bz_type" IS '编制类型';
COMMENT ON COLUMN "bz_bzsq"."apply_time" IS '申请时间';
COMMENT ON COLUMN "bz_bzsq"."concat_name" IS '联系人';
COMMENT ON COLUMN "bz_bzsq"."concat_phone" IS '联系电话';
COMMENT ON COLUMN "bz_bzsq"."status" IS '审批状态，字典表 BZ_HBZT';
COMMENT ON COLUMN "bz_bzsq"."deadline" IS '有效期限';
COMMENT ON COLUMN "bz_bzsq"."approval_num" IS '批复单号';

COMMENT ON COLUMN "bz_bzsq"."create_time" IS '创建时间';
COMMENT ON COLUMN "bz_bzsq"."create_user" IS '创建人';
COMMENT ON COLUMN "bz_bzsq"."update_time" IS '修改时间';
COMMENT ON COLUMN "bz_bzsq"."update_user" IS '修改人';
COMMENT ON COLUMN "bz_bzsq"."is_delete" IS '是否删除';

DROP TABLE IF EXISTS "bz_bzsq";
CREATE TABLE IF NOT EXISTS "bz_bzsq" (
    "id" varchar(64) NOT NULL,
    "geocode" varchar(255) ,
    "is_bb" int2 ,
    "org_id" varchar(255) ,
    "org_level_code" varchar(255) ,
    "title" varchar(200) ,
    "apply_unit_name" varchar(200) ,
    "apply_time" timestamp,
    "concat_name" varchar(255) ,
    "concat_phone" varchar(255) ,
    "status" varchar(10) ,
    "opinion" text ,
    "deadline" timestamp ,
    "approval_num" varchar(100) ,
    "create_time" timestamp ,
    "create_user" varchar(32) ,
    "update_time" timestamp ,
    "update_user" varchar(32) ,
    "is_delete" int2  NOT NULL DEFAULT 0,

    CONSTRAINT "bz_bzsq_pkey" PRIMARY KEY ("id")
    )
;
COMMENT ON TABLE "bz_bzsq" IS '核编管理-用编申请';
COMMENT ON COLUMN "bz_bzsq"."id" IS '主键id';
COMMENT ON COLUMN "bz_bzsq"."geocode" IS '所属区划';
COMMENT ON COLUMN "bz_bzsq"."is_bb" IS '是否编办单位申请';
COMMENT ON COLUMN "bz_bzsq"."org_level_code" IS '提交单位层级码';
COMMENT ON COLUMN "bz_bzsq"."title" IS '标题';
COMMENT ON COLUMN "bz_bzsq"."apply_unit_name" IS '申请单位名称';
COMMENT ON COLUMN "bz_bzsq"."apply_time" IS '申请时间';
COMMENT ON COLUMN "bz_bzsq"."concat_name" IS '联系人';
COMMENT ON COLUMN "bz_bzsq"."concat_phone" IS '联系电话';
COMMENT ON COLUMN "bz_bzsq"."status" IS '审批状态，字典表 BZ_HBZT';
COMMENT ON COLUMN "bz_bzsq"."deadline" IS '有效期限';
COMMENT ON COLUMN "bz_bzsq"."approval_num" IS '批复单号';

COMMENT ON COLUMN "bz_bzsq"."create_time" IS '创建时间';
COMMENT ON COLUMN "bz_bzsq"."create_user" IS '创建人';
COMMENT ON COLUMN "bz_bzsq"."update_time" IS '修改时间';
COMMENT ON COLUMN "bz_bzsq"."update_user" IS '修改人';
COMMENT ON COLUMN "bz_bzsq"."is_delete" IS '是否删除';
drop index IF EXISTS  bz_bzsq_is_delete_idx;
CREATE INDEX bz_bzsq_is_delete_idx ON public.bz_bzsq USING hash (is_delete);
drop index IF EXISTS  bz_bzsq_geocode_idx;
CREATE INDEX bz_bzsq_geocode_idx ON public.bz_bzsq USING hash (geocode);
drop index IF EXISTS  bz_bzsq_org_level_code_idx;
CREATE INDEX bz_bzsq_org_level_code_idx ON public.bz_bzsq USING btree (org_level_code);

DROP TABLE IF EXISTS "bz_bzsq_detail";
CREATE TABLE IF NOT EXISTS "bz_bzsq_detail" (
    "id" varchar(64) NOT NULL,
    "bzsq_id" varchar(255) ,
    "bz_type" varchar(100) ,
    "yb_type" varchar(100) ,
    "dsb_num" int4,
    "zb_num" int4,
    "yb_num" int4,
    "apply_num" int4,
    "remark" text ,
    "is_back" int2 default 0 ,
    "back_reason" text ,
    "create_time" timestamp ,
    "create_user" varchar(32) ,
    "update_time" timestamp ,
    "update_user" varchar(32) ,
    "is_delete" int2  NOT NULL DEFAULT 0,

    CONSTRAINT "bz_bzsq_detail_pkey" PRIMARY KEY ("id")
    )
;
COMMENT ON TABLE "bz_bzsq_detail" IS '核编管理-用编申请-明细';
COMMENT ON COLUMN "bz_bzsq_detail"."id" IS '主键id';
COMMENT ON COLUMN "bz_bzsq_detail"."bzsq_id" IS '编制申请主键id';
COMMENT ON COLUMN "bz_bzsq_detail"."bz_type" IS '编制类型';
COMMENT ON COLUMN "bz_bzsq_detail"."yb_type" IS '用编类型';
COMMENT ON COLUMN "bz_bzsq_detail"."dsb_num" IS '待上编数';
COMMENT ON COLUMN "bz_bzsq_detail"."zb_num" IS '在编数';
COMMENT ON COLUMN "bz_bzsq_detail"."yb_num" IS '余编数';
COMMENT ON COLUMN "bz_bzsq_detail"."apply_num" IS '申请数';
COMMENT ON COLUMN "bz_bzsq_detail"."remark" IS '备注';
COMMENT ON COLUMN "bz_bzsq_detail"."is_back" IS '是否被退回，1-是，0-否';
COMMENT ON COLUMN "bz_bzsq_detail"."back_reason" IS '退回原因';
COMMENT ON COLUMN "bz_bzsq_detail"."create_time" IS '创建时间';
COMMENT ON COLUMN "bz_bzsq_detail"."create_user" IS '创建人';
COMMENT ON COLUMN "bz_bzsq_detail"."update_time" IS '修改时间';
COMMENT ON COLUMN "bz_bzsq_detail"."update_user" IS '修改人';
COMMENT ON COLUMN "bz_bzsq_detail"."is_delete" IS '是否删除';
drop index IF EXISTS  bz_bzsq_detail_bzsq_id_idx;
CREATE INDEX bz_bzsq_detail_bzsq_id_idx ON public.bz_bzsq_detail USING hash (bzsq_id);


DROP TABLE IF EXISTS "bz_bzsq_flow";
CREATE TABLE IF NOT EXISTS "bz_bzsq_flow" (
    "id" varchar(64) NOT NULL,
    "bzsq_id" varchar(255) ,
    "bzsq_status" varchar(255) ,
    "status_desc" varchar(255) ,
    "is_merge" int2 ,
    "opinion" text ,
    "is_agree" int2,
    "is_finish" int2,
    "create_time" timestamp ,
    "create_user" varchar(32) ,
    "update_time" timestamp ,
    "update_user" varchar(32) ,
    "is_delete" int2  NOT NULL DEFAULT 0,

    CONSTRAINT "bz_bzsq_flow_pkey" PRIMARY KEY ("id")
    )
;
COMMENT ON TABLE "bz_bzsq_flow" IS '核编管理-用编审核';
COMMENT ON COLUMN "bz_bzsq_flow"."id" IS '主键id';
COMMENT ON COLUMN "bz_bzsq_flow"."bzsq_id" IS '编制申请主键id';
COMMENT ON COLUMN "bz_bzsq_flow"."bzsq_status" IS '当前状态';
COMMENT ON COLUMN "bz_bzsq_flow"."status_desc" IS '状态描述';
COMMENT ON COLUMN "bz_bzsq_flow"."is_merge" IS '是否合并审核';
COMMENT ON COLUMN "bz_bzsq_flow"."opinion" IS '意见';
COMMENT ON COLUMN "bz_bzsq_flow"."is_agree" IS '是否同意';
COMMENT ON COLUMN "bz_bzsq_flow"."is_finish" IS '是否结束';
COMMENT ON COLUMN "bz_bzsq_flow"."create_time" IS '创建时间';
COMMENT ON COLUMN "bz_bzsq_flow"."create_user" IS '创建人';
COMMENT ON COLUMN "bz_bzsq_flow"."update_time" IS '修改时间';
COMMENT ON COLUMN "bz_bzsq_flow"."update_user" IS '修改人';
COMMENT ON COLUMN "bz_bzsq_flow"."is_delete" IS '是否删除';
drop index IF EXISTS  bz_bzsq_flowl_bzsq_id_idx;
CREATE INDEX bz_bzsq_flowl_bzsq_id_idx ON public.bz_bzsq_flow USING hash (bzsq_id);
drop index IF EXISTS  bz_bzsq_flowl_is_merge_idx;
CREATE INDEX bz_bzsq_flowl_is_merge_idx ON public.bz_bzsq_flow USING hash (is_merge);

DROP TABLE IF EXISTS "bz_bzsq_merge";
CREATE TABLE IF NOT EXISTS "bz_bzsq_merge" (
    "id" varchar(64) NOT NULL,
    "title" text ,
    "geocode" varchar(10) ,
    "status" varchar(10) ,
    "is_agree" int2 default 0 ,
    "bzsq_id_list" text ,
    "create_user_name" varchar(100) ,
    "create_time" timestamp ,
    "create_user" varchar(32) ,
    "update_time" timestamp ,
    "update_user" varchar(32) ,
    "is_delete" int2  NOT NULL DEFAULT 0,

    CONSTRAINT "bz_bzsq_merge_pkey" PRIMARY KEY ("id")
    )
;
COMMENT ON TABLE "bz_bzsq_merge" IS '核编管理-用编审核-合并审核';
COMMENT ON COLUMN "bz_bzsq_merge"."id" IS '主键id';
COMMENT ON COLUMN "bz_bzsq_merge"."status" IS '状态';
COMMENT ON COLUMN "bz_bzsq_merge"."geocode" IS '区划';
COMMENT ON COLUMN "bz_bzsq_merge"."bzsq_id_list" IS '是否合并审核';
COMMENT ON COLUMN "bz_bzsq_merge"."is_agree" IS '是否同意';
COMMENT ON COLUMN "bz_bzsq_merge"."create_user_name" IS '创建人姓名';
COMMENT ON COLUMN "bz_bzsq_merge"."create_time" IS '创建时间';
COMMENT ON COLUMN "bz_bzsq_merge"."create_user" IS '创建人';
COMMENT ON COLUMN "bz_bzsq_merge"."update_time" IS '修改时间';
COMMENT ON COLUMN "bz_bzsq_merge"."update_user" IS '修改人';
COMMENT ON COLUMN "bz_bzsq_merge"."is_delete" IS '是否删除';

ALTER TABLE  "bz_bzsq"  ADD  merge_id varchar(100);
comment on column bz_bzsq.merge_id is '合并ID';


DROP TABLE IF EXISTS "register_unit_bd";
CREATE TABLE IF NOT EXISTS "register_unit_bd" (
    "id" varchar(64) NOT NULL,
    "type" varchar(64) ,
    "geocode" varchar(64) ,
    "unit_id" varchar(64) ,
    "unit_level_code" varchar(255) ,
    "unit_name" varchar(255) ,
    "old_info" text,
    "new_info" text ,
    "is_handle" int2 default 0,
    "handle_time" timestamp ,
    "remark" text ,
    "source_type" int2 ,
    "create_time" timestamp ,
    "create_user" varchar(32) ,
    "update_time" timestamp ,
    "update_user" varchar(32) ,
    "is_delete" int2  NOT NULL DEFAULT 0,
    CONSTRAINT "register_unit_bd_pkey" PRIMARY KEY ("id")
    )
;
COMMENT ON TABLE "register_unit_bd" IS '登记信息-预警感知-信息变动';
COMMENT ON COLUMN "register_unit_bd"."id" IS '主键id';
COMMENT ON COLUMN "register_unit_bd"."type" IS '变动类型';
COMMENT ON COLUMN "register_unit_bd"."geocode" IS '区划';
COMMENT ON COLUMN "register_unit_bd"."unit_id" IS '单位ID';
COMMENT ON COLUMN "register_unit_bd"."unit_level_code" IS '单位层级码';
COMMENT ON COLUMN "register_unit_bd"."unit_name" IS '单位名称';
COMMENT ON COLUMN "register_unit_bd"."old_info" IS '原信息';
COMMENT ON COLUMN "register_unit_bd"."new_info" IS '新信息';
COMMENT ON COLUMN "register_unit_bd"."is_handle" IS '是否处理';
COMMENT ON COLUMN "register_unit_bd"."handle_time" IS '处理时间';
COMMENT ON COLUMN "register_unit_bd"."remark" IS '备注';
COMMENT ON COLUMN "register_unit_bd"."source_type" IS '来源类型，1-自建，2-同步';
COMMENT ON COLUMN "register_unit_bd"."create_time" IS '创建时间';
COMMENT ON COLUMN "register_unit_bd"."create_user" IS '创建人';
COMMENT ON COLUMN "register_unit_bd"."update_time" IS '修改时间';
COMMENT ON COLUMN "register_unit_bd"."update_user" IS '修改人';
COMMENT ON COLUMN "register_unit_bd"."is_delete" IS '是否删除';

drop index IF EXISTS  register_unit_bd_unit_level_code_idx;
CREATE INDEX register_unit_bd_unit_level_code_idx ON public.register_unit_bd USING btree (unit_level_code);
drop index IF EXISTS  register_unit_bd_unit_id_idx;
CREATE INDEX register_unit_bd_unit_id_idx ON public.register_unit_bd USING hash (unit_id);


DROP TABLE IF EXISTS "register_unit_bd";
CREATE TABLE IF NOT EXISTS "register_unit_bd" (
                                                  "id" varchar(64) NOT NULL,
    "type" varchar(64) ,
    "geocode" varchar(64) ,
    "unit_id" varchar(64) ,
    "unit_level_code" varchar(255) ,
    "unit_name" varchar(255) ,
    "old_info" text,
    "new_info" text ,
    "is_handle" int2 default 0,
    "handle_time" timestamp ,
    "remark" text ,
    "source_type" int2 ,
    "create_time" timestamp ,
    "create_user" varchar(32) ,
    "update_time" timestamp ,
    "update_user" varchar(32) ,
    "is_delete" int2  NOT NULL DEFAULT 0,
    CONSTRAINT "register_unit_bd_pkey" PRIMARY KEY ("id")
    )
;
COMMENT ON TABLE "register_unit_bd" IS '登记信息-预警感知-信息变动';
COMMENT ON COLUMN "register_unit_bd"."id" IS '主键id';
COMMENT ON COLUMN "register_unit_bd"."type" IS '变动类型';
COMMENT ON COLUMN "register_unit_bd"."geocode" IS '区划';
COMMENT ON COLUMN "register_unit_bd"."unit_id" IS '单位ID';
COMMENT ON COLUMN "register_unit_bd"."unit_level_code" IS '单位层级码';
COMMENT ON COLUMN "register_unit_bd"."unit_name" IS '单位名称';
COMMENT ON COLUMN "register_unit_bd"."old_info" IS '原信息';
COMMENT ON COLUMN "register_unit_bd"."new_info" IS '新信息';
COMMENT ON COLUMN "register_unit_bd"."is_handle" IS '是否处理';
COMMENT ON COLUMN "register_unit_bd"."handle_time" IS '处理时间';
COMMENT ON COLUMN "register_unit_bd"."remark" IS '备注';
COMMENT ON COLUMN "register_unit_bd"."source_type" IS '来源类型，1-自建，2-同步';
COMMENT ON COLUMN "register_unit_bd"."create_time" IS '创建时间';
COMMENT ON COLUMN "register_unit_bd"."create_user" IS '创建人';
COMMENT ON COLUMN "register_unit_bd"."update_time" IS '修改时间';
COMMENT ON COLUMN "register_unit_bd"."update_user" IS '修改人';
COMMENT ON COLUMN "register_unit_bd"."is_delete" IS '是否删除';

drop index IF EXISTS  register_unit_bd_unit_level_code_idx;
CREATE INDEX register_unit_bd_unit_level_code_idx ON public.register_unit_bd USING btree (unit_level_code);
drop index IF EXISTS  register_unit_bd_unit_id_idx;
CREATE INDEX register_unit_bd_unit_id_idx ON public.register_unit_bd USING hash (unit_id);



-- sd_org_relation  三定机构关系
-- sd_module   三定类型
-- sd_module_detail  三定明细
-- sd_duty_detail  细化职责
-- sd_core_business  核心业务
-- sd_business_item  业务事项
-- sd_business_item_one  业务事项一件事关联表
-- sd_one_thing  一件事

DROP TABLE IF EXISTS "sd_org_relation";
CREATE TABLE IF NOT EXISTS "sd_org_relation" (
    "id" varchar(64) NOT NULL,
    "unit_id" varchar(64) ,
    "unit_level_code" varchar(255) ,
    "module_count" int4 default 0 ,
    "module_detail_count" int4 default 0 ,
    "duty_detail_count" int4 default 0 ,
    "core_business_count" int4 default 0 ,
    "business_item_count" int4 default 0 ,
    "one_thing_count" int4 default 0 ,
    "create_time" timestamp ,
    "create_user" varchar(32) ,
    "update_time" timestamp ,
    "update_user" varchar(32) ,
    "is_delete" int2  NOT NULL DEFAULT 0,
    CONSTRAINT "sd_org_relation_pkey" PRIMARY KEY ("id")
    )
;
COMMENT ON TABLE "sd_org_relation" IS '三定-三定机构关联关系';
COMMENT ON COLUMN "sd_org_relation"."id" IS '主键id';
COMMENT ON COLUMN "sd_org_relation"."unit_id" IS '单位ID';
COMMENT ON COLUMN "sd_org_relation"."unit_level_code" IS '单位层级码';
COMMENT ON COLUMN "sd_org_relation"."module_count" IS '三定类型数量';
COMMENT ON COLUMN "sd_org_relation"."module_detail_count" IS '三定明细数量';
COMMENT ON COLUMN "sd_org_relation"."duty_detail_count" IS '细化职责数量';
COMMENT ON COLUMN "sd_org_relation"."core_business_count" IS '核心业务数量';
COMMENT ON COLUMN "sd_org_relation"."business_item_count" IS '业务事项数量';
COMMENT ON COLUMN "sd_org_relation"."one_thing_count" IS '一件事数量';

DROP TABLE IF EXISTS "sd_module";
CREATE TABLE IF NOT EXISTS "sd_module" (
    "id" varchar(64) NOT NULL,
    "unit_id" varchar(64) ,
    "unit_level_code" varchar(255) ,
    "unit_name" varchar(255) ,
    "geocode" varchar(64) ,
    "title" text ,
    "describe" text ,
    "remark" text ,
    "sort" int4 default 0,
    "create_time" timestamp ,
    "create_user" varchar(32) ,
    "update_time" timestamp ,
    "update_user" varchar(32) ,
    "is_delete" int2  NOT NULL DEFAULT 0,
    CONSTRAINT "sd_module_pkey" PRIMARY KEY ("id")
    )
;
COMMENT ON TABLE "sd_module" IS '三定-三定类型';
COMMENT ON COLUMN "sd_module"."id" IS '主键id';
COMMENT ON COLUMN "sd_module"."unit_id" IS '单位ID';
COMMENT ON COLUMN "sd_module"."unit_level_code" IS '单位层级码';
COMMENT ON COLUMN "sd_module"."unit_name" IS '单位名称';
COMMENT ON COLUMN "sd_module"."geocode" IS '区划code';
COMMENT ON COLUMN "sd_module"."title" IS '名称';
COMMENT ON COLUMN "sd_module"."describe" IS '描述';
COMMENT ON COLUMN "sd_module"."remark" IS '备注';
COMMENT ON COLUMN "sd_module"."sort" IS '排序号';


DROP TABLE IF EXISTS "sd_module_detail";
CREATE TABLE IF NOT EXISTS "sd_module_detail" (
    "id" varchar(64) NOT NULL,
    "unit_id" varchar(64) ,
    "unit_level_code" varchar(255) ,
    "unit_name" varchar(255) ,
    "geocode" varchar(64) ,
    "module_id" varchar(64) NOT NULL,
    "title" text ,
    "describe" text ,
    "remark" text ,
    "sort" int4 default 0,
    "create_time" timestamp ,
    "create_user" varchar(32) ,
    "update_time" timestamp ,
    "update_user" varchar(32) ,
    "is_delete" int2  NOT NULL DEFAULT 0,
    CONSTRAINT "sd_module_detail_pkey" PRIMARY KEY ("id")
    )
;
COMMENT ON TABLE "sd_module_detail" IS '三定-三定明细';
COMMENT ON COLUMN "sd_module_detail"."id" IS '主键id';
COMMENT ON COLUMN "sd_module_detail"."module_id" IS '三定类型主键';
COMMENT ON COLUMN "sd_module_detail"."unit_id" IS '单位ID';
COMMENT ON COLUMN "sd_module_detail"."unit_level_code" IS '单位层级码';
COMMENT ON COLUMN "sd_module_detail"."unit_name" IS '单位名称';
COMMENT ON COLUMN "sd_module_detail"."geocode" IS '区划code';
COMMENT ON COLUMN "sd_module_detail"."title" IS '名称';
COMMENT ON COLUMN "sd_module_detail"."describe" IS '描述';
COMMENT ON COLUMN "sd_module_detail"."remark" IS '备注';
COMMENT ON COLUMN "sd_module_detail"."sort" IS '排序号';

DROP TABLE IF EXISTS "sd_duty_detail";
CREATE TABLE IF NOT EXISTS "sd_duty_detail" (
    "id" varchar(64) NOT NULL,
    "module_id" varchar(64) NOT NULL,
    "module_detail_id" varchar(64) NOT NULL,
    "take_unit_id" varchar(64) ,
    "take_unit_level_code" varchar(255) ,
    "take_unit_name" varchar(255) ,
    "title" text ,
    "describe" text ,
    "remark" text ,
    "sort" int4 default 0,
    "create_time" timestamp ,
    "create_user" varchar(32) ,
    "update_time" timestamp ,
    "update_user" varchar(32) ,
    "is_delete" int2  NOT NULL DEFAULT 0,
    CONSTRAINT "sd_duty_detail_pkey" PRIMARY KEY ("id")
    )
;
COMMENT ON TABLE "sd_duty_detail" IS '三定-细化职责';
COMMENT ON COLUMN "sd_duty_detail"."id" IS '主键id';
COMMENT ON COLUMN "sd_duty_detail"."module_id" IS '三定类型主键';
COMMENT ON COLUMN "sd_duty_detail"."module_detail_id" IS '三定明细主键';
COMMENT ON COLUMN "sd_duty_detail"."take_unit_id" IS '承担处室id';
COMMENT ON COLUMN "sd_duty_detail"."take_unit_level_code" IS '承担处室层级码';
COMMENT ON COLUMN "sd_duty_detail"."take_unit_name" IS '承担处室名称';
COMMENT ON COLUMN "sd_duty_detail"."title" IS '名称';
COMMENT ON COLUMN "sd_duty_detail"."describe" IS '描述';
COMMENT ON COLUMN "sd_duty_detail"."remark" IS '备注';
COMMENT ON COLUMN "sd_duty_detail"."sort" IS '排序号';

DROP TABLE IF EXISTS "sd_core_business";
CREATE TABLE IF NOT EXISTS "sd_core_business" (
    "id" varchar(64) NOT NULL,
    "module_id" varchar(64) NOT NULL,
    "module_detail_id" varchar(64) NOT NULL,
    "duty_detail_id" varchar(64) NOT NULL,
    "title" text ,
    "describe" text ,
    "remark" text ,
    "sort" int4 default 0,
    "create_time" timestamp ,
    "create_user" varchar(32) ,
    "update_time" timestamp ,
    "update_user" varchar(32) ,
    "is_delete" int2  NOT NULL DEFAULT 0,
    CONSTRAINT "sd_core_business_pkey" PRIMARY KEY ("id")
    )
;
COMMENT ON TABLE "sd_core_business" IS '三定-核心业务';
COMMENT ON COLUMN "sd_core_business"."id" IS '主键id';
COMMENT ON COLUMN "sd_core_business"."module_id" IS '三定类型主键';
COMMENT ON COLUMN "sd_core_business"."module_detail_id" IS '三定明细主键';
COMMENT ON COLUMN "sd_core_business"."duty_detail_id" IS '细化职责主键';
COMMENT ON COLUMN "sd_core_business"."title" IS '名称';
COMMENT ON COLUMN "sd_core_business"."describe" IS '描述';
COMMENT ON COLUMN "sd_core_business"."remark" IS '备注';
COMMENT ON COLUMN "sd_core_business"."sort" IS '排序号';


DROP TABLE IF EXISTS "sd_business_item";
CREATE TABLE IF NOT EXISTS "sd_business_item" (
    "id" varchar(64) NOT NULL,
    "module_id" varchar(64) NOT NULL,
    "module_detail_id" varchar(64) NOT NULL,
    "duty_detail_id" varchar(64) NOT NULL,
    "core_business_id" varchar(64) NOT NULL,
    "title" text ,
    "describe" text ,
    "remark" text ,
    "sort" int4 default 0,
    "create_time" timestamp ,
    "create_user" varchar(32) ,
    "update_time" timestamp ,
    "update_user" varchar(32) ,
    "is_delete" int2  NOT NULL DEFAULT 0,
    CONSTRAINT "sd_business_item_pkey" PRIMARY KEY ("id")
    )
;
COMMENT ON TABLE "sd_business_item" IS '三定-业务事项';
COMMENT ON COLUMN "sd_business_item"."id" IS '主键id';
COMMENT ON COLUMN "sd_business_item"."module_id" IS '三定类型主键';
COMMENT ON COLUMN "sd_business_item"."module_detail_id" IS '三定明细主键';
COMMENT ON COLUMN "sd_business_item"."duty_detail_id" IS '细化职责主键';
COMMENT ON COLUMN "sd_business_item"."core_business_id" IS '核心业务主键';
COMMENT ON COLUMN "sd_business_item"."title" IS '名称';
COMMENT ON COLUMN "sd_business_item"."describe" IS '描述';
COMMENT ON COLUMN "sd_business_item"."remark" IS '备注';
COMMENT ON COLUMN "sd_business_item"."sort" IS '排序号';

DROP TABLE IF EXISTS "sd_one_thing";
CREATE TABLE IF NOT EXISTS "sd_one_thing" (
    "id" varchar(64) NOT NULL,
    "one_type" varchar(10) ,
    "title" text ,
    "describe" text ,
    "remark" text ,
    "sort" int4 default 0,
    "create_time" timestamp ,
    "create_user" varchar(32) ,
    "update_time" timestamp ,
    "update_user" varchar(32) ,
    "is_delete" int2  NOT NULL DEFAULT 0,
    CONSTRAINT "sd_one_thing_pkey" PRIMARY KEY ("id")
    )
;
COMMENT ON TABLE "sd_one_thing" IS '三定-一件事';
COMMENT ON COLUMN "sd_one_thing"."id" IS '主键id';
COMMENT ON COLUMN "sd_one_thing"."one_type" IS '一件事类型';
COMMENT ON COLUMN "sd_one_thing"."title" IS '名称';
COMMENT ON COLUMN "sd_one_thing"."describe" IS '描述';
COMMENT ON COLUMN "sd_one_thing"."remark" IS '备注';
COMMENT ON COLUMN "sd_one_thing"."sort" IS '排序号';

DROP TABLE IF EXISTS "sd_business_item_one";
CREATE TABLE IF NOT EXISTS "sd_business_item_one" (
    "id" varchar(64) NOT NULL,
    "item_id" varchar(64) NOT NULL,
    "one_id"  varchar(64) NOT NULL,
    "create_time" timestamp ,
    "create_user" varchar(32) ,
    "update_time" timestamp ,
    "update_user" varchar(32) ,
    "is_delete" int2  NOT NULL DEFAULT 0,
    CONSTRAINT "sd_business_item_one_pkey" PRIMARY KEY ("id")
    )
;
COMMENT ON TABLE "sd_business_item_one" IS '三定-业务事项一件事关联表';
COMMENT ON COLUMN "sd_business_item_one"."id" IS '主键id';
COMMENT ON COLUMN "sd_business_item_one"."item_id" IS '业务事项主键';
COMMENT ON COLUMN "sd_business_item_one"."one_id" IS '一件事主键';


DROP TABLE IF EXISTS "register_unit_bd_flow";
CREATE TABLE IF NOT EXISTS "register_unit_bd_flow" (
    "id" varchar(64) NOT NULL,
    "biz_id" varchar(64) NOT NULL,
    "code" varchar(64) NOT NULL,
    "describe"  varchar(64) ,
    "create_time" timestamp ,
    "create_user" varchar(32) ,
    "update_time" timestamp ,
    "update_user" varchar(32) ,
    "is_delete" int2  NOT NULL DEFAULT 0,
    CONSTRAINT "register_unit_bd_flow_pkey" PRIMARY KEY ("id")
    )
;
COMMENT ON TABLE "register_unit_bd_flow" IS '登记信息-变动流程记录';
COMMENT ON COLUMN "register_unit_bd_flow"."id" IS '主键id';
COMMENT ON COLUMN "register_unit_bd_flow"."biz_id" IS '业务主键';
COMMENT ON COLUMN "register_unit_bd_flow"."code" IS '变动code';
COMMENT ON COLUMN "register_unit_bd_flow"."describe" IS '描述';


DROP TABLE IF EXISTS "bz_unit_bd";
CREATE TABLE IF NOT EXISTS "bz_unit_bd" (
    "id" varchar(64) NOT NULL,
    "title" varchar(64) NOT NULL,
    "unit_id" varchar(64) NOT NULL,
    "unit_level_code" varchar(255) NOT NULL,
    "unit_name" varchar(255) NOT NULL,
    "smz_unit_code" varchar(255) ,
    "credit_code" varchar(255) ,
    "bd_type" varchar(64),
    "remark"  text ,
    "change_time" timestamp ,
    "create_time" timestamp ,
    "create_user" varchar(32) ,
    "update_time" timestamp ,
    "update_user" varchar(32) ,
    "is_delete" int2  NOT NULL DEFAULT 0,
    CONSTRAINT "bz_unit_bd_pkey" PRIMARY KEY ("id")
    )
;
COMMENT ON TABLE "bz_unit_bd" IS '核编管理-动态信息维护';
COMMENT ON COLUMN "bz_unit_bd"."id" IS '主键id';
COMMENT ON COLUMN "bz_unit_bd"."title" IS '标题';
COMMENT ON COLUMN "bz_unit_bd"."unit_id" IS '单位主键';
COMMENT ON COLUMN "bz_unit_bd"."unit_level_code" IS '单位层级码';
COMMENT ON COLUMN "bz_unit_bd"."unit_name" IS '单位名称';
COMMENT ON COLUMN "bz_unit_bd"."smz_unit_code" IS '实名制单位code';
COMMENT ON COLUMN "bz_unit_bd"."credit_code" IS '统一社会信用代码';
COMMENT ON COLUMN "bz_unit_bd"."bd_type" IS '变动类型，BZ_UNIT_BD_TYPE，默认值';
COMMENT ON COLUMN "bz_unit_bd"."remark" IS '备注';
COMMENT ON COLUMN "bz_unit_bd"."change_time" IS '感知时间';

DROP TABLE IF EXISTS "bz_ry_bd";
CREATE TABLE IF NOT EXISTS "bz_ry_bd" (
    "id" varchar(64) NOT NULL,
    "unit_bd_id" varchar(64) NOT NULL,
    "name" varchar(64),
    "id_card"  varchar(64) ,
    "bd_type"  varchar(64) ,
    "old_unit_id" varchar(64) ,
    "old_unit_level_code" varchar(255) ,
    "old_unit_name" varchar(255) ,
    "old_dept_id" varchar(255) ,
    "old_dept_level_code" varchar(255) ,
    "old_dept_name" varchar(255) ,
    "old_bz_type" varchar(255) ,
    "old_yb_type" varchar(255) ,
    "old_post_name" varchar(255) ,
    "old_smz_unit_code" varchar(255) ,
    "old_credit_code" varchar(255) ,

    "new_unit_id" varchar(64) ,
    "new_unit_level_code" varchar(255) ,
    "new_unit_name" varchar(255) ,
    "new_dept_id" varchar(255) ,
    "new_dept_level_code" varchar(255) ,
    "new_dept_name" varchar(255) ,
    "new_bz_type" varchar(255) ,
    "new_yb_type" varchar(255) ,
    "new_post_name" varchar(255) ,
    "new_smz_unit_code" varchar(255) ,
    "new_credit_code" varchar(255) ,

    "bd_time" timestamp ,
    "remark" text ,
    "create_time" timestamp ,
    "create_user" varchar(32) ,
    "update_time" timestamp ,
    "update_user" varchar(32) ,
    "is_delete" int2  NOT NULL DEFAULT 0,
    CONSTRAINT "bz_ry_bd_pkey" PRIMARY KEY ("id")
    )
;
COMMENT ON TABLE "bz_ry_bd" IS '核编管理-动态信息维护-人员详情';
COMMENT ON COLUMN "bz_ry_bd"."id" IS '主键id';
COMMENT ON COLUMN "bz_ry_bd"."unit_bd_id" IS '变动信息主键';
COMMENT ON COLUMN "bz_ry_bd"."name" IS '名称';
COMMENT ON COLUMN "bz_ry_bd"."id_card" IS '身份证';
COMMENT ON COLUMN "bz_ry_bd"."bd_type" IS '人员变动类型，BZ_RY_BD_TYPE';
COMMENT ON COLUMN "bz_ry_bd"."old_unit_id" IS '原单位ID';
COMMENT ON COLUMN "bz_ry_bd"."old_unit_level_code" IS '原单位层级码';
COMMENT ON COLUMN "bz_ry_bd"."old_unit_name" IS '原单位名称';
COMMENT ON COLUMN "bz_ry_bd"."old_dept_id" IS '原处室ID';
COMMENT ON COLUMN "bz_ry_bd"."old_dept_level_code" IS '原处室层级码';
COMMENT ON COLUMN "bz_ry_bd"."old_dept_name" IS '原处室层名称';
COMMENT ON COLUMN "bz_ry_bd"."old_bz_type" IS '原编制类型，BZ_BZLX';
COMMENT ON COLUMN "bz_ry_bd"."old_yb_type" IS '原用编类型，BZ_JFXX';
COMMENT ON COLUMN "bz_ry_bd"."old_smz_unit_code" IS '原实名制单位code';
COMMENT ON COLUMN "bz_ry_bd"."old_post_name" IS '原职务';
COMMENT ON COLUMN "bz_ry_bd"."old_credit_code" IS '原单位统一社会信用代码';

COMMENT ON COLUMN "bz_ry_bd"."new_unit_id" IS '现单位ID';
COMMENT ON COLUMN "bz_ry_bd"."new_unit_level_code" IS '现单位层级码';
COMMENT ON COLUMN "bz_ry_bd"."new_unit_name" IS '现单位名称';
COMMENT ON COLUMN "bz_ry_bd"."new_dept_id" IS '现处室ID';
COMMENT ON COLUMN "bz_ry_bd"."new_dept_level_code" IS '现处室层级码';
COMMENT ON COLUMN "bz_ry_bd"."new_dept_name" IS '现处室层名称';
COMMENT ON COLUMN "bz_ry_bd"."new_bz_type" IS '现编制类型，BZ_BZLX';
COMMENT ON COLUMN "bz_ry_bd"."new_yb_type" IS '现用编类型，BZ_JFXX';
COMMENT ON COLUMN "bz_ry_bd"."new_smz_unit_code" IS '现实名制单位code';
COMMENT ON COLUMN "bz_ry_bd"."new_post_name" IS '现职务';
COMMENT ON COLUMN "bz_ry_bd"."new_credit_code" IS '现单位统一社会信用代码';
COMMENT ON COLUMN "bz_ry_bd"."bd_time" IS '变动时间';
COMMENT ON COLUMN "bz_ry_bd"."remark" IS '备注';


DROP TABLE IF EXISTS "register_double_random";
CREATE TABLE IF NOT EXISTS "register_double_random" (
    "id" varchar(64) NOT NULL,
    "record_unit_id" varchar(255) ,
    "record_unit_level_code" varchar(255) ,
    "record_unit_name" varchar(255) ,
    "unit_name" varchar(255) ,
    "credit_code" varchar(100) ,
    "certificate_num" varchar(100) ,
    "legal_representative" varchar(100) ,
    "source_of_funds" text ,
    "start_up_capital" varchar(255) ,
    "hold_organizer" varchar(255) ,
    "business_scope" text ,
    "domicile" text ,
    "start_time" timestamp ,
    "end_time" timestamp ,
    "org_id" varchar(255) ,
    "org_level_code" varchar(255) ,
    "check_date" timestamp ,
    "check_type" text ,
    "check_result" text ,
    "check_unit" varchar(100) ,
    "check_user" varchar(100),
    "check_user_phone" varchar(100),
    "check_record" text,
    "create_time" timestamp ,
    "create_user" varchar(32) ,
    "update_time" timestamp ,
    "update_user" varchar(32) ,
    "is_delete" int2  NOT NULL DEFAULT 0,

    CONSTRAINT "register_double_random_pkey" PRIMARY KEY ("id")
    )
;
COMMENT ON TABLE "register_double_random" IS '登记信息-内部事务-双随机检查';
COMMENT ON COLUMN "register_double_random"."id" IS '主键id';
COMMENT ON COLUMN "register_double_random"."record_unit_id" IS '录入单位ID';
COMMENT ON COLUMN "register_double_random"."record_unit_level_code" IS '录入单位层级码';
COMMENT ON COLUMN "register_double_random"."record_unit_name" IS '录入单位名称';
COMMENT ON COLUMN "register_double_random"."unit_name" IS '名称';
COMMENT ON COLUMN "register_double_random"."credit_code" IS '统一社会信用代码';
COMMENT ON COLUMN "register_double_random"."certificate_num" IS '证书编号';
COMMENT ON COLUMN "register_double_random"."domicile" IS '住所';
COMMENT ON COLUMN "register_double_random"."legal_representative" IS '法定代表人';
COMMENT ON COLUMN "register_double_random"."source_of_funds" IS '经费来源';
COMMENT ON COLUMN "register_double_random"."start_up_capital" IS '开办资金';
COMMENT ON COLUMN "register_double_random"."hold_organizer" IS '举办单位';
COMMENT ON COLUMN "register_double_random"."business_scope" IS '宗旨和业务范围';
COMMENT ON COLUMN "register_double_random"."start_time" IS '开始时间';
COMMENT ON COLUMN "register_double_random"."end_time" IS '结束时间';
COMMENT ON COLUMN "register_double_random"."check_date" IS '抽取时间';
COMMENT ON COLUMN "register_double_random"."check_type" IS '抽取方式';
COMMENT ON COLUMN "register_double_random"."check_result" IS '检查结果';
COMMENT ON COLUMN "register_double_random"."check_unit" IS '检查单位';
COMMENT ON COLUMN "register_double_random"."check_user" IS '联系人';
COMMENT ON COLUMN "register_double_random"."check_user_phone" IS '联系电话';
COMMENT ON COLUMN "register_double_random"."check_record" IS '检查记录';
COMMENT ON COLUMN "register_double_random"."create_time" IS '创建时间';
COMMENT ON COLUMN "register_double_random"."create_user" IS '创建人';
COMMENT ON COLUMN "register_double_random"."update_time" IS '修改时间';
COMMENT ON COLUMN "register_double_random"."update_user" IS '修改人';
COMMENT ON COLUMN "register_double_random"."is_delete" IS '是否删除';


DROP TABLE IF EXISTS "bz_qx_bzsq";
CREATE TABLE IF NOT EXISTS "bz_qx_bzsq" (
    "id" varchar(64) NOT NULL,
    "geocode" varchar(255) ,
    "record_unit_id" varchar(255) ,
    "record_unit_level_code" varchar(255) ,
    "record_unit_name" varchar(255) ,
    "title" text ,
    "apply_unit_id" varchar(255) ,
    "apply_unit_name" varchar(255) ,
    "apply_unit_level_code" varchar(255) ,
    "apply_time" timestamp ,
    "contact_name" varchar(100) ,
    "contact_phone" varchar(100) ,
    "status" varchar(2) ,
    "check_reason" text ,
    "check_time" timestamp,
    "check_user_id" varchar(255) ,
    "create_time" timestamp ,
    "create_user" varchar(32) ,
    "update_time" timestamp ,
    "update_user" varchar(32) ,
    "is_delete" int2  NOT NULL DEFAULT 0,

    CONSTRAINT "bz_qx_bzsq_pkey" PRIMARY KEY ("id")
    )
;
COMMENT ON TABLE "bz_qx_bzsq" IS '核编管理-用编管理-区县用编申请计划';
COMMENT ON COLUMN "bz_qx_bzsq"."id" IS '主键id';
COMMENT ON COLUMN "bz_qx_bzsq"."geocode" IS '所属区县';
COMMENT ON COLUMN "bz_qx_bzsq"."record_unit_id" IS '录入单位ID';
COMMENT ON COLUMN "bz_qx_bzsq"."record_unit_level_code" IS '录入单位层级码';
COMMENT ON COLUMN "bz_qx_bzsq"."record_unit_name" IS '录入单位名称';
COMMENT ON COLUMN "bz_qx_bzsq"."title" IS '标题';
COMMENT ON COLUMN "bz_qx_bzsq"."apply_unit_id" IS '申请单位ID';
COMMENT ON COLUMN "bz_qx_bzsq"."apply_unit_name" IS '申请单位名称';
COMMENT ON COLUMN "bz_qx_bzsq"."apply_unit_level_code" IS '申请单位层级码';
COMMENT ON COLUMN "bz_qx_bzsq"."apply_time" IS '申请时间';
COMMENT ON COLUMN "bz_qx_bzsq"."contact_name" IS '联系人姓名';
COMMENT ON COLUMN "bz_qx_bzsq"."contact_phone" IS '联系人电话';
COMMENT ON COLUMN "bz_qx_bzsq"."status" IS '审核状态';
COMMENT ON COLUMN "bz_qx_bzsq"."check_reason" IS '审核理由';
COMMENT ON COLUMN "bz_qx_bzsq"."check_time" IS '审核时间';
COMMENT ON COLUMN "bz_qx_bzsq"."check_user_id" IS '审核人';
COMMENT ON COLUMN "bz_qx_bzsq"."create_time" IS '创建时间';
COMMENT ON COLUMN "bz_qx_bzsq"."create_user" IS '创建人';
COMMENT ON COLUMN "bz_qx_bzsq"."update_time" IS '修改时间';
COMMENT ON COLUMN "bz_qx_bzsq"."update_user" IS '修改人';
COMMENT ON COLUMN "bz_qx_bzsq"."is_delete" IS '是否删除';

DROP TABLE IF EXISTS "bz_qx_bzsq_detail";
CREATE TABLE IF NOT EXISTS "bz_qx_bzsq_detail" (
    "id" varchar(64) NOT NULL,
    "qx_bzsq_id" varchar(64) NOT NULL,
    "bz_type" varchar(10) ,
    "yb_type" varchar(255) ,
    "apply_used" text ,
    "valid_time" timestamp,
    "apply_num" int4 ,
    "remark" text,
    "create_time" timestamp ,
    "create_user" varchar(32) ,
    "update_time" timestamp ,
    "update_user" varchar(32) ,
    "is_delete" int2  NOT NULL DEFAULT 0,
    CONSTRAINT "bz_qx_bzsq_detail_pkey" PRIMARY KEY ("id")
    )
;
COMMENT ON TABLE "bz_qx_bzsq_detail" IS '核编管理-用编管理-区县用编申请计划明细';
COMMENT ON COLUMN "bz_qx_bzsq_detail"."id" IS '主键id';
COMMENT ON COLUMN "bz_qx_bzsq_detail"."qx_bzsq_id" IS '录入单位ID';
COMMENT ON COLUMN "bz_qx_bzsq_detail"."bz_type" IS '编制类型，字典表-BZ_BZLX';
COMMENT ON COLUMN "bz_qx_bzsq_detail"."yb_type" IS '用编类型，字典表-BZ_YBLX';
COMMENT ON COLUMN "bz_qx_bzsq_detail"."apply_used" IS '申请用途';
COMMENT ON COLUMN "bz_qx_bzsq_detail"."valid_time" IS '有效期限';
COMMENT ON COLUMN "bz_qx_bzsq_detail"."apply_num" IS '申请数量';
COMMENT ON COLUMN "bz_qx_bzsq_detail"."remark" IS '备注';
COMMENT ON COLUMN "bz_qx_bzsq_detail"."create_time" IS '创建时间';
COMMENT ON COLUMN "bz_qx_bzsq_detail"."create_user" IS '创建人';
COMMENT ON COLUMN "bz_qx_bzsq_detail"."update_time" IS '修改时间';
COMMENT ON COLUMN "bz_qx_bzsq_detail"."update_user" IS '修改人';
COMMENT ON COLUMN "bz_qx_bzsq_detail"."is_delete" IS '是否删除';


ALTER TABLE  "sd_module_detail"  ADD  record_num text;
comment on column sd_module_detail.record_num is '文号'

ALTER TABLE  "sd_duty_detail"  ADD  record_num text;
comment on column sd_duty_detail.record_num is '文号'

ALTER TABLE  "bz_bzsq"  ADD  remark text;
comment on column bz_bzsq.remark is '备注'


ALTER TABLE  "bz_bzsq_merge"  ADD  remark text;
comment on column bz_bzsq_merge.remark is '备注'

ALTER TABLE  "bz_bzsq_detail"  ADD  use_bz_unit_id varchar(100);
comment on column bz_bzsq_detail.use_bz_unit_id is '编制使用单位id'
ALTER TABLE  "bz_bzsq_detail"  ADD  use_bz_unit_name varchar(100);
comment on column bz_bzsq_detail.use_bz_unit_name is '编制使用单位名称'
ALTER TABLE  "bz_bzsq_detail"  ADD  use_bz_unit_level_code varchar(100);
comment on column bz_bzsq_detail.use_bz_unit_level_code is '编制使用单位层级码'


DROP TABLE IF EXISTS "bz_aduit_opinion";
CREATE TABLE IF NOT EXISTS "bz_aduit_opinion" (
    "id" varchar(64) NOT NULL,
    "module" varchar(64) NOT NULL,
    "name" text NOT NULL,
    "sort" int4 ,
    "create_time" timestamp NOT NULL,
    "create_user" varchar(32) NOT NULL,
    "update_time" timestamp ,
    "update_user" varchar(32) ,
    "is_delete" int2  NOT NULL DEFAULT 0,
    CONSTRAINT "bz_aduit_opinion_pkey" PRIMARY KEY ("id")
    )
;
COMMENT ON TABLE "bz_aduit_opinion" IS '核编管理-常用审批意见';
COMMENT ON COLUMN "bz_aduit_opinion"."id" IS '主键id';
COMMENT ON COLUMN "bz_aduit_opinion"."module" IS '模块名称';
COMMENT ON COLUMN "bz_aduit_opinion"."name" IS '意见';
COMMENT ON COLUMN "bz_aduit_opinion"."sort" IS '排序';
COMMENT ON COLUMN "bz_aduit_opinion"."create_time" IS '创建时间';
COMMENT ON COLUMN "bz_aduit_opinion"."create_user" IS '创建人';
COMMENT ON COLUMN "bz_aduit_opinion"."update_time" IS '修改时间';
COMMENT ON COLUMN "bz_aduit_opinion"."update_user" IS '修改人';
COMMENT ON COLUMN "bz_aduit_opinion"."is_delete" IS '是否删除';