ALTER TABLE BBYKZ."org_info" MODIFY "id" VARCHAR(100) NOT NULL;
ALTER TABLE BBYKZ."org_info" MODIFY "level_code" VARCHAR(100) NOT NULL;
ALTER TABLE BBYKZ."org_info" MODIFY "parent_code" VARCHAR(100) NOT NULL;
ALTER TABLE BBYKZ."org_info" MODIFY "jgsy_code" VARCHAR(100) NOT NULL;
ALTER TABLE BBYKZ."org_info" MODIFY "name" VARCHAR(200) NOT NULL;
ALTER TABLE BBYKZ."org_info" MODIFY "short_name" VARCHAR(200) NOT NULL;


ALTER TABLE BBYKZ."sys_user" ADD SSDW_ID varchar(200) NULL;
COMMENT ON COLUMN "sys_user"."SSDW_ID" IS '所属单位ID';


        -- SYSTEMCODE definition

CREATE TABLE SYSTEMCODE (
                                   SYSTEM_CODE VARCHAR2(2) NOT NULL,
                                   SYSTEM_CODE_NAME VARCHAR2(100) NULL,
                                   SYSTEM_CODE_TYPE VARCHAR2(2) NULL,
                                   SYSTEM_CODE_DESP VARCHAR2(200) NULL,
                                   SYSTEM_CODE_STANDBY1 VARCHAR2(100) NULL,
                                   SYSTEM_CODE_ORDER INTEGER NULL,
                                   CONSTRAINT SYS_C0011177 PRIMARY KEY (SYSTEM_CODE)
);
CREATE UNIQUE INDEX INDEX33564983 ON SYSTEMCODE (SYSTEM_CODE);

INSERT INTO SYSTEMCODE
(SYSTEM_CODE, SYSTEM_CODE_NAME, SYSTEM_CODE_TYPE, SYSTEM_CODE_DESP, SYSTEM_CODE_STANDBY1, SYSTEM_CODE_ORDER)
VALUES('01', '党委', '1', '党委', '党中央', 1);
INSERT INTO SYSTEMCODE
(SYSTEM_CODE, SYSTEM_CODE_NAME, SYSTEM_CODE_TYPE, SYSTEM_CODE_DESP, SYSTEM_CODE_STANDBY1, SYSTEM_CODE_ORDER)
VALUES('02', '人大', '1', '人大', '全国人大机关', 2);
INSERT INTO SYSTEMCODE
(SYSTEM_CODE, SYSTEM_CODE_NAME, SYSTEM_CODE_TYPE, SYSTEM_CODE_DESP, SYSTEM_CODE_STANDBY1, SYSTEM_CODE_ORDER)
VALUES('03', '政府', '1', '政府', '国务院', 3);
INSERT INTO SYSTEMCODE
(SYSTEM_CODE, SYSTEM_CODE_NAME, SYSTEM_CODE_TYPE, SYSTEM_CODE_DESP, SYSTEM_CODE_STANDBY1, SYSTEM_CODE_ORDER)
VALUES('04', '政协', '1', '政协', '全国政协机关', 4);
INSERT INTO SYSTEMCODE
(SYSTEM_CODE, SYSTEM_CODE_NAME, SYSTEM_CODE_TYPE, SYSTEM_CODE_DESP, SYSTEM_CODE_STANDBY1, SYSTEM_CODE_ORDER)
VALUES('11', '监察机关', '1', '监察机关', '监察机关', 5);
INSERT INTO SYSTEMCODE
(SYSTEM_CODE, SYSTEM_CODE_NAME, SYSTEM_CODE_TYPE, SYSTEM_CODE_DESP, SYSTEM_CODE_STANDBY1, SYSTEM_CODE_ORDER)
VALUES('07', '法院', '1', '法院', '最高法院机关', 6);
INSERT INTO SYSTEMCODE
(SYSTEM_CODE, SYSTEM_CODE_NAME, SYSTEM_CODE_TYPE, SYSTEM_CODE_DESP, SYSTEM_CODE_STANDBY1, SYSTEM_CODE_ORDER)
VALUES('08', '检察院', '1', '检察院', '最高检察院机关', 7);
INSERT INTO SYSTEMCODE
(SYSTEM_CODE, SYSTEM_CODE_NAME, SYSTEM_CODE_TYPE, SYSTEM_CODE_DESP, SYSTEM_CODE_STANDBY1, SYSTEM_CODE_ORDER)
VALUES('05', '民主党派', '1', '民主党派', '民主党派中央机关', 8);
INSERT INTO SYSTEMCODE
(SYSTEM_CODE, SYSTEM_CODE_NAME, SYSTEM_CODE_TYPE, SYSTEM_CODE_DESP, SYSTEM_CODE_STANDBY1, SYSTEM_CODE_ORDER)
VALUES('06', '群众团体', '1', '群众团体', '群众团体机关', 9);
INSERT INTO SYSTEMCODE
(SYSTEM_CODE, SYSTEM_CODE_NAME, SYSTEM_CODE_TYPE, SYSTEM_CODE_DESP, SYSTEM_CODE_STANDBY1, SYSTEM_CODE_ORDER)
VALUES('09', '经济实体', '1', '经济实体', '经济实体', 10);
INSERT INTO SYSTEMCODE
(SYSTEM_CODE, SYSTEM_CODE_NAME, SYSTEM_CODE_TYPE, SYSTEM_CODE_DESP, SYSTEM_CODE_STANDBY1, SYSTEM_CODE_ORDER)
VALUES('10', '其他', '1', '其他', '其他', 11);
INSERT INTO SYSTEMCODE
(SYSTEM_CODE, SYSTEM_CODE_NAME, SYSTEM_CODE_TYPE, SYSTEM_CODE_DESP, SYSTEM_CODE_STANDBY1, SYSTEM_CODE_ORDER)
VALUES('0x', '街道', '2', '街道', '街道', 1);
INSERT INTO SYSTEMCODE
(SYSTEM_CODE, SYSTEM_CODE_NAME, SYSTEM_CODE_TYPE, SYSTEM_CODE_DESP, SYSTEM_CODE_STANDBY1, SYSTEM_CODE_ORDER)
VALUES('0y', '乡', '2', '乡', '乡', 2);
INSERT INTO SYSTEMCODE
(SYSTEM_CODE, SYSTEM_CODE_NAME, SYSTEM_CODE_TYPE, SYSTEM_CODE_DESP, SYSTEM_CODE_STANDBY1, SYSTEM_CODE_ORDER)
VALUES('0z', '镇', '2', '镇', '镇', 3);

delete from  BBYKZ."geocode";
INSERT INTO BBYKZ."geocode"
("code", "name", "type", "sort", "org_id", "level_code")
VALUES('500000', '重庆市', '2', 1, NULL, NULL);
INSERT INTO BBYKZ."geocode"
("code", "name", "type", "sort", "org_id", "level_code")
VALUES('500100', '市辖区', '8', 2, NULL, NULL);
INSERT INTO BBYKZ."geocode"
("code", "name", "type", "sort", "org_id", "level_code")
VALUES('500200', '县', '9', 3, NULL, NULL);
INSERT INTO BBYKZ."geocode"
("code", "name", "type", "sort", "org_id", "level_code")
VALUES('500101', '万州区', '4', 10, NULL, NULL);
INSERT INTO BBYKZ."geocode"
("code", "name", "type", "sort", "org_id", "level_code")
VALUES('500102', '涪陵区', '4', 20, NULL, NULL);
INSERT INTO BBYKZ."geocode"
("code", "name", "type", "sort", "org_id", "level_code")
VALUES('500103', '渝中区', '4', 30, NULL, NULL);
INSERT INTO BBYKZ."geocode"
("code", "name", "type", "sort", "org_id", "level_code")
VALUES('500104', '大渡口区', '4', 40, NULL, NULL);
INSERT INTO BBYKZ."geocode"
("code", "name", "type", "sort", "org_id", "level_code")
VALUES('500105', '江北区', '4', 50, NULL, NULL);
INSERT INTO BBYKZ."geocode"
("code", "name", "type", "sort", "org_id", "level_code")
VALUES('500106', '沙坪坝区', '4', 60, NULL, NULL);
INSERT INTO BBYKZ."geocode"
("code", "name", "type", "sort", "org_id", "level_code")
VALUES('500107', '九龙坡区', '4', 70, NULL, NULL);
INSERT INTO BBYKZ."geocode"
("code", "name", "type", "sort", "org_id", "level_code")
VALUES('500108', '南岸区', '4', 80, NULL, NULL);
INSERT INTO BBYKZ."geocode"
("code", "name", "type", "sort", "org_id", "level_code")
VALUES('500109', '北碚区', '4', 90, NULL, NULL);
INSERT INTO BBYKZ."geocode"
("code", "name", "type", "sort", "org_id", "level_code")
VALUES('500124', '綦江区', '4', 100, NULL, NULL);
INSERT INTO BBYKZ."geocode"
("code", "name", "type", "sort", "org_id", "level_code")
VALUES('500125', '大足区', '4', 110, NULL, NULL);
INSERT INTO BBYKZ."geocode"
("code", "name", "type", "sort", "org_id", "level_code")
VALUES('500112', '渝北区', '4', 12, NULL, NULL);
INSERT INTO BBYKZ."geocode"
("code", "name", "type", "sort", "org_id", "level_code")
VALUES('500113', '巴南区', '4', 130, NULL, NULL);
INSERT INTO BBYKZ."geocode"
("code", "name", "type", "sort", "org_id", "level_code")
VALUES('500114', '黔江区', '4', 140, NULL, NULL);
INSERT INTO BBYKZ."geocode"
("code", "name", "type", "sort", "org_id", "level_code")
VALUES('500115', '长寿区', '4', 150, NULL, NULL);
INSERT INTO BBYKZ."geocode"
("code", "name", "type", "sort", "org_id", "level_code")
VALUES('500119', '江津区', '4', 160, NULL, NULL);
INSERT INTO BBYKZ."geocode"
("code", "name", "type", "sort", "org_id", "level_code")
VALUES('500120', '合川区', '4', 170, NULL, NULL);
INSERT INTO BBYKZ."geocode"
("code", "name", "type", "sort", "org_id", "level_code")
VALUES('500118', '两江新区', '4', 300, NULL, NULL);
INSERT INTO BBYKZ."geocode"
("code", "name", "type", "sort", "org_id", "level_code")
VALUES('500121', '永川区', '4', 180, NULL, NULL);
INSERT INTO BBYKZ."geocode"
("code", "name", "type", "sort", "org_id", "level_code")
VALUES('500122', '南川区', '4', 190, NULL, NULL);
INSERT INTO BBYKZ."geocode"
("code", "name", "type", "sort", "org_id", "level_code")
VALUES('500227', '璧山区', '4', 200, NULL, NULL);
INSERT INTO BBYKZ."geocode"
("code", "name", "type", "sort", "org_id", "level_code")
VALUES('500224', '铜梁区', '4', 210, NULL, NULL);
INSERT INTO BBYKZ."geocode"
("code", "name", "type", "sort", "org_id", "level_code")
VALUES('500223', '潼南区', '4', 220, NULL, NULL);
INSERT INTO BBYKZ."geocode"
("code", "name", "type", "sort", "org_id", "level_code")
VALUES('500226', '荣昌区', '4', 230, NULL, NULL);
INSERT INTO BBYKZ."geocode"
("code", "name", "type", "sort", "org_id", "level_code")
VALUES('500234', '开州区', '4', 240, NULL, NULL);
INSERT INTO BBYKZ."geocode"
("code", "name", "type", "sort", "org_id", "level_code")
VALUES('500228', '梁平区', '4', 250, NULL, NULL);
INSERT INTO BBYKZ."geocode"
("code", "name", "type", "sort", "org_id", "level_code")
VALUES('500232', '武隆区', '4', 260, NULL, NULL);
INSERT INTO BBYKZ."geocode"
("code", "name", "type", "sort", "org_id", "level_code")
VALUES('500229', '城口县', '5', 1270, NULL, NULL);
INSERT INTO BBYKZ."geocode"
("code", "name", "type", "sort", "org_id", "level_code")
VALUES('500230', '丰都县', '5', 1280, NULL, NULL);
INSERT INTO BBYKZ."geocode"
("code", "name", "type", "sort", "org_id", "level_code")
VALUES('500231', '垫江县', '5', 1290, NULL, NULL);
INSERT INTO BBYKZ."geocode"
("code", "name", "type", "sort", "org_id", "level_code")
VALUES('500233', '忠县', '5', 1300, NULL, NULL);
INSERT INTO BBYKZ."geocode"
("code", "name", "type", "sort", "org_id", "level_code")
VALUES('500235', '云阳县', '5', 1310, NULL, NULL);
INSERT INTO BBYKZ."geocode"
("code", "name", "type", "sort", "org_id", "level_code")
VALUES('500236', '奉节县', '5', 1320, NULL, NULL);
INSERT INTO BBYKZ."geocode"
("code", "name", "type", "sort", "org_id", "level_code")
VALUES('500237', '巫山县', '5', 1330, NULL, NULL);
INSERT INTO BBYKZ."geocode"
("code", "name", "type", "sort", "org_id", "level_code")
VALUES('500238', '巫溪县', '5', 1340, NULL, NULL);
INSERT INTO BBYKZ."geocode"
("code", "name", "type", "sort", "org_id", "level_code")
VALUES('500240', '石柱土家族自治县', '5', 1350, NULL, NULL);
INSERT INTO BBYKZ."geocode"
("code", "name", "type", "sort", "org_id", "level_code")
VALUES('500241', '秀山土家族苗族自治县', '5', 1360, NULL, NULL);
INSERT INTO BBYKZ."geocode"
("code", "name", "type", "sort", "org_id", "level_code")
VALUES('500242', '酉阳土家族苗族自治县', '5', 1370, NULL, NULL);
INSERT INTO BBYKZ."geocode"
("code", "name", "type", "sort", "org_id", "level_code")
VALUES('500243', '彭水苗族土家族自治县', '5', 1380, NULL, NULL);


INSERT INTO BBYKZ.efficient_dict_code
("code_type", "code", "code_name", "short_name", "sort", "is_enable", "parent_code", "code_level", "is_leaf")
VALUES('34', '01', '中央部委机构', '中央部委机构', 1, 1, -1, 1, 1);
INSERT INTO BBYKZ.efficient_dict_code
("code_type", "code", "code_name", "short_name", "sort", "is_enable", "parent_code", "code_level", "is_leaf")
VALUES('34', '02', '中央司局级机构', '中央司局级机构', 2, 1, -1, 1, 1);
INSERT INTO BBYKZ.efficient_dict_code
("code_type", "code", "code_name", "short_name", "sort", "is_enable", "parent_code", "code_level", "is_leaf")
VALUES('34', '03', '中央处级机构', '中央处级机构', 3, 1, -1, 1, 1);
INSERT INTO BBYKZ.efficient_dict_code
("code_type", "code", "code_name", "short_name", "sort", "is_enable", "parent_code", "code_level", "is_leaf")
VALUES('34', '04', '地方行政机关', '地方行政机关', 4, 1, -1, 1, 1);
INSERT INTO BBYKZ.efficient_dict_code
("code_type", "code", "code_name", "short_name", "sort", "is_enable", "parent_code", "code_level", "is_leaf")
VALUES('34', '05', '内设机构', '内设机构', 5, 1, -1, 1, 1);
INSERT INTO BBYKZ.efficient_dict_code
("code_type", "code", "code_name", "short_name", "sort", "is_enable", "parent_code", "code_level", "is_leaf")
VALUES('34', '06', '下设机构', '下设机构', 6, 1, -1, 1, 1);
INSERT INTO BBYKZ.efficient_dict_code
("code_type", "code", "code_name", "short_name", "sort", "is_enable", "parent_code", "code_level", "is_leaf")
VALUES('34', '07', '下设机构内设机构', '下设机构内设机构', 7, 1, -1, 1, 1);
INSERT INTO BBYKZ.efficient_dict_code
("code_type", "code", "code_name", "short_name", "sort", "is_enable", "parent_code", "code_level", "is_leaf")
VALUES('34', '08', '垂直管理部门', '垂直管理部门', 8, 1, -1, 1, 1);
INSERT INTO BBYKZ.efficient_dict_code
("code_type", "code", "code_name", "short_name", "sort", "is_enable", "parent_code", "code_level", "is_leaf")
VALUES('34', '09', '垂直管理部门内设机构', '垂直管理部门内设机构', 9, 1, -1, 1, 1);
INSERT INTO BBYKZ.efficient_dict_code
("code_type", "code", "code_name", "short_name", "sort", "is_enable", "parent_code", "code_level", "is_leaf")
VALUES('34', '10', '街道乡镇', '街道乡镇', 10, 1, -1, 1, 1);
INSERT INTO BBYKZ.efficient_dict_code
("code_type", "code", "code_name", "short_name", "sort", "is_enable", "parent_code", "code_level", "is_leaf")
VALUES('34', '11', '街道乡镇内设机构', '街道乡镇内设机构', 11, 1, -1, 1, 1);
INSERT INTO BBYKZ.efficient_dict_code
("code_type", "code", "code_name", "short_name", "sort", "is_enable", "parent_code", "code_level", "is_leaf")
VALUES('34', '12', '事业单位', '事业单位', 12, 1, -1, 1, 1);
INSERT INTO BBYKZ.efficient_dict_code
("code_type", "code", "code_name", "short_name", "sort", "is_enable", "parent_code", "code_level", "is_leaf")
VALUES('34', '13', '事业单位内设机构', '事业单位内设机构', 13, 1, -1, 1, 1);
INSERT INTO BBYKZ.efficient_dict_code
("code_type", "code", "code_name", "short_name", "sort", "is_enable", "parent_code", "code_level", "is_leaf")
VALUES('34', '14', '经济实体', '经济实体', 14, 1, -1, 1, 1);
INSERT INTO BBYKZ.efficient_dict_code
("code_type", "code", "code_name", "short_name", "sort", "is_enable", "parent_code", "code_level", "is_leaf")
VALUES('34', '15', '其他', '其他', 15, 1, -1, 1, 1);

ALTER TABLE BBYKZ."efficient_sys_notify" MODIFY "is_delete" SMALLINT DEFAULT 0 NULL;
CREATE INDEX LEGAL_PERSON_REGISTRATION_UNIFIED_SOCIAL_CREDIT_CODE_IDX ON BBYKZ."legal_person_registration" ("unified_social_credit_code");
ALTER TABLE BBYKZ."efficient_sys_file_info" DROP COLUMN "remark";

INSERT INTO BBYKZ."efficient_sys_task"
("id", "task_code", "task_describe", "task_class", "enabled", "cron_expression", "create_time", "task_status")
VALUES('3', 'SYNC_SMZ', '同步SMZ数据', 'com.zenith.bbykz.service.task.SmzUserCenterTask', 1, '0 30 2 * * ?', '2024-05-16 14:56:44.282', 1);


-- 2024-09-09

DELETE FROM "register_unit_bd" ;
DELETE FROM efficient_dict_code WHERE "code_type" ='JGSY_BD_TYPE';

INSERT INTO BBYKZ."efficient_dict_code"
("code_type", "code", "code_name", "short_name", "sort", "is_enable", "parent_code", "code_level", "is_leaf", "pin_yin", "remark")
VALUES('JGSY_BD_TYPE', '01', '统一社会信用代码为空（A）', '统一社会信用代码为空（A）', 1, 1, '-1', 1, 1, NULL, NULL);
INSERT INTO BBYKZ."efficient_dict_code"
("code_type", "code", "code_name", "short_name", "sort", "is_enable", "parent_code", "code_level", "is_leaf", "pin_yin", "remark")
VALUES('JGSY_BD_TYPE', '02', '统一社会信用代码为空（B）', '统一社会信用代码为空（B）', 2, 1, '-1', 1, 1, NULL, NULL);
INSERT INTO BBYKZ."efficient_dict_code"
("code_type", "code", "code_name", "short_name", "sort", "is_enable", "parent_code", "code_level", "is_leaf", "pin_yin", "remark")
VALUES('JGSY_BD_TYPE', '03', '机构不匹配（A）', '机构不匹配（A）', 3, 1, '-1', 1, 1, NULL, NULL);
INSERT INTO BBYKZ."efficient_dict_code"
("code_type", "code", "code_name", "short_name", "sort", "is_enable", "parent_code", "code_level", "is_leaf", "pin_yin", "remark")
VALUES('JGSY_BD_TYPE', '04', '中央系统中证书到期', '中央系统中证书到期', 9, 1, '-1', 1, 1, NULL, NULL);
INSERT INTO BBYKZ."efficient_dict_code"
("code_type", "code", "code_name", "short_name", "sort", "is_enable", "parent_code", "code_level", "is_leaf", "pin_yin", "remark")
VALUES('JGSY_BD_TYPE', '05', '机构不匹配（B）', '机构不匹配（B）', 4, 1, '-1', 1, 1, NULL, NULL);
INSERT INTO BBYKZ."efficient_dict_code"
("code_type", "code", "code_name", "short_name", "sort", "is_enable", "parent_code", "code_level", "is_leaf", "pin_yin", "remark")
VALUES('JGSY_BD_TYPE', '06', '名称不一致', '名称不一致', 5, 1, '-1', 1, 1, NULL, NULL);


--  20240910
-- DROP INDEX BBYKZ.LEGAL_PERSON_REGISTRATION_IS_DELETE_IDX;
CREATE INDEX LEGAL_PERSON_REGISTRATION_REGISTRATION_AUTHORITY_CODE_IDX ON BBYKZ."legal_person_registration" ("registration_authority_code");

ALTER TABLE BBYKZ."legal_person_registration" ADD NAME_TEST varchar(2000) NULL;
ALTER TABLE BBYKZ."legal_person_registration" ADD STATUS_TEXT varchar(100) NULL;
UPDATE legal_person_registration SET NAME_TEST  = name;
UPDATE legal_person_registration SET STATUS_TEXT  = "status" ;
ALTER TABLE BBYKZ."legal_person_registration" DROP COLUMN "name";
ALTER TABLE BBYKZ."legal_person_registration" DROP COLUMN "status";
ALTER TABLE BBYKZ."legal_person_registration" RENAME COLUMN NAME_TEST TO NAME;
ALTER TABLE BBYKZ."legal_person_registration" RENAME COLUMN STATUS_TEXT TO STATUS;

CREATE INDEX LEGAL_PERSON_REGISTRATION_STATUS_IDX ON BBYKZ."legal_person_registration" (STATUS);

INSERT INTO BBYKZ."sys_menu"
("id", "code", "parent_code", "name", "description", "enable", "is_leaf", "create_time", "system_code")
VALUES('133', '003001003', '003001', '事业单位法人登记信息查询(已注销)', NULL, 1, 1, NULL, '1');

ALTER TABLE BBYKZ."unified_social_credit_code" ADD INSTITUTION_NAME_TEXT varchar(2000) NULL;
UPDATE unified_social_credit_code SET INSTITUTION_NAME_TEXT  = INSTITUTION_NAME;
ALTER TABLE BBYKZ."unified_social_credit_code" DROP COLUMN "institution_name";
ALTER TABLE BBYKZ."unified_social_credit_code" RENAME COLUMN INSTITUTION_NAME_TEXT TO institution_name;
INSERT INTO BBYKZ."sys_menu"
("id", "code", "parent_code", "name", "description", "enable", "is_leaf", "create_time", "system_code")
VALUES('82', '001001003', '001001', '单位登记信息', NULL, 1, 1, NULL, '1');

UPDATE BBYKZ."efficient_dict_code"
SET  "code_name"='证书到期', "short_name"='证书到期'
WHERE "code_type"='JGSY_BD_TYPE' AND "code"='04';


DELETE FROM unified_social_credit_code WHERE version != ( select version
        from unified_social_credit_code
        where is_delete = 0
        order by version desc limit 1)


DELETE FROM legal_person_registration WHERE version != ( select version
        from legal_person_registration
        where is_delete = 0
        order by version desc limit 1)

-- 2024-09-13
ALTER TABLE BBYKZ."unified_social_credit_code" ADD GEOCODE varchar(100) NULL;

-- 2024-09-14
ALTER TABLE BBYKZ."register_unit_bd" ADD ZY_CODE varchar(200) NULL;

