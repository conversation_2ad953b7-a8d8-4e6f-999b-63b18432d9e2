<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.zenith</groupId>
        <artifactId>sb</artifactId>
        <version>2.2.2</version>
    </parent>
    <groupId>com.zenith</groupId>
    <artifactId>bb_ykz</artifactId>
    <version>1.0.1</version>
    <name>bbykz</name>
    <description>bbykz</description>
    <properties>
        <java.version>1.8</java.version>
    </properties>
    <dependencies>
<!--        <dependency>-->
<!--            <groupId>org.postgresql</groupId>-->
<!--            <artifactId>postgresql</artifactId>-->
<!--        </dependency>-->
<!--        <dependency>-->
<!--            <groupId>net.postgis</groupId>-->
<!--            <artifactId>postgis-jdbc</artifactId>-->
<!--            <version>2.5.1</version>-->
<!--        </dependency>-->
<!--        <dependency>-->
<!--            <groupId>org.apache.commons</groupId>-->
<!--            <artifactId>commons-compress</artifactId>-->
<!--            <version>1.26.1</version>-->
<!--        </dependency>-->


<!--        <dependency>-->
<!--            <groupId>org.springframework.boot</groupId>-->
<!--            <artifactId>spring-boot-starter-web</artifactId>-->
<!--        </dependency>-->

        <dependency>
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok</artifactId>
            <optional>true</optional>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-test</artifactId>
            <scope>test</scope>
        </dependency>

<!--        <dependency>-->
<!--            <groupId>org.apache.httpcomponents</groupId>-->
<!--            <artifactId>httpclient</artifactId>-->
<!--        </dependency>-->
<!--        <dependency>-->
<!--            <groupId>org.apache.commons</groupId>-->
<!--            <artifactId>commons-lang3</artifactId>-->
<!--        </dependency>-->

<!--        <dependency>-->
<!--            <groupId>com.alibaba</groupId>-->
<!--            <artifactId>fastjson</artifactId>-->
<!--        </dependency>-->

<!--        <dependency>-->
<!--            <groupId>joda-time</groupId>-->
<!--            <artifactId>joda-time</artifactId>-->
<!--        </dependency>-->

<!--        <dependency>-->
<!--            <groupId>cn.hutool</groupId>-->
<!--            <artifactId>hutool-all</artifactId>-->
<!--        </dependency>-->

        <!-- 政务钉 -->
        <dependency>
            <groupId>com.alibaba</groupId>
            <artifactId>zwdd-sdk-java</artifactId>
            <version>1.2.0</version>
            <scope>system</scope>
            <systemPath>${basedir}/lib/zwdd-sdk-java-1.2.0.jar</systemPath>
        </dependency>
        <!-- 愉快政 SDK -->
        <dependency>
            <groupId>com.zenith.ykz</groupId>
            <artifactId>ykz-sdk</artifactId>
            <version>2.0.2</version>
            <scope>system</scope>
            <systemPath>${project.basedir}/lib/ykz-sdk-2.0.2.jar</systemPath>
        </dependency>
        <!--  用户中心对接SDK      -->
        <dependency>
            <groupId>com.dcqc</groupId>
            <artifactId>dcqc-uc-oauth-sdk</artifactId>
            <version>3.0.0-RELEASE</version>
            <scope>system</scope>
            <systemPath>${project.basedir}/lib/dcqc-uc-oauth-sdk-3.0.0-RELEASE.jar</systemPath>
        </dependency>
        <dependency>
            <groupId>dm</groupId>
            <artifactId>Dm18</artifactId>
            <version>18</version>
            <scope>system</scope>
            <systemPath>${project.basedir}/lib/Dm7JdbcDriver18.jar</systemPath>
        </dependency>

        <!-- jwt -->
        <dependency>
            <groupId>com.auth0</groupId>
            <artifactId>java-jwt</artifactId>
        </dependency>
        <!-- bc库，BouncyCastle是一款开源的密码包，其中包含了大量的密码算法，使用BouncyCastle的目的就是为了扩充算法支持   -->
        <dependency>
            <groupId>org.bouncycastle</groupId>
            <artifactId>bcprov-jdk15on</artifactId>
        </dependency>
        <dependency>
            <groupId>org.bouncycastle</groupId>
            <artifactId>bcpkix-jdk15on</artifactId>
        </dependency>

        <dependency>
            <groupId>com.alibaba</groupId>
            <artifactId>easyexcel</artifactId>
        </dependency>
        <dependency>
            <groupId>cn.afterturn</groupId>
            <artifactId>easypoi-spring-boot-starter</artifactId>
        </dependency>
        <dependency>
            <groupId>com.baomidou</groupId>
            <artifactId>mybatis-plus-boot-starter</artifactId>
        </dependency>

        <dependency>
            <groupId>com.zenith</groupId>
            <artifactId>sb-cache-start</artifactId>
        </dependency>
        <dependency>
            <groupId>com.zenith</groupId>
            <artifactId>sb-swagger-start</artifactId>
        </dependency>
        <dependency>
            <groupId>com.zenith</groupId>
            <artifactId>sb-configs-start</artifactId>
        </dependency>
        <dependency>
            <groupId>com.zenith</groupId>
            <artifactId>sb-auth-start</artifactId>
        </dependency>
        <dependency>
            <groupId>com.zenith</groupId>
            <artifactId>sb-rate-start</artifactId>
        </dependency>
<!--        <dependency>-->
<!--            <groupId>com.zenith</groupId>-->
<!--            <artifactId>sb-openapi-start</artifactId>-->
<!--        </dependency>-->
        <dependency>
            <groupId>com.zenith</groupId>
            <artifactId>sb-logs-start</artifactId>
        </dependency>
        <dependency>
            <groupId>com.zenith</groupId>
            <artifactId>sb-file-start</artifactId>
        </dependency>
        <dependency>
            <groupId>com.zenith</groupId>
            <artifactId>sb-yu-start</artifactId>
        </dependency>
        <dependency>
            <groupId>com.zenith</groupId>
            <artifactId>sb-system-start</artifactId>
        </dependency>
        <dependency>
            <groupId>com.zenith</groupId>
            <artifactId>sb-task-start</artifactId>
        </dependency>
        <dependency>
            <groupId>net.sf.ehcache</groupId>
            <artifactId>ehcache</artifactId>
        </dependency>
<!--        <dependency>-->
<!--            <groupId>org.springframework.boot</groupId>-->
<!--            <artifactId>spring-boot-starter-data-redis</artifactId>-->
<!--        </dependency>-->
        <dependency>
            <groupId>org.mapstruct</groupId>
            <artifactId>mapstruct</artifactId>
        </dependency>
        <dependency>
            <groupId>org.mapstruct</groupId>
            <artifactId>mapstruct-processor</artifactId>
        </dependency>

        <!--文件生成与上传 begin-->
        <dependency>
            <groupId>org.springframework</groupId>
            <artifactId>spring-test</artifactId>
        </dependency>
        <!--文件生成与上传 end-->
        <dependency>
            <groupId>com.hankcs</groupId>
            <artifactId>hanlp</artifactId>
        </dependency>

    </dependencies>

    <repositories>
        <repository>
            <id>central</id>
            <url>http://192.168.0.76:9999/repository/maven-public/</url>
            <releases>
                <enabled>true</enabled>
            </releases>
            <snapshots>
                <enabled>true</enabled>
            </snapshots>
        </repository>
    </repositories>

    <!-- 环境 -->
    <profiles>
        <profile>
            <id>dev</id>
            <properties>
                <env>dev</env>
                <datasource.url>
                    <![CDATA[jdbc:dm://192.168.0.246:5236/BBYKZ?useUnicode=true&characterEncoding=utf8&useSSL=false&serverTimezone=GMT%2B8]]></datasource.url>
                <datasource.driver>dm.jdbc.driver.DmDriver</datasource.driver>
                <datasource.username>BBYKZ</datasource.username>
                <datasource.password>Cqbbykz.123</datasource.password>
                <swagger-enable>true</swagger-enable>
            </properties>
            <activation>
                <activeByDefault>true</activeByDefault>
            </activation>
        </profile>
        <profile>
            <id>test</id>
            <properties>
                <env>test</env>
                <datasource.url>
                    <![CDATA[jdbc:dm://192.168.0.246:5236/BBYKZ?useUnicode=true&characterEncoding=utf8&useSSL=false&serverTimezone=GMT%2B8]]></datasource.url>
                <datasource.driver>dm.jdbc.driver.DmDriver</datasource.driver>
                <datasource.username>BBYKZ</datasource.username>
                <datasource.password>Cqbbykz.123</datasource.password>
                <swagger-enable>true</swagger-enable>
            </properties>
        </profile>
        <profile>
            <id>prod</id>
            <properties>
                <env>prod</env>
                <datasource.url>
                    <![CDATA[**************************************************************]]></datasource.url>
                <datasource.driver>org.postgresql.Driver</datasource.driver>
                <datasource.username>swbbjgbsjg</datasource.username>
                <datasource.password>Cqbbjgbz@1016#</datasource.password>
                <swagger-enable>true</swagger-enable>
            </properties>
        </profile>
    </profiles>

    <!-- 编译打包 -->
    <build>
        <finalName>${project.name}-${project.version}</finalName>
        <resources>
            <resource>
                <directory>src/main/resources</directory>
                <filtering>true</filtering>
                <excludes>
                    <exclude>templates/**</exclude>
                    <exclude>**/*.xls</exclude>
                    <exclude>**/*.xlsx</exclude>
                    <exclude>**/*.xlsm</exclude>
                    <exclude>**/*.doc</exclude>
                    <exclude>**/*.docx</exclude>
                    <exclude>**/*.cer</exclude>
                </excludes>
            </resource>
            <resource>
                <directory>src/main/resources</directory>
                <filtering>false</filtering>
                <includes>
                    <include>templates/**</include>
                    <include>**/*.xls</include>
                    <include>**/*.xlsx</include>
                    <include>**/*.xlsm</include>
                    <include>**/*.doc</include>
                    <include>**/*.docx</include>
                    <include>**/*.cer</include>
                </includes>
            </resource>
        </resources>
        <plugins>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-shade-plugin</artifactId>
                <version>3.2.4</version>
                <configuration>
                    <createDependencyReducedPom>false</createDependencyReducedPom>
                    <filters>
                        <filter>
                            <artifact>*:*</artifact>
                            <excludes>
                                <exclude>META-INF/*.SF</exclude>
                                <exclude>META-INF/*.sf</exclude>
                                <exclude>META-INF/*.DSA</exclude>
                                <exclude>META-INF/*.dsa</exclude>
                                <exclude>META-INF/*.RSA</exclude>
                                <exclude>META-INF/*.rsa</exclude>
                                <exclude>META-INF/*.EC</exclude>
                                <exclude>META-INF/*.ec</exclude>
                                <exclude>META-INF/MSFTSIG.SF</exclude>
                                <exclude>META-INF/MSFTSIG.RSA</exclude>
                            </excludes>
                        </filter>
                    </filters>
                    <artifactSet>
                        <includes>
                            <include>com.zenith:sb-*</include>
                        </includes>
                    </artifactSet>
                </configuration>
                <executions>
                    <execution>
                        <phase>package</phase>
                        <goals>
                            <goal>shade</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-jar-plugin</artifactId>
                <version>3.2.2</version>
                <configuration>
                    <!-- 将静态资源排除出 jar 包 -->
                    <excludes>
                        <exclude>*.**</exclude>
                        <exclude>static/**</exclude>
                        <exclude>public/**</exclude>
                        <exclude>templates/**</exclude>
                        <exclude>fonts/**</exclude>
                        <!-- 自定义自己的配置文件 -->
                        <exclude>*.yml</exclude>
                    </excludes>
                    <archive>
                        <manifest>
                            <addClasspath>true</addClasspath>
                            <!-- MANIFEST.MF 中 Class-Path 加入前缀 -->
                            <classpathPrefix>lib/</classpathPrefix>
                            <!-- jar 包不包含唯一版本标识 -->
                            <useUniqueVersions>false</useUniqueVersions>
                            <!-- 指定启动类的包路径 -->
                            <mainClass>com.zenith.bbykz.BbYkzApplication</mainClass>
                        </manifest>
                        <manifestEntries>
                            <!--MANIFEST.MF 中 Class-Path 加入资源文件目录 -->
                            <Class-Path>config/ lib/dcqc-uc-oauth-sdk-3.0.0-RELEASE.jar lib/ykz-sdk-2.0.2.jar
                                lib/zwdd-sdk-java-1.2.0.jar lib/Dm18-18.jar
                            </Class-Path>
                        </manifestEntries>
                    </archive>
                    <!-- 指定打出的 jar 包路径 -->
                    <outputDirectory>${project.build.directory}</outputDirectory>
                </configuration>
            </plugin>
            <!-- 这个插件是用来复制项目依赖的 jar 包 -->
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-dependency-plugin</artifactId>
                <executions>
                    <execution>
                        <!-- 自定义 -->
                        <id>copy-dependencies</id>
                        <phase>package</phase>
                        <goals>
                            <!-- 复制依赖的 jar 包 -->
                            <goal>copy-dependencies</goal>
                        </goals>
                        <configuration>
                            <!-- 将依赖的 jar 包复制到该路径下 -->
                            <outputDirectory>${project.build.directory}/lib</outputDirectory>
<!--                            <excludeGroupIds>com.zenith</excludeGroupIds>-->
                            <!-- 排除特定的 Artifact ID -->
                            <excludeArtifactIds>sb-*</excludeArtifactIds>
                            <!--这里指定包含 system scope -->
                        </configuration>
                    </execution>
                </executions>
            </plugin>

            <!-- 这个插件是用来复制项目的静态资源 -->
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-resources-plugin</artifactId>
                <executions>
                    <execution>
                        <!-- 自定义 -->
                        <id>copy-resources</id>
                        <phase>package</phase>
                        <goals>
                            <!-- 复制静态资源 -->
                            <goal>copy-resources</goal>
                        </goals>
                        <configuration>
                            <resources>
                                <resource>
                                    <!-- 指定静态资源的路径 -->
                                    <directory>src/main/resources</directory>
                                    <!--                                    &lt;!&ndash; 处理文件时替换文件中的变量 &ndash;&gt;-->
                                    <filtering>true</filtering>
                                    <!-- 指定需要复制的文件 -->
                                    <includes>
                                        <include>application.yml</include>
                                    </includes>
                                </resource>
                                <resource>
                                    <!-- 指定静态资源的路径 -->
                                    <directory>src/main/resources</directory>
                                    <filtering>false</filtering>
                                    <!-- 指定需要复制的文件 -->
                                    <includes>
                                        <include>application-${env}.yml</include>
                                        <include>*.xml</include>
                                        <include>*.jks</include>
                                        <include>*.properties</include>
                                        <include>public/**</include>
                                        <include>static/**</include>
                                        <include>templates/**</include>
                                        <include>fonts/**</include>
                                        <include>xsd/**</include>
                                        <include>**/*.cer</include>
                                    </includes>
                                    <excludes>
                                        <exclude>**/assembly.xml</exclude>
                                    </excludes>
                                </resource>
                            </resources>
                            <!-- 指定复制到该目录下 -->
                            <outputDirectory>${project.build.directory}/config</outputDirectory>
                        </configuration>
                    </execution>
                </executions>
            </plugin>
            <!--     打包 zip    -->
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-assembly-plugin</artifactId>
                <configuration>
                    <!-- 这个插件需要指定一个配置文件 -->
                    <descriptors>
                        <descriptor>src/main/resources/assembly.xml</descriptor>
                    </descriptors>
                </configuration>
                <executions>
                    <execution>
                        <!-- 自定义 -->
                        <id>make-assembly</id>
                        <phase>package</phase>
                        <goals>
                            <!-- 只执行一次 -->
                            <goal>single</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
        </plugins>
    </build>

</project>
