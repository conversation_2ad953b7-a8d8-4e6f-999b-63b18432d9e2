url=jdbc:dm://192.168.0.246:5236/BBYKZ?useUnicode=true&characterEncoding=utf8&useSSL=false&serverTimezone=GMT%2B8
driver=dm.jdbc.driver.DmDriver
user=BBYKZ
password=Cqbbykz.123

## 是否删除以生成文件（用于配置错误生成的错误文件删除操作）,默认false
isDelete=
## 删除层级，默认生成文件，如top.tanmw.demo.SysUserController 默认只删除 SysUserController.java，如果值为2，则删除demo包下所有文件
deleteLevel=
## 注意文件路径采用反斜杠,默认当前工程路径
#basePath=D:/project/tmw/efficient-boot/efficient-boot-system-start
basePath=
## 注意文件路径采用反斜杠,将在每个文件路径后面在拼接上这个路径/register/interior/
suffixPath=/rwfp/
## 包工程名称，xxx-web,此处写 xxx,默认取当前工程目录名,D:\java\ideaWorkingSpace\my\generator->generator
## moduleName=bbykz
## 包名称,多模块使用
projectName=demo
## 包名称
#packageName=com.efficient.system
packageName=com.zenith.bbykz
## 模式，single工程，multi 多模块，默认多模块
pattern=single
## 包含表名，多个 英文逗号分隔
includeSet=
## 包含前缀
includePrefix=
## 替换前缀
replacePrefix=
## 不包含表名，多个 英文逗号分隔
excludeSet=
## 不包含表名前缀，如sys,sys_等,多个逗号分割
excludePrefix=
## 是否替换覆盖现有文件，默认不会覆盖
replace=true
## 生成文件,1-model,2-dto,3-listDto,4-VO,5-converter,6-mapper,7-dao,8-service,9-serviceimpl,10-controller
## 1-4,生成表示包含中间连续的类型，英文逗号包括分隔
fileType=
## 是否自动生成增删改查方法，模式true
crud=true
## 使用外包模版,模版放在resources/templates不用配置，会默认读取外包模版
templatePath=
## mybatis-plus 逻辑删除字段
tableLogic=is_delete
## mybatis-plus 新增时自动插入字段
tableFieldInsert=create_user,create_time
## mybatis-plus 修改时自动插入字段
tableFieldUpdate=update_user,update_user
## mybatis-plus 新增修改同时变更字段
tableFieldInsertUpdate=