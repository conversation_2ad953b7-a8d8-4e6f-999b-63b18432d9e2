<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <groupId>top.tanmw</groupId>
    <artifactId>code-generate</artifactId>
    <version>1.3.24</version>

    <properties>
        <maven.compiler.source>8</maven.compiler.source>
        <maven.compiler.target>8</maven.compiler.target>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
    </properties>
    <dependencies>
        <dependency>
            <groupId>top.tanmw</groupId>
            <artifactId>efficient-generator</artifactId>
            <version>${project.version}</version>
        </dependency>
        <dependency>
            <groupId>org.postgresql</groupId>
            <artifactId>postgresql</artifactId>
            <version>42.7.3</version>
        </dependency>
        <dependency>
            <groupId>dm</groupId>
            <artifactId>Dm18</artifactId>
            <version>18</version>
            <scope>system</scope>
            <systemPath>${project.basedir}/../lib/Dm7JdbcDriver18.jar</systemPath>
        </dependency>
    </dependencies>
</project>